import { MerchantStatus } from '../types';
/**
 * Merchant status type
 */
export type MerchantStatus = "active" | "pending" | "suspended" | "inactive";
/**
 * Merchant update event data
 */
export interface MerchantUpdateEvent {
    id: string;
    status: MerchantStatus;
    updatedAt: Date | string;
    updatedBy?: string;
    reason?: string;
}
/**
 * Event emitter for merchant events
 */
export declare const merchantEvents: any;
/**
 * Merchant events service
 */
export declare class MerchantEventsService {
    /**
   * Emit merchant created event
   * @param merchant Merchant data
   */
    static emitMerchantCreated(merchant: any): void;
    /**
   * Emit merchant updated event
   * @param merchant Merchant data
   */
    static emitMerchantUpdated(merchant: any): void;
    /**
   * Emit merchant status updated event
   * @param merchantId Merchant ID
   * @param status New status
   * @param updatedBy User who updated the status
   * @param reason Reason for status update
   */
    static emitMerchantStatusUpdated(merchantId: string, status: MerchantStatus, updatedBy?: string, reason?: string): void;
}
//# sourceMappingURL=merchant-events.service.d.ts.map