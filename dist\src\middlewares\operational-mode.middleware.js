"use strict";
// jscpd:ignore-file
/**
 * Operational Mode Middleware
 *
 * Middleware for checking operational mode.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.addOperationalModeHeaders = exports.requireProductionMode = exports.checkSystemEnabled = void 0;
const DIContainer_1 = require("../lib/DIContainer");
const logger_1 = require("../lib/logger");
const error_middleware_1 = require("./error.middleware");
/**
 * Check if system is enabled
 */
const checkSystemEnabled = (req, res, next) => {
    try {
        const operationalModeService = DIContainer_1.container.resolve("operationalModeService");
        if (!operationalModeService.isSystemEnabled()) {
            logger_1.logger.warn("System is disabled, rejecting request", {
                path: req.path,
                method: req.method,
                ip: req.ip
            });
            return next(new error_middleware_1.AppError({
                message: "System is currently disabled for maintenance",
                type: ErrorType.INTERNAL,
                code: ErrorCode.INTERNAL_SERVER_ERROR
            }));
        }
        next();
    }
    catch (error) {
        logger_1.logger.error("Error checking system status:", error);
        next(new error_middleware_1.AppError({
            message: "Error checking system status",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.checkSystemEnabled = checkSystemEnabled;
/**
 * Check if system is in production mode
 */
const requireProductionMode = (req, res, next) => {
    try {
        const operationalModeService = DIContainer_1.container.resolve("operationalModeService");
        if (!operationalModeService.isProductionMode()) {
            logger_1.logger.warn("System is not in production mode, rejecting request", {
                path: req.path,
                method: req.method,
                ip: req.ip,
                currentMode: operationalModeService.getCurrentMode()
            });
            return next(new error_middleware_1.AppError({
                message: "This operation is only available in production mode",
                type: ErrorType.AUTHORIZATION,
                code: ErrorCode.FORBIDDEN
            }));
        }
        next();
    }
    catch (error) {
        logger_1.logger.error("Error checking operational mode:", error);
        next(new error_middleware_1.AppError({
            message: "Error checking operational mode",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};
exports.requireProductionMode = requireProductionMode;
// Demo mode has been removed - only production is supported
// Development mode has been removed - only production is supported
/**
 * Add operational mode to response headers
 */
const addOperationalModeHeaders = (req, res, next) => {
    try {
        const operationalModeService = DIContainer_1.container.resolve("operationalModeService");
        // Add operational mode to response headers - always production
        res.setHeader("X-Operational-Mode", "production");
        res.setHeader("X-System-Enabled", operationalModeService.isSystemEnabled().toString());
        res.setHeader("X-Production-Mode", "true");
        next();
    }
    catch (error) {
        logger_1.logger.error("Error adding operational mode headers:", error);
        next();
    }
};
exports.addOperationalModeHeaders = addOperationalModeHeaders;
//# sourceMappingURL=operational-mode.middleware.js.map