// Jest setup file
import dotenv from 'dotenv';

// Load environment variables for testing
dotenv.config();

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';

// Set timeout for all tests
jest.setTimeout(30000);

// Global beforeAll hook
beforeAll(async () => {
  console.log('Starting test suite');
});

// Global afterAll hook
afterAll(async () => {
  console.log('Test suite completed');
});

// Mock console.error to avoid cluttering test output
const originalConsoleError: any = console.error;
console.error = (...args) => {
  // Check if this is a test-related error that we want to see
  if (
    args[0] &&
    typeof args[0] === 'string' &&
    (args[0].includes('FAIL') || args[0].includes('ERROR'))
  ) {
    originalConsoleError(...args);
  }
  // Otherwise suppress the error in test output
};

// Restore console.error after tests
afterAll(() => {
  console.error = originalConsoleError;
});
