"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toIncludeAnyMembers = toIncludeAnyMembers;
const utils_1 = require("../utils");
function toIncludeAnyMembers(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    const pass = Array.isArray(actual) &&
        Array.isArray(expected) &&
        // @ts-expect-error OK to have implicit any for this.equals
        expected.some(member => (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), actual, member));
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toIncludeAnyMembers') +
                '\n\n' +
                'Expected list to not include any of the following members:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toIncludeAnyMembers') +
                '\n\n' +
                'Expected list to include any of the following members:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
