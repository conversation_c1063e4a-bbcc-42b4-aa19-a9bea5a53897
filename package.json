{"name": "amazing-pay-flow-consolidated", "version": "1.0.0", "description": "Consolidated AmazingPay Flow Server", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:duplication": "node scripts/test-duplication.js", "test:new-code-duplication": "node scripts/test-new-code-duplication.js", "test:all": "npm run test && npm run test:duplication", "lint": "eslint . --ext .ts", "check:duplication": "jscpd . --config .jscpd.json", "check:duplication:strict": "jscpd . --config .jscpd.json --threshold 0", "check:duplication:report": "jscpd . --config .jscpd.json --reporters html,console --output reports/duplication", "check:duplication:precommit": "jscpd . --config .jscpd.json --silent", "check:duplication:fix": "node scripts/fix-duplication.js", "duplication:dashboard": "node scripts/generate-duplication-dashboard.js", "prepare": "husky install", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "ts-node prisma/seed.ts", "db:setup": "npm run prisma:generate && npm run prisma:migrate && npm run prisma:seed", "setup:db": "node scripts/setup-database.js"}, "dependencies": {"@prisma/client": "^6.8.2", "@types/bcrypt": "^5.0.2", "@types/uuid": "^10.0.0", "archiver": "^7.0.1", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "chalk": "^4.1.2", "chart.js": "^4.4.9", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "currency-formatter": "^1.5.9", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "geoip-lite": "^1.4.10", "helmet": "^7.2.0", "i18n-js": "^4.5.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cache": "^5.1.2", "node-cron": "^4.0.7", "nodemailer": "^7.0.3", "pdfkit": "^0.17.1", "pg": "^8.16.0", "prisma": "^6.8.2", "qrcode": "^1.5.4", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.6.1", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "web-push": "^3.6.7", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/chai": "^4.3.11", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.5", "@types/mocha": "^10.0.6", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.4.1", "chai": "^4.3.10", "cross-env": "^7.0.3", "eslint": "^8.37.0", "husky": "^9.0.11", "jest": "^29.7.0", "jscpd": "^3.5.3", "mocha": "^10.2.0", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2", "vitest": "^3.1.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}