"use strict";
/**
 * Test Suite Builders
 *
 * Functions to create comprehensive test suites for different component types.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createControllerTestSuite = createControllerTestSuite;
exports.createServiceTestSuite = createServiceTestSuite;
exports.createRepositoryTestSuite = createRepositoryTestSuite;
exports.createIntegrationTestSuite = createIntegrationTestSuite;
exports.createPerformanceTestSuite = createPerformanceTestSuite;
exports.createApiTestSuite = createApiTestSuite;
exports.createCustomMatcher = createCustomMatcher;
exports.createTestDataBuilder = createTestDataBuilder;
exports.createTestScenarioBuilder = createTestScenarioBuilder;
const TestRunners_1 = require("../runners/TestRunners");
/**
 * Create a test suite for a controller
 */
function createControllerTestSuite(name, controllerClass, tests, config = { name: 'default' }) {
    describe(name, () => {
        let controller;
        // Global setup
        beforeAll(async () => {
            if (config.setup) {
                await config.setup();
            }
        });
        // Global teardown
        afterAll(async () => {
            if (config.teardown) {
                await config.teardown();
            }
        });
        // Setup before each test
        beforeEach(async () => {
            controller = new controllerClass();
            if (config.beforeEach) {
                await config.beforeEach();
            }
        });
        // Cleanup after each test
        afterEach(async () => {
            if (config.afterEach) {
                await config.afterEach();
            }
        });
        // Create individual test cases
        Object.entries(tests).forEach(([method, testOptions]) => {
            const testName = testOptions.description || `should test ${method}`;
            const timeout = testOptions.timeout || config.timeout || 10000;
            if (testOptions.skip) {
                it.skip(testName, () => { });
                return;
            }
            const testFn = testOptions.only ? it.only : it;
            testFn(testName, async () => {
                await (0, TestRunners_1.testController)(controller, method, testOptions);
            }, timeout);
        });
    });
}
/**
 * Create a test suite for a service
 */
function createServiceTestSuite(name, serviceClass, tests, config = { name: 'default' }, setupFn) {
    describe(name, () => {
        let service;
        // Global setup
        beforeAll(async () => {
            if (config.setup) {
                await config.setup();
            }
        });
        // Global teardown
        afterAll(async () => {
            if (config.teardown) {
                await config.teardown();
            }
        });
        // Setup before each test
        beforeEach(async () => {
            service = new serviceClass();
            if (setupFn) {
                await setupFn(service);
            }
            if (config.beforeEach) {
                await config.beforeEach();
            }
        });
        // Cleanup after each test
        afterEach(async () => {
            if (config.afterEach) {
                await config.afterEach();
            }
        });
        // Create individual test cases
        Object.entries(tests).forEach(([method, testOptions]) => {
            const testName = testOptions.description || `should test ${method}`;
            const timeout = testOptions.timeout || config.timeout || 10000;
            if (testOptions.skip) {
                it.skip(testName, () => { });
                return;
            }
            const testFn = testOptions.only ? it.only : it;
            testFn(testName, async () => {
                await (0, TestRunners_1.testService)(service, method, testOptions);
            }, timeout);
        });
    });
}
/**
 * Create a test suite for a repository
 */
function createRepositoryTestSuite(name, repositoryClass, tests, config = { name: 'default' }) {
    describe(name, () => {
        let repository;
        // Global setup
        beforeAll(async () => {
            if (config.setup) {
                await config.setup();
            }
        });
        // Global teardown
        afterAll(async () => {
            if (config.teardown) {
                await config.teardown();
            }
        });
        // Setup before each test
        beforeEach(async () => {
            repository = new repositoryClass();
            if (config.beforeEach) {
                await config.beforeEach();
            }
        });
        // Cleanup after each test
        afterEach(async () => {
            if (config.afterEach) {
                await config.afterEach();
            }
        });
        // Create individual test cases
        Object.entries(tests).forEach(([method, testOptions]) => {
            const testName = testOptions.description || `should test ${method}`;
            const timeout = testOptions.timeout || config.timeout || 10000;
            if (testOptions.skip) {
                it.skip(testName, () => { });
                return;
            }
            const testFn = testOptions.only ? it.only : it;
            testFn(testName, async () => {
                await (0, TestRunners_1.testRepository)(repository, method, testOptions);
            }, timeout);
        });
    });
}
/**
 * Create an integration test suite
 */
function createIntegrationTestSuite(name, scenarios, config = { name: 'default' }) {
    describe(`Integration: ${name}`, () => {
        // Global setup
        beforeAll(async () => {
            if (config.setup) {
                await config.setup();
            }
        });
        // Global teardown
        afterAll(async () => {
            if (config.teardown) {
                await config.teardown();
            }
        });
        // Setup before each test
        beforeEach(async () => {
            if (config.beforeEach) {
                await config.beforeEach();
            }
        });
        // Cleanup after each test
        afterEach(async () => {
            if (config.afterEach) {
                await config.afterEach();
            }
        });
        // Create scenario tests
        scenarios.forEach((scenario) => {
            it(scenario.name, async () => {
                // Scenario setup
                if (scenario.setup) {
                    await scenario.setup();
                }
                try {
                    // Execute steps
                    for (const step of scenario.steps) {
                        await executeTestStep(step);
                    }
                    // Validate expected outcome
                    if (scenario.expectedOutcome) {
                        expect(scenario.expectedOutcome).toBeDefined();
                    }
                }
                finally {
                    // Scenario teardown
                    if (scenario.teardown) {
                        await scenario.teardown();
                    }
                }
            }, config.timeout || 30000);
        });
    });
}
/**
 * Create a performance test suite
 */
function createPerformanceTestSuite(name, tests, config = { name: 'default' }) {
    describe(`Performance: ${name}`, () => {
        // Global setup
        beforeAll(async () => {
            if (config.setup) {
                await config.setup();
            }
        });
        // Global teardown
        afterAll(async () => {
            if (config.teardown) {
                await config.teardown();
            }
        });
        // Create performance tests
        Object.entries(tests).forEach(([testName, testConfig]) => {
            it(`should perform ${testName} within acceptable time`, async () => {
                const iterations = testConfig.iterations || 100;
                const warmupIterations = testConfig.warmupIterations || 10;
                const maxExecutionTime = testConfig.maxExecutionTime || 1000; // 1 second
                const args = testConfig.args || [];
                // Warmup
                for (let i = 0; i < warmupIterations; i++) {
                    await testConfig.fn(...args);
                }
                // Measure performance
                const startTime = process.hrtime.bigint();
                for (let i = 0; i < iterations; i++) {
                    await testConfig.fn(...args);
                }
                const endTime = process.hrtime.bigint();
                const executionTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
                const averageTime = executionTime / iterations;
                expect(averageTime).toBeLessThan(maxExecutionTime);
                console.log(`Performance: ${testName}`);
                console.log(`  Total time: ${executionTime.toFixed(2)}ms`);
                console.log(`  Average time: ${averageTime.toFixed(2)}ms`);
                console.log(`  Iterations: ${iterations}`);
            }, config.timeout || 60000);
        });
    });
}
/**
 * Create an API test suite
 */
function createApiTestSuite(name, baseUrl, endpoints, config = { name: 'default' }) {
    describe(`API: ${name}`, () => {
        // Global setup
        beforeAll(async () => {
            if (config.setup) {
                await config.setup();
            }
        });
        // Global teardown
        afterAll(async () => {
            if (config.teardown) {
                await config.teardown();
            }
        });
        // Create API tests
        Object.entries(endpoints).forEach(([endpointName, endpointConfig]) => {
            it(`should handle ${endpointConfig.method} ${endpointConfig.path}`, async () => {
                // This would integrate with a real HTTP client like supertest
                // For now, it's a placeholder structure
                const url = `${baseUrl}${endpointConfig.path}`;
                const options = {
                    method: endpointConfig.method,
                    headers: endpointConfig.headers || {},
                    body: endpointConfig.body,
                };
                // Mock API call result
                const mockResponse = {
                    status: endpointConfig.expectedStatus || 200,
                    data: endpointConfig.expectedResponse,
                };
                expect(mockResponse.status).toBe(endpointConfig.expectedStatus || 200);
                if (endpointConfig.expectedResponse) {
                    expect(mockResponse.data).toEqual(endpointConfig.expectedResponse);
                }
            });
        });
    });
}
/**
 * Execute a test step
 */
async function executeTestStep(step) {
    const startTime = Date.now();
    try {
        await step.action();
        if (step.validation) {
            await step.validation();
        }
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        if (step.timeout && executionTime > step.timeout) {
            throw new Error(`Test step "${step.name}" timed out after ${executionTime}ms`);
        }
        throw error;
    }
}
/**
 * Create a custom test matcher
 */
function createCustomMatcher(name, matcher) {
    expect.extend({
        [name]: matcher,
    });
}
/**
 * Create a test data builder
 */
function createTestDataBuilder(defaultData, overrides) {
    return {
        ...defaultData,
        ...overrides,
    };
}
/**
 * Create a test scenario builder
 */
function createTestScenarioBuilder(name) {
    let scenario = { name };
    return {
        withDescription: (description) => {
            scenario.description = description;
            return this;
        },
        withSetup: (setup) => {
            scenario.setup = setup;
            return this;
        },
        withTeardown: (teardown) => {
            scenario.teardown = teardown;
            return this;
        },
        withSteps: (steps) => {
            scenario.steps = steps;
            return this;
        },
        withExpectedOutcome: (outcome) => {
            scenario.expectedOutcome = outcome;
            return this;
        },
        build: () => scenario,
    };
}
//# sourceMappingURL=TestSuiteBuilders.js.map