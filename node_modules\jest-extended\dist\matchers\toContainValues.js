"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainValues = toContainValues;
const utils_1 = require("../utils");
function toContainValues(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    let pass = false;
    if (typeof actual === 'object' && actual !== null && !Array.isArray(actual)) {
        const values = Object.keys(actual).map(k => actual[k]);
        // @ts-expect-error OK to have implicit any for this.equals
        pass = expected.every(value => (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), values, value));
    }
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainValues') +
                '\n\n' +
                'Expected object to not contain all values:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainValues') +
                '\n\n' +
                'Expected object to contain all values:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
