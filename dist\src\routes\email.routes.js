"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const email_controller_ts_1 = require("../controllers/refactored/email.controller.ts");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// Email routes
router.post("/test", auth_1.authenticate, email_controller_ts_1.EmailController.testEmailService);
router.post("/send", auth_1.authenticate, email_controller_ts_1.EmailController.sendCustomEmail);
router.get("/admin-emails", auth_1.authenticate, email_controller_ts_1.EmailController.getAdminEmails);
exports.default = router;
//# sourceMappingURL=email.routes.js.map