/**
 * Binance TRC20 Verification Strategy
 *
 * Implements the verification strategy for Binance TRC20 payments.
 */
import { IVerificationStrategy, VerificationRequest, VerificationResult } from "../../../interfaces/verification/IVerificationStrategy";
import { PaymentMethodType } from "../../../types/payment-method.types";
import { VerificationField } from "../../../types/verification.types";
/**
 * Binance TRC20 verification strategy
 */
export declare class BinanceTRC20VerificationStrategy implements IVerificationStrategy {
    private enabled;
    private configuration;
    /**
   * Get the verification method type
   */
    getType(): string;
    /**
   * Get the supported payment method types
   */
    getSupportedPaymentMethods(): PaymentMethodType[];
    /**
   * Verify a payment
   */
    verify(request: VerificationRequest): Promise<VerificationResult>;
    /**
   * Get the required fields for verification
   */
    getRequiredFields(): VerificationField[];
    /**
   * Get the display name of the verification method
   */
    getDisplayName(): string;
    /**
   * Get the description of the verification method
   */
    getDescription(): string;
    /**
   * Check if the verification method is enabled
   */
    isEnabled(): boolean;
    /**
   * Get the configuration of the verification method
   */
    getConfiguration(): Record<string, any>;
    /**
   * Set the configuration of the verification method
   */
    setConfiguration(config: Record<string, any>): void;
    /**
   * Verify a transaction on the Binance API
   */
    private verifyTransactionOnBinance;
}
//# sourceMappingURL=BinanceTRC20VerificationStrategy.d.ts.map