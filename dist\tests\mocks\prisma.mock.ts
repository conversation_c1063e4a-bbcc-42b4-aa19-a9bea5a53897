// jscpd:ignore-file
/**
 * Mock implementation of PrismaClient for testing
 */
import { vi } from "vitest";

// Define common enums used in tests
export enum IdentityVerificationMethodEnum {
  EMAIL = "EMAIL",
  PHONE = "PHONE",
  DOCUMENT = "DOCUMENT",
  BLOCKCHAIN = "B<PERSON><PERSON><PERSON><PERSON>AIN",
  SOCIAL = "SOCIAL"
}

export enum IdentityVerificationStatusEnum {
  PENDING = "PENDING",
  VERIFIED = "VERIFIED",
  REJECTED = "REJECTED",
  EXPIRED = "EXPIRED"
}

export enum PaymentMethodTypeEnum {
  CRYPTO = "CRYPTO",
  BINANCE_PAY = "BINANCE_PAY",
  BINANCE_C2C = "BINANCE_C2C",
  BINANCE_TRC20 = "BINANCE_TRC20",
  BANK_TRANSFER = "BANK_TRANSFER"
}


export enum PaymentStatusEnum {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  EXPIRED = "EXPIRED",
  REFUNDED = "REFUNDED"
}

// Create mock PrismaClient
export const mockPrismaClient = {
}