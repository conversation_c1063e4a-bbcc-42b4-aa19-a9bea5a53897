{"version": 3, "file": "log-rotation.js", "sourceRoot": "", "sources": ["../../../src/utils/log-rotation.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;GAKG;;;;;;AAEH,4CAAoB;AACpB,gDAAwB;AACxB,uCAAyD;AACzD,0CAAuC;AAEvC,uDAAuD;AAKvD,gBAAgB;AAChB,MAAM,gBAAgB,GAAU,EAAE,CAAC,CAAC,mCAAmC;AAEvE,0CAA0C;AAC1C,MAAM,eAAe,GAAQ,GAAW,EAAE;IACtC,MAAM,GAAG,GAAG,IAAA,4BAAc,GAAE,CAAC;IAC7B,OAAO,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;AACjD,CAAC,CAAC;AACF,MAAM,SAAS,GAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;AAEzE;;;GAGG;AACI,MAAM,cAAc,GAAQ,KAAK,IAAmB,EAAE;IACzD,IAAI,CAAC;QACL,yCAAyC;QACrC,MAAM,YAAY,GAAQ,eAAe,EAAE,CAAC;QAE5C,+BAA+B;QAC/B,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YAC/B,eAAM,CAAC,IAAI,CAAC,iBAAiB,YAAY,8BAA8B,CAAC,CAAC;YACzE,YAAE,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAChD,OAAO;QACX,CAAC;QAED,sCAAsC;QACtC,MAAM,KAAK,GAAQ,YAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEhD,wBAAwB;QACxB,MAAM,UAAU,GAAQ,IAAA,kBAAO,EAAC,IAAI,IAAI,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAE9D,0CAA0C;QAC1C,MAAM,WAAW,GAAQ,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;IAAC,CAAC,AAAF;YACzC,8BAA8B;KADY,CAAC,CAAD,CAAC,AAAF;AAAC,CAAC,AAAF,CAAA;AAnBxC,QAAA,cAAc,kBAmB0B;AAAI,CAAC;IAC9C,8BAA8B;IAC9B,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAE/F,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,2DAA2D;IAC3D,MAAM,SAAS,GAAQ,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAEvD,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,2BAA2B;IAC3B,MAAM,QAAQ,GAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAE7C,0CAA0C;IAC1C,OAAO,QAAQ,GAAG,UAAU,CAAC;AACjC,CAAC;AAAC,CAAC;AAEH,uBAAuB;AACvB,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;IAC7B,MAAM,QAAQ,GAAQ,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACpD,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACxB,eAAM,CAAC,IAAI,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;AACjD,CAAC;AAED,eAAM,CAAC,IAAI,CAAC,kCAAkC,WAAW,CAAC,MAAM,iBAAiB,CAAC,CAAC;AACrF,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACb,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;AACtD,CAAC;AACJ,CAAC;AAEF;;;;GAIG;AACI,MAAM,eAAe,GAAQ,KAAK,EAAE,OAAe,CAAC,EAAiB,EAAE;IAC1E,kDAAkD;IAClD,6DAA6D;IAC7D,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;AACvD,CAAC,CAAC;AAJW,QAAA,eAAe,mBAI1B;AAEF;;GAEG;AACI,MAAM,mBAAmB,GAAQ,GAAmB,EAAE;IACzD,0CAA0C;IAC1C,IAAA,sBAAc,GAAE,CAAC;IAEjB,iDAAiD;IACjD,MAAM,GAAG,GAAQ,IAAI,IAAI,EAAE,CAAC;IAC5B,MAAM,QAAQ,GAAQ,IAAI,IAAI,CAC1B,GAAG,CAAC,WAAW,EAAE,EACjB,GAAG,CAAC,QAAQ,EAAE,EACd,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,EACjB,CAAC,EAAE,CAAC,EAAE,CAAC,CACV,CAAC;IACF,MAAM,iBAAiB,GAAQ,QAAQ,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;IAElE,yBAAyB;IACzB,MAAM,KAAK,GAAQ,WAAW,CAAC,GAAG,EAAE;QAChC,IAAA,sBAAc,GAAE,CAAC;IACrB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW;IAEpC,qCAAqC;IACrC,UAAU,CAAC,GAAG,EAAE;QACZ,IAAA,sBAAc,GAAE,CAAC;IACrB,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAEtB,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAzBW,QAAA,mBAAmB,uBAyB9B;AAEF,kBAAe;IACX,cAAc,EAAd,sBAAc;IACd,eAAe,EAAf,uBAAe;IACf,mBAAmB,EAAnB,2BAAmB;CACtB,CAAC"}