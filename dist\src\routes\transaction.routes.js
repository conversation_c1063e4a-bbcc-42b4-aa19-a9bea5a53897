"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const transaction_controller_ts_1 = require("../controllers/refactored/transaction.controller.ts");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_middleware_1.authMiddleware);
// Routes accessible by ADMIN and SUPER_ADMIN
router.get("/", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN"]), transaction_controller_ts_1.getAllTransactions);
router.get("/stats", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN"]), transaction_controller_ts_1.getTransactionStats);
// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
router.get("/:id", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), transaction_controller_ts_1.getTransactionById);
router.get("/reference/:reference", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), transaction_controller_ts_1.getTransactionByReference);
router.post("/", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), transaction_controller_ts_1.createTransaction);
router.put("/:id/status", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), transaction_controller_ts_1.updateTransactionStatus);
router.post("/verify", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), transaction_controller_ts_1.verifyPayment);
// Merchant-specific routes
router.get("/merchant/:merchantId", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), transaction_controller_ts_1.getMerchantTransactions);
router.get("/merchant/:merchantId/stats", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), transaction_controller_ts_1.getTransactionStats);
exports.default = router;
//# sourceMappingURL=transaction.routes.js.map