"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const auth_controller_1 = __importDefault(require("../controllers/auth.controller"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const rate_limit_middleware_1 = require("../middlewares/rate-limit.middleware");
const router = (0, express_1.Router)();
// Admin login route
router.post("/login", rate_limit_middleware_1.authLimiter, (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("email").isEmail().withMessage("Valid email is required"),
    (0, express_validator_1.body)("password").isString().withMessage("Password is required")
]), auth_controller_1.default.login);
// Logout route
router.post("/logout", auth_middleware_1.authenticate, auth_controller_1.default.logout);
// Get current user info
router.get("/me", auth_middleware_1.authenticate, auth_controller_1.default.getCurrentUser);
// Get user permissions
router.get("/permissions", auth_middleware_1.authenticate, auth_controller_1.default.getUserPermissions);
// Two-factor authentication routes
router.get("/2fa/status", auth_middleware_1.authenticate, auth_controller_1.default.getTwoFactorStatus);
router.post("/2fa/setup", auth_middleware_1.authenticate, auth_controller_1.default.setupTwoFactor);
router.post("/2fa/verify", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("code").isString().isLength({ min: 6, max: 6 }),
    (0, express_validator_1.body)("secret").isString()
]), auth_controller_1.default.verifyAndEnableTwoFactor);
router.post("/2fa/disable", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("code").isString().isLength({ min: 6, max: 6 })
]), auth_controller_1.default.disableTwoFactor);
router.post("/2fa/backup-codes", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("code").isString().isLength({ min: 6, max: 6 })
]), auth_controller_1.default.generateBackupCodes);
router.post("/2fa/recover", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("backupCode").isString()
]), auth_controller_1.default.recoverWithBackupCode);
// Merchant registration route
router.post("/merchant/register", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("email").isEmail().withMessage("Valid email is required"),
    (0, express_validator_1.body)("password").isString().isLength({ min: 8 }).withMessage("Password must be at least 8 characters"),
    (0, express_validator_1.body)("name").isString().withMessage("Name is required"),
    (0, express_validator_1.body)("contactPhone").isString().withMessage("Contact phone is required"),
    (0, express_validator_1.body)("merchantLocation").isString().withMessage("Merchant location is required"),
    (0, express_validator_1.body)("country").isString().withMessage("Country is required"),
    (0, express_validator_1.body)("governorate").isString().withMessage("Governorate/Province is required")
]), auth_controller_1.default.registerMerchant);
exports.default = router;
//# sourceMappingURL=auth.routes.js.map