"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const fraud_detection_1 = require("../controllers/fraud-detection");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const role_middleware_1 = require("../middlewares/role.middleware");
const router = express_1.default.Router();
const fraudDetectionController = new fraud_detection_1.FraudDetectionController();
/**
 * @route POST /api/fraud-detection/assess
 * @desc Assess transaction risk
 * @access Private (Merchant, Admin)
 */
router.post('/assess', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['MERCHANT', 'ADMIN']), fraudDetectionController.assessTransactionRisk);
/**
 * @route GET /api/fraud-detection/transaction/:transactionId
 * @desc Get risk assessment for a transaction
 * @access Private (Merchant, Admin)
 */
router.get('/transaction/:transactionId', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['MERCHANT', 'ADMIN']), fraudDetectionController.getTransactionRiskAssessment);
/**
 * @route GET /api/fraud-detection/config/:merchantId
 * @desc Get fraud detection configuration for a merchant
 * @access Private (Merchant, Admin)
 */
router.get('/config/:merchantId', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['MERCHANT', 'ADMIN']), fraudDetectionController.getMerchantFraudConfig);
/**
 * @route PUT /api/fraud-detection/config/:merchantId
 * @desc Update fraud detection configuration for a merchant
 * @access Private (Admin)
 */
router.put('/config/:merchantId', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['ADMIN']), fraudDetectionController.updateMerchantFraudConfig);
/**
 * @route GET /api/fraud-detection/flagged
 * @desc Get flagged transactions
 * @access Private (Admin)
 */
router.get('/flagged', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['ADMIN']), fraudDetectionController.getFlaggedTransactions);
/**
 * @route GET /api/fraud-detection/statistics
 * @desc Get fraud statistics
 * @access Private (Admin)
 */
router.get('/statistics', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['ADMIN']), fraudDetectionController.getFraudStatistics);
exports.default = router;
//# sourceMappingURL=fraud-detection.routes.js.map