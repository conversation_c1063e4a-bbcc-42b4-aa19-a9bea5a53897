/**
 * RBAC Middleware
 *
 * Middleware for role-based access control.
 */
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Middleware to check if user has required permission
 */
export declare const requirePermission: any;
/**
 * Middleware to check if user has any of the required permissions
 */
export declare const requireAnyPermission: any;
/**
 * Middleware to check if user has required role
 */
export declare const requireRole: any;
/**
 * Middleware to attach user permissions to request
 */
export declare const attachPermissions: any;
//# sourceMappingURL=rbac.middleware.d.ts.map