{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:55:34.621Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:55:34.623Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:55:34.623Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:55:34.624Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:55:34.697Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:55:34.865Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:55:34.974Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:58:45.743Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:58:45.744Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:58:45.745Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:58:45.745Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:58:45.807Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:58:45.922Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:58:46.011Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:59:13.557Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:59:13.558Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:59:13.559Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:59:13.559Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:59:13.574Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:59:13.666Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:59:13.800Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:59:25.142Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:59:25.144Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:59:25.145Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:59:25.146Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:59:25.154Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:59:25.175Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:59:25.333Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:59:37.393Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:59:37.394Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:59:37.396Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:59:37.396Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:59:37.406Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:59:37.423Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:59:37.736Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:00:53.974Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:00:53.975Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:00:53.976Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:00:53.976Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:00:54.048Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:00:54.139Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:00:54.226Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:01:27.364Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:01:27.365Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:01:27.366Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:01:27.366Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:01:27.376Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:01:27.526Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:01:27.633Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:01:37.027Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:01:37.030Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:01:37.031Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:01:37.034Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:01:37.043Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:01:37.077Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:01:37.262Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:01:48.236Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:01:48.238Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:01:48.238Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:01:48.239Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:01:48.247Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:01:48.273Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:01:48.378Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:06:48.266Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:06:48.268Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:06:48.268Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:06:48.269Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:06:48.339Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:06:48.482Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:06:48.647Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:07:22.523Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:07:22.524Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:07:22.525Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:07:22.526Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:07:22.551Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:07:22.574Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:07:22.665Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:07:35.147Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:07:35.148Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:07:35.149Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:07:35.149Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:07:35.159Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:07:35.214Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:07:35.327Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:08:37.696Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:08:37.698Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:08:37.698Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:08:37.699Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:08:37.707Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:08:37.765Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:08:37.879Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:08:55.092Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:08:55.094Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:08:55.094Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:08:55.095Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:08:55.107Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:08:55.124Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:08:55.419Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:09:32.833Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:09:32.834Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:09:32.835Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:09:32.836Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:09:32.848Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:09:33.084Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:09:33.215Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:09:45.838Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:09:45.860Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:09:45.892Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:09:45.935Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:09:45.988Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:09:46.307Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:09:46.444Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:10:26.128Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:10:26.130Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:10:26.132Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:10:26.133Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:10:26.145Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:10:26.367Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:10:26.464Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:10:38.707Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:10:38.709Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:10:38.710Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:10:38.710Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:10:38.718Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:10:39.265Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:10:39.428Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:20:36.263Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:20:36.264Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:20:36.265Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:20:36.266Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:20:36.340Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:20:36.530Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:20:36.617Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:21:09.865Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:21:09.867Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:21:09.868Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:21:09.868Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:21:09.878Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:21:09.901Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:21:10.000Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:21:21.941Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:21:21.942Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:21:21.942Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:21:21.943Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:21:21.951Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:21:21.979Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:21:22.076Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:21:34.726Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:21:34.727Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:21:34.728Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:21:34.728Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:21:34.738Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:21:34.759Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:21:34.860Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:21:46.810Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:21:46.812Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:21:46.813Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:21:46.813Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:21:46.823Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:21:46.850Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:21:46.954Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:21:59.312Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:21:59.313Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:21:59.314Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:21:59.315Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:21:59.323Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:21:59.448Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:21:59.547Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:22:08.888Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:22:08.889Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:22:08.889Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:22:08.892Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:22:08.901Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:22:08.921Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:22:09.017Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:22:20.315Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:22:20.316Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:22:20.317Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:22:20.317Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:22:20.325Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:22:20.350Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:22:20.450Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:22:32.771Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:22:32.773Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:22:32.773Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:22:32.776Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:22:32.785Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:22:32.810Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:22:32.916Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:22:45.229Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:22:45.231Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:22:45.232Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:22:45.232Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:22:45.240Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:22:45.266Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:22:45.376Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:22:57.405Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:22:57.406Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:22:57.406Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:22:57.407Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:22:57.418Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:22:57.445Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:22:57.549Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:23:43.486Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:23:43.487Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:23:43.488Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:23:43.488Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:23:43.499Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:23:43.523Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:23:43.731Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:23:55.326Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:23:55.328Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:23:55.328Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:23:55.329Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:23:55.338Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:23:55.359Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:23:55.457Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:24:07.514Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:24:07.515Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:24:07.516Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:24:07.516Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:24:07.524Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:24:07.550Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:24:07.762Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:24:21.311Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:24:21.312Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:24:21.312Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:24:21.313Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:24:21.325Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:24:21.347Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:24:21.446Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:24:33.568Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:24:33.569Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:24:33.570Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:24:33.570Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:24:33.580Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:24:33.673Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:24:33.782Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:24:46.177Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:24:46.179Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:24:46.180Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:24:46.181Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:24:46.189Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:24:46.386Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:24:46.505Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:25:29.441Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:25:29.442Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:25:29.443Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:25:29.444Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:25:29.458Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:25:29.601Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:25:29.854Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:25:51.710Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:25:51.716Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:25:51.717Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:25:51.718Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:25:51.730Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:25:51.848Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:25:51.947Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:26:16.409Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:26:16.411Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:26:16.411Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:26:16.412Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:26:16.424Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:26:16.518Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:26:16.605Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:26:29.496Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:26:29.497Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:26:29.498Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:26:29.498Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:26:29.523Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:26:29.646Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:26:29.739Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:26:41.964Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:26:41.965Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:26:41.966Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:26:41.966Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:26:41.976Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:26:42.097Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:26:42.203Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:26:54.647Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:26:54.649Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:26:54.649Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:26:54.650Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:26:54.658Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:26:54.773Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:26:54.992Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:27:03.926Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:27:03.928Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:27:03.929Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:27:03.929Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:27:03.937Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:27:04.034Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:27:04.257Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:27:16.980Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:27:16.983Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:27:16.984Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:27:16.985Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:27:16.993Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:27:17.102Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:27:17.193Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:27:29.642Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:27:29.643Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:27:29.644Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:27:29.644Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:27:29.654Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:27:29.742Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:27:29.829Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:28:22.330Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:28:22.331Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:28:22.332Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:28:22.332Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:28:22.404Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:28:22.790Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:28:22.877Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:28:58.843Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:28:58.844Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:28:58.845Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:28:58.846Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:28:58.854Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:28:59.041Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:28:59.173Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:29:12.931Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:29:12.936Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:29:12.938Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:29:12.939Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:29:12.948Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:29:13.062Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:29:13.148Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:29:25.499Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:29:25.501Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:29:25.502Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:29:25.502Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:29:25.518Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:29:25.650Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:29:25.738Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:29:39.186Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:29:39.188Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:29:39.221Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:29:39.251Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:29:39.264Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:29:39.356Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:29:39.444Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:29:53.263Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:29:53.264Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:29:53.265Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:29:53.270Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:29:53.281Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:29:53.386Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:29:53.472Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:30:06.512Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:30:06.516Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:30:06.517Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:30:06.518Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:30:06.525Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:30:06.627Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:30:06.728Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:30:29.733Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:30:29.734Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:30:29.735Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:30:29.736Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:30:29.745Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:30:29.831Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:30:29.917Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:30:51.401Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:30:51.402Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:30:51.403Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:30:51.403Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:30:51.414Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:30:51.519Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:30:51.611Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:31:10.260Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:31:10.261Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:31:10.261Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:31:10.262Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:31:10.271Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:31:10.484Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:31:10.629Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:31:35.677Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:31:35.678Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:31:35.679Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:31:35.679Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:31:35.688Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:31:35.781Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:31:35.871Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:31:49.251Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:31:49.252Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:31:49.253Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:31:49.254Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:31:49.265Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:31:49.364Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:31:49.450Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:32:31.084Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:32:31.085Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:32:31.085Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:32:31.086Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:32:31.098Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:32:31.228Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:32:31.325Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:32:44.095Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:32:44.098Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:32:44.098Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:32:44.099Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:32:44.108Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:32:44.289Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:32:44.378Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:33:22.756Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:33:22.757Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:33:22.758Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:33:22.759Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:33:22.769Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:33:22.862Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:33:22.949Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:33:38.870Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:33:38.871Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:33:38.871Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:33:38.872Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:33:38.881Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:33:38.974Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:33:39.066Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:33:53.853Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:33:53.855Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:33:53.855Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:33:53.856Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:33:53.865Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:33:54.128Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:33:54.253Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:34:07.909Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:34:07.911Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:34:07.911Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:34:07.912Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:34:07.919Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:34:08.031Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:34:08.137Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:34:21.981Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:34:21.982Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:34:21.982Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:34:21.983Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:34:21.992Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:34:22.096Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:34:22.185Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:34:36.436Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:34:36.437Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:34:36.437Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:34:36.438Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:34:36.445Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:34:36.540Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:34:36.771Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:34:50.043Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:34:50.044Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:34:50.045Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:34:50.046Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:34:50.055Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:34:50.145Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:34:50.237Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:35:04.403Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:35:04.405Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:35:04.405Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:35:04.406Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:35:04.413Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:35:04.529Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:35:04.619Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:35:17.999Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:35:18.001Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:35:18.001Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:35:18.002Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:35:18.009Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:35:18.102Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:35:18.197Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:35:31.703Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:35:31.704Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:35:31.705Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:35:31.705Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:35:31.717Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:35:31.807Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:35:31.899Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:35:45.283Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:35:45.284Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:35:45.285Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:35:45.285Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:35:45.302Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:35:45.539Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:35:45.663Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:36:57.230Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:36:57.232Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:36:57.233Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:36:57.234Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:36:57.243Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:36:57.632Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:36:57.723Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:52:27.456Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:52:27.457Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:52:27.457Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:52:27.458Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:52:27.526Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:52:28.137Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:52:28.452Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:53:54.502Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:53:54.505Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:53:54.506Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:53:54.506Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:53:54.514Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:53:54.737Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:53:54.829Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:54:09.685Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:54:09.688Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:54:09.689Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:54:09.691Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:54:09.700Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:54:09.949Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:54:10.050Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:54:23.431Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:54:23.433Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:54:23.433Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:54:23.434Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:54:23.442Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:54:23.666Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:54:23.760Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:54:37.760Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:54:37.761Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:54:37.761Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:54:37.762Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:54:37.769Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:54:38.027Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:54:38.129Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:54:53.042Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:54:53.043Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:54:53.044Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:54:53.044Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:54:53.052Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:54:53.260Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:54:53.354Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:55:10.135Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:55:10.136Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:55:10.136Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:55:10.137Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:55:10.146Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:55:10.352Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:55:10.445Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:55:26.344Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:55:26.346Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:55:26.346Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:55:26.346Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:55:26.354Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:55:26.559Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:55:26.652Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:55:40.057Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:55:40.077Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:55:40.078Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:55:40.082Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:55:40.092Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:55:40.482Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:55:40.606Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:56:18.506Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:56:18.508Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:56:18.509Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:56:18.510Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:56:18.521Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:56:19.487Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:56:19.582Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:56:35.856Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:56:35.858Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:56:35.858Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:56:35.859Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:56:35.892Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:56:36.478Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:56:36.587Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:56:48.842Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:56:48.844Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:56:48.844Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:56:48.845Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:56:48.855Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:56:49.262Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:56:49.368Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:57:13.897Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:57:13.899Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:57:13.899Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:57:13.900Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:57:13.910Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:57:14.204Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:57:14.294Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:57:44.167Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:57:44.168Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:57:44.169Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:57:44.169Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:57:44.177Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:57:44.428Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:57:44.521Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:57:57.136Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:57:57.139Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:57:57.139Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:57:57.140Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:57:57.148Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:57:57.423Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:57:57.524Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:58:34.507Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:58:34.508Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:58:34.509Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:58:34.510Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:58:34.519Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:58:35.028Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:58:35.124Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:58:46.718Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:58:46.720Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:58:46.721Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:58:46.724Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:58:46.740Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:58:47.159Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:58:47.247Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:59:15.732Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:59:15.733Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:59:15.734Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:59:15.734Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:59:15.744Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:59:16.032Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:59:16.135Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:59:28.838Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:59:28.840Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:59:28.840Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:59:28.841Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:59:28.848Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:59:29.126Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:59:29.216Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:59:42.033Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:59:42.034Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:59:42.035Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:59:42.035Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:59:42.043Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:59:42.418Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:59:42.505Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T16:59:54.849Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T16:59:54.850Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T16:59:54.851Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T16:59:54.851Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T16:59:54.861Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T16:59:55.160Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T16:59:55.383Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:00:09.536Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:00:09.537Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:00:09.537Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:00:09.538Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:00:09.548Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:00:09.820Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:00:09.909Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:00:22.893Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:00:22.894Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:00:22.895Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:00:22.896Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:00:22.904Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:00:23.196Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:00:23.380Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:00:36.886Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:00:36.888Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:00:36.888Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:00:36.889Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:00:36.896Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:00:37.169Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:00:37.377Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:00:50.827Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:00:50.828Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:00:50.829Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:00:50.830Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:00:50.839Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:00:51.273Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:00:51.367Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:02:28.730Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:02:28.731Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:02:28.731Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:02:28.732Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:02:28.799Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:02:29.578Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:02:29.670Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:03:26.270Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:03:26.302Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:03:26.312Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:03:26.316Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:03:26.335Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:03:26.778Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:03:26.883Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:03:58.023Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:03:58.024Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:03:58.025Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:03:58.025Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:03:58.036Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:03:58.356Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:03:58.454Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:04:12.403Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:04:12.405Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:04:12.405Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:04:12.406Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:04:12.420Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:04:12.723Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:04:12.817Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:04:26.933Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:04:26.935Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:04:26.935Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:04:26.936Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:04:26.946Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:04:27.285Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:04:27.376Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:04:42.861Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:04:42.863Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:04:42.864Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:04:42.865Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:04:42.873Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:04:43.177Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:04:43.266Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:04:58.018Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:04:58.019Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:04:58.020Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:04:58.020Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:04:58.031Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:04:58.347Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:04:58.437Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:05:11.701Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:05:11.702Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:05:11.703Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:05:11.703Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:05:11.711Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:05:12.095Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:05:12.185Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:05:25.590Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:05:25.591Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:05:25.592Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:05:25.592Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:05:25.600Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:05:25.925Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:05:26.022Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:05:41.171Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:05:41.173Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:05:41.174Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:05:41.174Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:05:41.190Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:05:41.817Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:05:42.260Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:06:24.004Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:06:24.005Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:06:24.006Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:06:24.006Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:06:24.018Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:06:24.516Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:06:24.612Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:06:56.640Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:06:56.641Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:06:56.642Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:06:56.642Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:06:56.653Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:06:57.251Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:06:57.356Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:07:07.423Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:07:07.425Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:07:07.425Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:07:07.427Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:07:07.436Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:07:07.748Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:07:07.844Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:07:23.742Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:07:23.744Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:07:23.745Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:07:23.747Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:07:23.755Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:07:24.078Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:07:24.170Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:07:40.509Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:07:40.510Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:07:40.511Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:07:40.511Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:07:40.523Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:07:41.068Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:07:41.160Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:08:03.765Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:08:03.767Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:08:03.767Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:08:03.768Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:08:03.777Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:08:04.239Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:08:04.432Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:08:42.420Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:08:42.421Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:08:42.421Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:08:42.422Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:08:42.431Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:08:42.756Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:08:42.851Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:08:57.874Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:08:57.875Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:08:57.876Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:08:57.876Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:08:57.883Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:08:58.210Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:08:58.302Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:09:12.435Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:09:12.437Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:09:12.437Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:09:12.438Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:09:12.448Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:09:12.768Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:09:12.862Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:09:47.007Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:09:47.011Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:09:47.012Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:09:47.013Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:09:47.021Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:09:47.335Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:09:47.425Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:10:01.473Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:10:01.474Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:10:01.475Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:10:01.475Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:10:01.493Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:10:01.951Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:10:02.101Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:10:34.688Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:10:34.690Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:10:34.690Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:10:34.691Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:10:34.711Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:10:35.034Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:10:35.131Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:10:47.583Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:10:47.586Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:10:47.587Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:10:47.588Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:10:47.595Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:10:47.931Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:10:48.023Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:10:59.703Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:10:59.704Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:10:59.705Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:10:59.707Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:10:59.715Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:11:00.027Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:11:00.124Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:11:11.945Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:11:11.947Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:11:11.947Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:11:11.948Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:11:11.958Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:11:12.439Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:11:12.531Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:11:46.664Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:11:46.666Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:11:46.666Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:11:46.667Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:11:46.678Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:11:47.009Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:11:47.105Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:11:59.473Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:11:59.475Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:11:59.475Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:11:59.475Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:11:59.486Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:11:59.812Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:11:59.906Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:12:10.816Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:12:10.817Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:12:10.817Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:12:10.818Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:12:10.827Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:12:11.148Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:12:11.246Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:12:22.886Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:12:22.887Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:12:22.887Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:12:22.888Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:12:22.896Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:12:23.229Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:12:23.323Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:12:34.649Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:12:34.651Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:12:34.651Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:12:34.652Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:12:34.661Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:12:35.006Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:12:35.099Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:12:47.564Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:12:47.568Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:12:47.569Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:12:47.569Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:12:47.577Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:12:47.916Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:12:48.009Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:13:00.184Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:13:00.185Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:13:00.186Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:13:00.186Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:13:00.195Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:13:00.654Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:13:00.765Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:13:12.726Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:13:12.727Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:13:12.729Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:13:12.731Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:13:12.751Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:13:13.249Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:13:13.346Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:13:32.381Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:13:32.383Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:13:32.389Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:13:32.391Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:13:32.401Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:13:32.741Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:13:32.836Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:30:38.328Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:30:38.329Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:30:38.330Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:30:38.331Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:30:38.345Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:30:38.849Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:30:38.946Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:37:38.189Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:37:38.190Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:37:38.191Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:37:38.191Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:37:38.203Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:37:38.781Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:37:39.105Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:47:51.232Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:47:51.233Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:47:51.233Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:47:51.235Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:47:51.243Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:47:51.582Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:47:51.825Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:49:32.405Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:49:32.406Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:49:32.407Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:49:32.408Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:49:32.418Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:49:32.640Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:49:32.861Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:49:54.390Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:49:54.392Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:49:54.392Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:49:54.393Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:49:54.467Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:49:54.928Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:49:55.127Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:52:21.221Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:52:21.222Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:52:21.222Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:52:21.223Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:52:21.235Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:52:21.353Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:52:21.449Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T17:58:06.093Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T17:58:06.094Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T17:58:06.094Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T17:58:06.095Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T17:58:06.167Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T17:58:06.624Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T17:58:06.713Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T18:28:02.042Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T18:28:02.043Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T18:28:02.044Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T18:28:02.044Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T18:28:02.118Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T18:28:02.648Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T18:28:02.737Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T18:38:07.668Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T18:38:07.670Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T18:38:07.671Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T18:38:07.672Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T18:38:07.741Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T18:38:08.260Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T18:38:08.347Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T18:39:45.092Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T18:39:45.094Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T18:39:45.094Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T18:39:45.095Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T18:39:45.172Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T18:39:45.698Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T18:39:45.786Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T18:53:15.316Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T18:53:15.317Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T18:53:15.317Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T18:53:15.318Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T18:53:15.391Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T18:53:16.028Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T18:53:16.120Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T19:42:50.766Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T19:42:50.768Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T19:42:50.768Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T19:42:50.768Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T19:42:50.849Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T19:42:51.378Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T19:42:51.472Z"}
