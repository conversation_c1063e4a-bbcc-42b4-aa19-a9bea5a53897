{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:55:34.621Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:55:34.623Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:55:34.623Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:55:34.624Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:55:34.697Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:55:34.865Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:55:34.974Z"}
