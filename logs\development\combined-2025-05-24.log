{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:55:34.621Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:55:34.623Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:55:34.623Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:55:34.624Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:55:34.697Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:55:34.865Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:55:34.974Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:58:45.743Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:58:45.744Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:58:45.745Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:58:45.745Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:58:45.807Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:58:45.922Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:58:46.011Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:59:13.557Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:59:13.558Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:59:13.559Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:59:13.559Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:59:13.574Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:59:13.666Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:59:13.800Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:59:25.142Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:59:25.144Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:59:25.145Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:59:25.146Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:59:25.154Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:59:25.175Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:59:25.333Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T00:59:37.393Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T00:59:37.394Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T00:59:37.396Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T00:59:37.396Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T00:59:37.406Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T00:59:37.423Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T00:59:37.736Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:00:53.974Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:00:53.975Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:00:53.976Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:00:53.976Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:00:54.048Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:00:54.139Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:00:54.226Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:01:27.364Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:01:27.365Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:01:27.366Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:01:27.366Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:01:27.376Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:01:27.526Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:01:27.633Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:01:37.027Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:01:37.030Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:01:37.031Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:01:37.034Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:01:37.043Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:01:37.077Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:01:37.262Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:01:48.236Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:01:48.238Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:01:48.238Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:01:48.239Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:01:48.247Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:01:48.273Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:01:48.378Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:06:48.266Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:06:48.268Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:06:48.268Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:06:48.269Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:06:48.339Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:06:48.482Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:06:48.647Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:07:22.523Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:07:22.524Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:07:22.525Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:07:22.526Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:07:22.551Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:07:22.574Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:07:22.665Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:07:35.147Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:07:35.148Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:07:35.149Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:07:35.149Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:07:35.159Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:07:35.214Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:07:35.327Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:08:37.696Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:08:37.698Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:08:37.698Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:08:37.699Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:08:37.707Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:08:37.765Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:08:37.879Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:08:55.092Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:08:55.094Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:08:55.094Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:08:55.095Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:08:55.107Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:08:55.124Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:08:55.419Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:09:32.833Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:09:32.834Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:09:32.835Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:09:32.836Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:09:32.848Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:09:33.084Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:09:33.215Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:09:45.838Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:09:45.860Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:09:45.892Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:09:45.935Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:09:45.988Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:09:46.307Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:09:46.444Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:10:26.128Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:10:26.130Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:10:26.132Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:10:26.133Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:10:26.145Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:10:26.367Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:10:26.464Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-24T01:10:38.707Z"}
{"level":"info","message":"Environment: development","timestamp":"2025-05-24T01:10:38.709Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-24T01:10:38.710Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\development","timestamp":"2025-05-24T01:10:38.710Z"}
{"level":"info","message":"PrismaClient initialized synchronously","timestamp":"2025-05-24T01:10:38.718Z"}
{"level":"info","message":"Prisma client initialized with logging middleware","timestamp":"2025-05-24T01:10:39.265Z"}
{"level":"info","message":"Database connection established successfully","timestamp":"2025-05-24T01:10:39.428Z"}
