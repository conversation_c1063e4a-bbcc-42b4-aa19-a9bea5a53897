/**
 * Security Testing Script
 *
 * This script performs security testing on the API to verify security features.
 * It tests:
 * - CSRF protection
 * - XSS protection
 * - SQL injection protection
 * - Rate limiting
 * - Authentication
 * - Authorization
 *
 * Note: This is a simple security testing script. For more comprehensive security testing,
 * consider using dedicated tools like OWASP ZAP, Burp Suite, or Snyk.
 */
declare function runSecurityTests(): any;
export default runSecurityTests;
//# sourceMappingURL=security-test.d.ts.map