"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const client_1 = require("@prisma/client");
const report_monitoring_service_1 = require("../services/report-monitoring.service");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const prisma = new client_1.PrismaClient();
const monitoringService = new report_monitoring_service_1.ReportMonitoringService();
class HealthController {
    constructor() {
        /**
         * Basic health check
         */
        this.healthCheck = async (req, res) => {
            try {
                const health = {
                    status: 'healthy',
                    timestamp: new Date().toISOString(),
                    uptime: process.uptime(),
                    version: process.env.npm_package_version || '1.0.0',
                    environment: process.env.NODE_ENV || 'development',
                };
                res.status(200).json(health);
            }
            catch (error) {
                res.status(503).json({
                    status: 'unhealthy',
                    timestamp: new Date().toISOString(),
                    error: error.message,
                });
            }
        };
        /**
         * Detailed health check including dependencies
         */
        this.detailedHealthCheck = async (req, res) => {
            try {
                const checks = await Promise.allSettled([
                    this.checkDatabase(),
                    this.checkFileSystem(),
                    this.checkReportingSystem(),
                    this.checkMemoryUsage(),
                ]);
                const results = {
                    status: 'healthy',
                    timestamp: new Date().toISOString(),
                    checks: {
                        database: this.getCheckResult(checks[0]),
                        fileSystem: this.getCheckResult(checks[1]),
                        reportingSystem: this.getCheckResult(checks[2]),
                        memoryUsage: this.getCheckResult(checks[3]),
                    },
                };
                // Determine overall status
                const hasFailures = Object.values(results.checks).some(check => check.status !== 'healthy');
                if (hasFailures) {
                    results.status = 'unhealthy';
                }
                const statusCode = results.status === 'healthy' ? 200 : 503;
                res.status(statusCode).json(results);
            }
            catch (error) {
                res.status(503).json({
                    status: 'unhealthy',
                    timestamp: new Date().toISOString(),
                    error: error.message,
                });
            }
        };
        /**
         * Get reporting system metrics
         */
        this.getMetrics = async (req, res) => {
            try {
                const timeRange = parseInt(req.query.hours) || 24;
                const [systemHealth, performanceStats, databaseMetrics, fileSystemMetrics,] = await Promise.all([
                    monitoringService.getSystemHealth(),
                    monitoringService.getPerformanceStats(timeRange),
                    monitoringService.getDatabaseMetrics(),
                    monitoringService.getFileSystemMetrics(),
                ]);
                res.json({
                    success: true,
                    data: {
                        systemHealth,
                        performanceStats,
                        databaseMetrics,
                        fileSystemMetrics,
                        timestamp: new Date().toISOString(),
                    },
                });
            }
            catch (error) {
                console.error('Error getting metrics:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting metrics',
                });
            }
        };
        /**
         * Trigger cleanup of old reports
         */
        this.cleanupReports = async (req, res) => {
            try {
                const maxAgeHours = parseInt(req.body.maxAgeHours) || 168; // Default 7 days
                const result = await monitoringService.cleanupOldReports(maxAgeHours);
                res.json({
                    success: true,
                    data: result,
                    message: `Cleanup completed: ${result.deletedFiles} files deleted, ${Math.round(result.freedSpace / 1024 / 1024)}MB freed`,
                });
            }
            catch (error) {
                console.error('Error during cleanup:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error during cleanup',
                });
            }
        };
        /**
         * Get system information
         */
        this.getSystemInfo = async (req, res) => {
            try {
                const memoryUsage = process.memoryUsage();
                const cpuUsage = process.cpuUsage();
                const systemInfo = {
                    node: {
                        version: process.version,
                        platform: process.platform,
                        arch: process.arch,
                        uptime: process.uptime(),
                    },
                    memory: {
                        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
                        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
                        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
                        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
                        arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024), // MB
                    },
                    cpu: {
                        user: cpuUsage.user,
                        system: cpuUsage.system,
                    },
                    environment: {
                        nodeEnv: process.env.NODE_ENV,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    },
                };
                res.json({
                    success: true,
                    data: systemInfo,
                });
            }
            catch (error) {
                console.error('Error getting system info:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting system info',
                });
            }
        };
    }
    /**
     * Check database connectivity
     */
    async checkDatabase() {
        const startTime = Date.now();
        try {
            await prisma.$queryRaw `SELECT 1`;
            const responseTime = Date.now() - startTime;
            return {
                status: 'healthy',
                responseTime,
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                responseTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }
    /**
     * Check file system access
     */
    async checkFileSystem() {
        try {
            const reportsDir = path.join(__dirname, '../../reports');
            // Check if reports directory exists and is writable
            if (!fs.existsSync(reportsDir)) {
                fs.mkdirSync(reportsDir, { recursive: true });
            }
            // Test write access
            const testFile = path.join(reportsDir, 'health-check.tmp');
            fs.writeFileSync(testFile, 'health check');
            fs.unlinkSync(testFile);
            // Get directory stats
            const stats = fs.statSync(reportsDir);
            const files = fs.readdirSync(reportsDir);
            return {
                status: 'healthy',
                details: {
                    directory: reportsDir,
                    fileCount: files.length,
                    lastModified: stats.mtime,
                },
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
            };
        }
    }
    /**
     * Check reporting system health
     */
    async checkReportingSystem() {
        try {
            const systemHealth = await monitoringService.getSystemHealth();
            return {
                status: systemHealth.status === 'critical' ? 'unhealthy' : 'healthy',
                details: {
                    systemStatus: systemHealth.status,
                    activeReports: systemHealth.metrics.activeReports,
                    queuedReports: systemHealth.metrics.queuedReports,
                    failedReports: systemHealth.metrics.failedReports,
                    alerts: systemHealth.alerts,
                },
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
            };
        }
    }
    /**
     * Check memory usage
     */
    async checkMemoryUsage() {
        const memoryUsage = process.memoryUsage();
        const heapUsedPercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
        let status = 'healthy';
        if (heapUsedPercent > 90) {
            status = 'unhealthy';
        }
        else if (heapUsedPercent > 80) {
            status = 'warning';
        }
        return {
            status,
            details: {
                heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
                heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
                heapUsedPercent: Math.round(heapUsedPercent),
                rssMB: Math.round(memoryUsage.rss / 1024 / 1024),
            },
        };
    }
    /**
     * Extract result from Promise.allSettled
     */
    getCheckResult(result) {
        if (result.status === 'fulfilled') {
            return result.value;
        }
        else {
            return {
                status: 'unhealthy',
                error: result.reason?.message || 'Unknown error',
            };
        }
    }
}
exports.HealthController = HealthController;
