"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CryptoUtils = void 0;
// jscpd:ignore-file
const crypto_1 = __importDefault(require("crypto"));
const util_1 = require("util");
// Promisify crypto functions
const randomBytesAsync = (0, util_1.promisify)(crypto_1.default.randomBytes);
const pbkdf2Async = (0, util_1.promisify)(crypto_1.default.pbkdf2);
/**
 * Crypto Utilities
 * This class provides utility methods for cryptographic operations
 */
class CryptoUtils {
    /**
     * Generate a random string
     * @param length Length of the string
     * @param encoding Encoding of the string
     * @returns Random string
     */
    static async generateRandomString(length = 32, encoding = 'hex') {
        const bytes = await randomBytesAsync(Math.ceil(length / 2));
        return bytes.toString(encoding).slice(0, length);
    }
    /**
     * Generate a random string synchronously
     * @param length Length of the string
     * @param encoding Encoding of the string
     * @returns Random string
     */
    static generateRandomStringSync(length = 32, encoding = 'hex') {
        const bytes = crypto_1.default.randomBytes(Math.ceil(length / 2));
        return bytes.toString(encoding).slice(0, length);
    }
    /**
     * Generate a UUID v4
     * @returns UUID v4
     */
    static generateUuid() {
        return crypto_1.default.randomUUID();
    }
    /**
     * Hash a password
     * @param password Password to hash
     * @param salt Salt to use (optional, will be generated if not provided)
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns Object containing the hash and salt
     */
    static async hashPassword(password, salt, iterations = 10000, keylen = 64, digest = 'sha512') {
        const saltToUse = salt || (await CryptoUtils.generateRandomString(16));
        const hash = await pbkdf2Async(password, saltToUse, iterations, keylen, digest);
        return {
            hash: hash.toString('hex'),
            salt: saltToUse
        };
    }
    /**
     * Hash a password synchronously
     * @param password Password to hash
     * @param salt Salt to use (optional, will be generated if not provided)
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns Object containing the hash and salt
     */
    static hashPasswordSync(password, salt, iterations = 10000, keylen = 64, digest = 'sha512') {
        const saltToUse = salt || CryptoUtils.generateRandomStringSync(16);
        const hash = crypto_1.default.pbkdf2Sync(password, saltToUse, iterations, keylen, digest);
        return {
            hash: hash.toString('hex'),
            salt: saltToUse
        };
    }
    /**
     * Verify a password
     * @param password Password to verify
     * @param hash Hash to compare against
     * @param salt Salt used to hash the password
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns True if the password is valid
     */
    static async verifyPassword(password, hash, salt, iterations = 10000, keylen = 64, digest = 'sha512') {
        const result = await CryptoUtils.hashPassword(password, salt, iterations, keylen, digest);
        return result.hash === hash;
    }
    /**
     * Verify a password synchronously
     * @param password Password to verify
     * @param hash Hash to compare against
     * @param salt Salt used to hash the password
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns True if the password is valid
     */
    static verifyPasswordSync(password, hash, salt, iterations = 10000, keylen = 64, digest = 'sha512') {
        const result = CryptoUtils.hashPasswordSync(password, salt, iterations, keylen, digest);
        return result.hash === hash;
    }
    /**
     * Encrypt data
     * @param data Data to encrypt
     * @param key Encryption key
     * @param algorithm Encryption algorithm (default: aes-256-cbc)
     * @returns Encrypted data
     */
    static encrypt(data, key, algorithm = 'aes-256-cbc') {
        // Create a buffer from the key
        const keyBuffer = Buffer.from(key);
        // Create an initialization vector
        const iv = crypto_1.default.randomBytes(16);
        // Create a cipher
        const cipher = crypto_1.default.createCipheriv(algorithm, keyBuffer, iv);
        // Encrypt the data
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return {
            encrypted,
            iv: iv.toString('hex')
        };
    }
    /**
     * Decrypt data
     * @param encrypted Encrypted data
     * @param key Encryption key
     * @param iv Initialization vector
     * @param algorithm Encryption algorithm (default: aes-256-cbc)
     * @returns Decrypted data
     */
    static decrypt(encrypted, key, iv, algorithm = 'aes-256-cbc') {
        // Create a buffer from the key
        const keyBuffer = Buffer.from(key);
        // Create a buffer from the initialization vector
        const ivBuffer = Buffer.from(iv, 'hex');
        // Create a decipher
        const decipher = crypto_1.default.createDecipheriv(algorithm, keyBuffer, ivBuffer);
        // Decrypt the data
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    /**
     * Generate a hash
     * @param data Data to hash
     * @param algorithm Hash algorithm (default: sha256)
     * @returns Hash
     */
    static hash(data, algorithm = 'sha256') {
        return crypto_1.default.createHash(algorithm).update(data).digest('hex');
    }
    /**
     * Generate an HMAC
     * @param data Data to sign
     * @param key Key to use
     * @param algorithm Hash algorithm (default: sha256)
     * @returns HMAC
     */
    static hmac(data, key, algorithm = 'sha256') {
        return crypto_1.default.createHmac(algorithm, key).update(data).digest('hex');
    }
    /**
     * Generate a secure token
     * @param length Token length (default: 32)
     * @returns Secure token
     */
    static async generateToken(length = 32) {
        return CryptoUtils.generateRandomString(length);
    }
    /**
     * Generate a secure token synchronously
     * @param length Token length (default: 32)
     * @returns Secure token
     */
    static generateTokenSync(length = 32) {
        return CryptoUtils.generateRandomStringSync(length);
    }
    /**
     * Generate a secure API key
     * @param prefix API key prefix
     * @param length API key length (default: 32)
     * @returns Secure API key
     */
    static async generateApiKey(prefix, length = 32) {
        const token = await CryptoUtils.generateRandomString(length);
        return `${prefix}_${token}`;
    }
    /**
     * Generate a secure API key synchronously
     * @param prefix API key prefix
     * @param length API key length (default: 32)
     * @returns Secure API key
     */
    static generateApiKeySync(prefix, length = 32) {
        const token = CryptoUtils.generateRandomStringSync(length);
        return `${prefix}_${token}`;
    }
    /**
     * Compare two strings in constant time
     * @param a First string
     * @param b Second string
     * @returns True if the strings are equal
     */
    static constantTimeCompare(a, b) {
        if (a.length !== b.length) {
            return false;
        }
        return crypto_1.default.timingSafeEqual(Buffer.from(a), Buffer.from(b));
    }
}
exports.CryptoUtils = CryptoUtils;
//# sourceMappingURL=CryptoUtils.js.map