import { Module } from '../../core/module';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Webhook Module
 * This module provides webhook functionality with zero duplication
 */
export declare class WebhookModule {
    private moduleFactory;
    private moduleRegistry;
    private container;
    private module;
    /**
     * Create a new webhook module
     */
    constructor();
    /**
     * Get the module
     * @returns Webhook module
     */
    getModule(): Module;
}
//# sourceMappingURL=webhook.module.d.ts.map