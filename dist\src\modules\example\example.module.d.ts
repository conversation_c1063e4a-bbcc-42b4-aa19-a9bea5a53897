import { Module } from '../../core/module';
/**
 * Example Module
 * This module provides a comprehensive example of using the enhanced factory system
 */
export declare class ExampleModule {
    private moduleFactory;
    private moduleRegistry;
    private container;
    private module;
    /**
     * Create a new example module
     */
    constructor();
    /**
     * Get the module
     * @returns Example module
     */
    getModule(): Module;
}
//# sourceMappingURL=example.module.d.ts.map