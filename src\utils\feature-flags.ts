// jscpd:ignore-file
/**
 * Feature Flags Utility
 *
 * This utility provides functions for managing environment-specific feature flags
 * to ensure complete isolation between production and demo environments.
 */

import { logger } from '../lib/logger';

/**
 * Feature flag interface
 */
export interface FeatureFlag {
  /** Feature flag name */
  name: string;
  /** Feature flag description */
  description: string;
  /** Whether the feature is enabled in production */
  enabledInProduction: boolean;
}

/**
 * Feature flags
 */
export const FEATURE_FLAGS: Record<string, FeatureFlag> = {
  // Payment features
  ENABLE_BINANCE_PAYMENTS: {
    name: 'Enable Binance Payments',
    description: 'Enable Binance payment methods',
    enabledInProduction: true,
  },
  ENABLE_CRYPTO_PAYMENTS: {
    name: 'Enable Crypto Payments',
    description: 'Enable cryptocurrency payment methods',
    enabledInProduction: true,
  },
  ENABLE_FIAT_PAYMENTS: {
    name: 'Enable Fiat Payments',
    description: 'Enable fiat payment methods',
    enabledInProduction: true,
  },

  // Security features
  ENABLE_2FA: {
    name: 'Enable Two-Factor Authentication',
    description: 'Enable two-factor authentication for users',
    enabledInProduction: true,
  },
  ENABLE_RATE_LIMITING: {
    name: 'Enable Rate Limiting',
    description: 'Enable rate limiting for API requests',
    enabledInProduction: true,
  },
  ENABLE_IP_BLOCKING: {
    name: 'Enable IP Blocking',
    description: 'Enable IP blocking for suspicious activity',
    enabledInProduction: true,
  },

  // Notification features
  ENABLE_EMAIL_NOTIFICATIONS: {
    name: 'Enable Email Notifications',
    description: 'Enable email notifications',
    enabledInProduction: true,
  },
  ENABLE_SMS_NOTIFICATIONS: {
    name: 'Enable SMS Notifications',
    description: 'Enable SMS notifications',
    enabledInProduction: true,
  },
  ENABLE_TELEGRAM_NOTIFICATIONS: {
    name: 'Enable Telegram Notifications',
    description: 'Enable Telegram notifications',
    enabledInProduction: true,
  },

  // All demo-specific features are disabled in production
  ENABLE_DEMO_BANNER: {
    name: 'Enable Demo Banner',
    description: 'Enable demo environment banner',
    enabledInProduction: false,
  },
  ENABLE_DEMO_ACCOUNTS: {
    name: 'Enable Demo Accounts',
    description: 'Enable demo accounts with pre-filled data',
    enabledInProduction: false,
  },
  DISABLE_REAL_PAYMENTS: {
    name: 'Disable Real Payments',
    description: 'Disable real payments in demo environment',
    enabledInProduction: false,
  },
};

/**
 * Check if a feature flag is enabled
 * @param flagName Feature flag name
 * @returns Whether the feature flag is enabled
 */
export const isFeatureEnabled = (flagName: string): boolean => {
  // Get feature flag
  const flag = FEATURE_FLAGS[flagName];

  if (!flag) {
    logger.warn(`Feature flag not found: ${flagName}`);
    return false;
  }

  // Always use production mode
  return flag.enabledInProduction;
};

/**
 * Get all feature flags for the current environment
 * @returns Feature flags for the current environment
 */
export const getFeatureFlags = (): Record<string, boolean> => {
  const flags: Record<string, boolean> = {};

  for (const flagName in FEATURE_FLAGS) {
    flags[flagName] = isFeatureEnabled(flagName);
  }

  return flags;
};

export default {
  FEATURE_FLAGS,
  isFeatureEnabled,
  getFeatureFlags,
};
