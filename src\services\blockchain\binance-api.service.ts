// jscpd:ignore-file
import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import crypto from 'crypto';
import { logger } from '../../utils/logger';
import { Transaction, Merchant } from '../../types';
import {
  VerificationErrorType,
  createVerificationError,
  handleVerificationError,
} from '../../utils/verification-error-handler';

/**
 * Binance payment method
 */
export enum BinancePaymentMethod {
  /**
   * Binance Pay
   */
  BINANCE_PAY = 'binance_pay',

  /**
   * Binance C2C
   */
  BINANCE_C2C = 'binance_c2c',

  /**
   * Binance TRC20
   */
  BINANCE_TRC20 = 'binance_trc20',
}

/**
 * Binance transaction status
 */
export enum BinanceTransactionStatus {
  /**
   * Pending
   */
  PENDING = 'PENDING',

  /**
   * Paid
   */
  PAID = 'PAID',

  /**
   * Canceled
   */
  CANCELED = 'CANCELED',

  /**
   * Expired
   */
  EXPIRED = 'EXPIRED',
}

/**
 * Binance transaction verification result
 */
export interface BinanceTransactionVerificationResult {
  /**
   * Transaction ID
   */
  transactionId: string;

  /**
   * Transaction status
   */
  status: BinanceTransactionStatus;

  /**
   * Transaction amount
   */
  amount: string;

  /**
   * Transaction currency
   */
  currency: string;

  /**
   * Transaction sender
   */
  sender?: string;

  /**
   * Transaction recipient
   */
  recipient?: string;

  /**
   * Transaction timestamp
   */
  timestamp: number;

  /**
   * Raw transaction data
   */
  rawData?: Record<string, any>;
}

/**
 * Binance API service
 */
export class BinanceApiService {
  /**
   * Binance API base URL
   */
  private readonly apiBaseUrl = 'https://api.binance.com';

  /**
   * Binance Pay API base URL
   */
  private readonly payApiBaseUrl = 'https://bpay.binanceapi.com';

  /**
   * Default retry configuration
   */
  private readonly defaultRetryConfig = {
    maxRetries: 3,
    initialDelayMs: 1000,
    maxDelayMs: 10000,
    retryStatusCodes: [408, 429, 500, 502, 503, 504],
    retryErrorCodes: ['ECONNABORTED', 'ETIMEDOUT', 'ECONNRESET', 'ECONNREFUSED'],
  };

  /**
   * Make a request with retry capability
   * @param config Request configuration
   * @param retryConfig Retry configuration
   * @returns Response data
   */
  private async makeRequestWithRetry<T>(
    config: AxiosRequestConfig,
    retryConfig = this.defaultRetryConfig
  ): Promise<T> {
    const { maxRetries, initialDelayMs, maxDelayMs, retryStatusCodes, retryErrorCodes } =
      retryConfig;

    let lastError;

    for (let attempt: number = 0; attempt <= maxRetries; attempt++) {
      try {
        // Add timeout to the request
        const requestConfig: any = {
          ...config,
          timeout: 30000, // 30 seconds timeout
        };

        const response: any = await axios(requestConfig);
        return response.data;
      } catch (error) {
        lastError = error;

        // Check if we should retry
        const shouldRetry: any = this.shouldRetryRequest(error, retryStatusCodes, retryErrorCodes);

        // If we shouldn't retry or this is the last attempt, throw the error
        if (!shouldRetry || attempt === maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay: any = Math.min(initialDelayMs * Math.pow(2, attempt), maxDelayMs);

        // Log retry attempt
        logger.warn(
          `Retrying Binance API request (attempt ${
            attempt + 1
          }/${maxRetries}) after ${delay}ms delay`,
          {
            url: config.url,
            method: config.method,
            errorCode: (error as AxiosError).code,
            errorStatus: (error as AxiosError).response?.status,
            errorMessage: (error as Error).message,
          }
        );

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    // If we get here, all retries failed
    throw lastError;
  }

  /**
   * Determine if a request should be retried
   * @param error Error from the request
   * @param retryStatusCodes HTTP status codes that should be retried
   * @param retryErrorCodes Error codes that should be retried
   * @returns Whether the request should be retried
   */
  private shouldRetryRequest(
    error: any,
    retryStatusCodes: number[],
    retryErrorCodes: string[]
  ): boolean {
    // Check if it's an Axios error
    if (error.isAxiosError) {
      // Check status code
      const status: any = error.response?.status;
      if (status && retryStatusCodes.includes(status)) {
        return true;
      }

      // Check error code
      if (error.code && retryErrorCodes.includes(error.code)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Verify a Binance Pay transaction
   * @param transactionId Binance Pay transaction ID
   * @param merchantApiKey Merchant API key
   * @param merchantSecretKey Merchant secret key
   * @param expectedAmount Expected transaction amount
   * @param expectedCurrency Expected transaction currency
   * @returns Transaction verification result
   */
  async verifyBinancePayTransaction(
    transactionId: string,
    merchantApiKey: string,
    merchantSecretKey: string,
    expectedAmount: string,
    expectedCurrency: string
  ): Promise<BinanceTransactionVerificationResult> {
    try {
      // Check if API keys are provided
      if (!merchantApiKey || !merchantSecretKey) {
        throw new Error('Binance Pay API keys are not configured');
      }

      // Get transaction details
      const url: any = `${this.payApiBaseUrl}/binancepay/openapi/v2/order/query`;

      // Prepare request body
      const requestBody: any = {
        merchantTradeNo: transactionId,
      };

      // Generate timestamp and nonce
      const timestamp: any = Date.now().toString();
      const nonce: any = this.generateNonce();

      // Generate signature
      const payload: any = timestamp + '\n' + nonce + '\n' + JSON.stringify(requestBody) + '\n';
      const signature: any = crypto
        .createHmac('sha512', merchantSecretKey)
        .update(payload)
        .digest('hex')
        .toUpperCase();

      // Prepare headers
      const headers: any = {
        'Content-Type': 'application/json',
        'BinancePay-Timestamp': timestamp,
        'BinancePay-Nonce': nonce,
        'BinancePay-Certificate-SN': merchantApiKey,
        'BinancePay-Signature': signature,
      };

      // Make request
      const response: any = await axios.post(url, requestBody, { headers });

      // Check response status
      if (response.data.status !== 'SUCCESS') {
        throw new Error(`Binance Pay API error: ${response.data.errorMessage}`);
      }

      // Extract transaction details
      const txData: any = response.data.data;

      // Verify transaction status
      if (txData.status !== 'PAID') {
        return {
          transactionId,
          status: txData.status as BinanceTransactionStatus,
          amount: txData.totalFee,
          currency: txData.currency,
          timestamp: txData.createTime,
          rawData: txData,
        };
      }

      // Verify transaction amount
      if (txData.totalFee !== expectedAmount) {
        throw new Error(
          `Transaction amount ${txData.totalFee} does not match expected amount ${expectedAmount}`
        );
      }

      // Verify transaction currency
      if (txData.currency !== expectedCurrency) {
        throw new Error(
          `Transaction currency ${txData.currency} does not match expected currency ${expectedCurrency}`
        );
      }

      return {
        transactionId,
        status: txData.status as BinanceTransactionStatus,
        amount: txData.totalFee,
        currency: txData.currency,
        timestamp: txData.createTime,
        rawData: txData,
      };
    } catch (error) {
      console.error(`Error verifying Binance Pay transaction ${transactionId}:`, error);
      throw new Error(`Failed to verify Binance Pay transaction ${transactionId}`);
    }
  }

  /**
   * Verify a Binance C2C transaction by note
   * @param note Transaction note
   * @param merchantApiKey Merchant API key
   * @param merchantSecretKey Merchant secret key
   * @param expectedAmount Expected transaction amount
   * @param expectedCurrency Expected transaction currency
   * @returns Transaction verification result
   */
  async verifyBinanceC2CTransactionByNote(
    note: string,
    merchantApiKey: string,
    merchantSecretKey: string,
    expectedAmount: string,
    expectedCurrency: string
  ): Promise<BinanceTransactionVerificationResult> {
    try {
      // Check if API keys are provided
      if (!merchantApiKey || !merchantSecretKey) {
        throw new Error('Binance API keys are not configured');
      }

      // Get C2C transaction history
      const url: any = `${this.apiBaseUrl}/sapi/v1/c2c/orderMatch/listUserOrderHistory`;

      // Prepare query parameters
      const params: any = {
        tradeType: 'BUY',
        timestamp: Date.now(),
        recvWindow: 5000,
      };

      // Generate signature
      const queryString: any = this.buildQueryString(params);
      const signature: any = crypto
        .createHmac('sha256', merchantSecretKey)
        .update(queryString)
        .digest('hex');

      // Prepare headers
      const headers: any = {
        'X-MBX-APIKEY': merchantApiKey,
      };

      const response: any = await axios.get(`${url}?${queryString}&signature=${signature}`, {
        headers,
      });

      // Find transaction with matching note
      const transaction: any = response.data.data.find((tx) => tx.note === note);

      if (!transaction) {
        throw new Error(`No transaction found with note: ${note}`);
      }

      // Verify transaction status
      if (transaction.orderStatus !== 'COMPLETED') {
        return {
          transactionId: transaction.orderNumber,
          status: BinanceTransactionStatus.PENDING,
          amount: transaction.totalPrice,
          currency: transaction.fiat,
          sender: transaction.counterPartNickName,
          recipient: transaction.advertisementOwnerNickName,
          timestamp: transaction.createTime,
          rawData: transaction,
        };
      }

      // Verify transaction amount
      if (transaction.totalPrice !== expectedAmount) {
        throw new Error(
          `Transaction amount ${transaction.totalPrice} does not match expected amount ${expectedAmount}`
        );
      }

      // Verify transaction currency
      if (transaction.fiat !== expectedCurrency) {
        throw new Error(
          `Transaction currency ${transaction.fiat} does not match expected currency ${expectedCurrency}`
        );
      }

      return {
        transactionId: transaction.orderNumber,
        status: BinanceTransactionStatus.PAID,
        amount: transaction.totalPrice,
        currency: transaction.fiat,
        sender: transaction.counterPartNickName,
        recipient: transaction.advertisementOwnerNickName,
        timestamp: transaction.createTime,
        rawData: transaction,
      };
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }

      console.error(`Error verifying Binance C2C transaction with note ${note}:`, error);
      throw this.blockchainError(`Failed to verify Binance C2C transaction with note ${note}`);
    }
  }

  /**
   * Verify a Binance TRC20 transaction
   * @param txHash Transaction hash
   * @param merchantApiKey Merchant API key
   * @param merchantSecretKey Merchant secret key
   * @param expectedAmount Expected transaction amount
   * @param expectedRecipient Expected recipient address
   * @returns Transaction verification result
   */
  async verifyBinanceTRC20Transaction(
    txHash: string,
    merchantApiKey: string,
    merchantSecretKey: string,
    expectedAmount: string,
    expectedRecipient: string
  ): Promise<BinanceTransactionVerificationResult> {
    try {
      // Validate input parameters
      if (!txHash) {
        throw createVerificationError(
          VerificationErrorType.INVALID_PARAMETERS,
          'Transaction hash is required',
          null,
          { method: 'verifyBinanceTRC20Transaction' }
        );
      }

      if (!expectedRecipient) {
        throw createVerificationError(
          VerificationErrorType.INVALID_PARAMETERS,
          'Recipient address is required',
          null,
          { method: 'verifyBinanceTRC20Transaction' }
        );
      }

      // Check if API keys are provided
      if (!merchantApiKey || !merchantSecretKey) {
        throw createVerificationError(
          VerificationErrorType.AUTHENTICATION_ERROR,
          'Binance API keys are not configured',
          null,
          { method: 'verifyBinanceTRC20Transaction' }
        );
      }

      // Log verification attempt
      logger.info('Verifying Binance TRC20 transaction', {
        txHash,
        expectedAmount,
        expectedRecipient: expectedRecipient.substring(0, 10) + '...',
        method: 'verifyBinanceTRC20Transaction',
      });

      // Get transaction details from Binance
      const url: any = `${this.apiBaseUrl}/sapi/v1/capital/deposit/hisrec`;

      // Prepare query parameters
      const params: any = {
        coin: 'USDT',
        status: 1, // Completed
        startTime: Date.now() - 30 * 24 * 60 * 60 * 1000, // Last 30 days
        endTime: Date.now(),
        timestamp: Date.now(),
        recvWindow: 5000,
      };

      // Generate signature
      const queryString: any = this.buildQueryString(params);
      const signature: any = crypto
        .createHmac('sha256', merchantSecretKey)
        .update(queryString)
        .digest('hex');

      // Prepare headers
      const headers: any = {
        'X-MBX-APIKEY': merchantApiKey,
      };

      const response: any = await this.makeRequestWithRetry<any[]>({
        method: 'GET',
        url: `${url}?${queryString}&signature=${signature}`,
        headers,
      });

      // Find transaction with matching hash
      const transaction: any = response.find((tx) => tx.txId === txHash);

      if (!transaction) {
        throw createVerificationError(
          VerificationErrorType.TRANSACTION_NOT_FOUND,
          `No transaction found with hash: ${txHash}`,
          null,
          { method: 'verifyBinanceTRC20Transaction' }
        );
      }

      // Verify transaction status
      if (transaction.status !== 1) {
        logger.info('Transaction is pending', {
          txHash,
          status: transaction.status,
          method: 'verifyBinanceTRC20Transaction',
        });

        return {
          transactionId: txHash,
          status: BinanceTransactionStatus.PENDING,
          amount: transaction.amount,
          currency: transaction.coin,
          recipient: transaction.address,
          timestamp: transaction.insertTime,
          rawData: transaction,
        };
      }

      // Verify transaction amount
      if (transaction.amount !== expectedAmount) {
        throw createVerificationError(
          VerificationErrorType.AMOUNT_MISMATCH,
          `Transaction amount ${transaction.amount} does not match expected amount ${expectedAmount}`,
          null,
          {
            txHash,
            actualAmount: transaction.amount,
            expectedAmount,
            method: 'verifyBinanceTRC20Transaction',
          }
        );
      }

      // Verify recipient address
      if (transaction.address !== expectedRecipient) {
        throw createVerificationError(
          VerificationErrorType.RECIPIENT_MISMATCH,
          `Transaction recipient ${transaction.address} does not match expected recipient ${expectedRecipient}`,
          null,
          {
            txHash,
            actualRecipient: transaction.address,
            expectedRecipient,
            method: 'verifyBinanceTRC20Transaction',
          }
        );
      }

      // Log successful verification
      logger.info('Binance TRC20 transaction verified successfully', {
        txHash,
        amount: transaction.amount,
        currency: transaction.coin,
        method: 'verifyBinanceTRC20Transaction',
      });

      return {
        transactionId: txHash,
        status: BinanceTransactionStatus.PAID,
        amount: transaction.amount,
        currency: transaction.coin,
        recipient: transaction.address,
        timestamp: transaction.insertTime,
        rawData: transaction,
      };
    } catch (error) {
      // If it's already a VerificationError, just rethrow it
      if (error.code && Object.values(VerificationErrorType).includes(error.code)) {
        throw error;
      }

      // Handle other errors
      logger.error(`Error verifying Binance TRC20 transaction ${txHash}:`, {
        error: error.message || error,
        txHash,
        method: 'verifyBinanceTRC20Transaction',
      });

      throw handleVerificationError(error, `Failed to verify Binance TRC20 transaction ${txHash}`, {
        txHash,
        method: 'verifyBinanceTRC20Transaction',
      });
    }
  }

  /**
   * Generate a random nonce
   * @returns Random nonce
   */
  private generateNonce(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Build query string from parameters
   * @param params Query parameters
   * @returns Query string
   */
  private buildQueryString(params: Record<string, any>): string {
    return Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
  }

  /**
   * Create a blockchain error
   * @param message Error message
   * @returns Service error
   */
  private blockchainError(message: string): ServiceError {
    return new ServiceError(message, ApiErrorCode.BLOCKCHAIN_ERROR);
  }

  /**
   * Get transaction details
   * @param transactionId Transaction ID
   * @param method Payment method
   * @returns Transaction details
   */
  async getTransaction(transactionId: string, method: BinancePaymentMethod): Promise<any> {
    switch (method) {
      case BinancePaymentMethod.BINANCE_PAY:
        // This is a mock implementation for testing
        return {
          id: transactionId,
          status: BinanceTransactionStatus.PAID,
          amount: '100',
          currency: 'USDT',
          toAddress: 'recipient-address',
          timestamp: Date.now(),
        };
      default:
        throw new Error(`Unsupported payment method: ${method}`);
    }
  }

  /**
   * Get transaction by note
   * @param note Transaction note
   * @param method Payment method
   * @returns Transaction details
   */
  async getTransactionByNote(note: string, method: BinancePaymentMethod): Promise<any> {
    if (method !== BinancePaymentMethod.BINANCE_C2C) {
      throw new Error(`Unsupported payment method for note verification: ${method}`);
    }

    // This is a mock implementation for testing
    return {
      id: `c2c-${Date.now()}`,
      status: BinanceTransactionStatus.PAID,
      amount: '100',
      currency: 'USDT',
      toAddress: 'recipient-address',
      timestamp: Date.now(),
      note,
    };
  }

  /**
   * Get latest transaction for an address
   * @param address Wallet address
   * @param method Payment method
   * @returns Transaction details
   */
  async getLatestTransaction(address: string, method: BinancePaymentMethod): Promise<any> {
    if (method !== BinancePaymentMethod.BINANCE_TRC20) {
      throw new Error(`Unsupported payment method for address verification: ${method}`);
    }

    // This is a mock implementation for testing
    return {
      id: `trc20-${Date.now()}`,
      status: BinanceTransactionStatus.PAID,
      amount: '100',
      currency: 'USDT',
      fromAddress: 'sender-address',
      toAddress: address,
      timestamp: Date.now(),
    };
  }
}
