"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toBeArrayOfSize = toBeArrayOfSize;
const utils_1 = require("../utils");
function toBeArrayOfSize(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printExpected, printReceived, matcherHint } = this.utils;
    const pass = Array.isArray(actual) && actual.length === expected;
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toBeArrayOfSize') +
                '\n\n' +
                'Expected value to not be an array of size:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  value: ${printReceived(actual)}\n` +
                `  length: ${printReceived((0, utils_1.determinePropertyMessage)(actual, 'length'))}`
            : matcherHint('.toBeArrayOfSize') +
                '\n\n' +
                'Expected value to be an array of size:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  value: ${printReceived(actual)}\n` +
                `  length: ${printReceived((0, utils_1.determinePropertyMessage)(actual, 'length'))}`,
    };
}
