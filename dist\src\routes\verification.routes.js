"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const blockchain_verification_controller_1 = __importDefault(require("../controllers/blockchain-verification.controller"));
const binance_verification_controller_ts_1 = require("../controllers/refactored/binance-verification.controller.ts");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// Blockchain verification routes
router.post("/blockchain", auth_1.authenticate, blockchain_verification_controller_1.default.verifyBlockchainTransaction);
router.post("/blockchain/trc20", auth_1.authenticate, blockchain_verification_controller_1.default.verifyBinanceTransaction);
// These methods don't exist in the controller, so we'll comment them out for now
// router.post('/blockchain/erc20', authenticate, BlockchainVerificationController.verifyBinanceTransaction);
// router.post('/blockchain/bep20', authenticate, BlockchainVerificationController.verifyBinanceTransaction);
// router.post('/blockchain/polygon', authenticate, BlockchainVerificationController.verifyBinanceTransaction);
// Binance verification routes
router.post("/binance/trc20", auth_1.authenticate, binance_verification_controller_ts_1.BinanceVerificationController.verifyTRC20Transaction);
router.post("/binance/c2c", auth_1.authenticate, binance_verification_controller_ts_1.BinanceVerificationController.verifyC2CTransaction);
router.post("/binance/pay", auth_1.authenticate, binance_verification_controller_ts_1.BinanceVerificationController.verifyPayTransaction);
router.post("/binance/test", auth_1.authenticate, binance_verification_controller_ts_1.BinanceVerificationController.testConnection);
exports.default = router;
//# sourceMappingURL=verification.routes.js.map