"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_validator_1 = require("express-validator");
const api_analytics_controller_1 = require("../controllers/refactored/api-analytics.controller");
const RouteProvider_1 = __importDefault(require("../core/RouteProvider"));
// Create API analytics controller
const apiAnalyticsController = new api_analytics_controller_1.ApiAnalyticsController();
// Create a route builder for API analytics routes
const routeBuilder = RouteProvider_1.default.createRouteBuilder("apiAnalytics", "/api/analytics/api", "API analytics routes")
    .tags("analytics", "api");
// Add routes
routeBuilder
    // Get API analytics
    .get("/", apiAnalyticsController.getAnalytics, ["ADMIN"])
    // Get API analytics summary
    .get("/summary", apiAnalyticsController.getAnalyticsSummary, ["ADMIN"])
    // Get API analytics by version
    .get("/version/:version", apiAnalyticsController.getAnalyticsByVersion, ["ADMIN"], [(0, express_validator_1.param)("version").notEmpty()])
    // Get API analytics summary by version
    .get("/version/:version/summary", apiAnalyticsController.getAnalyticsSummaryByVersion, ["ADMIN"], [(0, express_validator_1.param)("version").notEmpty()]);
// Build the router
const router = routeBuilder.build();
exports.default = router;
//# sourceMappingURL=api-analytics.routes.js.map