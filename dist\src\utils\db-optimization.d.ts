/**
 * Database Query Optimization Utility
 * Re-exports from shared DatabaseUtils to eliminate duplication
 */
/**
 * Log a slow query
 * @param metrics Query metrics
 */
export declare const logSlowQuery: any;
/**
 * Get recent slow queries
 * @returns Recent slow queries
 */
export declare const getRecentSlowQueries: any;
/**
 * Clear recent slow queries
 */
export declare const clearRecentSlowQueries: any;
/**
 * Set up query performance monitoring
 * @param prismaClient Prisma client instance
 */
export declare const setupQueryPerformanceMonitoring: any;
/**
 * Optimize a findMany query by adding pagination and limiting fields
 * @param model Model name
 * @param args Query arguments
 * @param defaultPageSize Default page size
 * @param maxPageSize Maximum page size
 * @returns Optimized query arguments
 */
export declare const optimizeFindManyQuery: any;
/**
 * Execute a query with timeout and retry
 * @param queryFn Function that executes the query
 * @param timeout Timeout in milliseconds
 * @param retries Number of retries
 * @returns Query result
 */
export declare const executeQueryWithTimeoutAndRetry: any;
/**
 * Initialize database optimization
 */
export declare const initializeDatabaseOptimization: any;
declare const _default: {
    logSlowQuery: any;
    getRecentSlowQueries: any;
    clearRecentSlowQueries: any;
    setupQueryPerformanceMonitoring: any;
    optimizeFindManyQuery: any;
    executeQueryWithTimeoutAndRetry: any;
    initializeDatabaseOptimization: any;
};
export default _default;
//# sourceMappingURL=db-optimization.d.ts.map