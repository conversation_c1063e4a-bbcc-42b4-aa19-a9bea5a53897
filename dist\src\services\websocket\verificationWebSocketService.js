"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verificationWebSocketService = exports.VerificationWebSocketService = exports.verificationEvents = void 0;
const socket_io_1 = require("socket.io");
const events_1 = require("events");
const logger_1 = require("../../lib/logger");
// Verification events
exports.verificationEvents = new events_1.EventEmitter();
/**
 * WebSocket service for payment verification
 */
class VerificationWebSocketService {
    constructor() {
        this.io = null;
        this.connectedClients = new Map();
    }
    /**
   * Get singleton instance
   */
    static getInstance() {
        if (!VerificationWebSocketService.instance) {
            VerificationWebSocketService.instance = new VerificationWebSocketService();
        }
        return VerificationWebSocketService.instance;
    }
    /**
   * Initialize WebSocket server
   * @param httpServer HTTP server
   */
    initialize(httpServer) {
        if (this.io) {
            return this.io;
        }
        // Create WebSocket server
        this.io = new socket_io_1.Server(httpServer, {
            path: "/ws/verification",
            cors: { origin: "*",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ["websocket", "polling"],
            pingInterval: 10000,
            pingTimeout: 5000,
            cookie: false
        });
        // Set up connection handler
        this.io.on("connection", this.handleConnection.bind(this));
        // Set up verification event listeners
        this.setupEventListeners();
        logger_1.logger.info("Verification WebSocket server initialized");
        return this.io;
    }
    /**
   * Handle WebSocket connection
   * @param socket Socket
   */
    handleConnection(socket) {
        logger_1.logger.info(`Verification client connected: ${socket.id}`);
        // Get payment ID and merchant ID from query parameters
        const paymentId = socket.handshake.query.paymentId;
        const merchantId = socket.handshake.query.merchantId;
        if (!paymentId) {
            logger_1.logger.warn(`Client ${socket.id} connected without payment ID, disconnecting`);
            socket.disconnect();
            return;
        }
        // Add client to connected clients
        this.addClient(paymentId, socket.id);
        // Join payment room
        socket.join(`payment:${paymentId}`);
        // Join merchant room if merchant ID is provided
        if (merchantId) {
            socket.join(`merchant:${merchantId}`);
        }
        // Handle join event
        socket.on("join", (data) => {
            if (data.paymentId) {
                socket.join(`payment:${data.paymentId}`);
                this.addClient(data.paymentId, socket.id);
                logger_1.logger.info(`Client ${socket.id} joined payment room: ${data.paymentId}`);
            }
            if (data.merchantId) {
                socket.join(`merchant:${data.merchantId}`);
                logger_1.logger.info(`Client ${socket.id} joined merchant room: ${data.merchantId}`);
            }
        });
        // Handle disconnect event
        socket.on("disconnect", () => {
            logger_1.logger.info(`Verification client disconnected: ${socket.id}`);
            this.removeClient(paymentId, socket.id);
        });
    }
    /**
   * Add client to connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
    addClient(paymentId, socketId) {
        if (!this.connectedClients.has(paymentId)) {
            this.connectedClients.set(paymentId, new Set());
        }
        this.connectedClients.get(paymentId)?.add(socketId);
    }
    /**
   * Remove client from connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
    removeClient(paymentId, socketId) {
        if (this.connectedClients.has(paymentId)) {
            this.connectedClients.get(paymentId)?.delete(socketId);
            // Remove payment ID if no clients are connected
            if (this.connectedClients.get(paymentId)?.size === 0) {
                this.connectedClients.delete(paymentId);
            }
        }
    }
    /**
   * Set up verification event listeners
   */
    setupEventListeners() {
        // Verification started event
        exports.verificationEvents.on("verification.started", (data) => {
            this.emitToPayment(data.paymentId, "verification.started", data);
            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "verification.started", data);
            }
        });
        // Verification updated event
        exports.verificationEvents.on("verification.updated", (data) => {
            this.emitToPayment(data.paymentId, "verification.updated", data);
            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "verification.updated", data);
            }
        });
        // Verification completed event
        exports.verificationEvents.on("verification.completed", (data) => {
            this.emitToPayment(data.paymentId, "verification.completed", data);
            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "verification.completed", data);
            }
        });
        // Verification failed event
        exports.verificationEvents.on("verification.failed", (data) => {
            this.emitToPayment(data.paymentId, "verification.failed", data);
            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "verification.failed", data);
            }
        });
        // Transaction updated event
        exports.verificationEvents.on("transaction.updated", (data) => {
            this.emitToPayment(data.paymentId, "transaction.updated", data);
            if (data.merchantId) {
                this.emitToMerchant(data.merchantId, "transaction.updated", data);
            }
        });
    }
    /**
   * Emit event to payment room
   * @param paymentId Payment ID
   * @param event Event name
   * @param data Event data
   */
    emitToPayment(paymentId, event, data) {
        if (!this.io) {
            logger_1.logger.warn("WebSocket server not initialized");
            return;
        }
        this.io.to(`payment:${paymentId}`).emit(event, data);
    }
    /**
   * Emit event to merchant room
   * @param merchantId Merchant ID
   * @param event Event name
   * @param data Event data
   */
    emitToMerchant(merchantId, event, data) {
        if (!this.io) {
            logger_1.logger.warn("WebSocket server not initialized");
            return;
        }
        this.io.to(`merchant:${merchantId}`).emit(event, data);
    }
    /**
   * Get connected clients count
   */
    getConnectedClientsCount() {
        let count = 0;
        this.connectedClients.forEach((clients));
        {
            count += clients.size;
        }
        ;
        return count;
    }
    /**
   * Get connected payments count
   */
    getConnectedPaymentsCount() {
        return this.connectedClients.size;
    }
}
exports.VerificationWebSocketService = VerificationWebSocketService;
// Export singleton instance
exports.verificationWebSocketService = VerificationWebSocketService.getInstance();
exports.default = exports.verificationWebSocketService;
//# sourceMappingURL=verificationWebSocketService.js.map