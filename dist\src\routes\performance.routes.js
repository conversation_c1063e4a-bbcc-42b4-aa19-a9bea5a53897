"use strict";
// jscpd:ignore-file
/**
 * Performance Monitoring Routes
 *
 * This module provides API endpoints for performance monitoring:
 * - Get performance metrics
 * - Reset performance metrics
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const logger_1 = require("../lib/logger");
const performance_monitor_middleware_1 = require("../middlewares/performance-monitor.middleware");
const environment_validator_1 = require("../utils/environment-validator");
const router = express_1.default.Router();
/**
 * @route GET /api/performance
 * @desc Get performance metrics
 * @access Private (in production)
 */
router.get("/", (req, res) => {
    try {
        // In production, require authorization
        if ((0, environment_validator_1.isProduction)() && !req.headers.authorization) {
            return res.status(401).json({
                status: "error",
                message: "Unauthorized",
                timestamp: new Date()
            });
        }
        const metrics = (0, performance_monitor_middleware_1.getPerformanceMetrics)();
        res.status(200).json({
            status: "success",
            data: metrics,
            timestamp: new Date()
        });
    }
    catch (error) {
        logger_1.logger.error("Failed to get performance metrics", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get performance metrics",
            timestamp: new Date()
        });
    }
});
/**
 * @route POST /api/performance/reset
 * @desc Reset performance metrics
 * @access Private
 */
router.post("/reset", (req, res) => {
    try {
        // Always require authorization
        if (!req.headers.authorization) {
            return res.status(401).json({
                status: "error",
                message: "Unauthorized",
                timestamp: new Date()
            });
        }
        (0, performance_monitor_middleware_1.resetPerformanceMetrics)();
        res.status(200).json({
            status: "success",
            message: "Performance metrics reset successfully",
            timestamp: new Date()
        });
    }
    catch (error) {
        logger_1.logger.error("Failed to reset performance metrics", error);
        res.status(500).json({
            status: "error",
            message: "Failed to reset performance metrics",
            timestamp: new Date()
        });
    }
});
exports.default = router;
//# sourceMappingURL=performance.routes.js.map