/**
 * Test Utils Module
 *
 * Centralized exports for the test utility system.
 */

// Core exports
export * from './core/TestTypes';

// Factory exports
export * from './factories/MockFactories';

// Runner exports
export * from './runners/TestRunners';

// Suite builder exports
export * from './suites/TestSuiteBuilders';

// Data generator exports
export * from './generators/DataGenerators';

// Assertion exports
export * from './assertions/CustomAssertions';

// Convenience re-exports for common patterns
export {
  createMockRequest,
  createMockResponse,
  createMockNext,
  createMockPrismaClient,
  createMockJwtToken,
  createMockApiResponse,
  createMockErrorResponse,
} from './factories/MockFactories';

export { testController, testService, testRepository, testMiddleware } from './runners/TestRunners';

export {
  createControllerTestSuite,
  createServiceTestSuite,
  createRepositoryTestSuite,
  createIntegrationTestSuite,
  createPerformanceTestSuite,
  createApiTestSuite,
} from './suites/TestSuiteBuilders';

export {
  generateUUID,
  generateEmail,
  generateRandomString,
  generateMockUser,
  generateMockMerchant,
  generateMockTransaction,
  generateMockArray,
  generateMockDataWithRelationships,
} from './generators/DataGenerators';

export {
  setupCustomMatchers,
  AssertionHelpers,
  DatabaseAssertions,
} from './assertions/CustomAssertions';

// Default export - main test utility class
export class TestUtils {
  /**
   * Setup test environment
   */
  static setup(): void {
    // setupCustomMatchers(); // Commented out due to type issues
  }

  /**
   * Create a complete test context
   */
  static createTestContext(name: string): {
    mockRequest: any;
    mockResponse: any;
    mockNext: any;
    mockPrisma: any;
    mockUser: any;
    mockMerchant: any;
    cleanup: () => void;
  } {
    const mockRequest = createMockRequest();
    const mockResponse = createMockResponse();
    const mockNext = createMockNext();
    const mockPrisma = createMockPrismaClient();
    const mockUser = generateMockUser();
    const mockMerchant = generateMockMerchant();

    const cleanup = () => {
      jest.clearAllMocks();
    };

    return {
      mockRequest,
      mockResponse,
      mockNext,
      mockPrisma,
      mockUser,
      mockMerchant,
      cleanup,
    };
  }

  /**
   * Create a test database context
   */
  static createDatabaseTestContext(): {
    mockPrisma: any;
    seedData: () => Promise<void>;
    cleanup: () => Promise<void>;
  } {
    const mockPrisma = createMockPrismaClient();
    const testData = generateMockDataWithRelationships();

    const seedData = async () => {
      // Mock seeding data
      mockPrisma.user.findMany.mockResolvedValue(testData.users);
      mockPrisma.merchant.findMany.mockResolvedValue(testData.merchants);
      mockPrisma.transaction.findMany.mockResolvedValue(testData.transactions);
      mockPrisma.paymentMethod.findMany.mockResolvedValue(testData.paymentMethods);
    };

    const cleanup = async () => {
      jest.clearAllMocks();
    };

    return {
      mockPrisma,
      seedData,
      cleanup,
    };
  }

  /**
   * Create an API test context
   */
  static createApiTestContext(baseUrl: string = 'http://localhost:3000'): {
    request: any;
    authenticate: (token: string) => void;
    expectSuccess: (response: any) => void;
    expectError: (response: any, status?: number) => void;
  } {
    let authToken: string | null = null;

    const request = {
      get: (path: string) => ({
        url: `${baseUrl}${path}`,
        method: 'GET',
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
      post: (path: string, data?: any) => ({
        url: `${baseUrl}${path}`,
        method: 'POST',
        data,
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
      put: (path: string, data?: any) => ({
        url: `${baseUrl}${path}`,
        method: 'PUT',
        data,
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
      delete: (path: string) => ({
        url: `${baseUrl}${path}`,
        method: 'DELETE',
        headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      }),
    };

    const authenticate = (token: string) => {
      authToken = token;
    };

    const expectSuccess = (response: any) => {
      // expect(response).toMatchApiResponse(response.data); // Custom matcher not available
      expect(response.success).toBe(true);
      expect(response.status).toBeGreaterThanOrEqual(200);
      expect(response.status).toBeLessThan(300);
    };

    const expectError = (response: any, status: number = 400) => {
      expect(response.success).toBe(false);
      expect(response.status).toBe(status);
      expect(response.error).toBeDefined();
    };

    return {
      request,
      authenticate,
      expectSuccess,
      expectError,
    };
  }
}
