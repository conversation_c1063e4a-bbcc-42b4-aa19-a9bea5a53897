// jscpd:ignore-file
/**
 * Binance transaction status
 * 0: pending
 * 1: success
 * 6: credited but cannot withdraw
 */
export type BinanceTransactionStatus = 0 | 1 | 6;

/**
 * Binance transaction
 */
export interface BinanceTransaction {
  /**
   * Amount
   */
  amount: string;
  
  /**
   * Coin symbol (e.g., USDT)
   */
  coin: string;
  
  /**
   * Network (e.g., TRC20)
   */
  network: string;
  
  /**
   * Transaction status
   * 0: pending
   * 1: success
   * 6: credited but cannot withdraw
   */
  status: BinanceTransactionStatus;
  
  /**
   * Deposit address
   */
  address: string;
  
  /**
   * Address tag
   */
  addressTag: string;
  
  /**
   * Transaction ID
   */
  txId: string;
  
  /**
   * Insert time
   */
  insertTime: number;
  
  /**
   * Transfer type
   * 0: deposit
   * 1: withdraw
   */
  transferType: number;
  
  /**
   * Confirmation
   */
  confirmTimes: string;
  
  /**
   * Unlock confirm
   */
  unlockConfirm: number;
  
  /**
   * Wallet type
   */
  walletType: number;
}

/**
 * Binance deposit address
 */
export interface BinanceDepositAddress {
  /**
   * Deposit address
   */
  address: string;
  
  /**
   * Coin symbol (e.g., USDT)
   */
  coin: string;
  
  /**
   * Address tag
   */
  tag: string;
  
  /**
   * URL
   */
  url: string;
}

/**
 * Binance API error
 */
export interface BinanceApiError {
  /**
   * Error code
   */
  code: number;
  
  /**
   * Error message
   */
  msg: string;
}
