"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerV1Routes = registerV1Routes;
const merchant_routes_1 = __importDefault(require("./merchant.routes"));
const payment_method_routes_1 = __importDefault(require("./payment-method.routes"));
const RouteProvider_1 = __importDefault(require("../../core/RouteProvider"));
/**
 * Register v1 routes
 * @param app Express application
 */
function registerV1Routes(app) {
    // Create version middleware
    const versionMiddleware = RouteProvider_1.default.createVersionMiddleware("v1");
    // Apply version middleware
    app.use("/api/v1", versionMiddleware);
    // Register routes
    app.use("/api/v1/merchants", merchant_routes_1.default);
    app.use("/api/v1/payment-methods", payment_method_routes_1.default);
}
exports.default = registerV1Routes;
//# sourceMappingURL=index.js.map