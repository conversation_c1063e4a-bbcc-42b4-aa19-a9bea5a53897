import { Module } from "../../core/module";
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * User Module
 * This module provides user functionality with zero duplication
 */
export declare class UserModule {
    private moduleFactory;
    private moduleRegistry;
    private container;
    private module;
    /**
     * Create a new user module
     */
    constructor();
    /**
     * Get the module
     * @returns User module
     */
    getModule(): Module;
}
//# sourceMappingURL=user.module.d.ts.map