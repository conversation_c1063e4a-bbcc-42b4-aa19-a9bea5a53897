"use strict";
// jscpd:ignore-file
// jscpd:ignore-start
// jscpd:ignore-start
// Duplicated code removed and replaced with import
// jscpd:ignore-end
Object.defineProperty(exports, "__esModule", { value: true });
// Create a route builder for payment method routes
const routeBuilder = routeProvider.createRouteBuilder("paymentMethod", 
// jscpd:ignore-end
"/payment-methods", "Payment method management routes")
    .version("v1");
//# sourceMappingURL=payment-method.routes.js.map