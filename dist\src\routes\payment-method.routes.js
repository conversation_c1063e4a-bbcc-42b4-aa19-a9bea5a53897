"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_validator_1 = require("express-validator");
const ControllerProvider_1 = __importDefault(require("../core/ControllerProvider"));
const RouteProvider_1 = __importDefault(require("../core/RouteProvider"));
const paymentMethodController = ControllerProvider_1.default.getPaymentMethodController();
// Create a route builder for payment method routes
const routeBuilder = RouteProvider_1.default.createRouteBuilder("paymentMethod", "/api/payment-methods", "Payment method management routes");
// Define payment method routes
routeBuilder.addRoute({
    method: "GET",
    path: "/",
    description: "Get all payment methods",
    middleware: ["authenticate"],
    handler: paymentMethodController.getAllPaymentMethods
});
routeBuilder.addRoute({
    method: "GET",
    path: "/:id",
    description: "Get payment method by ID",
    middleware: ["authenticate"],
    validation: [
        (0, express_validator_1.param)("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: paymentMethodController.getPaymentMethodById
});
routeBuilder.addRoute({
    method: "GET",
    path: "/merchant/:merchantId",
    description: "Get payment methods by merchant ID",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        (0, express_validator_1.param)("merchantId").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: paymentMethodController.getPaymentMethodsByMerchantId
});
routeBuilder.addRoute({
    method: "POST",
    path: "/",
    description: "Create payment method",
    middleware: ["authenticate", "isMerchantOrAdmin"],
    handler: paymentMethodController.createPaymentMethod
});
routeBuilder.addRoute({
    method: "PUT",
    path: "/:id",
    description: "Update payment method",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        (0, express_validator_1.param)("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: paymentMethodController.updatePaymentMethod
});
routeBuilder.addRoute({
    method: "DELETE",
    path: "/:id",
    description: "Delete payment method",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        (0, express_validator_1.param)("id").isUUID().withMessage("Invalid payment method ID format")
    ],
    handler: paymentMethodController.deletePaymentMethod
});
// Export the router
exports.default = routeBuilder.build();
//# sourceMappingURL=payment-method.routes.js.map