"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ControllerUtils = void 0;
const appError_1 = require("./appError");
const logger_1 = require("./logger");
/**
 * Controller utilities for common controller patterns
 */
exports.ControllerUtils = {
    /**
     * Check if user is authenticated and has required role
     * @param req Express request
     * @param requiredRoles Array of allowed roles (optional)
     * @returns User ID and role information
     * @throws AppError if user is not authenticated or doesn't have required role
     */
    checkAuth(req, requiredRoles) {
        const user = req.user;
        if (!user) {
            throw new appError_1.AppError({
                message: 'Authentication required',
                type: appError_1.ErrorType.AUTHENTICATION,
                code: appError_1.ErrorCode.MISSING_TOKEN,
            });
        }
        const { userId, role: userRole, merchantId } = user;
        if (requiredRoles && !requiredRoles.includes(userRole)) {
            throw new appError_1.AppError({
                message: 'Unauthorized',
                type: appError_1.ErrorType.AUTHORIZATION,
                code: appError_1.ErrorCode.INSUFFICIENT_PERMISSIONS,
            });
        }
        return { userId, userRole, merchantId };
    },
    /**
     * Get and validate pagination parameters from request
     * @param req Express request
     * @returns Pagination parameters
     */
    getPaginationParams(req) {
        const limit = parseInt(req.query.limit) || 10;
        const offset = parseInt(req.query.offset) || 0;
        const page = Math.floor(offset / limit) + 1;
        const sortBy = req.query.sortBy || 'createdAt';
        const sortOrder = req.query.sortOrder || 'desc';
        return { limit, offset, page, sortBy, sortOrder };
    },
    /**
     * Format paginated response
     * @param data Data to return
     * @param total Total number of items
     * @param pagination Pagination parameters
     * @returns Formatted response
     */
    formatPaginatedResponse(data, total, pagination) {
        return {
            success: true,
            data,
            total,
            page: Math.floor(pagination.offset / pagination.limit) + 1,
            totalPages: Math.ceil(total / pagination.limit),
        };
    },
    /**
     * Format success response
     * @param data Data to return
     * @returns Formatted response
     */
    formatSuccessResponse(data) {
        return {
            success: true,
            data,
        };
    },
    /**
     * Format message response
     * @param message Message to return
     * @returns Formatted response
     */
    formatMessageResponse(message) {
        return {
            success: true,
            message,
        };
    },
    /**
     * Format error response
     * @param error Error to format
     * @returns Formatted error response
     */
    formatErrorResponse(error) {
        if (error instanceof appError_1.AppError) {
            return {
                success: false,
                error: {
                    code: error.statusCode.toString(),
                    message: error.message,
                    details: error.details,
                },
            };
        }
        // Log unexpected errors
        logger_1.logger.error('Unexpected error:', error);
        return {
            success: false,
            error: {
                code: '500',
                message: error.message || 'An unexpected error occurred',
            },
        };
    },
    /**
     * Validate required fields in request body
     * @param req Express request
     * @param requiredFields Array of required field names
     * @throws AppError if any required field is missing
     */
    validateRequiredFields(req, requiredFields) {
        const missingFields = requiredFields.filter((field) => {
            const value = req.body[field];
            return value === undefined || value === null || value === '';
        });
        if (missingFields?.length > 0) {
            throw new appError_1.AppError({
                message: `Missing required fields: ${missingFields.join(', ')}`,
                type: appError_1.ErrorType.VALIDATION,
                code: appError_1.ErrorCode.MISSING_REQUIRED_FIELD,
            });
        }
    },
    /**
     * Validate enum values
     * @param value Value to validate
     * @param enumObject Enum object to validate against
     * @param fieldName Field name for error message
     * @throws AppError if value is not in enum
     */
    validateEnum(value, enumObject, fieldName) {
        if (!Object.values(enumObject).includes(value)) {
            throw new appError_1.AppError({
                message: `Invalid ${fieldName}`,
                type: appError_1.ErrorType.VALIDATION,
                code: appError_1.ErrorCode.INVALID_VALUE,
            });
        }
    },
};
exports.default = exports.ControllerUtils;
