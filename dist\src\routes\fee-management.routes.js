"use strict";
// jscpd:ignore-file
/**
 * Fee Management Routes
 *
 * This file defines the routes for fee management-related API endpoints.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const fee_management_controller_1 = __importDefault(require("../controllers/fee-management.controller"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = express_1.default.Router();
// Simple test route
router.get('/test', (req, res) => {
    res.json({ message: 'Fee management routes working!' });
});
/**
 * @swagger
 * /api/fee-management/merchants/{merchantId}/calculate-fee: *, post:
 *     summary: Calculate fee for a transaction
 *     description: Calculates the fee for a transaction based on merchant, amount, currency, and payment method
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             required:
 *               - amount
 *               - currency
 *             properties: *, amount:
 *                 type: number
 *                 description: Transaction amount
 *               currency: *, type: string
 *                 description: Currency code
 *               paymentMethodId: *, type: string
 *                 description: Payment method ID
 *     responses: *, 200:
 *         description: Fee calculation result
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
// Temporarily comment out to test
// router.post(
//   '/merchants/:merchantId/calculate-fee',
//   authenticateJWT,
//   authorize(['ADMIN', 'MERCHANT']),
//   feeManagementController.calculateFee
// );
/**
 * @swagger
 * /api/fee-management/fee-tiers: *, get:
 *     summary: Get fee tiers
 *     description: Returns fee tiers based on filters
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema: *, type: string
 *         description: Fee tier type
 *       - in: query
 *         name: merchantId
 *         schema: *, type: string
 *         description: Merchant ID
 *       - in: query
 *         name: paymentMethodType
 *         schema: *, type: string
 *         description: Payment method type
 *     responses: *, 200:
 *         description: Fee tiers
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
// Temporarily comment out to test
// router.get(
//   '/fee-tiers',
//   authenticateJWT,
//   authorize(['ADMIN']),
//   feeManagementController.getFeeTiers
// );
/**
 * @swagger
 * /api/fee-management/fee-tiers: *, post:
 *     summary: Create fee tier
 *     description: Creates a new fee tier
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *               - calculationMethod
 *               - priority
 *             properties: *, name:
 *                 type: string
 *                 description: Fee tier name
 *               type: *, type: string
 *                 description: Fee tier type
 *               minVolume: *, type: number
 *                 description: Minimum transaction volume
 *               maxVolume: *, type: number
 *                 description: Maximum transaction volume
 *               merchantId: *, type: string
 *                 description: Merchant ID
 *               paymentMethodType: *, type: string
 *                 description: Payment method type
 *               calculationMethod: *, type: string
 *                 description: Fee calculation method
 *               percentageFee: *, type: number
 *                 description: Percentage fee
 *               fixedFee: *, type: number
 *                 description: Fixed fee
 *               currency: *, type: string
 *                 description: Currency code
 *               priority: *, type: integer
 *                 description: Priority
 *     responses: *, 200:
 *         description: Fee tier created
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
// Temporarily comment out to test
// router.post(
//   '/fee-tiers',
//   authenticateJWT,
//   authorize(['ADMIN']),
//   feeManagementController.createFeeTier
// );
/**
 * @swagger
 * /api/fee-management/fee-tiers/{tierId}:
 *   get: *, summary: Get fee tier by ID
 *     description: Returns a fee tier by ID
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tierId
 *         required: true
 *         schema: *, type: string
 *         description: Fee tier ID
 *     responses: *, 200:
 *         description: Fee tier
 *       401: *, description: Unauthorized
 *       404: *, description: Fee tier not found
 *       500: *, description: Server error
 */
// Temporarily comment out to test
// router.get(
//   '/fee-tiers/:tierId',
//   authenticateJWT,
//   authorize(['ADMIN']),
//   feeManagementController.getFeeTierById
// );
/**
 * @swagger
 * /api/fee-management/fee-tiers/{tierId}:
 *   put: *, summary: Update fee tier
 *     description: Updates a fee tier
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tierId
 *         required: true
 *         schema: *, type: string
 *         description: Fee tier ID
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             properties: *, name:
 *                 type: string
 *                 description: Fee tier name
 *               type: *, type: string
 *                 description: Fee tier type
 *               minVolume: *, type: number
 *                 description: Minimum transaction volume
 *               maxVolume: *, type: number
 *                 description: Maximum transaction volume
 *               merchantId: *, type: string
 *                 description: Merchant ID
 *               paymentMethodType: *, type: string
 *                 description: Payment method type
 *               calculationMethod: *, type: string
 *                 description: Fee calculation method
 *               percentageFee: *, type: number
 *                 description: Percentage fee
 *               fixedFee: *, type: number
 *                 description: Fixed fee
 *               currency: *, type: string
 *                 description: Currency code
 *               priority: *, type: integer
 *                 description: Priority
 *               isActive: *, type: boolean
 *                 description: Whether the tier is active
 *     responses: *, 200:
 *         description: Fee tier updated
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       404: *, description: Fee tier not found
 *       500: *, description: Server error
 */
router.put('/fee-tiers/:tierId', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(['ADMIN']), fee_management_controller_1.default.updateFeeTier);
/**
 * @swagger
 * /api/fee-management/fee-tiers/{tierId}:
 *   delete: *, summary: Delete fee tier
 *     description: Deletes a fee tier
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tierId
 *         required: true
 *         schema: *, type: string
 *         description: Fee tier ID
 *     responses: *, 200:
 *         description: Fee tier deleted
 *       401: *, description: Unauthorized
 *       404: *, description: Fee tier not found
 *       500: *, description: Server error
 */
router.delete('/fee-tiers/:tierId', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(['ADMIN']), fee_management_controller_1.default.deleteFeeTier);
/**
 * @swagger
 * /api/fee-management/merchants/{merchantId}/fee-calculations: *, get:
 *     summary: Get fee calculations for merchant
 *     description: Returns fee calculations for a merchant
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *       - in: query
 *         name: startDate
 *         schema: *, type: string
 *           format: date
 *         description: Start date
 *       - in: query
 *         name: endDate
 *         schema: *, type: string
 *           format: date
 *         description: End date
 *     responses: *, 200:
 *         description: Fee calculations
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
router.get('/merchants/:merchantId/fee-calculations', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(['ADMIN', 'MERCHANT']), fee_management_controller_1.default.getMerchantFeeCalculations);
/**
 * @swagger
 * /api/fee-management/merchants/{merchantId}/fee-discounts: *, get:
 *     summary: Get merchant fee discounts
 *     description: Returns fee discounts for a merchant
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *     responses: *, 200:
 *         description: Fee discounts
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
router.get('/merchants/:merchantId/fee-discounts', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(['ADMIN', 'MERCHANT']), fee_management_controller_1.default.getMerchantFeeDiscounts);
/**
 * @swagger
 * /api/fee-management/merchants/{merchantId}/fee-discounts: *, post:
 *     summary: Create merchant fee discount
 *     description: Creates a new fee discount for a merchant
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: merchantId
 *         required: true
 *         schema: *, type: string
 *         description: Merchant ID
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             required:
 *               - discountType
 *               - discountValue
 *               - startDate
 *             properties: *, discountType:
 *                 type: string
 *                 description: Discount type
 *               discountValue: *, type: number
 *                 description: Discount value
 *               startDate: *, type: string
 *                 format: date
 *                 description: Start date
 *               endDate: *, type: string
 *                 format: date
 *                 description: End date
 *               reason: *, type: string
 *                 description: Reason for discount
 *     responses: *, 200:
 *         description: Fee discount created
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       500: *, description: Server error
 */
router.post('/merchants/:merchantId/fee-discounts', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(['ADMIN']), fee_management_controller_1.default.createMerchantFeeDiscount);
/**
 * @swagger
 * /api/fee-management/fee-discounts/{discountId}:
 *   put: *, summary: Update merchant fee discount
 *     description: Updates a fee discount
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: discountId
 *         required: true
 *         schema: *, type: string
 *         description: Discount ID
 *     requestBody: *, required: true
 *       content:
 *         application/json: *, schema:
 *             type: object
 *             properties: *, discountValue:
 *                 type: number
 *                 description: Discount value
 *               endDate: *, type: string
 *                 format: date
 *                 description: End date
 *               reason: *, type: string
 *                 description: Reason for discount
 *               isActive: *, type: boolean
 *                 description: Whether the discount is active
 *     responses: *, 200:
 *         description: Fee discount updated
 *       400: *, description: Bad request
 *       401: *, description: Unauthorized
 *       404: *, description: Fee discount not found
 *       500: *, description: Server error
 */
router.put('/fee-discounts/:discountId', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(['ADMIN']), fee_management_controller_1.default.updateMerchantFeeDiscount);
/**
 * @swagger
 * /api/fee-management/fee-discounts/{discountId}:
 *   delete: *, summary: Delete merchant fee discount
 *     description: Deletes a fee discount
 *     tags: [Fee Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: discountId
 *         required: true
 *         schema: *, type: string
 *         description: Discount ID
 *     responses: *, 200:
 *         description: Fee discount deleted
 *       401: *, description: Unauthorized
 *       404: *, description: Fee discount not found
 *       500: *, description: Server error
 */
router.delete('/fee-discounts/:discountId', auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(['ADMIN']), fee_management_controller_1.default.deleteMerchantFeeDiscount);
exports.default = router;
//# sourceMappingURL=fee-management.routes.js.map