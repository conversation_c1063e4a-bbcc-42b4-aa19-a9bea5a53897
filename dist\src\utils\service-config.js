"use strict";
// jscpd:ignore-file
/**
 * Service Configuration Utility
 *
 * This utility provides functions for managing environment-specific third-party service configurations
 * to ensure complete isolation between production and demo environments.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.logServiceConfig = exports.getServiceCredentials = exports.getCallbackUrl = exports.getWebhookUrl = exports.getApiUrl = exports.getApiSecret = exports.getApiKey = exports.getServiceConfig = void 0;
const logger_1 = require("../lib/logger");
const environment_1 = require("../config/environment");
/**
 * Get environment-specific service configuration
 * @param serviceName Name of the service
 * @param configKey Configuration key
 * @param defaultValue Default value if not found
 * @returns Environment-specific configuration value
 */
const getServiceConfig = (serviceName, configKey, defaultValue) => {
    const env = (0, environment_1.getEnvironment)();
    // Try to get environment-specific configuration
    const envSpecificKey = `${env.toUpperCase()}_${serviceName.toUpperCase()}_${configKey.toUpperCase()}`;
    const envSpecificValue = process.env[envSpecificKey];
    if (envSpecificValue !== undefined) {
        return envSpecificValue;
    }
    // Try to get service-specific configuration
    const serviceSpecificKey = `${serviceName.toUpperCase()}_${configKey.toUpperCase()}`;
    const serviceSpecificValue = process.env[serviceSpecificKey];
    if (serviceSpecificValue !== undefined) {
        return serviceSpecificValue;
    }
    // Return default value
    return defaultValue;
};
exports.getServiceConfig = getServiceConfig;
/**
 * Get environment-specific API key
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API key
 */
const getApiKey = (serviceName, defaultValue = "") => {
    return (0, exports.getServiceConfig)(serviceName, "API_KEY", defaultValue);
};
exports.getApiKey = getApiKey;
/**
 * Get environment-specific API secret
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API secret
 */
const getApiSecret = (serviceName, defaultValue = "") => {
    return (0, exports.getServiceConfig)(serviceName, "API_SECRET", defaultValue);
};
exports.getApiSecret = getApiSecret;
/**
 * Get environment-specific API URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API URL
 */
const getApiUrl = (serviceName, defaultValue = "") => {
    return (0, exports.getServiceConfig)(serviceName, "API_URL", defaultValue);
};
exports.getApiUrl = getApiUrl;
/**
 * Get environment-specific webhook URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific webhook URL
 */
const getWebhookUrl = (serviceName, defaultValue = "") => {
    return (0, exports.getServiceConfig)(serviceName, "WEBHOOK_URL", defaultValue);
};
exports.getWebhookUrl = getWebhookUrl;
/**
 * Get environment-specific callback URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific callback URL
 */
const getCallbackUrl = (serviceName, defaultValue = "") => {
    return (0, exports.getServiceConfig)(serviceName, "CALLBACK_URL", defaultValue);
};
exports.getCallbackUrl = getCallbackUrl;
/**
 * Get environment-specific service credentials
 * @param serviceName Name of the service
 * @returns Environment-specific service credentials
 */
const getServiceCredentials = (serviceName) => {
    const env = (0, environment_1.getEnvironment)();
    const credentials = {};
    // Get all environment variables
    for (const key in process.env) {
        // Check if the key is for this service and environment
        const envPrefix = `${env.toUpperCase()}_${serviceName.toUpperCase()}_`;
        const servicePrefix = `${serviceName.toUpperCase()}_`;
        if (key.startsWith(envPrefix)) {
            // Extract the credential key (remove the prefix)
            const credentialKey = key.substring(envPrefix.length).toLowerCase();
            credentials[credentialKey] = process.env[key] || "";
        }
        else if (key.startsWith(servicePrefix) && !credentials[key.substring(servicePrefix.length).toLowerCase()]) {
            // If we don't already have an environment-specific value, use the service-specific one
            const credentialKey = key.substring(servicePrefix.length).toLowerCase();
            credentials[credentialKey] = process.env[key] || "";
        }
    }
    return credentials;
};
exports.getServiceCredentials = getServiceCredentials;
/**
 * Log service configuration for debugging
 * @param serviceName Name of the service
 */
const logServiceConfig = (serviceName) => {
    const env = (0, environment_1.getEnvironment)();
    const credentials = (0, exports.getServiceCredentials)(serviceName);
    // Mask sensitive values
    const maskedCredentials = {};
    for (const key in credentials) {
        if (key.includes("key") || key.includes("secret") || key.includes("password") || key.includes("token")) {
            maskedCredentials[key] = credentials[key] ? "********" : "not set";
        }
        else {
            maskedCredentials[key] = credentials[key] || "not set";
        }
    }
    logger_1.logger.debug(`Service configuration for ${serviceName} in ${env} environment:`, maskedCredentials);
};
exports.logServiceConfig = logServiceConfig;
exports.default = {
    getServiceConfig: exports.getServiceConfig,
    getApiKey: exports.getApiKey,
    getApiSecret: exports.getApiSecret,
    getApiUrl: exports.getApiUrl,
    getWebhookUrl: exports.getWebhookUrl,
    getCallbackUrl: exports.getCallbackUrl,
    getServiceCredentials: exports.getServiceCredentials,
    logServiceConfig: exports.logServiceConfig
};
//# sourceMappingURL=service-config.js.map