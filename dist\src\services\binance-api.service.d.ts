import { BinanceApiConfig, BinanceApiResponse, BinanceTransaction } from "@amazingpay/shared";
/**
 * Service for interacting with the Binance API
 */
export declare class BinanceApiService {
    private apiKey;
    private apiSecret;
    private baseUrl;
    /**
   * Create a new BinanceApiService instance
   * @param apiConfig Binance API configuration
   */
    constructor(apiConfig?: BinanceApiConfig);
    /**
   * Create a BinanceApiService instance from merchant configuration
   * @param apiKey Binance API key
   * @param secretKey Binance API secret
   * @returns BinanceApiService instance
   */
    static createFromMerchantConfig(apiKey: string, secretKey: string): BinanceApiService;
    /**
   * Generate signature for Binance API requests
   * @param queryString Query string to sign
   * @returns Signature
   */
    private makeRequest;
    /**
   * Test API connection
   * @returns Connection status
   */
    testConnection(): Promise<BinanceApiResponse<{
        success: boolean;
    }>>;
    /**
   * Get account information
   * @returns Account information
   */
    getAccountInfo(): Promise<BinanceApiResponse<any>>;
    /**
   * Get deposit history
   * @param coin Coin symbol (e.g., USDT)
   * @param network Network (e.g., TRC20)
   * @param startTime Start time in milliseconds
   * @param endTime End time in milliseconds
   * @param status Deposit status (0: pending, 1: success, etc.)
   * @returns Deposit history
   */
    getDepositHistory(coin?: string, network?: string, startTime?: number, endTime?: number, status?: number): Promise<BinanceApiResponse<BinanceTransaction[]>>;
    /**
   * Verify a TRC20 transaction
   * @param walletAddress Wallet address
   * @param amount Expected amount
   * @param txHash Transaction hash (optional)
   * @param coin Coin symbol (e.g., USDT)
   * @returns Verification result
   */
    verifyTRC20Transaction(walletAddress: string, amount: number, txHash?: string, coin?: string): Promise<BinanceApiResponse<{
        verified: boolean;
        transaction?: BinanceTransaction;
    }>>;
}
//# sourceMappingURL=binance-api.service.d.ts.map