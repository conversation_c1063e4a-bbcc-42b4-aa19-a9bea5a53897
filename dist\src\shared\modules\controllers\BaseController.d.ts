/**
 * Base Controller
 *
 * This is a base controller class that provides common functionality
 * for all controllers in the application.
 */
import { Request, Response, NextFunction } from 'express';
export declare class BaseController {
    /**
     * Send a success response
     */
    protected sendSuccess(res: Response, data?: any, message?: string, statusCode?: number): Response<any, Record<string, any>>;
    /**
     * Send an error response
     */
    protected sendError(res: Response, message?: string, statusCode?: number, error?: any): Response<any, Record<string, any>>;
    /**
     * Handle async controller methods
     */
    protected asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<any>): (req: Request, res: Response, next: NextFunction) => Promise<void>;
    /**
     * Validate request
     */
    protected validateRequest(req: Request, schema: any): any;
}
//# sourceMappingURL=BaseController.d.ts.map