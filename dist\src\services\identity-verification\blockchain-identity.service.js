"use strict";
// jscpd:ignore-file
/**
 * Blockchain Identity Verification Service
 *
 * This service provides methods for verifying user identity using blockchain technology.
 * It supports multiple blockchain networks and verification methods.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockchainIdentityService = void 0;
const client_1 = require("@prisma/client");
const ethers_1 = require("ethers");
const axios_1 = __importDefault(require("axios"));
const crypto = __importStar(require("crypto"));
const logger_1 = require("../../utils/logger");
// Initialize Prisma client
const prisma = new client_1.PrismaClient();
class BlockchainIdentityService {
    constructor() {
        this.providers = new Map();
        this.initializeProviders();
    }
    /**
   * Get singleton instance of BlockchainIdentityService
   */
    static getInstance() {
        if (!BlockchainIdentityService.instance) {
            BlockchainIdentityService.instance = new BlockchainIdentityService();
        }
        return BlockchainIdentityService.instance;
    }
    /**
   * Initialize blockchain providers for different networks
   */
    initializeProviders() {
        try {
            // Ethereum Mainnet
            this.providers.set("ethereum", new ethers_1.ethers.providers.JsonRpcProvider(process.env.ETHEREUM_RPC_URL || "https://mainnet.infura.io/v3/your-infura-key"));
            // Binance Smart Chain
            this.providers.set("bsc", new ethers_1.ethers.providers.JsonRpcProvider(process.env.BSC_RPC_URL || "https://bsc-dataseed.binance.org/"));
            // Polygon (Matic)
            this.providers.set("polygon", new ethers_1.ethers.providers.JsonRpcProvider(process.env.POLYGON_RPC_URL || "https://polygon-rpc.com"));
            // Tron
            // Note: Tron uses a different API structure, so we'll handle it separately in the verification methods
            logger_1.logger.info("Blockchain providers initialized successfully");
        }
        catch (error) {
            logger_1.logger.error("Failed to initialize blockchain providers:", error);
        }
    }
    /**
   * Verify a wallet address ownership through signature
   * @param address Wallet address to verify
   * @param signature Signature provided by the user
   * @param message Message that was signed
   * @param network Blockchain network (ethereum, bsc, polygon, tron)
   */
    async verifyWalletOwnership(address, signature, message, network) {
        try {
            if (network === "tron") {
                return this.verifyTronSignature(address, signature, message);
            }
            const provider = this.providers.get(network);
            if (!provider) {
                throw new Error(`Unsupported network: ${network}`);
            }
            const recoveredAddress = ethers_1.ethers.utils.verifyMessage(message, signature);
            return recoveredAddress.toLowerCase() === address.toLowerCase();
        }
        catch (error) {
            logger_1.logger.error("Error verifying wallet ownership:", error);
            return false;
        }
    }
    /**
   * Verify Tron signature
   * @param address Tron wallet address
   * @param signature Signature provided by the user
   * @param message Message that was signed
   */
    async verifyTronSignature(address, signature, message) {
        try {
            // For Tron, we need to use their API
            const response = await axios_1.default.post("https://api.trongrid.io/wallet/validateaddress", {
                address,
                signature,
                message
            });
            return response.data.result;
        }
        catch (error) {
            logger_1.logger.error("Error verifying Tron signature:", error);
            return false;
        }
    }
    /**
   * Generate a challenge message for the user to sign
   * @param userId User ID
   * @param timestamp Current timestamp
   */
    generateChallengeMessage(userId, timestamp) {
        return `Verify your identity for AmazingPay: ${userId}-${timestamp}`;
    }
    /**
   * Create a verification request in the database
   * @param userId User ID
   * @param walletAddress Wallet address
   * @param network Blockchain network
   */
    async createVerificationRequest(userId, walletAddress, network) {
        try {
            const timestamp = Date.now();
            const message = this.generateChallengeMessage(userId, timestamp);
            const nonce = crypto.randomBytes(16).toString("hex");
            const verificationRequest = await prisma.identityVerification.create({
                data: {
                    userId,
                    walletAddress,
                    network,
                    status: "PENDING",
                    challengeMessage: message,
                    nonce,
                    expiresAt: new Date(timestamp + 3600000) // 1 hour expiration
                }
            });
            return {
                requestId: verificationRequest.id,
                message,
                nonce,
                expiresAt: verificationRequest.expiresAt
            };
        }
        catch (error) {
            logger_1.logger.error("Error creating verification request:", error);
            throw error;
        }
    }
    /**
   * Complete a verification request
   * @param requestId Verification request ID
   * @param signature Signature provided by the user
   */
    async completeVerification(requestId, signature) {
        try {
            const verificationRequest = await prisma.identityVerification.findUnique({
                where: { id: requestId }
            });
            if (!verificationRequest) {
                throw new Error("Verification request not found");
            }
            if (verificationRequest.status !== "PENDING") {
                throw new Error("Verification request is not pending");
            }
            if (verificationRequest.expiresAt < new Date()) {
                await prisma.identityVerification.update({
                    where: { id: requestId },
                    data: { status: "EXPIRED" }
                });
                throw new Error("Verification request has expired");
            }
            const isValid = await this.verifyWalletOwnership(verificationRequest.walletAddress, signature, verificationRequest.challengeMessage, verificationRequest.network);
            if (isValid) {
                await prisma.identityVerification.update({
                    where: { id: requestId },
                    data: { status: "VERIFIED", verifiedAt: new Date() }
                });
                // Update user's verification status
                await prisma.user.update({
                    where: { id: verificationRequest.id //, Fixed: using id instead of userId },
                        , //, Fixed: using id instead of userId },
                        data: { isVerified: true }
                    }
                });
                return true;
            }
            else {
                await prisma.identityVerification.update({
                    where: { id: requestId },
                    data: { status: "FAILED" }
                });
                return false;
            }
        }
        catch (error) {
            logger_1.logger.error("Error completing verification:", error);
            return false;
        }
    }
    /**
   * Get verification status for a user
   * @param userId User ID
   */
    async getVerificationStatus(userId) {
        try {
            const verifications = await prisma.identityVerification.findMany({
                where: { userId },
                orderBy: { createdAt: "desc" }
            });
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: { isVerified: true }
            });
            return {
                isVerified: user?.isVerified || false,
                verifications
            };
        }
        catch (error) {
            logger_1.logger.error("Error getting verification status:", error);
            throw error;
        }
    }
}
exports.BlockchainIdentityService = BlockchainIdentityService;
//# sourceMappingURL=blockchain-identity.service.js.map