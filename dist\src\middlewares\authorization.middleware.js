"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireMerchantAccess = exports.requireMerchant = exports.requireAdmin = exports.requireRole = exports.requireAuth = void 0;
const appError_1 = require("../utils/appError");
/**
 * Middleware to check if user is authenticated
 */
const requireAuth = (req, res, next) => {
    if (!req.user) {
        return next(new appError_1.AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
    }
    next();
};
exports.requireAuth = requireAuth;
/**
 * Middleware to check if user has required role
 * @param roles Allowed roles
 */
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user || !req.user.role) {
            return next(new appError_1.AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            }));
        }
        if (!roles.includes(req.user.role)) {
            return next(new appError_1.AppError({
                message: "Forbidden",
                type: ErrorType.AUTHORIZATION,
                code: ErrorCode.FORBIDDEN
            }));
        }
        next();
    };
};
exports.requireRole = requireRole;
/**
 * Middleware to check if user is admin
 */
const requireAdmin = (req, res, next) => {
    if (!req.user || req.user.role !== "ADMIN") {
        return next(new appError_1.AppError({
            message: "Forbidden",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
    }
    next();
};
exports.requireAdmin = requireAdmin;
/**
 * Middleware to check if user is merchant
 */
const requireMerchant = (req, res, next) => {
    if (!req.user || req.user.role !== "MERCHANT") {
        return next(new appError_1.AppError({
            message: "Forbidden",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
    }
    next();
};
exports.requireMerchant = requireMerchant;
/**
 * Middleware to check if user has access to merchant data
 * @param paramName Parameter name containing merchant ID (default: "merchantId")
 */
const requireMerchantAccess = (paramName = "merchantId") => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new appError_1.AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            }));
        }
        const merchantId = req.params[paramName] || req.query[paramName];
        if (!merchantId) {
            return next(new appError_1.AppError(`${paramName} is required`, 400));
        }
        // Admins can access any merchant data
        if (req.user.role === "ADMIN") {
            return next();
        }
        // Merchants can only access their own data
        if (req.user.role === "MERCHANT" && req.user.merchantId === merchantId) {
            return next();
        }
        return next(new appError_1.AppError({
            message: "Forbidden",
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
    };
};
exports.requireMerchantAccess = requireMerchantAccess;
//# sourceMappingURL=authorization.middleware.js.map