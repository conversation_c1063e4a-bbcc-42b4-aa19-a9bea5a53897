"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Jest setup file
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables for testing
dotenv_1.default.config();
// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
// Set timeout for all tests
jest.setTimeout(30000);
// Global beforeAll hook
beforeAll(async () => {
    console.log('Starting test suite');
});
// Global afterAll hook
afterAll(async () => {
    console.log('Test suite completed');
});
// Mock console.error to avoid cluttering test output
const originalConsoleError = console.error;
console.error = (...args) => {
    // Check if this is a test-related error that we want to see
    if (args[0] &&
        typeof args[0] === 'string' &&
        (args[0].includes('FAIL') || args[0].includes('ERROR'))) {
        originalConsoleError(...args);
    }
    // Otherwise suppress the error in test output
};
// Restore console.error after tests
afterAll(() => {
    console.error = originalConsoleError;
});
//# sourceMappingURL=setup.js.map