"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const paymentVerificationController_1 = require("../controllers/paymentVerificationController");
const client_1 = require("@prisma/client");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const router = (0, express_1.Router)();
const prisma = new client_1.PrismaClient();
const paymentVerificationController = new paymentVerificationController_1.PaymentVerificationController(prisma);
/**
 * @route POST /api/verify
 * @desc Verify a payment
 * @access Private
 */
router.post("/", authMiddleware_1.authenticateJWT, paymentVerificationController.verifyPayment.bind(paymentVerificationController));
/**
 * @route GET /api/verify/:id
 * @desc Get a transaction by ID
 * @access Private
 */
router.get("/:id", authMiddleware_1.authenticateJWT, paymentVerificationController.getTransaction.bind(paymentVerificationController));
/**
 * @route PUT /api/verify/:id/status
 * @desc Update a transaction status
 * @access Private
 */
router.put("/:id/status", authMiddleware_1.authenticateJWT, paymentVerificationController.updateTransactionStatus.bind(paymentVerificationController));
exports.default = router;
//# sourceMappingURL=paymentVerificationRoutes.js.map