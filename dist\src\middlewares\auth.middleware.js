"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = exports.authenticateJWT = exports.isResourceOwner = exports.authorize = exports.isMerchantOrAdmin = exports.isMerchant = exports.isAdmin = exports.authenticate = exports.requestIdMiddleware = void 0;
const jwt_utils_1 = require("../utils/jwt.utils");
const logger_1 = require("../lib/logger");
const AppError_1 = require("../utils/errors/AppError");
/**
 * Generate a unique request ID
 * @returns Unique request ID
 */
const generateRequestId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
};
/**
 * Request ID middleware
 * Adds a unique request ID to each request for tracking
 */
const requestIdMiddleware = (req, res, next) => {
    const requestId = generateRequestId();
    req.requestId = requestId;
    res.setHeader('X-Request-ID', requestId);
    next();
};
exports.requestIdMiddleware = requestIdMiddleware;
/**
 * Authentication middleware
 */
const authenticate = (req, res, next) => {
    try {
        // Get token from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new AppError_1.AppError('No token provided. Please log in.', 401, true);
        }
        // Extract the token
        const token = authHeader.split(' ')[1];
        if (!token) {
            throw new AppError_1.AppError('Invalid token format. Please log in again.', 401, true);
        }
        // Verify the token
        const decoded = (0, jwt_utils_1.verifyToken)(token);
        // Set the user in the request object
        req.user = decoded;
        logger_1.logger.debug(`User ${decoded.id} (${decoded.role}) authenticated successfully`, {
            // Fixed: using id instead of userId
            userId: decoded.id, // Fixed: using id instead of userId
            role: decoded.role,
            requestId: req.requestId,
        });
        next();
    }
    catch (error) {
        // If the error is already an AppError, pass it to the next middleware
        if (error instanceof AppError_1.AppError) {
            next(error);
        }
        else {
            // Otherwise, create a new AppError
            logger_1.logger.error('Authentication error:', error);
            next(new AppError_1.AppError('Invalid or expired token. Please log in again.', 401, true));
        }
    }
};
exports.authenticate = authenticate;
/**
 * Admin role middleware
 */
const isAdmin = (req, res, next) => {
    if (!req.user) {
        return next(new AppError_1.AppError('Authentication required. Please log in.', 401, true));
    }
    if (req.user.role !== 'ADMIN') {
        logger_1.logger.warn(`Unauthorized admin access attempt by user ${req.user.id}`, {
            // Fixed: using id instead of userId
            userId: req.user.id, // Fixed: using id instead of userId
            role: req.user.role,
            requestId: req.requestId,
            path: req.path,
            method: req.method,
        });
        return next(new AppError_1.AppError('Admin access required.', 403, true));
    }
    next();
};
exports.isAdmin = isAdmin;
/**
 * Merchant role middleware
 */
const isMerchant = (req, res, next) => {
    if (!req.user) {
        return next(new AppError_1.AppError('Authentication required. Please log in.', 401, true));
    }
    if (req.user.role !== 'MERCHANT') {
        logger_1.logger.warn(`Unauthorized merchant access attempt by user ${req.user.id}`, {
            // Fixed: using id instead of userId
            userId: req.user.id, // Fixed: using id instead of userId
            role: req.user.role,
            requestId: req.requestId,
            path: req.path,
            method: req.method,
        });
        return next(new AppError_1.AppError('Merchant access required.', 403, true));
    }
    next();
};
exports.isMerchant = isMerchant;
/**
 * Merchant or Admin role middleware
 */
const isMerchantOrAdmin = (req, res, next) => {
    if (!req.user) {
        return next(new AppError_1.AppError('Authentication required. Please log in.', 401, true));
    }
    if (req.user.role !== 'MERCHANT' &&
        req.user.role !== 'ADMIN' &&
        req.user.role !== 'SUPER_ADMIN') {
        logger_1.logger.warn(`Unauthorized merchant/admin access attempt by user ${req.user.id}`, {
            // Fixed: using id instead of userId
            userId: req.user.id, // Fixed: using id instead of userId
            role: req.user.role,
            requestId: req.requestId,
            path: req.path,
            method: req.method,
        });
        return next(new AppError_1.AppError('Merchant or admin access required.', 403, true));
    }
    next();
};
exports.isMerchantOrAdmin = isMerchantOrAdmin;
/**
 * Authorization middleware
 * @param roles Allowed roles
 */
const authorize = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new AppError_1.AppError('Authentication required. Please log in.', 401, true));
        }
        if (!roles.includes(req.user.role)) {
            logger_1.logger.warn(`Unauthorized role access attempt by user ${req.user.id}`, {
                // Fixed: using id instead of userId
                userId: req.user.id, // Fixed: using id instead of userId
                role: req.user.role,
                requestId: req.requestId,
                path: req.path,
                method: req.method,
                requiredRoles: roles,
            });
            return next(new AppError_1.AppError('You do not have permission to perform this action.', 403, true));
        }
        next();
    };
};
exports.authorize = authorize;
/**
 * Resource owner middleware
 * Checks if the user is the owner of the resource
 * @param getResourceOwnerId Function to get the resource owner ID from the request
 */
const isResourceOwner = (getResourceOwnerId) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new AppError_1.AppError('Authentication required. Please log in.', 401, true));
            }
            const ownerId = await getResourceOwnerId(req);
            if (req.user.id !== ownerId && req.user.role !== 'ADMIN' && req.user.role !== 'SUPER_ADMIN') {
                // Fixed: using id instead of userId
                logger_1.logger.warn(`Unauthorized resource access attempt by user ${req.user.id}`, {
                    // Fixed: using id instead of userId
                    userId: req.user.id, // Fixed: using id instead of userId
                    role: req.user.role,
                    resourceOwnerId: ownerId,
                    requestId: req.requestId,
                    path: req.path,
                    method: req.method,
                });
                return next(new AppError_1.AppError('You do not have permission to access this resource.', 403, true));
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.isResourceOwner = isResourceOwner;
// For backward compatibility
exports.authenticateJWT = exports.authenticate;
// Export middleware as a group for convenience
exports.authMiddleware = {
    authenticate: exports.authenticate,
    isAdmin: exports.isAdmin,
    isMerchant: exports.isMerchant,
    isMerchantOrAdmin: exports.isMerchantOrAdmin,
    authorize: exports.authorize,
    isResourceOwner: exports.isResourceOwner,
    requestIdMiddleware: exports.requestIdMiddleware,
};
