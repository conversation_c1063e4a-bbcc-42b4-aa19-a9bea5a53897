// jscpd:ignore-file
/**
 * Verification Monitoring Controller
 *
 * This controller provides endpoints for monitoring the verification system.
 */

import { Request, Response } from 'express';
import { BaseController } from '../base/BaseController';
import prisma from '../../lib/prisma';
import { logger } from '../../utils/logger';

/**
 * Verification monitoring controller
 */
export class VerificationMonitoringController extends BaseController {
  /**
   * Constructor
   */
  constructor() {
    super();
  }

  /**
   * Get verification metrics
   * @param req Request
   * @param res Response
   */
  public async getVerificationMetrics(req: Request, res: Response) {
    try {
      const { startDate, endDate, period = 'day' } = req.query;

      // Parse dates
      const parsedStartDate: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const parsedEndDate: any = endDate ? new Date(endDate as string) : new Date();

      // Validate dates
      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format',
        });
      }

      // Get metrics from database
      const metrics: any = await prisma.verificationMetrics.findMany({
        where: { timestamp: {
            gte: parsedStartDate,
            lte: parsedEndDate,
          },
        },
        orderBy: { timestamp: 'asc',
        },
      });

      // Aggregate metrics
      const aggregatedMetrics: any = this.aggregateMetrics(metrics);
      const errorDistribution: any = this.aggregateErrorDistribution(metrics);

      // Return metrics
      return res.status(200).json({
        success: true,
        data: { metrics: aggregatedMetrics,
          errorDistribution,
          period,
          startDate: parsedStartDate,
          endDate: parsedEndDate,
        },
      });
    } catch (error) {
      logger.error('Error getting verification metrics', {
        error: (error as Error).message || error,
      });

      return res.status(500).json({
        success: false,
        message: 'Error getting verification metrics',
        error: (error as Error).message || 'Unknown error',
      });
    }
  }

  /**
   * Get verification method metrics
   * @param req Request
   * @param res Response
   */
  public async getVerificationMethodMetrics(req: Request, res: Response) {
    try {
      const { startDate, endDate, period = 'day' } = req.query;

      // Parse dates
      const parsedStartDate: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const parsedEndDate: any = endDate ? new Date(endDate as string) : new Date();

      // Validate dates
      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format',
        });
      }

      // Get metrics from database
      const metrics: any = await prisma.verificationMetrics.findMany({
        where: { timestamp: {
            gte: parsedStartDate,
            lte: parsedEndDate,
          },
        },
        orderBy: { timestamp: 'asc',
        },
      });

      // Aggregate method distribution
      const methodDistribution: any = this.aggregateMethodDistribution(metrics);

      // Return method distribution
      return res.status(200).json({
        success: true,
        data: {
          methodDistribution,
          period,
          startDate: parsedStartDate,
          endDate: parsedEndDate,
        },
      });
    } catch (error) {
      logger.error('Error getting verification method metrics', {
        error: (error as Error).message || error,
      });

      return res.status(500).json({
        success: false,
        message: 'Error getting verification method metrics',
        error: (error as Error).message || 'Unknown error',
      });
    }
  }

  /**
   * Aggregate metrics
   * @param metrics Metrics
   * @returns Aggregated metrics
   */
  private aggregateMetrics(metrics: any[]): Record<string, number> {
    if (metrics.length === 0) {
      return {
        attempts: 0,
        successes: 0,
        failures: 0,
        successRate: 0,
        avgLatency: 0,
      };
    }

    const totalAttempts: any = metrics.reduce((sum, metric) => sum + metric.attempts, 0);
    const totalSuccesses: any = metrics.reduce((sum, metric) => sum + metric.successes, 0);
    const totalFailures: any = metrics.reduce((sum, metric) => sum + metric.failures, 0);
    const totalLatency: any = metrics.reduce((sum, metric) => sum + (metric.avgLatency * metric.attempts), 0);


    const successRate: any = totalAttempts > 0 ? (totalSuccesses / totalAttempts) * 100 : 0;
    const avgLatency = totalAttempts > 0 ? totalLatency / totalAttempts : 0;

    return {
      attempts: totalAttempts,
      successes: totalSuccesses,
      failures: totalFailures,
      successRate,
      avgLatency,
    };
  }

  /**
   * Aggregate error distribution
   * @param metrics Metrics
   * @returns Aggregated error distribution
   */
  private aggregateErrorDistribution(metrics: any[]): Record<string, number> {
    const errorDistribution: Record<string, number> = {};

    metrics.forEach((metric)) => {
      try {
        const distribution: any = JSON.parse(metric.errorDistribution || '{}');

        Object.entries(distribution).forEach((([errorType, count])) => {
          if (typeof count === 'number') {
            errorDistribution[errorType] = (errorDistribution[errorType] || 0) + count;
          }
        });
      } catch (error) {
        logger.error('Error parsing error distribution', {
          metricId: metric.id,
          errorDistribution: metric.errorDistribution,
          error: (error as Error).message || error,
        });
      }
    });

    return errorDistribution;
  }

  /**
   * Aggregate method distribution
   * @param metrics Metrics
   * @returns Aggregated method distribution
   */
  private aggregateMethodDistribution(metrics: any[]): Record<string, any> {
    const methodDistribution: Record<string, any> = {};

    metrics.forEach((metric)) => {
      try {
        const distribution: any = JSON.parse(metric.methodDistribution || '{}');

        Object.entries(distribution).forEach((([method, counts])) => {
          if (!methodDistribution[method]) {
            methodDistribution[method] = {
              attempts: 0,
              successes: 0,
              failures: 0,
              successRate: 0,
            };
          }

          const methodCounts: any = counts as Record<string, number>;

          methodDistribution[method].attempts += methodCounts.ATTEMPT || 0;
          methodDistribution[method].successes += methodCounts.SUCCESS || 0;
          methodDistribution[method].failures += methodCounts.FAILURE || 0;
        });
      } catch (error) {
        logger.error('Error parsing method distribution', {
          metricId: metric.id,
          methodDistribution: metric.methodDistribution,
          error: (error as Error).message || error,
        });
      }
    });

    // Calculate success rates
    Object.values(methodDistribution).forEach(((method)) => {
      method.successRate = method.attempts > 0 ? (method.successes / method.attempts) * 100 : 0;
    });

    return methodDistribution;
  }
}
