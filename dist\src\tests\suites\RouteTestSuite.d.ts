import { Application } from "express";
/**
 * Route test suite
 * This class provides a suite of tests for routes
 */
export declare class RouteTestSuite {
    private app;
    private routeTestHelper;
    private routeRegistry;
    private routeVersionManager;
    private routeMonitor;
    private routeHealthChecker;
    /**
     * Create a new route test suite
     * @param app Express application
     */
    constructor(app: Application);
    /**
     * Run all tests
     */
    runAllTests(): Promise<void>;
    /**
     * Test route registry
     */
    private testRouteRegistry;
    /**
     * Test route version manager
     */
    private testRouteVersionManager;
    /**
     * Test route monitor
     */
    private testRouteMonitor;
    /**
     * Test route health checker
     */
    private testRouteHealthChecker;
    /**
     * Test explorer routes
     */
    private testExplorerRoutes;
    /**
     * Test health check routes
     */
    private testHealthCheckRoutes;
}
export default RouteTestSuite;
//# sourceMappingURL=RouteTestSuite.d.ts.map