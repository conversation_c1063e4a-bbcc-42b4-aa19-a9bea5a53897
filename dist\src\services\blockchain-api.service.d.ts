import { BaseService } from "../base/BaseService";
/**
 * Blockchain network types
 */
export declare enum BlockchainNetwork {
    TRC20 = "trc20",
    ERC20 = "erc20",
    BEP20 = "bep20",
    POLYGON = "polygon"
}
/**
 * Blockchain transaction status
 */
export declare enum BlockchainTransactionStatus {
    PENDING = "pending",
    CONFIRMED = "confirmed",
    FAILED = "failed",
    NOT_FOUND = "not_found"
}
/**
 * Blockchain transaction
 */
export interface BlockchainTransaction {
    hash: string;
    from: string;
    to: string;
    value: string;
    valueInUSD?: number;
    timestamp: number;
    blockNumber?: number;
    confirmations?: number;
    status: BlockchainTransactionStatus;
    network: BlockchainNetwork;
    tokenSymbol?: string;
    tokenDecimals?: number;
    fee?: string;
    gasPrice?: string;
    gasUsed?: string;
}
/**
 * Blockchain API response
 */
export interface BlockchainApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
/**
 * Service for interacting with blockchain APIs
 */
export declare class BlockchainApiService extends BaseService {
    private networkConfigs;
    private defaultRetryConfig;
    /**
     * Create a new BlockchainApiService instance
     */
    constructor();
    /**
     * Initialize network configurations
     */
    private initializeNetworkConfigs;
    /**
     * Get network configuration
     * @param network Blockchain network
     * @returns Network configuration
     */
    private getNetworkConfig;
    /**
     * Make a request with retry capability
     * @param config Request configuration
     * @param retryConfig Retry configuration
     * @returns Response data
     */
    private makeRequestWithRetry;
    /**
     * Verify a blockchain transaction
     * @param txHash Transaction hash
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address (for ERC20/BEP20/POLYGON)
     * @returns Verification result
     */
    verifyTransaction(txHash: string, network: BlockchainNetwork, toAddress: string, amount: string, tokenAddress?: string): Promise<BlockchainApiResponse<BlockchainTransaction>>;
    /**
     * Verify a TRON transaction
     * @param txHash Transaction hash
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @returns Verification result
     */
    private verifyTronTransaction;
    /**
     * Process TRON transaction response
     * @param response API response
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @returns Processed transaction
     */
    private processTronTransaction;
    /**
     * Verify an EVM transaction (Ethereum, BSC, Polygon)
     * @param txHash Transaction hash
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address
     * @returns Verification result
     */
    private verifyEVMTransaction;
    /**
     * Process EVM transaction response
     * @param response API response
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address
     * @returns Processed transaction
     */
    private processEVMTransaction;
}
//# sourceMappingURL=blockchain-api.service.d.ts.map