"use strict";
// jscpd:ignore-file
/**
 * Error Handling Utilities
 *
 * This file contains utility functions for error handling.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleError = handleError;
const errors_1 = require("../utils/errors");
const logger_1 = require("../utils/logger");
/**
 * Handle an error
 * @param error Error to handle
 * @param resource Resource name
 * @param id Resource ID
 * @returns Formatted error
 */
function handleError(error, resource, id) {
    logger_1.logger.error(`Error in ${resource || 'unknown'}${id ? ` (${id})` : ''}:`, error);
    // If it's already an AppError, return it
    if (error instanceof errors_1.AppError) {
        return error;
    }
    // Handle database errors
}
//# sourceMappingURL=errorHandling.js.map