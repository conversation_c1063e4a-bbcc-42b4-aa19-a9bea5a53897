"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainValue = toContainValue;
const utils_1 = require("../utils");
function toContainValue(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    let pass = false;
    if (typeof actual === 'object' && actual !== null && !Array.isArray(actual)) {
        const values = Object.keys(actual).map(k => actual[k]);
        // @ts-expect-error OK to have implicit any for this.equals
        pass = (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), values, expected);
    }
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainValue') +
                '\n\n' +
                'Expected object to not contain value:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainValue') +
                '\n\n' +
                'Expected object to contain value:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
