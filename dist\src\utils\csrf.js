"use strict";
// jscpd:ignore-file
/**
 * CSRF Protection Utility
 *
 * This utility provides functions for generating and validating CSRF tokens.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractCsrfToken = exports.validateCsrfToken = exports.generateCsrfToken = void 0;
const crypto = __importStar(require("crypto"));
const logger_1 = require("../lib/logger");
// Secret key for CSRF token generation
const CSRF_SECRET = process.env.CSRF_SECRET || crypto.randomBytes(32).toString('hex');
// Token expiration time (1 hour)
const TOKEN_EXPIRATION = 60 * 60 * 1000;
/**
 * Generate a CSRF token
 * @param userId User ID to associate with the token
 * @returns CSRF token
 */
const generateCsrfToken = (userId) => {
    // Generate a random token
    const randomToken = crypto.randomBytes(32).toString('hex');
    // Generate a timestamp
    const timestamp = Date.now();
    // Generate a signature
    const signature = crypto
        .createHmac('sha256', CSRF_SECRET)
        .update(`${randomToken}:${userId}:${timestamp}`)
        .digest('hex');
    // Combine token, timestamp, and signature
    const csrfToken = `${randomToken}.${timestamp}.${signature}`;
    // Encode token
    return Buffer.from(csrfToken).toString('base64');
};
exports.generateCsrfToken = generateCsrfToken;
/**
 * Validate a CSRF token
 * @param token CSRF token to validate
 * @param userId User ID associated with the token
 * @returns Whether the token is valid
 */
const validateCsrfToken = (token, userId) => {
    try {
        // Decode token
        const decodedToken = Buffer.from(token, 'base64').toString();
        // Split token into parts
        const [randomToken, timestampStr, signature] = decodedToken.split('.');
        // Check if all parts exist
        if (!randomToken || !timestampStr || !signature) {
            logger_1.logger.warn('Invalid CSRF token format');
            return false;
        }
        // Parse timestamp
        const timestamp = parseInt(timestampStr, 10);
        // Check if timestamp is valid
        if (isNaN(timestamp)) {
            logger_1.logger.warn('Invalid CSRF token timestamp');
            return false;
        }
        // Check if token has expired
        if (Date.now() - timestamp > TOKEN_EXPIRATION) {
            logger_1.logger.warn('CSRF token expired');
            return false;
        }
        // Generate expected signature
        const expectedSignature = crypto
            .createHmac('sha256', CSRF_SECRET)
            .update(`${randomToken}:${userId}:${timestamp}`)
            .digest('hex');
        // Compare signatures
        if (signature !== expectedSignature) {
            logger_1.logger.warn('Invalid CSRF token signature');
            return false;
        }
        return true;
    }
    catch (error) {
        logger_1.logger.error('Error validating CSRF token:', error);
        return false;
    }
};
exports.validateCsrfToken = validateCsrfToken;
/**
 * Extract CSRF token from request
 * @param req Express request
 * @returns CSRF token or null if not found
 */
const extractCsrfToken = (req) => {
    // Check headers
    const headerToken = req.headers['x-csrf-token'] || req.headers['x-xsrf-token'];
    if (headerToken && typeof headerToken === 'string') {
        return headerToken;
    }
    // Check body
    if (req.body && req.body._csrf) {
        return req.body._csrf;
    }
    // Check query
    if (req.query && req.query._csrf) {
        return req.query._csrf;
    }
    return null;
};
exports.extractCsrfToken = extractCsrfToken;
exports.default = {
    generateCsrfToken: exports.generateCsrfToken,
    validateCsrfToken: exports.validateCsrfToken,
    extractCsrfToken: exports.extractCsrfToken,
};
