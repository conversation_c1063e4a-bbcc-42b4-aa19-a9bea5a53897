window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":0,"numPassedTestSuites":0,"numPassedTests":0,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":0,"startTime":1748138881168,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"runtime":0,"slow":false,"start":0},"testFilePath":"F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: testUtils is not defined\n\n    \u001b[0m \u001b[90m 49 |\u001b[39m   describe(\u001b[32m'verifyEthereumSignature'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n     \u001b[90m 50 |\u001b[39m     \u001b[36mconst\u001b[39m validVerificationData \u001b[33m=\u001b[39m {\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 51 |\u001b[39m       address\u001b[33m:\u001b[39m testUtils\u001b[33m.\u001b[39mmockEthereumAddress()\u001b[33m,\u001b[39m\n     \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 52 |\u001b[39m       message\u001b[33m:\u001b[39m \u001b[32m'Verify identity for AmazingPay'\u001b[39m\u001b[33m,\u001b[39m\n     \u001b[90m 53 |\u001b[39m       signature\u001b[33m:\u001b[39m \u001b[32m'0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c'\u001b[39m\u001b[33m,\u001b[39m\n     \u001b[90m 54 |\u001b[39m       userId\u001b[33m:\u001b[39m testUtils\u001b[33m.\u001b[39mmockUUID()\u001b[33m,\u001b[39m\u001b[0m\n\n      \u001b[2mat \u001b[22m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:51:16\u001b[22m\n      \u001b[2mat \u001b[22m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:49:11\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:16:9)\u001b[22m\n","testResults":[]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["src/**/*.{js,ts}","!src/**/*.d.ts","!src/**/*.test.ts","!src/**/__tests__/**","!src/**/index.ts","!src/types/**","!src/migrations/**"],"coverageDirectory":"F:\\Amazing pay flow\\coverage","coverageProvider":"babel","coverageReporters":["json","lcov","text","text-summary","clover","html"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":90,"statements":90},"./src/services/":{"branches":85,"functions":90,"lines":95,"statements":95},"./src/controllers/":{"branches":80,"functions":85,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":2,"noStackTrace":false,"nonFlagArgs":["src/services/identity-verification/__tests__/IdentityVerificationService.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\Amazing pay flow\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./test-reports","filename":"jest-report.html","expand":true}]],"rootDir":"F:\\Amazing pay flow","runTestsByPath":false,"seed":-1636339847,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"src\\\\services\\\\identity-verification\\\\__tests__\\\\IdentityVerificationService.test.ts","testSequencer":"F:\\Amazing pay flow\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1748138883031,"_reporterOptions":{"publicPath":"./test-reports","filename":"jest-report.html","expand":true,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})