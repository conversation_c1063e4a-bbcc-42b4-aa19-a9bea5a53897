window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":10,"numPassedTestSuites":0,"numPassedTests":6,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":16,"startTime":1748143659831,"success":false,"testResults":[{"numFailingTests":10,"numPassingTests":6,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1748143661616,"runtime":1447,"slow":false,"start":1748143660169},"testFilePath":"F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › verifyEthereumSignature › should successfully verify a valid Ethereum signature\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32mtrue\u001b[39m\n    Received: \u001b[31mfalse\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 77 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 78 |\u001b[39m       expect(result)\u001b[33m.\u001b[39mtoBeDefined()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 79 |\u001b[39m       expect(result\u001b[33m.\u001b[39msuccess)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 80 |\u001b[39m       expect(result\u001b[33m.\u001b[39mverificationId)\u001b[33m.\u001b[39mtoBe(mockVerificationResult\u001b[33m.\u001b[39mid)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 81 |\u001b[39m       expect(result\u001b[33m.\u001b[39mmethod)\u001b[33m.\u001b[39mtoBe(\u001b[32m'ETHEREUM_SIGNATURE'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 82 |\u001b[39m       expect(result\u001b[33m.\u001b[39mstatus)\u001b[33m.\u001b[39mtoBe(\u001b[32m'VERIFIED'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:79:30)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › verifyEthereumSignature › should throw error for invalid signature\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'isAddress')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 121 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 122 |\u001b[39m       \u001b[36mconst\u001b[39m { ethers } \u001b[33m=\u001b[39m require(\u001b[32m'ethers'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 123 |\u001b[39m       ethers\u001b[33m.\u001b[39mutils\u001b[33m.\u001b[39misAddress\u001b[33m.\u001b[39mmockReturnValue(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 124 |\u001b[39m       ethers\u001b[33m.\u001b[39mutils\u001b[33m.\u001b[39mverifyMessage\u001b[33m.\u001b[39mmockReturnValue(\u001b[32m'0xdifferentaddress'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 125 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 126 |\u001b[39m       \u001b[90m// Act & Assert\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:123:20)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › verifyEthereumSignature › should throw error when user not found\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'isAddress')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 137 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 138 |\u001b[39m       \u001b[36mconst\u001b[39m { ethers } \u001b[33m=\u001b[39m require(\u001b[32m'ethers'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 139 |\u001b[39m       ethers\u001b[33m.\u001b[39mutils\u001b[33m.\u001b[39misAddress\u001b[33m.\u001b[39mmockReturnValue(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 140 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 141 |\u001b[39m       \u001b[90m// Act & Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 142 |\u001b[39m       \u001b[36mawait\u001b[39m expect(service\u001b[33m.\u001b[**************************(validVerificationData))\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:139:20)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › verifyEthereumSignature › should throw error when merchant not found\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'isAddress')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 154 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 155 |\u001b[39m       \u001b[36mconst\u001b[39m { ethers } \u001b[33m=\u001b[39m require(\u001b[32m'ethers'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 156 |\u001b[39m       ethers\u001b[33m.\u001b[39mutils\u001b[33m.\u001b[39misAddress\u001b[33m.\u001b[39mmockReturnValue(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 157 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 158 |\u001b[39m       \u001b[90m// Act & Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m       \u001b[36mawait\u001b[39m expect(service\u001b[33m.\u001b[**************************(validVerificationData))\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:156:20)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › verifyEthereumSignature › should handle database errors gracefully\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'isAddress')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 174 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 175 |\u001b[39m       \u001b[36mconst\u001b[39m { ethers } \u001b[33m=\u001b[39m require(\u001b[32m'ethers'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 176 |\u001b[39m       ethers\u001b[33m.\u001b[39mutils\u001b[33m.\u001b[39misAddress\u001b[33m.\u001b[39mmockReturnValue(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 177 |\u001b[39m       ethers\u001b[33m.\u001b[39mutils\u001b[33m.\u001b[39mverifyMessage\u001b[33m.\u001b[39mmockReturnValue(validVerificationData\u001b[33m.\u001b[39maddress)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 178 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 179 |\u001b[39m       \u001b[90m// Act & Assert\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:176:20)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › getVerificationById › should return verification details for valid ID\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    \u001b[32m- Expected\u001b[39m\n    \u001b[31m+ Received\u001b[39m\n\n    \u001b[2m  Object {\u001b[22m\n    \u001b[31m+   \"include\": Object {\u001b[39m\n    \u001b[31m+     \"claims\": true,\u001b[39m\n    \u001b[31m+   },\u001b[39m\n    \u001b[2m    \"where\": Object {\u001b[22m\n    \u001b[2m      \"id\": \"verification-123-456-789\",\u001b[22m\n    \u001b[2m    },\u001b[22m\n    \u001b[2m  }\u001b[22m,\n\n    Number of calls: \u001b[31m1\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 207 |\u001b[39m       \u001b[90m// Assert\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 208 |\u001b[39m       expect(result)\u001b[33m.\u001b[39mtoEqual(mockVerification)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 209 |\u001b[39m       expect(mockPrisma\u001b[33m.\u001b[39midentityVerification\u001b[33m.\u001b[39mfindUnique)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 210 |\u001b[39m         where\u001b[33m:\u001b[39m { id\u001b[33m:\u001b[39m verificationId }\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 211 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 212 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:209:58)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › getVerificationById › should throw error for non-existent verification\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected constructor: \u001b[32mIdentityVerificationError\u001b[39m\n    Received constructor: \u001b[31mAppError\u001b[39m\n\n    Received message: \u001b[31m\"Failed to retrieve identity verification\"\u001b[39m\n\n        \u001b[0m \u001b[90m 90 |\u001b[39m \u001b[90m     */\u001b[39m\n         \u001b[90m 91 |\u001b[39m     \u001b[36mstatic\u001b[39m internalError(message\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m\"Internal error\"\u001b[39m)\u001b[33m:\u001b[39m \u001b[33mIdentityVerificationError\u001b[39m {\n        \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m         \u001b[36mreturn\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mIdentityVerificationError\u001b[39m(\n         \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n         \u001b[90m 93 |\u001b[39m             message\u001b[33m,\u001b[39m\n         \u001b[90m 94 |\u001b[39m             \u001b[33mIdentityVerificationErrorCode\u001b[39m\u001b[33m.\u001b[39m\u001b[33mINTERNAL_ERROR\u001b[39m\u001b[33m,\u001b[39m\n         \u001b[90m 95 |\u001b[39m             \u001b[35m500\u001b[39m\u001b[0m\n\n          \u001b[2mat Function.internalError (\u001b[22msrc/services/identity-verification/core/IdentityVerificationError.ts\u001b[2m:92:16)\u001b[22m\n          \u001b[2mat IdentityVerificationService.getVerificationById (\u001b[22msrc/services/identity-verification/core/IdentityVerificationService.ts\u001b[2m:88:39)\u001b[22m\n          \u001b[2mat Object.<anonymous> (\u001b[22msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[2m:220:7)\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 218 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 219 |\u001b[39m       \u001b[90m// Act & Assert\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 220 |\u001b[39m       \u001b[36mawait\u001b[39m expect(service\u001b[33m.\u001b[39mgetVerificationById(verificationId))\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 221 |\u001b[39m         \u001b[33mIdentityVerificationError\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 222 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 223 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toThrow (\u001b[22m\u001b[2mnode_modules/expect/build/index.js\u001b[2m:218:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:220:73)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › getVerificationStats › should return verification statistics\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'mockResolvedValue')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 290 |\u001b[39m         \u001b[33m.\u001b[39mmockResolvedValueOnce(\u001b[35m10\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// pending\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 291 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 292 |\u001b[39m       mockPrisma\u001b[33m.\u001b[39midentityVerification\u001b[33m.\u001b[39mgroupBy\u001b[33m.\u001b[39mmockResolvedValue([\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 293 |\u001b[39m         { method\u001b[33m:\u001b[39m \u001b[32m'ETHEREUM_SIGNATURE'\u001b[39m\u001b[33m,\u001b[39m _count\u001b[33m:\u001b[39m { method\u001b[33m:\u001b[39m \u001b[35m50\u001b[39m } }\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 294 |\u001b[39m         { method\u001b[33m:\u001b[39m \u001b[32m'ERC1484'\u001b[39m\u001b[33m,\u001b[39m _count\u001b[33m:\u001b[39m { method\u001b[33m:\u001b[39m \u001b[35m30\u001b[39m } }\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 295 |\u001b[39m       ])\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:292:47)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › getVerificationStats › should handle zero verifications\u001b[39m\u001b[22m\n\n    TypeError: Cannot read properties of undefined (reading 'mockResolvedValue')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 319 |\u001b[39m       \u001b[90m// Arrange\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 320 |\u001b[39m       mockPrisma\u001b[33m.\u001b[39midentityVerification\u001b[33m.\u001b[39mcount\u001b[33m.\u001b[39mmockResolvedValue(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 321 |\u001b[39m       mockPrisma\u001b[33m.\u001b[39midentityVerification\u001b[33m.\u001b[39mgroupBy\u001b[33m.\u001b[39mmockResolvedValue([])\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 322 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 323 |\u001b[39m       \u001b[90m// Act\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 324 |\u001b[39m       \u001b[36mconst\u001b[39m result \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m service\u001b[33m.\u001b[39mgetVerificationStats()\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:321:47)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mIdentityVerificationService › Error Handling › should handle network errors gracefully\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\n    Expected substring: \u001b[32m\"Network error\"\u001b[39m\n    Received string:    \u001b[31m\"Invalid Ethereum address format\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 346 |\u001b[39m       \u001b[36mconst\u001b[39m result \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m service\u001b[33m.\u001b[**************************(testData)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 347 |\u001b[39m       expect(result\u001b[33m.\u001b[39msuccess)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 348 |\u001b[39m       expect(result\u001b[33m.\u001b[39merror)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Network error'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 349 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 350 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 351 |\u001b[39m     it(\u001b[32m'should handle timeout errors'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[39m\u001b[0m\u001b[2m:348:28)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["IdentityVerificationService","verifyEthereumSignature"],"duration":36,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:79:30)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"],"fullName":"IdentityVerificationService verifyEthereumSignature should successfully verify a valid Ethereum signature","status":"failed","title":"should successfully verify a valid Ethereum signature"},{"ancestorTitles":["IdentityVerificationService","verifyEthereumSignature"],"duration":4,"failureMessages":[],"fullName":"IdentityVerificationService verifyEthereumSignature should return error for invalid Ethereum address","status":"passed","title":"should return error for invalid Ethereum address"},{"ancestorTitles":["IdentityVerificationService","verifyEthereumSignature"],"duration":0,"failureMessages":["TypeError: Cannot read properties of undefined (reading 'isAddress')\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:123:20)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)"],"fullName":"IdentityVerificationService verifyEthereumSignature should throw error for invalid signature","status":"failed","title":"should throw error for invalid signature"},{"ancestorTitles":["IdentityVerificationService","verifyEthereumSignature"],"duration":1,"failureMessages":["TypeError: Cannot read properties of undefined (reading 'isAddress')\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:139:20)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)"],"fullName":"IdentityVerificationService verifyEthereumSignature should throw error when user not found","status":"failed","title":"should throw error when user not found"},{"ancestorTitles":["IdentityVerificationService","verifyEthereumSignature"],"duration":1,"failureMessages":["TypeError: Cannot read properties of undefined (reading 'isAddress')\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:156:20)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)"],"fullName":"IdentityVerificationService verifyEthereumSignature should throw error when merchant not found","status":"failed","title":"should throw error when merchant not found"},{"ancestorTitles":["IdentityVerificationService","verifyEthereumSignature"],"duration":0,"failureMessages":["TypeError: Cannot read properties of undefined (reading 'isAddress')\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:176:20)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)"],"fullName":"IdentityVerificationService verifyEthereumSignature should handle database errors gracefully","status":"failed","title":"should handle database errors gracefully"},{"ancestorTitles":["IdentityVerificationService","getVerificationById"],"duration":4,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[31m+   \"include\": Object {\u001b[39m\n\u001b[31m+     \"claims\": true,\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[2m    \"where\": Object {\u001b[22m\n\u001b[2m      \"id\": \"verification-123-456-789\",\u001b[22m\n\u001b[2m    },\u001b[22m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:209:58)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"],"fullName":"IdentityVerificationService getVerificationById should return verification details for valid ID","status":"failed","title":"should return verification details for valid ID"},{"ancestorTitles":["IdentityVerificationService","getVerificationById"],"duration":23,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected constructor: \u001b[32mIdentityVerificationError\u001b[39m\nReceived constructor: \u001b[31mAppError\u001b[39m\n\nReceived message: \u001b[31m\"Failed to retrieve identity verification\"\u001b[39m\n\n    \u001b[0m \u001b[90m 90 |\u001b[39m \u001b[90m     */\u001b[39m\n     \u001b[90m 91 |\u001b[39m     \u001b[36mstatic\u001b[39m internalError(message\u001b[33m:\u001b[39m string \u001b[33m=\u001b[39m \u001b[32m\"Internal error\"\u001b[39m)\u001b[33m:\u001b[39m \u001b[33mIdentityVerificationError\u001b[39m {\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m         \u001b[36mreturn\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mIdentityVerificationError\u001b[39m(\n     \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 93 |\u001b[39m             message\u001b[33m,\u001b[39m\n     \u001b[90m 94 |\u001b[39m             \u001b[33mIdentityVerificationErrorCode\u001b[39m\u001b[33m.\u001b[39m\u001b[33mINTERNAL_ERROR\u001b[39m\u001b[33m,\u001b[39m\n     \u001b[90m 95 |\u001b[39m             \u001b[35m500\u001b[39m\u001b[0m\n\n      \u001b[2mat Function.internalError (\u001b[22msrc/services/identity-verification/core/IdentityVerificationError.ts\u001b[2m:92:16)\u001b[22m\n      \u001b[2mat IdentityVerificationService.getVerificationById (\u001b[22msrc/services/identity-verification/core/IdentityVerificationService.ts\u001b[2m:88:39)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22msrc/services/identity-verification/__tests__/IdentityVerificationService.test.ts\u001b[2m:220:7)\u001b[22m\n    at Object.toThrow (F:\\Amazing pay flow\\node_modules\\expect\\build\\index.js:218:22)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:220:73)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)"],"fullName":"IdentityVerificationService getVerificationById should throw error for non-existent verification","status":"failed","title":"should throw error for non-existent verification"},{"ancestorTitles":["IdentityVerificationService","getVerificationsForUser"],"duration":2,"failureMessages":[],"fullName":"IdentityVerificationService getVerificationsForUser should return user verifications","status":"passed","title":"should return user verifications"},{"ancestorTitles":["IdentityVerificationService","getVerificationsForUser"],"duration":1,"failureMessages":[],"fullName":"IdentityVerificationService getVerificationsForUser should handle empty results","status":"passed","title":"should handle empty results"},{"ancestorTitles":["IdentityVerificationService","getVerificationStats"],"duration":1,"failureMessages":["TypeError: Cannot read properties of undefined (reading 'mockResolvedValue')\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:292:47)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)"],"fullName":"IdentityVerificationService getVerificationStats should return verification statistics","status":"failed","title":"should return verification statistics"},{"ancestorTitles":["IdentityVerificationService","getVerificationStats"],"duration":0,"failureMessages":["TypeError: Cannot read properties of undefined (reading 'mockResolvedValue')\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:321:47)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)"],"fullName":"IdentityVerificationService getVerificationStats should handle zero verifications","status":"failed","title":"should handle zero verifications"},{"ancestorTitles":["IdentityVerificationService","Error Handling"],"duration":2,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Network error\"\u001b[39m\nReceived string:    \u001b[31m\"Invalid Ethereum address format\"\u001b[39m\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:348:28)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"],"fullName":"IdentityVerificationService Error Handling should handle network errors gracefully","status":"failed","title":"should handle network errors gracefully"},{"ancestorTitles":["IdentityVerificationService","Error Handling"],"duration":5,"failureMessages":[],"fullName":"IdentityVerificationService Error Handling should handle timeout errors","status":"passed","title":"should handle timeout errors"},{"ancestorTitles":["IdentityVerificationService","Input Validation"],"duration":6,"failureMessages":[],"fullName":"IdentityVerificationService Input Validation should validate required fields","status":"passed","title":"should validate required fields"},{"ancestorTitles":["IdentityVerificationService","Input Validation"],"duration":3,"failureMessages":[],"fullName":"IdentityVerificationService Input Validation should validate address format","status":"passed","title":"should validate address format"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["src/**/*.{js,ts}","!src/**/*.d.ts","!src/**/*.test.ts","!src/**/__tests__/**","!src/**/index.ts","!src/types/**","!src/migrations/**"],"coverageDirectory":"F:\\Amazing pay flow\\coverage","coverageProvider":"babel","coverageReporters":["json","lcov","text","text-summary","clover","html"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":90,"statements":90},"./src/services/":{"branches":85,"functions":90,"lines":95,"statements":95},"./src/controllers/":{"branches":80,"functions":85,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":2,"noStackTrace":false,"nonFlagArgs":["src/services/identity-verification/__tests__/IdentityVerificationService.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\Amazing pay flow\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./test-reports","filename":"jest-report.html","expand":true}]],"rootDir":"F:\\Amazing pay flow","runTestsByPath":false,"seed":1947740850,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"src\\\\services\\\\identity-verification\\\\__tests__\\\\IdentityVerificationService.test.ts","testSequencer":"F:\\Amazing pay flow\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1748143661637,"_reporterOptions":{"publicPath":"./test-reports","filename":"jest-report.html","expand":true,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})