window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":0,"numPassedTestSuites":0,"numPassedTests":0,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":0,"startTime":1748138633648,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"runtime":0,"slow":false,"start":0},"testFilePath":"F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[1m\u001b[31mJ<PERSON> encountered an unexpected token\u001b[39m\u001b[22m\n\n    Jest failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when Jest is not configured to support such syntax.\n\n    Out of the box Jest supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see \u001b[4mhttps://jestjs.io/docs/ecmascript-modules\u001b[24m for how to enable it.\n     • If you are trying to use TypeScript, see \u001b[4mhttps://jestjs.io/docs/getting-started#using-typescript\u001b[24m\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \u001b[1m\"transformIgnorePatterns\"\u001b[22m in your config.\n     • If you need a custom transformation specify a \u001b[1m\"transform\"\u001b[22m option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \u001b[1m\"moduleNameMapper\"\u001b[22m config option.\n\n    You'll find more details and examples of these config options in the docs:\n    \u001b[36mhttps://jestjs.io/docs/configuration\u001b[39m\n    For information about custom transformations, see:\n    \u001b[36mhttps://jestjs.io/docs/code-transformation\u001b[39m\n\n    \u001b[1m\u001b[31mDetails:\u001b[39m\u001b[22m\n\n    SyntaxError: F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts: Missing semicolon. (17:13)\n\n    \u001b[0m \u001b[90m 15 |\u001b[39m\n     \u001b[90m 16 |\u001b[39m describe(\u001b[32m'IdentityVerificationService'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m   \u001b[36mlet\u001b[39m service\u001b[33m:\u001b[39m \u001b[33mIdentityVerificationService\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 18 |\u001b[39m   \u001b[36mlet\u001b[39m mockPrisma\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39m\u001b[33mMocked\u001b[39m\u001b[33m<\u001b[39m\u001b[33mPrismaClient\u001b[39m\u001b[33m>\u001b[39m\u001b[33m;\u001b[39m\n     \u001b[90m 19 |\u001b[39m\n     \u001b[90m 20 |\u001b[39m   beforeEach(() \u001b[33m=>\u001b[39m {\u001b[0m\n\n      \u001b[2mat constructor (\u001b[22mnode_modules/@babel/parser/src/parse-error.ts\u001b[2m:95:45)\u001b[22m\n      \u001b[2mat Parser.toParseError [as raise] (\u001b[22mnode_modules/@babel/parser/src/tokenizer/index.ts\u001b[2m:1503:19)\u001b[22m\n      \u001b[2mat Parser.raise [as semicolon] (\u001b[22mnode_modules/@babel/parser/src/parser/util.ts\u001b[2m:150:10)\u001b[22m\n      \u001b[2mat Parser.semicolon [as parseVarStatement] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1223:10)\u001b[22m\n      \u001b[2mat Parser.parseVarStatement [as parseStatementContent] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:578:21)\u001b[22m\n      \u001b[2mat Parser.parseStatementContent [as parseStatementLike] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:449:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementLike [as parseStatementListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:398:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementListItem [as parseBlockOrModuleBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1415:16)\u001b[22m\n      \u001b[2mat Parser.parseBlockOrModuleBlockBody [as parseBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1388:10)\u001b[22m\n      \u001b[2mat Parser.parseBlockBody [as parseBlock] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1356:10)\u001b[22m\n      \u001b[2mat Parser.parseBlock [as parseFunctionBody] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2579:24)\u001b[22m\n      \u001b[2mat Parser.parseFunctionBody [as parseArrowExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2520:10)\u001b[22m\n      \u001b[2mat Parser.parseArrowExpression [as parseParenAndDistinguishExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1807:12)\u001b[22m\n      \u001b[2mat Parser.parseParenAndDistinguishExpression [as parseExprAtom] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1146:21)\u001b[22m\n      \u001b[2mat Parser.parseExprAtom [as parseExprSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:723:23)\u001b[22m\n      \u001b[2mat Parser.parseExprSubscripts [as parseUpdate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:702:21)\u001b[22m\n      \u001b[2mat Parser.parseUpdate [as parseMaybeUnary] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:664:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnary [as parseMaybeUnaryOrPrivate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:398:14)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnaryOrPrivate [as parseExprOps] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:410:23)\u001b[22m\n      \u001b[2mat Parser.parseExprOps [as parseMaybeConditional] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:365:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeConditional [as parseMaybeAssign] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:296:21)\u001b[22m\n      \u001b[2mat parseMaybeAssign (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:252:12)\u001b[22m\n      \u001b[2mat Parser.callback [as allowInAnd] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:3136:12)\u001b[22m\n      \u001b[2mat Parser.allowInAnd [as parseMaybeAssignAllowIn] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:251:17)\u001b[22m\n      \u001b[2mat Parser.parseMaybeAssignAllowIn [as parseExprListItem] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:2742:18)\u001b[22m\n      \u001b[2mat Parser.parseExprListItem [as parseCallExpressionArguments] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:1025:14)\u001b[22m\n      \u001b[2mat Parser.parseCallExpressionArguments [as parseCoverCallAndAsyncArrowHead] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:903:29)\u001b[22m\n      \u001b[2mat Parser.parseCoverCallAndAsyncArrowHead [as parseSubscript] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:785:19)\u001b[22m\n      \u001b[2mat Parser.parseSubscript [as parseSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:744:19)\u001b[22m\n      \u001b[2mat Parser.parseSubscripts [as parseExprSubscripts] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:729:17)\u001b[22m\n      \u001b[2mat Parser.parseExprSubscripts [as parseUpdate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:702:21)\u001b[22m\n      \u001b[2mat Parser.parseUpdate [as parseMaybeUnary] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:664:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnary [as parseMaybeUnaryOrPrivate] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:398:14)\u001b[22m\n      \u001b[2mat Parser.parseMaybeUnaryOrPrivate [as parseExprOps] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:410:23)\u001b[22m\n      \u001b[2mat Parser.parseExprOps [as parseMaybeConditional] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:365:23)\u001b[22m\n      \u001b[2mat Parser.parseMaybeConditional [as parseMaybeAssign] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:296:21)\u001b[22m\n      \u001b[2mat Parser.parseMaybeAssign [as parseExpressionBase] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:221:23)\u001b[22m\n      \u001b[2mat parseExpressionBase (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:212:39)\u001b[22m\n      \u001b[2mat Parser.callback [as allowInAnd] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:3131:16)\u001b[22m\n      \u001b[2mat Parser.allowInAnd [as parseExpression] (\u001b[22mnode_modules/@babel/parser/src/parser/expression.ts\u001b[2m:212:17)\u001b[22m\n      \u001b[2mat Parser.parseExpression [as parseStatementContent] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:654:23)\u001b[22m\n      \u001b[2mat Parser.parseStatementContent [as parseStatementLike] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:449:17)\u001b[22m\n      \u001b[2mat Parser.parseStatementLike [as parseModuleItem] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:386:17)\u001b[22m\n      \u001b[2mat Parser.parseModuleItem [as parseBlockOrModuleBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1414:16)\u001b[22m\n      \u001b[2mat Parser.parseBlockOrModuleBlockBody [as parseBlockBody] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:1388:10)\u001b[22m\n      \u001b[2mat Parser.parseBlockBody [as parseProgram] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:225:10)\u001b[22m\n      \u001b[2mat Parser.parseProgram [as parseTopLevel] (\u001b[22mnode_modules/@babel/parser/src/parser/statement.ts\u001b[2m:203:25)\u001b[22m\n      \u001b[2mat Parser.parseTopLevel [as parse] (\u001b[22mnode_modules/@babel/parser/src/parser/index.ts\u001b[2m:93:10)\u001b[22m\n      \u001b[2mat parse (\u001b[22mnode_modules/@babel/parser/src/index.ts\u001b[2m:92:38)\u001b[22m\n      \u001b[2mat parser (\u001b[22mnode_modules/@babel/core/src/parser/index.ts\u001b[2m:28:19)\u001b[22m\n          at parser.next (<anonymous>)\n      \u001b[2mat normalizeFile (\u001b[22mnode_modules/@babel/core/src/transformation/normalize-file.ts\u001b[2m:49:24)\u001b[22m\n          at normalizeFile.next (<anonymous>)\n      \u001b[2mat run (\u001b[22mnode_modules/@babel/core/src/transformation/index.ts\u001b[2m:40:36)\u001b[22m\n          at run.next (<anonymous>)\n      \u001b[2mat transform (\u001b[22mnode_modules/@babel/core/src/transform.ts\u001b[2m:29:20)\u001b[22m\n          at transform.next (<anonymous>)\n      \u001b[2mat evaluateSync (\u001b[22mnode_modules/gensync/index.js\u001b[2m:251:28)\u001b[22m\n      \u001b[2mat sync (\u001b[22mnode_modules/gensync/index.js\u001b[2m:89:14)\u001b[22m\n      \u001b[2mat fn (\u001b[22mnode_modules/@babel/core/src/errors/rewrite-stack-trace.ts\u001b[2m:99:14)\u001b[22m\n      \u001b[2mat transformSync (\u001b[22mnode_modules/@babel/core/src/transform.ts\u001b[2m:66:52)\u001b[22m\n      \u001b[2mat ScriptTransformer.transformSource (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:545:31)\u001b[22m\n      \u001b[2mat ScriptTransformer._transformAndBuildScript (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:674:40)\u001b[22m\n      \u001b[2mat ScriptTransformer.transform (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:726:19)\u001b[22m\n","testResults":[]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["src/**/*.{js,ts}","!src/**/*.d.ts","!src/**/*.test.ts","!src/**/__tests__/**","!src/**/index.ts","!src/types/**","!src/migrations/**"],"coverageDirectory":"F:\\Amazing pay flow\\coverage","coverageProvider":"babel","coverageReporters":["json","lcov","text","text-summary","clover","html"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":90,"statements":90},"./src/services/":{"branches":85,"functions":90,"lines":95,"statements":95},"./src/controllers/":{"branches":80,"functions":85,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":2,"noStackTrace":false,"nonFlagArgs":["src/services/identity-verification/__tests__/IdentityVerificationService.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[{"displayName":"unit","testMatch":["<rootDir>/src/**/*.test.ts"],"testEnvironment":"node","rootDir":"F:\\Amazing pay flow"},{"displayName":"integration","testMatch":["<rootDir>/tests/integration/**/*.test.ts"],"testEnvironment":"node","rootDir":"F:\\Amazing pay flow"},{"displayName":"performance","testMatch":["<rootDir>/tests/performance/**/*.test.ts"],"testEnvironment":"node","rootDir":"F:\\Amazing pay flow"}],"reporters":[["default",{}],["F:\\Amazing pay flow\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./test-reports","filename":"jest-report.html","expand":true}]],"rootDir":"F:\\Amazing pay flow","runTestsByPath":false,"seed":246728874,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"src\\\\services\\\\identity-verification\\\\__tests__\\\\IdentityVerificationService.test.ts","testSequencer":"F:\\Amazing pay flow\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1748138634407,"_reporterOptions":{"publicPath":"./test-reports","filename":"jest-report.html","expand":true,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})