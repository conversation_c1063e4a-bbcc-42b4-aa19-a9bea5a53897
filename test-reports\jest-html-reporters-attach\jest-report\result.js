window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":12,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":12,"startTime":1748145404756,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":12,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1748145421085,"runtime":15999,"slow":true,"start":1748145405086},"testFilePath":"F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["IdentityVerificationService - Production Tests","Core Business Logic Validation"],"duration":4,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Core Business Logic Validation should validate service initialization","status":"passed","title":"should validate service initialization"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Core Business Logic Validation"],"duration":12,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Core Business Logic Validation should handle valid verification requests gracefully","status":"passed","title":"should handle valid verification requests gracefully"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Core Business Logic Validation"],"duration":33,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Core Business Logic Validation should validate input parameters correctly","status":"passed","title":"should validate input parameters correctly"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Core Business Logic Validation"],"duration":3,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Core Business Logic Validation should handle invalid address formats","status":"passed","title":"should handle invalid address formats"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Core Business Logic Validation"],"duration":5,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Core Business Logic Validation should handle database errors gracefully","status":"passed","title":"should handle database errors gracefully"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Verification Retrieval"],"duration":3,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Verification Retrieval should retrieve verification by ID successfully","status":"passed","title":"should retrieve verification by ID successfully"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Verification Retrieval"],"duration":3,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Verification Retrieval should handle non-existent verification appropriately","status":"passed","title":"should handle non-existent verification appropriately"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Verification Retrieval"],"duration":1,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Verification Retrieval should retrieve user verifications successfully","status":"passed","title":"should retrieve user verifications successfully"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Statistics and Analytics"],"duration":2,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Statistics and Analytics should generate verification statistics","status":"passed","title":"should generate verification statistics"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Statistics and Analytics"],"duration":1,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Statistics and Analytics should handle empty statistics gracefully","status":"passed","title":"should handle empty statistics gracefully"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Error Handling and Resilience"],"duration":5,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Error Handling and Resilience should handle network timeouts gracefully","status":"passed","title":"should handle network timeouts gracefully"},{"ancestorTitles":["IdentityVerificationService - Production Tests","Error Handling and Resilience"],"duration":22,"failureMessages":[],"fullName":"IdentityVerificationService - Production Tests Error Handling and Resilience should validate service resilience under load","status":"passed","title":"should validate service resilience under load"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["src/**/*.{js,ts}","!src/**/*.d.ts","!src/**/*.test.ts","!src/**/__tests__/**","!src/**/index.ts","!src/types/**","!src/migrations/**"],"coverageDirectory":"F:\\Amazing pay flow\\coverage","coverageProvider":"babel","coverageReporters":["json","lcov","text","text-summary","clover","html"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":90,"statements":90},"./src/services/":{"branches":85,"functions":90,"lines":95,"statements":95},"./src/controllers/":{"branches":80,"functions":85,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":2,"noStackTrace":false,"nonFlagArgs":["src/services/identity-verification/__tests__/IdentityVerificationService.production.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\Amazing pay flow\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./test-reports","filename":"jest-report.html","expand":true}]],"rootDir":"F:\\Amazing pay flow","runTestsByPath":false,"seed":-1927260970,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"src\\\\services\\\\identity-verification\\\\__tests__\\\\IdentityVerificationService.production.test.ts","testSequencer":"F:\\Amazing pay flow\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1748145421100,"_reporterOptions":{"publicPath":"./test-reports","filename":"jest-report.html","expand":true,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})