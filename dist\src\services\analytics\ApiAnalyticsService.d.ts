import { Request, Response, NextFunction } from "express";
/**
 * API analytics service
 * This service tracks API usage
 */
export declare class ApiAnalyticsService {
    private static instance;
    private eventBuffer;
    private bufferSize;
    private flushInterval;
    /**
     * Create a new API analytics service
     */
    private constructor();
    /**
     * Get the API analytics service instance
     * @returns API analytics service instance
     */
    static getInstance(): ApiAnalyticsService;
    /**
     * Track API request
     * @param req Express request
     * @param res Express response
     * @param responseTime Response time in milliseconds
     */
    trackRequest(req: Request, res: Response, responseTime: number): void;
    /**
     * Flush event buffer
     */
    private flushBuffer;
    /**
     * Create API analytics middleware
     * @returns Express middleware
     */
    createMiddleware(): (req: Request, res: Response, next: NextFunction) => void;
    /**
     * Get API analytics
     * @param filter Filter
     * @returns API analytics
     */
    getAnalytics(filter: {
        startDate?: Date;
        endDate?: Date;
        path?: string;
        method?: string;
        statusCode?: number;
        userId?: string;
        userRole?: string;
        apiVersion?: string;
        limit?: number;
        offset?: number;
    }): Promise<{
        data: any[];
        total: number;
    }>;
    if(filter: any, userRole: any): void;
    const total: any;
    const data: any;
}
export default ApiAnalyticsService;
//# sourceMappingURL=ApiAnalyticsService.d.ts.map