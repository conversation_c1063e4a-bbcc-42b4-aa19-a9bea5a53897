2025-05-25 08:01 +03:00: 🚀 AmazingPay Flow server running on port 3002
2025-05-25 08:01 +03:00: 📊 Health check: http://localhost:3002/api/health
2025-05-25 08:01 +03:00: 🌐 Environment: production
2025-05-25 08:01 +03:00: 🚀 AmazingPay Flow server running on port 3002
2025-05-25 08:01 +03:00: 📊 Health check: http://localhost:3002/api/health
2025-05-25 08:01 +03:00: 🌐 Environment: production
2025-05-25 08:01 +03:00: 🚀 AmazingPay Flow server running on port 3002
2025-05-25 08:01 +03:00: 📊 Health check: http://localhost:3002/api/health
2025-05-25 08:01 +03:00: 🌐 Environment: production
2025-05-25 08:01 +03:00: 🚀 AmazingPay Flow server running on port 3002
2025-05-25 08:01 +03:00: 📊 Health check: http://localhost:3002/api/health
2025-05-25 08:01 +03:00: 🌐 Environment: production
2025-05-25 08:02 +03:00: 127.0.0.1 - - [25/May/2025:05:02:01 +0000] "GET /api/health HTTP/1.1" 200 118 "-" "curl/8.12.1"
2025-05-25 08:02 +03:00: 127.0.0.1 - - [25/May/2025:05:02:16 +0000] "GET /api/health HTTP/1.1" 200 119 "-" "curl/8.12.1"
2025-05-25 08:02 +03:00: 127.0.0.1 - - [25/May/2025:05:02:28 +0000] "GET /api/health HTTP/1.1" 200 119 "-" "curl/8.12.1"
2025-05-25 08:02 +03:00: 127.0.0.1 - - [25/May/2025:05:02:40 +0000] "GET /api HTTP/1.1" 200 70 "-" "curl/8.12.1"
2025-05-25 08:02 +03:00: 127.0.0.1 - - [25/May/2025:05:02:52 +0000] "GET /api HTTP/1.1" 200 70 "-" "curl/8.12.1"
2025-05-25 08:03 +03:00: 127.0.0.1 - - [25/May/2025:05:03:03 +0000] "GET /api HTTP/1.1" 200 70 "-" "curl/8.12.1"
2025-05-25 08:03 +03:00: 127.0.0.1 - - [25/May/2025:05:03:16 +0000] "GET /api HTTP/1.1" 200 70 "-" "curl/8.12.1"
2025-05-25 08:03 +03:00: 127.0.0.1 - - [25/May/2025:05:03:51 +0000] "GET /api/health HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
2025-05-25 08:03 +03:00: 127.0.0.1 - - [25/May/2025:05:03:52 +0000] "GET /favicon.ico HTTP/1.1" 404 49 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
