/**
 * Storage Path Utility
 *
 * This utility provides functions for managing environment-specific storage paths
 * to ensure complete isolation between production and demo environments.
 */
/**
 * Get environment-specific storage path
 * @param subPath Optional sub-path within the environment directory
 * @returns Full path to the environment-specific storage location
 */
export declare const getStoragePath: any;
/**
 * Get environment-specific logs path
 * @returns Full path to the environment-specific logs directory
 */
export declare const getLogsPath: any;
/**
 * Get environment-specific uploads path
 * @param subPath Optional sub-path within the uploads directory
 * @returns Full path to the environment-specific uploads location
 */
export declare const getUploadsPath: any;
/**
 * Get environment-specific temp path
 * @param subPath Optional sub-path within the temp directory
 * @returns Full path to the environment-specific temp location
 */
export declare const getTempPath: any;
/**
 * Get environment-specific cache path
 * @param subPath Optional sub-path within the cache directory
 * @returns Full path to the environment-specific cache location
 */
export declare const getCachePath: any;
/**
 * Ensure a directory exists, creating it if necessary
 * @param dirPath Directory path to ensure
 */
export declare const ensureDirectoryExists: any;
/**
 * Initialize storage directories for the current environment
 */
export declare const initializeStorage: any;
declare const _default: {
    getStoragePath: any;
    getLogsPath: any;
    getUploadsPath: any;
    getTempPath: any;
    getCachePath: any;
    ensureDirectoryExists: any;
    initializeStorage: any;
};
export default _default;
//# sourceMappingURL=storage-path.d.ts.map