/**
 * Auth Module
 *
 * This module handles authentication and authorization.
 */
import { BaseModule } from '../../factories/ModuleFactory';
/**
 * Auth Module
 */
declare class AuthModule extends BaseModule {
    /**
     * Constructor
     */
    constructor();
    /**
     * Initialize the module
     */
    initialize(): void;
}
declare const _default: AuthModule;
export default _default;
//# sourceMappingURL=auth.module.d.ts.map