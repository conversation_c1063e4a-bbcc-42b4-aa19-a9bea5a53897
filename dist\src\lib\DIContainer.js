"use strict";
// jscpd:ignore-file
/**
 * Dependency Injection Container
 *
 * A simple DI container for managing service dependencies.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.container = exports.DIContainer = void 0;
const logger_1 = require("./logger");
/**
 * Dependency injection container
 */
class DIContainer {
    constructor() {
        this.factories = new Map();
        this.instances = new Map();
        this.dependencies = new Map();
    }
    /**
     * Get the singleton instance
     */
    static getInstance() {
        if (!DIContainer.instance) {
            DIContainer.instance = new DIContainer();
        }
        return DIContainer.instance;
    }
    /**
     * Register a service factory
     *
     * @param name Service name
     * @param factory Factory function to create the service
     * @param dependencies Array of dependency service names
     */
    register(name, factory, dependencies = []) {
        this.factories.set(name, factory);
        this.dependencies.set(name, dependencies);
        // Remove any existing instance when re-registering
        this.instances.delete(name);
        logger_1.logger.debug(`Registered service: ${name}`, { dependencies });
    }
    /**
     * Register an instance directly
     *
     * @param name Service name
     * @param instance Service instance
     */
    registerInstance(name, instance) {
        this.instances.set(name, instance);
        this.dependencies.set(name, []);
        // Remove any existing factory when registering an instance
        this.factories.delete(name);
        logger_1.logger.debug(`Registered service instance: ${name}`);
    }
    /**
     * Resolve a service
     *
     * @param name Service name
     * @returns Service instance
     */
    resolve(name) {
        // Check if instance already exists
        if (this.instances.has(name)) {
            return this.instances.get(name);
        }
        // Check if factory exists
        const factory = this.factories.get(name);
        if (!factory) {
            throw new Error(`Service not registered: ${name}`);
        }
        // Resolve dependencies
        const dependencies = this.dependencies.get(name) || [];
        const resolvedDependencies = dependencies.map((dep) => this.resolve(dep));
        // Create instance
        const instance = factory(...resolvedDependencies);
        // Cache instance
        this.instances.set(name, instance);
        return instance;
    }
    /**
     * Check if a service is registered
     *
     * @param name Service name
     * @returns True if service is registered
     */
    has(name) {
        return this.factories.has(name) || this.instances.has(name);
    }
    /**
     * Remove a service
     *
     * @param name Service name
     */
    remove(name) {
        this.factories.delete(name);
        this.instances.delete(name);
        this.dependencies.delete(name);
        logger_1.logger.debug(`Removed service: ${name}`);
    }
    /**
     * Clear all services
     */
    clear() {
        this.factories.clear();
        this.instances.clear();
        this.dependencies.clear();
        logger_1.logger.debug('Cleared all services');
    }
    /**
     * Get all registered service names
     *
     * @returns Array of service names
     */
    getServiceNames() {
        const factoryNames = Array.from(this.factories.keys());
        const instanceNames = Array.from(this.instances.keys());
        // Combine and deduplicate
        const combinedNames = [...factoryNames, ...instanceNames];
        return Array.from(new Set(combinedNames));
    }
}
exports.DIContainer = DIContainer;
// Export singleton instance
exports.container = DIContainer.getInstance();
