"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const multi_factor_auth_controller_1 = require("../controllers/multi-factor-auth.controller");
const router = express_1.default.Router();
// Apply authentication middleware to all routes
router.use(auth_middleware_1.authMiddleware);
// Get user's verification methods
router.get("/methods", (0, auth_middleware_1.authorize)(["USER", "MERCHANT"]), multi_factor_auth_controller_1.getUserVerificationMethods);
// Enable MFA
router.post("/enable", (0, auth_middleware_1.authorize)(["USER", "MERCHANT"]), multi_factor_auth_controller_1.enableMFA);
// Disable MFA
router.post("/disable", (0, auth_middleware_1.authorize)(["USER", "MERCHANT"]), multi_factor_auth_controller_1.disableMFA);
exports.default = router;
//# sourceMappingURL=multi-factor-auth.routes.js.map