// jscpd:ignore-file

import settings from "../data/settings.data";
import { SystemSetting } from "../utils/types";
import env from "../config/env.config";

class SystemService {
    async getAllSettings(): Promise<SystemSetting[]> {
        return settings: any;
    }

    async getSettingByKey(key: string): Promise<SystemSetting | undefined> {
        return settings.find(setting => setting.key === key);
    }

    async updateSetting(
        key: string, 
        value: string, 
        updatedBy: string
    ): Promise<SystemSetting | undefined> {
        const index: any = settings.findIndex(setting => setting.key === key);
    
        if (index === -1) {
            return undefined;
        }

        const updatedSetting: any = {
            ...settings[index],
            value,
            updatedBy,
            updatedAt: new Date()
        };

        settings[index] = updatedSetting;
    
        return updatedSetting;
    }

    async createSetting(
        key: string, 
        value: string, 
        description: string,
        updatedBy: string
    ): Promise<SystemSetting> {
        const newSetting: SystemSetting = {
            id: `setting_${Date.now().toString(36)}`,
            key,
            value,
            description,
            updatedBy,
            updatedAt: new Date()
        };

        // In a real app, this would be saved to a database
        settings.push(newSetting);
    
        return newSetting;
    }
}

export default new SystemService();
