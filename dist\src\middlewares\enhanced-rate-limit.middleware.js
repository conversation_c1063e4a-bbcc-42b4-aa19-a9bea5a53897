"use strict";
// jscpd:ignore-file
/**
 * Enhanced Rate Limiting Middleware
 *
 * This middleware provides improved rate limiting with:
 * - Tiered rate limiting based on endpoint sensitivity
 * - IP-based and user-based rate limiting
 * - Proper rate limit headers
 * - Environment-specific rate limits
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminLimiter = exports.verificationLimiter = exports.paymentLimiter = exports.authLimiter = exports.apiLimiter = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const rate_limit_redis_1 = __importDefault(require("rate-limit-redis"));
const logger_1 = require("../lib/logger");
const environment_validator_1 = require("../utils/environment-validator");
const app_error_1 = require("../utils/app-error");
const redis_manager_1 = __importDefault(require("../lib/redis-manager"));
// Create rate limit store based on environment
const createLimitStore = () => {
    const redisClient = redis_manager_1.default.getClient();
    if (redisClient && redis_manager_1.default.isRedisEnabled() && ((0, environment_validator_1.isProduction)() || process.env.USE_REDIS_RATE_LIMIT === "true")) {
        logger_1.logger.info("Using Redis store for rate limiting");
        return new rate_limit_redis_1.default({
            sendCommand: (...args) => redisClient.sendCommand(args),
            prefix: "rl:"
        });
    }
    logger_1.logger.info("Using memory store for rate limiting");
    return undefined; // Use default memory store
};
// Base rate limiter configuration
const baseLimiter = (options) => {
    const defaultOptions = {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: (0, environment_validator_1.isProduction)() ? 100 : 1000, // Limit each IP to 100 requests per windowMs in production, 1000 in development
        standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false, // Disable the `X-RateLimit-*` headers
        store: createLimitStore(),
        message: { status: "error",
            code: "RATE_LIMIT_EXCEEDED",
            message: "Too many requests, please try again later."
        },
        handler: (req, res, next, options) => {
            const error = new app_error_1.AppError(options.message.message, 429, true, "RATE_LIMIT_EXCEEDED", { retryAfter: Math.ceil(options.windowMs / 1000) });
            logger_1.logger.warn(`Rate limit exceeded: ${req.ip} - ${req.method} ${req.originalUrl}`);
            res.status(429).json({
                status: "error",
                code: "RATE_LIMIT_EXCEEDED",
                message: options.message.message,
                retryAfter: Math.ceil(options.windowMs / 1000)
            });
        },
        skip: (req) => {
            // Skip rate limiting for health check endpoints
            return req.path === "/health" || req.path === "/api/health";
        },
        keyGenerator: (req) => {
            // Use user ID if available, otherwise use IP
            return req.user?.id || req.ip;
        }
    };
    return (0, express_rate_limit_1.default)({
        ...defaultOptions,
        ...options
    });
};
// General API rate limiter
exports.apiLimiter = baseLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: (0, environment_validator_1.isProduction)() ? 300 : 3000 // 300 requests per 15 minutes in production
});
// Authentication rate limiter (more strict)
exports.authLimiter = baseLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: (0, environment_validator_1.isProduction)() ? 20 : 100, // 20 requests per 15 minutes in production
    message: { status: "error",
        code: "AUTH_RATE_LIMIT_EXCEEDED",
        message: "Too many authentication attempts, please try again later."
    }
});
// Payment rate limiter (more strict)
exports.paymentLimiter = baseLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: (0, environment_validator_1.isProduction)() ? 60 : 300, // 60 requests per hour in production
    message: { status: "error",
        code: "PAYMENT_RATE_LIMIT_EXCEEDED",
        message: "Too many payment requests, please try again later."
    }
});
// Verification rate limiter
exports.verificationLimiter = baseLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: (0, environment_validator_1.isProduction)() ? 100 : 500, // 100 requests per hour in production
    message: { status: "error",
        code: "VERIFICATION_RATE_LIMIT_EXCEEDED",
        message: "Too many verification requests, please try again later."
    }
});
// Admin rate limiter (less strict)
exports.adminLimiter = baseLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: (0, environment_validator_1.isProduction)() ? 500 : 2000 // 500 requests per 15 minutes in production
});
exports.default = {
    apiLimiter: exports.apiLimiter,
    authLimiter: exports.authLimiter,
    paymentLimiter: exports.paymentLimiter,
    verificationLimiter: exports.verificationLimiter,
    adminLimiter: exports.adminLimiter
};
//# sourceMappingURL=enhanced-rate-limit.middleware.js.map