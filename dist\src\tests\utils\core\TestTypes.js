"use strict";
/**
 * Test Types and Interfaces
 *
 * Centralized type definitions for the test utility system.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestError = exports.TestErrorType = void 0;
/**
 * Test error types
 */
var TestErrorType;
(function (TestErrorType) {
    TestErrorType["SETUP_ERROR"] = "SETUP_ERROR";
    TestErrorType["EXECUTION_ERROR"] = "EXECUTION_ERROR";
    TestErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    TestErrorType["CLEANUP_ERROR"] = "CLEANUP_ERROR";
    TestErrorType["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    TestErrorType["ASSERTION_ERROR"] = "ASSERTION_ERROR";
})(TestErrorType || (exports.TestErrorType = TestErrorType = {}));
/**
 * Test error class
 */
class TestError extends Error {
    constructor(message, type, context, originalError) {
        super(message);
        this.name = 'TestError';
        this.type = type;
        this.context = context;
        this.originalError = originalError;
    }
}
exports.TestError = TestError;
//# sourceMappingURL=TestTypes.js.map