import { BaseService } from "../shared/modules/services/BaseService";
import { PaymentMethod } from '../types';
/**
 * Payment verification result
 */
export interface PaymentVerificationResult {
    verified: boolean;
    method: PaymentMethod;
    amount: string;
    currency: string;
    transactionId: string;
    timestamp: string;
    sender?: string;
    recipient?: string;
    rawData?: any;
}
/**
 * Payment verification service
 */
export declare class PaymentVerificationService extends BaseService {
    private blockchainApiService;
    private binanceApiService;
    constructor();
    /**
     * Execute a database operation with error handling
     * @param operation Operation to execute
     * @param errorMessage Error message
     * @param context Error context
     * @returns Operation result
     */
    private executeDbOperation;
    /**
     * Create a payment error
     * @param message Error message
     * @returns Service error
     */
    private paymentError;
    /**
   * Verify a payment
   * @param method Payment method
   * @param transactionId Transaction ID
   * @param amount Expected amount
   * @param currency Expected currency
   * @param recipientAddress Expected recipient address
   * @param merchantApiKey Merchant API key
   * @param merchantSecretKey Merchant secret key
   * @returns Payment verification result
   */
    verifyPayment(method: PaymentMethod, transactionId: string, amount: string, currency: string, recipientAddress: string, merchantApiKey?: string, merchantSecretKey?: string): Promise<PaymentVerificationResult>;
    /**
     * Verify a Binance Pay payment
     * @param transactionId Transaction ID
     * @param amount Expected amount
     * @param currency Expected currency
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    private verifyBinancePayPayment;
    /**
     * Verify a Binance C2C payment
     * @param note Transaction note
     * @param amount Expected amount
     * @param currency Expected currency
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    private verifyBinanceC2CPayment;
    /**
     * Verify a Binance TRC20 payment
     * @param txHash Transaction hash
     * @param amount Expected amount
     * @param currency Expected currency
     * @param recipientAddress Expected recipient address
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    private verifyBinanceTRC20Payment;
    /**
   * Verify a crypto transfer payment
   * @param txHash Transaction hash
   * @param amount Expected amount
   * @param currency Expected currency
   * @param recipientAddress Expected recipient address
   * @returns Payment verification result
   */
    private verifyCryptoTransferPayment;
    /**
   * Map currency to blockchain network and token
   * @param currency Currency
   * @returns Blockchain network and token
   */
    private mapCurrencyToNetworkAndToken;
}
//# sourceMappingURL=payment-verification.service.d.ts.map