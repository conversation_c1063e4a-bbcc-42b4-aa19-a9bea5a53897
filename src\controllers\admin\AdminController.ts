/**
 * Admin Controller
 * 
 * Modular controller for admin operations.
 */

import { Response } from 'express';
import { BaseController } from '../base.controller';
import { asyncHandler } from '../../utils/asyncHandler';
import * as prisma from '../../lib/prisma';

import { AdminAuthorizationService } from './services/AdminAuthorizationService';
import { AdminValidationService } from './services/AdminValidationService';
import { AdminBusinessService } from './services/AdminBusinessService';
import { AdminResponseMapper } from './mappers/AdminResponseMapper';

import {
  AuthenticatedRequest,
  AdminUserFilters,
  RoleFilters,
  PermissionFilters
} from './types/AdminControllerTypes';

/**
 * Modular Admin Controller
 */
export class AdminController extends BaseController {
  private authService: AdminAuthorizationService;
  private validationService: AdminValidationService;
  private businessService: AdminBusinessService;

  constructor() {
    super();
    this.authService = new AdminAuthorizationService();
    this.validationService = new AdminValidationService();
    this.businessService = new AdminBusinessService(prisma);
  }

  /**
   * Get dashboard data
   */
  getDashboardData = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'dashboard',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Business logic
      const dashboardData = await this.businessService.getDashboardData();

      // Response
      AdminResponseMapper.sendDashboardData(res, dashboardData);
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get dashboard statistics
   */
  getDashboardStatistics = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'dashboard',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Business logic
      const statistics = await this.businessService.getDashboardStatistics();

      // Response
      AdminResponseMapper.sendDashboardStatistics(res, statistics);
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get all admin users
   */
  getAdminUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'admin-users',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const pagination = this.validationService.validatePaginationParams(req.query);
      
      // Build filters
      const filters: AdminUserFilters = {};
      if (req.query.status) filters.status = req.query.status as any;
      if (req.query.roleId) filters.roleId = req.query.roleId as string;
      if (req.query.search) filters.search = req.query.search as string;
      if (req.query.dateFrom) filters.dateFrom = new Date(req.query.dateFrom as string);
      if (req.query.dateTo) filters.dateTo = new Date(req.query.dateTo as string);

      // Business logic
      const result = await this.businessService.getAdminUsers(filters, pagination);

      // Response
      AdminResponseMapper.sendAdminUsersList(
        res,
        result.users,
        result.total,
        pagination.page,
        pagination.limit
      );
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get admin user by ID
   */
  getAdminUserById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'admin-users',
        'read',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const userId = this.validationService.validateId(req.params.id, 'User ID');

      // Business logic
      const user = await this.businessService.getAdminUserById(userId);

      // Response
      AdminResponseMapper.sendAdminUser(res, user);
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Create admin user
   */
  createAdminUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'admin-users',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateCreateAdminUser(req.body);

      // Business logic
      const user = await this.businessService.createAdminUser(validatedData, req.user!.id);

      // Response
      AdminResponseMapper.sendAdminUserCreated(res, user);
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Update admin user
   */
  updateAdminUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'admin-users',
        'update',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const userId = this.validationService.validateId(req.params.id, 'User ID');
      const validatedData = this.validationService.validateUpdateAdminUser(req.body);

      // Business logic
      const user = await this.businessService.updateAdminUser(userId, validatedData);

      // Response
      AdminResponseMapper.sendAdminUserUpdated(res, user);
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Delete admin user
   */
  deleteAdminUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'admin-users',
        'delete',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const userId = this.validationService.validateId(req.params.id, 'User ID');

      // Business logic
      await this.businessService.deleteAdminUser(userId);

      // Response
      AdminResponseMapper.sendAdminUserDeleted(res);
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get system health
   */
  getSystemHealth = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'system',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Business logic - simple health check
      const health = {
        status: 'healthy' as const,
        timestamp: new Date(),
        services: {
          database: 'connected' as const,
          cache: 'connected' as const,
          external: 'connected' as const
        },
        metrics: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // MB
          cpuUsage: process.cpuUsage().user / 1000000 // seconds
        }
      };

      // Response
      AdminResponseMapper.sendSystemHealth(res, health);
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Health check endpoint
   */
  healthCheck = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      AdminResponseMapper.sendSuccess(res, {
        status: 'healthy',
        timestamp: new Date(),
        version: '1.0.0',
        services: {
          authorization: 'active',
          validation: 'active',
          business: 'active',
          database: 'connected'
        }
      }, 'Admin Controller is healthy');
    } catch (error) {
      AdminResponseMapper.sendError(res, error as Error);
    }
  });
}
