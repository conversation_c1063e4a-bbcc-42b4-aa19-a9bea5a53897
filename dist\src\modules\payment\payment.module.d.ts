import { Module } from '../../core/module';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Payment Module
 * This module provides payment functionality with zero duplication
 */
export declare class PaymentModule {
    private moduleFactory;
    private moduleRegistry;
    private container;
    private module;
    /**
     * Create a new payment module
     */
    constructor();
    /**
     * Get the module
     * @returns Payment module
     */
    getModule(): Module;
}
//# sourceMappingURL=payment.module.d.ts.map