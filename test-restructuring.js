// Test script to verify the restructuring works
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Restructuring Results...\n');

// Check if new files exist
const newFiles = [
    'src/services/identity-verification/core/IdentityVerificationTypes.ts',
    'src/services/identity-verification/core/IdentityVerificationError.ts',
    'src/services/identity-verification/core/IdentityVerificationService.ts',
    'src/services/identity-verification/methods/EthereumSignatureVerification.ts',
    'src/services/identity-verification/utils/BlockchainUtils.ts',
    'src/services/identity-verification/index.ts'
];

console.log('📁 Checking new modular files:');
let allFilesExist = true;

newFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        const lines = fs.readFileSync(file, 'utf8').split('\n').length;
        console.log(`✅ ${file} (${lines} lines)`);
    } else {
        console.log(`❌ ${file} - NOT FOUND`);
        allFilesExist = false;
    }
});

// Check original file
const originalFile = 'src/services/identity-verification.service.ts';
if (fs.existsSync(originalFile)) {
    const stats = fs.statSync(originalFile);
    const lines = fs.readFileSync(originalFile, 'utf8').split('\n').length;
    console.log(`\n📄 Original file: ${originalFile} (${lines} lines)`);
}

// Calculate total lines
let totalNewLines = 0;
newFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const lines = fs.readFileSync(file, 'utf8').split('\n').length;
        totalNewLines += lines;
    }
});

console.log('\n📊 Restructuring Results:');
console.log(`- New modular files: ${newFiles.length}`);
console.log(`- Total lines in new structure: ${totalNewLines}`);
console.log(`- Average lines per file: ${Math.round(totalNewLines / newFiles.length)}`);
console.log(`- All files exist: ${allFilesExist ? '✅ YES' : '❌ NO'}`);

if (allFilesExist) {
    console.log('\n🎉 Restructuring Phase 1 Complete!');
    console.log('✅ Identity Verification Service successfully modularized');
    console.log('✅ File sizes reduced to manageable chunks');
    console.log('✅ Better separation of concerns achieved');
} else {
    console.log('\n⚠️  Some files are missing. Please check the file creation process.');
}

console.log('\n🚀 Next Steps:');
console.log('1. Test the new modular structure');
console.log('2. Update imports in controllers');
console.log('3. Extract remaining verification methods');
console.log('4. Apply same restructuring to other large files');
