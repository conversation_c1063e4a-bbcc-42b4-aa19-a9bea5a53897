"use strict";
// jscpd:ignore-file
/**
 * Monitoring Dashboard Routes
 *
 * These routes provide endpoints for monitoring the application's health and performance.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const monitoring_1 = require("../utils/monitoring");
const db_optimization_1 = require("../utils/db-optimization");
const cache_1 = __importDefault(require("../utils/cache"));
const prisma_1 = __importDefault(require("../lib/prisma"));
const os_1 = __importDefault(require("os"));
const logger_1 = require("../lib/logger");
const router = express_1.default.Router();
/**
 * @route GET /api/monitoring-dashboard/health
 * @desc Get system health status
 * @access Private (Admin)
 */
router.get("/health", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, async (req, res) => {
    try {
        const healthReport = await (0, monitoring_1.checkSystemHealth)();
        res.status(200).json({
            status: "success",
            data: { health: healthReport
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting system health:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get system health"
        });
    }
});
/**
 * @route GET /api/monitoring-dashboard/metrics
 * @desc Get system metrics
 * @access Private (Admin)
 */
router.get("/metrics", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        const metrics = (0, monitoring_1.getMetrics)();
        res.status(200).json({
            status: "success",
            data: {
                metrics
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting system metrics:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get system metrics"
        });
    }
});
/**
 * @route GET /api/monitoring-dashboard/database
 * @desc Get database metrics
 * @access Private (Admin)
 */
router.get("/database", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, async (req, res) => {
    try {
        // Get database metrics
        const startTime = Date.now();
        await prisma_1.default.$queryRaw `SELECT 1`;
        const responseTime = Date.now() - startTime;
        // Get recent slow queries
        const slowQueries = (0, db_optimization_1.getRecentSlowQueries)();
        // Get database statistics
        const databaseStats = {
            responseTime,
            slowQueries,
            status: responseTime < 500 ? "healthy" : responseTime < 1000 ? "warning" : "critical"
        };
        res.status(200).json({
            status: "success",
            data: { database: databaseStats
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting database metrics:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get database metrics"
        });
    }
});
/**
 * @route GET /api/monitoring-dashboard/cache
 * @desc Get cache metrics
 * @access Private (Admin)
 */
router.get("/cache", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        const cacheStats = cache_1.default.getStats();
        res.status(200).json({
            status: "success",
            data: { cache: cacheStats
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting cache metrics:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get cache metrics"
        });
    }
});
/**
 * @route POST /api/monitoring-dashboard/cache/clear
 * @desc Clear the cache
 * @access Private (Admin)
 */
router.post("/cache/clear", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        cache_1.default.clear();
        res.status(200).json({
            status: "success",
            message: "Cache cleared successfully"
        });
    }
    catch (error) {
        logger_1.logger.error("Error clearing cache:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to clear cache"
        });
    }
});
/**
 * @route GET /api/monitoring-dashboard/system
 * @desc Get system information
 * @access Private (Admin)
 */
router.get("/system", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        const systemInfo = {
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
            uptime: Math.round(process.uptime()),
            memory: { total: Math.round(os_1.default.totalmem() / 1024 / 1024),
                free: Math.round(os_1.default.freemem() / 1024 / 1024),
                used: Math.round((os_1.default.totalmem() - os_1.default.freemem()) / 1024 / 1024),
                usagePercent: Math.round(((os_1.default.totalmem() - os_1.default.freemem()) / os_1.default.totalmem()) * 100)
            },
            cpu: { cores: os_1.default.cpus().length,
                model: os_1.default.cpus()[0].model,
                speed: os_1.default.cpus()[0].speed,
                loadAvg: os_1.default.loadavg()
            },
            network: { interfaces: Object.entries(os_1.default.networkInterfaces() || {}).reduce((acc, [name, interfaces]) => {
                    acc[name] = interfaces?.map(iface => ({
                        address: iface.address,
                        netmask: iface.netmask,
                        family: iface.family,
                        mac: iface.mac,
                        internal: iface.internal
                    })) || [];
                    return acc;
                }, {})
            }
        };
        res.status(200).json({
            status: "success",
            data: { system: systemInfo
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting system information:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get system information"
        });
    }
});
/**
 * @route GET /api/monitoring-dashboard/logs
 * @desc Get recent logs
 * @access Private (Admin)
 */
router.get("/logs", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        // In a real implementation, you would fetch logs from a log store
        // For now, we'll just return a message
        res.status(200).json({
            status: "success",
            message: "Log retrieval not implemented yet. Check server logs directly."
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting logs:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get logs"
        });
    }
});
exports.default = router;
//# sourceMappingURL=monitoring-dashboard.routes.js.map