"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookService = exports.WebhookDeliveryStatus = exports.WebhookEventType = void 0;
// jscpd:ignore-file
const axios_1 = __importDefault(require("axios"));
const crypto_1 = __importDefault(require("crypto"));
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const prisma_1 = __importDefault(require("../lib/prisma"));
/**
 * Webhook event types
 */
var WebhookEventType;
(function (WebhookEventType) {
    WebhookEventType["PAYMENT_CREATED"] = "payment.created";
    WebhookEventType["PAYMENT_COMPLETED"] = "payment.completed";
    WebhookEventType["PAYMENT_FAILED"] = "payment.failed";
    WebhookEventType["PAYMENT_EXPIRED"] = "payment.expired";
    WebhookEventType["PAYMENT_REFUNDED"] = "payment.refunded";
    WebhookEventType["VERIFICATION_SUCCEEDED"] = "verification.succeeded";
    WebhookEventType["VERIFICATION_FAILED"] = "verification.failed";
    WebhookEventType["ALERT_CREATED"] = "alert.created";
    WebhookEventType["ALERT_UPDATED"] = "alert.updated";
    WebhookEventType["ALERT_RESOLVED"] = "alert.resolved";
})(WebhookEventType || (exports.WebhookEventType = WebhookEventType = {}));
/**
 * Webhook delivery status
 */
var WebhookDeliveryStatus;
(function (WebhookDeliveryStatus) {
    WebhookDeliveryStatus["PENDING"] = "pending";
    WebhookDeliveryStatus["SUCCESS"] = "success";
    WebhookDeliveryStatus["FAILED"] = "failed";
    WebhookDeliveryStatus["RETRYING"] = "retrying";
})(WebhookDeliveryStatus || (exports.WebhookDeliveryStatus = WebhookDeliveryStatus = {}));
/**
 * Webhook service
 */
class WebhookService {
    /**
     * Create a new webhook service
     */
    constructor() {
        this.maxRetries = config_1.config.webhook.retryAttempts || 3;
        this.retryDelay = config_1.config.webhook.retryDelay || 5000; // 5 seconds
        this.timeout = config_1.config.webhook.timeout || 10000; // 10 seconds
    }
    /**
     * Generate a signature for the webhook payload
     * @param payload Webhook payload
     * @param secret Merchant secret
     * @returns Signature
     */
    generateSignature(payload, secret) {
        const hmac = crypto_1.default.createHmac('sha256', secret);
        hmac.update(JSON.stringify(payload));
        return hmac.digest('hex');
    }
    /**
     * Create a webhook event
     * @param merchantId Merchant ID
     * @param event Event type
     * @param data Event data
     * @returns Webhook ID
     */
    async createWebhookEvent(merchantId, event, data) {
        try {
            // Get merchant
            const merchant = await prisma_1.default.merchant.findUnique({
                where: { id: merchantId },
                select: {
                    id: true,
                    webhookUrl: true,
                    apiSecret: true,
                },
            });
            if (!merchant || !merchant.webhookUrl) {
                logger_1.logger.warn('Merchant has no webhook URL configured', { merchantId });
                return '';
            }
            // Create webhook payload
            const payload = {
                id: crypto_1.default.randomUUID(),
                event,
                created: Date.now(),
                data,
            };
            // Create webhook event in database
            const webhook = await prisma_1.default.webhook.create({
                data: {
                    id: payload.id,
                    merchantId,
                    event,
                    payload: payload,
                    status: WebhookDeliveryStatus.PENDING,
                    url: merchant.webhookUrl,
                    retryCount: 0,
                    maxRetries: this.maxRetries,
                },
            });
            // Deliver webhook asynchronously
            this.deliverWebhook(webhook.id, merchant.webhookUrl, payload, merchant.apiSecret).catch((error) => {
                logger_1.logger.error('Error delivering webhook', { error, webhookId: webhook.id });
            });
            return webhook.id;
        }
        catch (error) {
            logger_1.logger.error('Error creating webhook event', { error, merchantId, event });
            throw new Error('Failed to create webhook event');
        }
    }
    /**
     * Deliver a webhook
     * @param webhookId Webhook ID
     * @param url Webhook URL
     * @param payload Webhook payload
     * @param secret Merchant secret
     * @param retryCount Retry count
     */
    async deliverWebhook(webhookId, url, payload, secret, retryCount = 0) {
        try {
            // Generate signature
            const signature = this.generateSignature(payload, secret);
            // Update webhook status to retrying if this is a retry
            if (retryCount > 0) {
                await prisma_1.default.webhook.update({
                    where: { id: webhookId },
                    data: {
                        status: WebhookDeliveryStatus.RETRYING,
                        retryCount,
                        lastAttemptAt: new Date(),
                    },
                });
            }
            // Send webhook
            const response = await axios_1.default.post(url, payload, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Signature': signature,
                    'X-Webhook-ID': webhookId,
                },
                timeout: this.timeout,
            });
            // Update webhook status
            await prisma_1.default.webhook.update({
                where: { id: webhookId },
                data: {
                    status: WebhookDeliveryStatus.SUCCESS,
                    statusCode: response.status,
                    response: response.data,
                    deliveredAt: new Date(),
                },
            });
            logger_1.logger.info('Webhook delivered successfully', { webhookId, url });
        }
        catch (error) {
            // Log error
            logger_1.logger.error('Error delivering webhook', { error, webhookId, url, retryCount });
            // Update webhook status
            await prisma_1.default.webhook.update({
                where: { id: webhookId },
                data: {
                    status: WebhookDeliveryStatus.FAILED,
                    statusCode: error.response?.status || 0,
                    response: error.response?.data || null,
                    error: error.message,
                    lastAttemptAt: new Date(),
                },
            });
            // Retry if not exceeded max retries
            if (retryCount < this.maxRetries) {
                // Calculate exponential backoff delay
                const delay = this.retryDelay * Math.pow(2, retryCount);
                logger_1.logger.info(`Scheduling webhook retry in ${delay}ms`, {
                    webhookId,
                    retryCount: retryCount + 1,
                });
                // Schedule retry
                setTimeout(() => {
                    this.deliverWebhook(webhookId, url, payload, secret, retryCount + 1).catch((retryError) => {
                        logger_1.logger.error('Error in webhook retry', { retryError, webhookId });
                    });
                }, delay);
            }
            else {
                logger_1.logger.warn('Max webhook retries exceeded', { webhookId, maxRetries: this.maxRetries });
            }
        }
    }
    /**
     * Retry a failed webhook
     * @param webhookId Webhook ID
     * @returns Success status
     */
    async retryWebhook(webhookId) {
        try {
            // Get webhook
            const webhook = await prisma_1.default.webhook.findUnique({
                where: { id: webhookId },
                include: {
                    merchant: {
                        select: { apiSecret: true },
                    },
                },
            });
            if (!webhook) {
                logger_1.logger.warn('Webhook not found', { webhookId });
                return false;
            }
            if (webhook.status === WebhookDeliveryStatus.SUCCESS) {
                logger_1.logger.info('Webhook already delivered successfully', { webhookId });
                return true;
            }
            // Deliver webhook
            await this.deliverWebhook(webhook.id, webhook.url, webhook.payload, webhook.merchant.apiSecret, webhook.retryCount);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Error retrying webhook', { error, webhookId });
            return false;
        }
    }
    /**
     * Get webhooks for a merchant
     * @param merchantId Merchant ID
     * @param limit Limit
     * @param offset Offset
     * @returns Webhooks
     */
    async getWebhooks(merchantId, limit = 10, offset = 0) {
        try {
            const webhooks = await prisma_1.default.webhook.findMany({
                where: { merchantId },
                orderBy: { createdAt: 'desc' },
                take: limit,
                skip: offset,
            });
            return webhooks;
        }
        catch (error) {
            logger_1.logger.error('Error getting webhooks', { error, merchantId });
            throw new Error('Failed to get webhooks');
        }
    }
    /**
     * Get a webhook by ID
     * @param webhookId Webhook ID
     * @returns Webhook
     */
    async getWebhook(webhookId) {
        try {
            const webhook = await prisma_1.default.webhook.findUnique({
                where: { id: webhookId },
            });
            if (!webhook) {
                throw new Error('Webhook not found');
            }
            return webhook;
        }
        catch (error) {
            logger_1.logger.error('Error getting webhook', { error, webhookId });
            throw new Error('Failed to get webhook');
        }
    }
}
exports.WebhookService = WebhookService;
