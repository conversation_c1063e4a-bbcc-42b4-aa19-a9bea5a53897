"use strict";
/**
 * Mock Factories
 *
 * Factory functions for creating mock objects used in tests.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMockRequest = createMockRequest;
exports.createMockResponse = createMockResponse;
exports.createMockNext = createMockNext;
exports.createMockModel = createMockModel;
exports.createMockPrismaClient = createMockPrismaClient;
exports.createMockJwtToken = createMockJwtToken;
exports.createMockApiResponse = createMockApiResponse;
exports.createMockErrorResponse = createMockErrorResponse;
exports.createMockFileUpload = createMockFileUpload;
exports.createMockWebSocket = createMockWebSocket;
exports.resetMocks = resetMocks;
exports.clearMocks = clearMocks;
// import { mockModelFactory } from '../../shared/test/mockModelFactory';
// Using placeholder for missing mock model factory
const mockModelFactory = (model, data) => ({ ...data, id: 'mock-id' });
/**
 * Create a mock request object
 */
function createMockRequest(options = {}) {
    const defaultRequest = {
        params: {},
        query: {},
        body: {},
        headers: {
            'content-type': 'application/json',
            'user-agent': 'test-agent',
            accept: 'application/json',
        },
        user: null,
        session: {},
        cookies: {},
        ip: '127.0.0.1',
        method: 'GET',
        url: '/test',
        originalUrl: '/test',
        path: '/test',
        protocol: 'http',
        secure: false,
        xhr: false,
        get: jest.fn((header) => options.headers?.[header.toLowerCase()]),
        header: jest.fn((header) => options.headers?.[header.toLowerCase()]),
        accepts: jest.fn(() => true),
        acceptsCharsets: jest.fn(() => true),
        acceptsEncodings: jest.fn(() => true),
        acceptsLanguages: jest.fn(() => true),
        is: jest.fn(() => false),
        param: jest.fn((name) => options.params?.[name] || options.query?.[name]),
        range: jest.fn(() => undefined),
        fresh: false,
        stale: true,
        subdomains: [],
        hostname: 'localhost',
        baseUrl: '',
        app: {},
        res: {},
        next: {},
        route: {},
    };
    return {
        ...defaultRequest,
        ...options,
    };
}
/**
 * Create a mock response object
 */
function createMockResponse(options = {}) {
    const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis(),
        end: jest.fn().mockReturnThis(),
        redirect: jest.fn().mockReturnThis(),
        cookie: jest.fn().mockReturnThis(),
        clearCookie: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        header: jest.fn().mockReturnThis(),
        get: jest.fn(),
        type: jest.fn().mockReturnThis(),
        format: jest.fn().mockReturnThis(),
        attachment: jest.fn().mockReturnThis(),
        sendFile: jest.fn().mockReturnThis(),
        download: jest.fn().mockReturnThis(),
        render: jest.fn().mockReturnThis(),
        vary: jest.fn().mockReturnThis(),
        append: jest.fn().mockReturnThis(),
        location: jest.fn().mockReturnThis(),
        links: jest.fn().mockReturnThis(),
        sendStatus: jest.fn().mockReturnThis(),
        locals: options.locals || {},
        statusCode: options.statusCode || 200,
        headersSent: options.headersSent || false,
        charset: 'utf-8',
        app: {},
        req: {},
    };
    return mockResponse;
}
/**
 * Create a mock next function
 */
function createMockNext() {
    return jest.fn();
}
/**
 * Create a mock database model
 */
function createMockModel(modelName) {
    const baseModel = {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        create: jest.fn(),
        createMany: jest.fn(),
        update: jest.fn(),
        updateMany: jest.fn(),
        upsert: jest.fn(),
        delete: jest.fn(),
        deleteMany: jest.fn(),
        count: jest.fn(),
        aggregate: jest.fn(),
        groupBy: jest.fn(),
        findRaw: jest.fn(),
        aggregateRaw: jest.fn(),
    };
    // Add model-specific methods if needed
    if (modelName) {
        switch (modelName.toLowerCase()) {
            case 'user':
                return {
                    ...baseModel,
                    findByEmail: jest.fn(),
                    findByUsername: jest.fn(),
                    updatePassword: jest.fn(),
                };
            case 'transaction':
                return {
                    ...baseModel,
                    findByMerchant: jest.fn(),
                    findByStatus: jest.fn(),
                    updateStatus: jest.fn(),
                };
            case 'merchant':
                return {
                    ...baseModel,
                    findByBusinessName: jest.fn(),
                    updateSettings: jest.fn(),
                };
            default:
                return baseModel;
        }
    }
    return baseModel;
}
/**
 * Create a mock Prisma client
 */
function createMockPrismaClient(options = {}) {
    // Define all Prisma models
    const models = [
        'user',
        'merchant',
        'transaction',
        'paymentMethod',
        'alert',
        'notification',
        'webhook',
        'subscription',
        'payment',
        'verification',
        'audit',
        'setting',
        'role',
        'permission',
        'identityVerification',
        'riskAssessment',
        'fraudDetectionConfig',
        'savedReport',
        'reportRun',
        'scheduledReport',
        'listEntry',
        'session',
        'refreshToken',
        'apiKey',
        'webhookEvent',
        'auditLog',
        'systemSetting',
        'userRole',
        'merchantSettings',
        'paymentGateway',
        'currency',
        'country',
        'timeZone',
    ];
    // Create base mock Prisma client
    const mockPrisma = {
        $connect: jest.fn().mockResolvedValue(undefined),
        $disconnect: jest.fn().mockResolvedValue(undefined),
        $transaction: jest.fn((callback) => {
            if (typeof callback === 'function') {
                return Promise.resolve(callback(mockPrisma));
            }
            return Promise.resolve(callback);
        }),
        $executeRaw: jest.fn().mockResolvedValue(0),
        $executeRawUnsafe: jest.fn().mockResolvedValue(0),
        $queryRaw: jest.fn().mockResolvedValue([]),
        $queryRawUnsafe: jest.fn().mockResolvedValue([]),
        $runCommandRaw: jest.fn().mockResolvedValue({}),
        $on: jest.fn(),
        $use: jest.fn(),
        $extends: jest.fn(),
    };
    // Add models to mock Prisma client
    models.forEach((model) => {
        mockPrisma[model] = createMockModel(model);
    });
    // Apply overrides if provided
    if (options.overrides) {
        Object.assign(mockPrisma, options.overrides);
    }
    // Freeze or seal if requested
    if (options.freeze) {
        Object.freeze(mockPrisma);
    }
    else if (options.seal) {
        Object.seal(mockPrisma);
    }
    return mockPrisma;
}
/**
 * Create a mock JWT token
 */
function createMockJwtToken(payload = {}, options = {}) {
    const defaultPayload = {
        sub: '123456789',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour
        ...payload,
    };
    if (options.expiresIn) {
        const expiresInSeconds = parseExpiresIn(options.expiresIn);
        defaultPayload.exp = Math.floor(Date.now() / 1000) + expiresInSeconds;
    }
    if (options.issuer) {
        defaultPayload.iss = options.issuer;
    }
    if (options.audience) {
        defaultPayload.aud = options.audience;
    }
    // Create a mock JWT token (not a real one, just for testing)
    const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url');
    const payloadStr = Buffer.from(JSON.stringify(defaultPayload)).toString('base64url');
    const signature = 'mock-signature';
    return `${header}.${payloadStr}.${signature}`;
}
/**
 * Create mock API response
 */
function createMockApiResponse(data, options = {}) {
    return {
        success: options.success !== false,
        status: options.status || 200,
        message: options.message || 'Success',
        data,
        pagination: options.pagination,
        metadata: options.metadata,
        timestamp: new Date().toISOString(),
    };
}
/**
 * Create mock error response
 */
function createMockErrorResponse(error, options = {}) {
    const errorMessage = error instanceof Error ? error.message : error;
    return {
        success: false,
        status: options.status || 400,
        error: {
            message: errorMessage,
            code: options.code || 'TEST_ERROR',
            details: options.details,
        },
        timestamp: new Date().toISOString(),
    };
}
/**
 * Create mock file upload
 */
function createMockFileUpload(options = {}) {
    return {
        fieldname: 'file',
        originalname: options.filename || 'test-file.txt',
        encoding: '7bit',
        mimetype: options.mimetype || 'text/plain',
        size: options.size || 1024,
        buffer: options.buffer || Buffer.from('test file content'),
        destination: '/tmp',
        filename: options.filename || 'test-file.txt',
        path: `/tmp/${options.filename || 'test-file.txt'}`,
        stream: {},
    };
}
/**
 * Create mock WebSocket
 */
function createMockWebSocket() {
    return {
        send: jest.fn(),
        close: jest.fn(),
        ping: jest.fn(),
        pong: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        readyState: 1, // OPEN
        url: 'ws://localhost:3000',
        protocol: '',
        extensions: '',
        bufferedAmount: 0,
        binaryType: 'blob',
    };
}
/**
 * Helper function to parse expires in string
 */
function parseExpiresIn(expiresIn) {
    const units = {
        s: 1,
        m: 60,
        h: 3600,
        d: 86400,
        w: 604800,
        y: 31536000,
    };
    const match = expiresIn.match(/^(\d+)([smhdwy])$/);
    if (!match) {
        throw new Error(`Invalid expiresIn format: ${expiresIn}`);
    }
    const [, value, unit] = match;
    return parseInt(value, 10) * units[unit];
}
/**
 * Reset all mocks in an object
 */
function resetMocks(obj) {
    Object.values(obj).forEach((value) => {
        if (jest.isMockFunction(value)) {
            value.mockReset();
        }
        else if (typeof value === 'object' && value !== null) {
            resetMocks(value);
        }
    });
}
/**
 * Clear all mocks in an object
 */
function clearMocks(obj) {
    Object.values(obj).forEach((value) => {
        if (jest.isMockFunction(value)) {
            value.mockClear();
        }
        else if (typeof value === 'object' && value !== null) {
            clearMocks(value);
        }
    });
}
//# sourceMappingURL=MockFactories.js.map