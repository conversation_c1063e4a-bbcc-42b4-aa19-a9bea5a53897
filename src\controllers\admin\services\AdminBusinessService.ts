/**
 * Admin Business Service
 * 
 * Handles business logic for admin operations.
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  CreateAdminUserRequest,
  UpdateAdminUserRequest,
  CreateRoleRequest,
  UpdateRoleRequest,
  CreatePermissionRequest,
  UpdatePermissionRequest,
  AdminUserResponse,
  RoleResponse,
  PermissionResponse,
  DashboardDataResponse,
  AdminUserFilters,
  RoleFilters,
  PermissionFilters,
  PaginationParams,
  DashboardStatistics,
  SystemHealthStatus
} from '../types/AdminControllerTypes';

/**
 * Business service for admin operations
 */
export class AdminBusinessService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Get dashboard data
   */
  async getDashboardData(): Promise<DashboardDataResponse> {
    try {
      const [
        merchantCount,
        transactionCount,
        activePaymentMethodsCount,
        recentTransactions,
        recentMerchants,
        totalRevenue
      ] = await Promise.all([
        this.prisma.merchant.count({ where: { isActive: true } }),
        this.prisma.transaction.count(),
        this.prisma.paymentMethod.count({ where: { isActive: true } }),
        this.prisma.transaction.findMany({
          take: 5,
          orderBy: { createdAt: 'desc' },
          include: { merchant: true }
        }),
        this.prisma.merchant.findMany({
          take: 5,
          orderBy: { createdAt: 'desc' }
        }),
        this.prisma.transaction.aggregate({
          _sum: { amount: true },
          where: { status: 'SUCCESS' }
        })
      ]);

      return {
        merchantCount,
        transactionCount,
        activePaymentMethodsCount,
        totalRevenue: totalRevenue._sum.amount || 0,
        recentTransactions,
        recentMerchants
      };
    } catch (error) {
      throw new AppError({
        message: 'Failed to get dashboard data',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error }
      });
    }
  }

  /**
   * Get dashboard statistics
   */
  async getDashboardStatistics(): Promise<DashboardStatistics> {
    try {
      const [
        totalUsers,
        activeUsers,
        totalMerchants,
        activeMerchants,
        totalTransactions,
        successfulTransactions,
        totalRevenue,
        monthlyRevenue
      ] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.user.count({ where: { isActive: true } }),
        this.prisma.merchant.count(),
        this.prisma.merchant.count({ where: { isActive: true } }),
        this.prisma.transaction.count(),
        this.prisma.transaction.count({ where: { status: 'SUCCESS' } }),
        this.prisma.transaction.aggregate({
          _sum: { amount: true },
          where: { status: 'SUCCESS' }
        }),
        this.prisma.transaction.aggregate({
          _sum: { amount: true },
          where: {
            status: 'SUCCESS',
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        })
      ]);

      return {
        totalUsers,
        activeUsers,
        totalMerchants,
        activeMerchants,
        totalTransactions,
        successfulTransactions,
        totalRevenue: totalRevenue._sum.amount || 0,
        monthlyRevenue: monthlyRevenue._sum.amount || 0,
        recentActivity: [] // This would be populated with recent activity data
      };
    } catch (error) {
      throw new AppError({
        message: 'Failed to get dashboard statistics',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error }
      });
    }
  }

  /**
   * Get all admin users with optional filtering and pagination
   */
  async getAdminUsers(
    filters?: AdminUserFilters,
    pagination?: PaginationParams
  ): Promise<{ users: AdminUserResponse[]; total: number }> {
    try {
      const where: any = {};

      // Apply filters
      if (filters?.status) {
        where.status = filters.status;
      }

      if (filters?.roleId) {
        where.user = {
          roles: {
            some: { id: filters.roleId }
          }
        };
      }

      if (filters?.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { email: { contains: filters.search, mode: 'insensitive' } }
        ];
      }

      if (filters?.dateFrom || filters?.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;
        if (filters.dateTo) where.createdAt.lte = filters.dateTo;
      }

      // Build query options
      const queryOptions: any = {
        where,
        include: {
          user: {
            include: {
              roles: {
                include: { permissions: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder || 'desc' };
        }
      }

      // Execute queries
      const [users, total] = await Promise.all([
        this.prisma.admin.findMany(queryOptions),
        this.prisma.admin.count({ where })
      ]);

      return {
        users: users.map(this.mapAdminUserToResponse),
        total
      };
    } catch (error) {
      throw new AppError({
        message: 'Failed to get admin users',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error }
      });
    }
  }

  /**
   * Get admin user by ID
   */
  async getAdminUserById(id: string): Promise<AdminUserResponse> {
    try {
      const adminUser = await this.prisma.admin.findUnique({
        where: { id },
        include: {
          user: {
            include: {
              roles: {
                include: { permissions: true }
              }
            }
          }
        }
      });

      if (!adminUser) {
        throw new AppError({
          message: 'Admin user not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND
        });
      }

      return this.mapAdminUserToResponse(adminUser);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to get admin user',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error }
      });
    }
  }

  /**
   * Create admin user
   */
  async createAdminUser(data: CreateAdminUserRequest, createdById: string): Promise<AdminUserResponse> {
    try {
      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: data.email }
      });

      if (existingUser) {
        throw new AppError({
          message: 'User with this email already exists',
          type: ErrorType.VALIDATION,
          code: ErrorCode.DUPLICATE_RESOURCE
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 10);

      // Create user and admin in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            email: data.email,
            hashedPassword,
            roles: { connect: { id: data.roleId } }
          }
        });

        // Create admin
        const admin = await tx.admin.create({
          data: {
            userId: user.id,
            name: data.name,
            email: data.email,
            hashedPassword,
            role: 'admin',
            createdById
          },
          include: {
            user: {
              include: {
                roles: {
                  include: { permissions: true }
                }
              }
            }
          }
        });

        return admin;
      });

      return this.mapAdminUserToResponse(result);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to create admin user',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error }
      });
    }
  }

  /**
   * Update admin user
   */
  async updateAdminUser(id: string, data: UpdateAdminUserRequest): Promise<AdminUserResponse> {
    try {
      // Get current admin user
      const currentAdmin = await this.prisma.admin.findUnique({
        where: { id },
        include: { user: true }
      });

      if (!currentAdmin) {
        throw new AppError({
          message: 'Admin user not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND
        });
      }

      // Check for duplicate email if email is being updated
      if (data.email && data.email !== currentAdmin.email) {
        const duplicateUser = await this.prisma.user.findFirst({
          where: { 
            email: data.email,
            id: { not: currentAdmin.userId }
          }
        });

        if (duplicateUser) {
          throw new AppError({
            message: 'User with this email already exists',
            type: ErrorType.VALIDATION,
            code: ErrorCode.DUPLICATE_RESOURCE
          });
        }
      }

      // Update admin and user in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Update user if needed
        if (data.email || data.roleId !== undefined || data.isActive !== undefined) {
          await tx.user.update({
            where: { id: currentAdmin.userId },
            data: {
              email: data.email || undefined,
              isActive: data.isActive !== undefined ? data.isActive : undefined,
              roles: data.roleId ? {
                set: [],
                connect: { id: data.roleId }
              } : undefined
            }
          });
        }

        // Update admin
        const admin = await tx.admin.update({
          where: { id },
          data: {
            name: data.name || undefined,
            email: data.email || undefined,
            status: data.isActive !== undefined ? (data.isActive ? 'active' : 'inactive') : undefined
          },
          include: {
            user: {
              include: {
                roles: {
                  include: { permissions: true }
                }
              }
            }
          }
        });

        return admin;
      });

      return this.mapAdminUserToResponse(result);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update admin user',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error }
      });
    }
  }

  /**
   * Delete admin user
   */
  async deleteAdminUser(id: string): Promise<void> {
    try {
      // Check if admin user exists
      const existingAdmin = await this.prisma.admin.findUnique({
        where: { id }
      });

      if (!existingAdmin) {
        throw new AppError({
          message: 'Admin user not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND
        });
      }

      // Delete admin
      await this.prisma.admin.delete({
        where: { id }
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to delete admin user',
        type: ErrorType.INTERNAL,
        code: ErrorCode.INTERNAL_SERVER_ERROR,
        details: { originalError: error instanceof Error ? error.message : error }
      });
    }
  }

  /**
   * Map admin user to response format
   */
  private mapAdminUserToResponse(admin: any): AdminUserResponse {
    return {
      id: admin.id,
      name: admin.name,
      email: admin.email,
      status: admin.status,
      createdAt: admin.createdAt,
      updatedAt: admin.updatedAt,
      user: {
        id: admin.user.id,
        email: admin.user.email,
        isActive: admin.user.isActive,
        roles: admin.user.roles.map((role: any) => ({
          id: role.id,
          name: role.name,
          type: role.type,
          description: role.description,
          isActive: role.isActive,
          createdAt: role.createdAt,
          updatedAt: role.updatedAt,
          permissions: role.permissions.map((permission: any) => ({
            id: permission.id,
            name: permission.name,
            description: permission.description,
            resource: permission.resource,
            action: permission.action,
            isActive: permission.isActive,
            createdAt: permission.createdAt,
            updatedAt: permission.updatedAt
          }))
        }))
      }
    };
  }
}
