/**
 * Prisma Type Extensions
 * 
 * This file extends the Prisma client types to include custom models that are not
 * automatically generated by Prisma.
 */

import { PrismaClient } from '@prisma/client';

/**
 * Alert Aggregation Rule
 */
export interface AlertAggregationRule {
  id: string;
  name: string;
  description: string;
  type: string;
  severity: string;
  timeWindow: number;
  threshold: number;
  groupBy: string[];
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Alert Correlation Rule
 */
export interface AlertCorrelationRule {
  id: string;
  name: string;
  description: string;
  primaryType: string;
  secondaryTypes: string[];
  timeWindow: number;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Alert Aggregation Rule Model
 */
export interface AlertAggregationRuleModel {
  findMany: (args?: any) => Promise<AlertAggregationRule[]>;
  findUnique: (args: { where: { id: string } }) => Promise<AlertAggregationRule | null>;
  create: (args: { data: Omit<AlertAggregationRule, 'id' | 'createdAt' | 'updatedAt'> }) => Promise<AlertAggregationRule>;
  update: (args: { where: { id: string }; data: Partial<Omit<AlertAggregationRule, 'id' | 'createdAt' | 'updatedAt'>> }) => Promise<AlertAggregationRule>;
  delete: (args: { where: { id: string } }) => Promise<AlertAggregationRule>;
}

/**
 * Alert Correlation Rule Model
 */
export interface AlertCorrelationRuleModel {
  findMany: (args?: any) => Promise<AlertCorrelationRule[]>;
  findUnique: (args: { where: { id: string } }) => Promise<AlertCorrelationRule | null>;
  create: (args: { data: Omit<AlertCorrelationRule, 'id' | 'createdAt' | 'updatedAt'> }) => Promise<AlertCorrelationRule>;
  update: (args: { where: { id: string }; data: Partial<Omit<AlertCorrelationRule, 'id' | 'createdAt' | 'updatedAt'>> }) => Promise<AlertCorrelationRule>;
  delete: (args: { where: { id: string } }) => Promise<AlertCorrelationRule>;
}

/**
 * Extended PrismaClient
 */
declare global {
  namespace PrismaClient {
    interface PrismaClient {
      alertAggregationRule: AlertAggregationRuleModel;
      alertCorrelationRule: AlertCorrelationRuleModel;
    }
  }
}
