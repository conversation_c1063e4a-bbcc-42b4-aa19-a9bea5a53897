"use strict";
// jscpd:ignore-file
/**
 * Auth Module
 *
 * This module handles authentication and authorization.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const ModuleFactory_1 = require("../../factories/ModuleFactory");
const logger_1 = require("../../utils/logger");
/**
 * Auth Module
 */
class AuthModule extends ModuleFactory_1.BaseModule {
    /**
     * Constructor
     */
    constructor() {
        super('AuthModule');
    }
    /**
     * Initialize the module
     */
    initialize() {
        logger_1.logger.info('Initializing AuthModule');
        // Get controllers
        const authController = this.controllerFactory.getController('auth');
        // Set up routes
        this.router.post('/login', authController.login);
        this.router.post('/register', authController.register);
        this.router.post('/refresh-token', authController.refreshToken);
        this.router.post('/logout', authController.logout);
        this.router.post('/forgot-password', authController.forgotPassword);
        this.router.post('/reset-password', authController.resetPassword);
        logger_1.logger.info('AuthModule initialized');
    }
}
// Export the module
exports.default = new AuthModule();
//# sourceMappingURL=auth.module.js.map