# Production Environment Configuration for AmazingPay Flow

# Server Configuration
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
API_PREFIX=/api

# Frontend Configuration
FRONTEND_URL=https://your-domain.com
API_URL=https://api.your-domain.com/api
DOMAIN=your-domain.com

# Database Configuration
DATABASE_URL="postgresql://postgres:Amz12344321@localhost:5432/amazingpay_production"
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=Amz12344321
DB_NAME=amazingpay_production
DB_SSL=true

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-production-minimum-32-characters
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=your-domain.com
JWT_AUDIENCE=your-domain.com

# Email Configuration
EMAIL_HOST=smtp.production.com
EMAIL_PORT=587
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-smtp-password
EMAIL_FROM=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-production-twilio-account-sid
TWILIO_AUTH_TOKEN=your-production-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Binance API Configuration
BINANCE_API_URL=https://api.binance.com
BINANCE_API_KEY=your-production-binance-api-key
BINANCE_API_SECRET=your-production-binance-api-secret

# Blockchain API Configuration
TRONSCAN_API_URL=https://apilist.tronscan.org/api
ETHERSCAN_API_URL=https://api.etherscan.io/api
ETHERSCAN_API_KEY=your-production-etherscan-api-key

# Security Configuration
BCRYPT_SALT_ROUNDS=12
CSRF_ENABLED=true
CSRF_SECRET=your-secure-csrf-secret-key-32-characters
XSS_PROTECTION=true
CONTENT_SECURITY_POLICY=true
HSTS=true
HSTS_MAX_AGE=********
FRAME_GUARD=true
NO_SNIFF=true
REFERRER_POLICY=strict-origin-when-cross-origin

# Report Storage Configuration
REPORTS_DIR=/var/lib/amazingpay/reports

# Performance Configuration
MAX_MEMORY_USAGE=*********  # 100MB
BATCH_SIZE=1000
STREAMING_THRESHOLD=*********  # 100MB

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=300000  # 5 minutes
METRICS_RETENTION_HOURS=168  # 7 days

# File Cleanup Configuration
REPORT_RETENTION_HOURS=168  # 7 days
CLEANUP_INTERVAL_HOURS=24   # Daily cleanup

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/var/log/amazingpay/app.log

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com
CORS_CREDENTIALS=true

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/amazingpay.crt
SSL_KEY_PATH=/etc/ssl/private/amazingpay.key

# External Services
WEBHOOK_SECRET=your-webhook-secret
API_VERSION=v1

# Feature Flags
ENABLE_ADVANCED_REPORTING=true
ENABLE_SCHEDULED_REPORTS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DASHBOARD=true

# Database Pool Configuration
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# Application Limits
MAX_REPORT_SIZE_MB=500
MAX_CONCURRENT_REPORTS=5
MAX_SCHEDULED_REPORTS_PER_USER=10

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
