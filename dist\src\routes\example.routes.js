"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const example_controller_1 = require("../controllers/example.controller");
const router = express_1.default.Router();
const exampleController = new example_controller_1.ExampleController();
/**
 * @route GET /api/examples
 * @desc Get all examples
 * @access Public
 */
router.get("/", exampleController.getAll);
/**
 * @route GET /api/examples/:id
 * @desc Get example by ID
 * @access Public
 */
router.get("/:id", exampleController.getById);
/**
 * @route POST /api/examples
 * @desc Create example
 * @access Private
 */
router.post("/", exampleController.create);
/**
 * @route PUT /api/examples/:id
 * @desc Update example
 * @access Private
 */
router.put("/:id", exampleController.update);
/**
 * @route DELETE /api/examples/:id
 * @desc Delete example
 * @access Private
 */
router.delete("/:id", exampleController.delete);
exports.default = router;
//# sourceMappingURL=example.routes.js.map