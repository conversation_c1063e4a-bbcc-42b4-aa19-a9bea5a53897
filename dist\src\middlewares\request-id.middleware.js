"use strict";
// jscpd:ignore-file
/**
 * Request ID Middleware
 *
 * This middleware adds a unique request ID to each request.
 * The request ID is used for tracking requests across the application.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestIdMiddleware = void 0;
const uuid_1 = require("uuid");
const logger_1 = require("../lib/logger");
/**
 * Add request ID to request object and response headers
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
const requestIdMiddleware = (req, res, next) => {
    // Generate a new UUID for the request
    const requestId = req.headers['x-request-id'] || (0, uuid_1.v4)();
    // Add request ID to request object
    req.id = requestId;
    // Add request ID to response headers
    res.setHeader('X-Request-ID', requestId);
    // Add request ID to response locals for use in other middleware
    res.locals.requestId = requestId;
    // Create a request-specific logger
    req.log = (0, logger_1.addRequestId)(requestId);
    next();
};
exports.requestIdMiddleware = requestIdMiddleware;
exports.default = exports.requestIdMiddleware;
//# sourceMappingURL=request-id.middleware.js.map