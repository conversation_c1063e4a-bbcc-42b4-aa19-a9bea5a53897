import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

interface PerformanceMetrics {
  reportType: string;
  format: string;
  recordCount: number;
  generationTimeMs: number;
  fileSizeBytes: number;
  memoryUsageMB: number;
  timestamp: Date;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  metrics: {
    activeReports: number;
    queuedReports: number;
    failedReports: number;
    avgGenerationTime: number;
    diskUsagePercent: number;
    memoryUsagePercent: number;
  };
  alerts: string[];
}

export class ReportMonitoringService {
  private metricsHistory: PerformanceMetrics[] = [];
  private readonly MAX_HISTORY_SIZE = 1000;
  private readonly ALERT_THRESHOLDS = {
    maxGenerationTime: 30000, // 30 seconds
    maxMemoryUsage: 512, // 512 MB
    maxDiskUsage: 80, // 80%
    maxFailureRate: 5, // 5%
  };

  /**
   * Record performance metrics for a report generation
   */
  public recordMetrics(metrics: PerformanceMetrics): void {
    this.metricsHistory.push(metrics);
    
    // Keep only the most recent metrics
    if (this.metricsHistory.length > this.MAX_HISTORY_SIZE) {
      this.metricsHistory = this.metricsHistory.slice(-this.MAX_HISTORY_SIZE);
    }

    // Log performance data
    console.log(`Report Performance: ${metrics.reportType} (${metrics.format}) - ${metrics.recordCount} records in ${metrics.generationTimeMs}ms`);
    
    // Check for performance alerts
    this.checkPerformanceAlerts(metrics);
  }

  /**
   * Get system health status
   */
  public async getSystemHealth(): Promise<SystemHealth> {
    const metrics = await this.calculateSystemMetrics();
    const alerts = this.generateAlerts(metrics);
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (alerts.some(alert => alert.includes('CRITICAL'))) {
      status = 'critical';
    } else if (alerts.length > 0) {
      status = 'warning';
    }

    return {
      status,
      metrics,
      alerts,
    };
  }

  /**
   * Get performance statistics
   */
  public getPerformanceStats(timeRangeHours: number = 24): {
    totalReports: number;
    avgGenerationTime: number;
    avgFileSize: number;
    reportsByType: { [key: string]: number };
    reportsByFormat: { [key: string]: number };
    performanceTrend: 'improving' | 'stable' | 'degrading';
  } {
    const cutoffTime = new Date(Date.now() - timeRangeHours * 60 * 60 * 1000);
    const recentMetrics = this.metricsHistory.filter(m => m.timestamp >= cutoffTime);

    if (recentMetrics.length === 0) {
      return {
        totalReports: 0,
        avgGenerationTime: 0,
        avgFileSize: 0,
        reportsByType: {},
        reportsByFormat: {},
        performanceTrend: 'stable',
      };
    }

    const totalReports = recentMetrics.length;
    const avgGenerationTime = recentMetrics.reduce((sum, m) => sum + m.generationTimeMs, 0) / totalReports;
    const avgFileSize = recentMetrics.reduce((sum, m) => sum + m.fileSizeBytes, 0) / totalReports;

    const reportsByType = recentMetrics.reduce((acc, m) => {
      acc[m.reportType] = (acc[m.reportType] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    const reportsByFormat = recentMetrics.reduce((acc, m) => {
      acc[m.format] = (acc[m.format] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    const performanceTrend = this.calculatePerformanceTrend(recentMetrics);

    return {
      totalReports,
      avgGenerationTime,
      avgFileSize,
      reportsByType,
      reportsByFormat,
      performanceTrend,
    };
  }

  /**
   * Get database performance metrics
   */
  public async getDatabaseMetrics(): Promise<{
    connectionCount: number;
    avgQueryTime: number;
    slowQueries: number;
    errorRate: number;
  }> {
    try {
      // Get active connections (this would depend on your database setup)
      const connectionCount = await this.getActiveConnections();
      
      // Calculate average query time from recent operations
      const avgQueryTime = await this.calculateAvgQueryTime();
      
      // Count slow queries (> 5 seconds)
      const slowQueries = await this.countSlowQueries();
      
      // Calculate error rate
      const errorRate = await this.calculateErrorRate();

      return {
        connectionCount,
        avgQueryTime,
        slowQueries,
        errorRate,
      };
    } catch (error) {
      console.error('Error getting database metrics:', error);
      return {
        connectionCount: 0,
        avgQueryTime: 0,
        slowQueries: 0,
        errorRate: 0,
      };
    }
  }

  /**
   * Get file system metrics
   */
  public async getFileSystemMetrics(): Promise<{
    totalSpace: number;
    usedSpace: number;
    freeSpace: number;
    usagePercent: number;
    reportFileCount: number;
  }> {
    try {
      const reportsDir = path.join(__dirname, '../../reports');
      
      // Get disk usage (this is a simplified version)
      const stats = fs.statSync(reportsDir);
      const reportFiles = fs.readdirSync(reportsDir);
      
      // Calculate total size of report files
      let totalReportSize = 0;
      for (const file of reportFiles) {
        const filePath = path.join(reportsDir, file);
        const fileStats = fs.statSync(filePath);
        totalReportSize += fileStats.size;
      }

      // For a more accurate disk usage, you would use a system call
      // This is a simplified implementation
      const totalSpace = 1000000000; // 1GB (placeholder)
      const usedSpace = totalReportSize;
      const freeSpace = totalSpace - usedSpace;
      const usagePercent = (usedSpace / totalSpace) * 100;

      return {
        totalSpace,
        usedSpace,
        freeSpace,
        usagePercent,
        reportFileCount: reportFiles.length,
      };
    } catch (error) {
      console.error('Error getting file system metrics:', error);
      return {
        totalSpace: 0,
        usedSpace: 0,
        freeSpace: 0,
        usagePercent: 0,
        reportFileCount: 0,
      };
    }
  }

  /**
   * Clean up old report files
   */
  public async cleanupOldReports(maxAgeHours: number = 168): Promise<{
    deletedFiles: number;
    freedSpace: number;
  }> {
    try {
      const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
      
      // Get old saved reports
      const oldReports = await prisma.savedReport.findMany({
        where: {
          createdAt: {
            lt: cutoffTime,
          },
        },
      });

      let deletedFiles = 0;
      let freedSpace = 0;

      for (const report of oldReports) {
        try {
          if (fs.existsSync(report.filePath)) {
            const stats = fs.statSync(report.filePath);
            fs.unlinkSync(report.filePath);
            freedSpace += stats.size;
            deletedFiles++;
          }

          // Delete the database record
          await prisma.savedReport.delete({
            where: { id: report.id },
          });
        } catch (error) {
          console.error(`Error deleting report file ${report.filePath}:`, error);
        }
      }

      console.log(`Cleanup completed: ${deletedFiles} files deleted, ${freedSpace} bytes freed`);

      return {
        deletedFiles,
        freedSpace,
      };
    } catch (error) {
      console.error('Error during cleanup:', error);
      return {
        deletedFiles: 0,
        freedSpace: 0,
      };
    }
  }

  /**
   * Check for performance alerts
   */
  private checkPerformanceAlerts(metrics: PerformanceMetrics): void {
    const alerts: string[] = [];

    if (metrics.generationTimeMs > this.ALERT_THRESHOLDS.maxGenerationTime) {
      alerts.push(`CRITICAL: Report generation took ${metrics.generationTimeMs}ms (threshold: ${this.ALERT_THRESHOLDS.maxGenerationTime}ms)`);
    }

    if (metrics.memoryUsageMB > this.ALERT_THRESHOLDS.maxMemoryUsage) {
      alerts.push(`WARNING: High memory usage: ${metrics.memoryUsageMB}MB (threshold: ${this.ALERT_THRESHOLDS.maxMemoryUsage}MB)`);
    }

    if (alerts.length > 0) {
      console.warn('Performance alerts:', alerts);
      // Here you could send alerts to monitoring systems
    }
  }

  /**
   * Calculate system metrics
   */
  private async calculateSystemMetrics(): Promise<SystemHealth['metrics']> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Get report counts
    const activeReports = await prisma.reportRun.count({
      where: {
        status: 'RUNNING',
      },
    });

    const queuedReports = await prisma.scheduledReport.count({
      where: {
        isActive: true,
        nextRun: {
          lte: now,
        },
      },
    });

    const failedReports = await prisma.reportRun.count({
      where: {
        status: 'FAILED',
        startedAt: {
          gte: oneHourAgo,
        },
      },
    });

    // Calculate average generation time
    const recentMetrics = this.metricsHistory.filter(m => m.timestamp >= oneHourAgo);
    const avgGenerationTime = recentMetrics.length > 0
      ? recentMetrics.reduce((sum, m) => sum + m.generationTimeMs, 0) / recentMetrics.length
      : 0;

    // Get file system metrics
    const fsMetrics = await this.getFileSystemMetrics();

    // Get memory usage (simplified)
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    return {
      activeReports,
      queuedReports,
      failedReports,
      avgGenerationTime,
      diskUsagePercent: fsMetrics.usagePercent,
      memoryUsagePercent,
    };
  }

  /**
   * Generate alerts based on metrics
   */
  private generateAlerts(metrics: SystemHealth['metrics']): string[] {
    const alerts: string[] = [];

    if (metrics.avgGenerationTime > this.ALERT_THRESHOLDS.maxGenerationTime) {
      alerts.push(`CRITICAL: Average generation time is ${metrics.avgGenerationTime}ms`);
    }

    if (metrics.diskUsagePercent > this.ALERT_THRESHOLDS.maxDiskUsage) {
      alerts.push(`WARNING: Disk usage is ${metrics.diskUsagePercent.toFixed(1)}%`);
    }

    if (metrics.memoryUsagePercent > 90) {
      alerts.push(`CRITICAL: Memory usage is ${metrics.memoryUsagePercent.toFixed(1)}%`);
    }

    if (metrics.failedReports > 0) {
      alerts.push(`WARNING: ${metrics.failedReports} reports failed in the last hour`);
    }

    return alerts;
  }

  /**
   * Calculate performance trend
   */
  private calculatePerformanceTrend(metrics: PerformanceMetrics[]): 'improving' | 'stable' | 'degrading' {
    if (metrics.length < 10) return 'stable';

    const recent = metrics.slice(-5);
    const older = metrics.slice(-10, -5);

    const recentAvg = recent.reduce((sum, m) => sum + m.generationTimeMs, 0) / recent.length;
    const olderAvg = older.reduce((sum, m) => sum + m.generationTimeMs, 0) / older.length;

    const change = (recentAvg - olderAvg) / olderAvg;

    if (change < -0.1) return 'improving';
    if (change > 0.1) return 'degrading';
    return 'stable';
  }

  // Placeholder methods for database metrics
  private async getActiveConnections(): Promise<number> {
    // This would depend on your database setup
    return 5; // Placeholder
  }

  private async calculateAvgQueryTime(): Promise<number> {
    // This would require query logging
    return 100; // Placeholder
  }

  private async countSlowQueries(): Promise<number> {
    // This would require query logging
    return 0; // Placeholder
  }

  private async calculateErrorRate(): Promise<number> {
    // This would require error logging
    return 0; // Placeholder
  }
}
