"use strict";
/**
 * Custom Assertions
 *
 * Custom Jest matchers for common test assertions.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseAssertions = exports.AssertionHelpers = exports.customMatchers = void 0;
exports.setupCustomMatchers = setupCustomMatchers;
/**
 * Custom Jest matchers
 */
exports.customMatchers = {
    /**
     * Check if a string is a valid UUID
     */
    toBeValidUUID(received) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        const pass = typeof received === 'string' && uuidRegex.test(received);
        return {
            message: () => pass
                ? `Expected ${received} not to be a valid UUID`
                : `Expected ${received} to be a valid UUID`,
            pass,
        };
    },
    /**
     * Check if a string is a valid email
     */
    toBeValidEmail(received) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const pass = typeof received === 'string' && emailRegex.test(received);
        return {
            message: () => pass
                ? `Expected ${received} not to be a valid email`
                : `Expected ${received} to be a valid email`,
            pass,
        };
    },
    /**
     * Check if a value is a valid date
     */
    toBeValidDate(received) {
        const pass = received instanceof Date && !isNaN(received.getTime());
        return {
            message: () => pass
                ? `Expected ${received} not to be a valid date`
                : `Expected ${received} to be a valid date`,
            pass,
        };
    },
    /**
     * Check if a string is a valid URL
     */
    toBeValidUrl(received) {
        let pass = false;
        try {
            new URL(received);
            pass = true;
        }
        catch {
            pass = false;
        }
        return {
            message: () => pass
                ? `Expected ${received} not to be a valid URL`
                : `Expected ${received} to be a valid URL`,
            pass,
        };
    },
    /**
     * Check if an object has a valid structure
     */
    toHaveValidStructure(received, structure) {
        const pass = validateStructure(received, structure);
        return {
            message: () => pass
                ? `Expected object not to have valid structure`
                : `Expected object to have valid structure`,
            pass,
        };
    },
    /**
     * Check if response matches API response format
     */
    toMatchApiResponse(received, expected) {
        const hasRequiredFields = typeof received === 'object' &&
            received !== null &&
            'success' in received &&
            'status' in received &&
            'data' in received;
        const dataMatches = expected
            ? JSON.stringify(received.data) === JSON.stringify(expected)
            : true;
        const pass = hasRequiredFields && dataMatches;
        return {
            message: () => pass
                ? `Expected response not to match API format`
                : `Expected response to match API format with required fields: success, status, data`,
            pass,
        };
    },
    /**
     * Check if a number is within a range
     */
    toBeWithinRange(received, min, max) {
        const pass = typeof received === 'number' && received >= min && received <= max;
        return {
            message: () => pass
                ? `Expected ${received} not to be within range ${min}-${max}`
                : `Expected ${received} to be within range ${min}-${max}`,
            pass,
        };
    },
    /**
     * Check if a mock was called with valid arguments
     */
    toHaveBeenCalledWithValidArgs(received, validator) {
        const calls = received.mock.calls;
        const pass = calls.length > 0 && calls.every((call) => validator(...call));
        return {
            message: () => pass
                ? `Expected mock not to have been called with valid arguments`
                : `Expected mock to have been called with valid arguments`,
            pass,
        };
    },
};
/**
 * Validate object structure recursively
 */
function validateStructure(obj, structure) {
    if (typeof structure !== 'object' || structure === null) {
        return typeof obj === typeof structure;
    }
    if (Array.isArray(structure)) {
        if (!Array.isArray(obj))
            return false;
        if (structure.length === 0)
            return true;
        return obj.every((item) => validateStructure(item, structure[0]));
    }
    if (typeof obj !== 'object' || obj === null) {
        return false;
    }
    for (const key in structure) {
        if (!(key in obj)) {
            return false;
        }
        if (!validateStructure(obj[key], structure[key])) {
            return false;
        }
    }
    return true;
}
/**
 * Additional assertion helpers
 */
class AssertionHelpers {
    /**
     * Assert that an array contains unique elements
     */
    static assertUniqueArray(array, keyExtractor) {
        const keys = keyExtractor ? array.map(keyExtractor) : array;
        const uniqueKeys = new Set(keys);
        if (keys.length !== uniqueKeys.size) {
            throw new Error('Array contains duplicate elements');
        }
    }
    /**
     * Assert that an object has all required properties
     */
    static assertRequiredProperties(obj, requiredProps) {
        const missingProps = requiredProps.filter((prop) => !(prop in obj));
        if (missingProps.length > 0) {
            throw new Error(`Missing required properties: ${missingProps.join(', ')}`);
        }
    }
    /**
     * Assert that a value is one of the allowed values
     */
    static assertOneOf(value, allowedValues) {
        if (!allowedValues.includes(value)) {
            throw new Error(`Value ${value} is not one of allowed values: ${allowedValues.join(', ')}`);
        }
    }
    /**
     * Assert that a string matches a pattern
     */
    static assertMatchesPattern(value, pattern, message) {
        if (!pattern.test(value)) {
            throw new Error(message ?? `Value "${value}" does not match pattern ${pattern}`);
        }
    }
    /**
     * Assert that an async function throws
     */
    static async assertAsyncThrows(fn, expectedError) {
        let thrownError = null;
        try {
            await fn();
        }
        catch (error) {
            thrownError = error;
        }
        if (!thrownError) {
            throw new Error('Expected function to throw, but it did not');
        }
        if (expectedError) {
            if (typeof expectedError === 'string') {
                expect(thrownError.message).toContain(expectedError);
            }
            else if (expectedError instanceof RegExp) {
                expect(thrownError.message).toMatch(expectedError);
            }
            else if (expectedError instanceof Error) {
                expect(thrownError).toEqual(expectedError);
            }
        }
    }
    /**
     * Assert that an async function does not throw
     */
    static async assertAsyncDoesNotThrow(fn) {
        try {
            await fn();
        }
        catch (error) {
            throw new Error(`Expected function not to throw, but it threw: ${error instanceof Error ? error.message : error}`);
        }
    }
    /**
     * Assert that a value is within a percentage of another value
     */
    static assertWithinPercentage(actual, expected, percentage) {
        const tolerance = Math.abs((expected * percentage) / 100);
        const difference = Math.abs(actual - expected);
        if (difference > tolerance) {
            throw new Error(`Expected ${actual} to be within ${percentage}% of ${expected}, but difference was ${difference}`);
        }
    }
    /**
     * Assert that an array is sorted
     */
    static assertArraySorted(array, compareFn, ascending = true) {
        for (let i = 1; i < array.length; i++) {
            const comparison = this.getComparison(array[i - 1], array[i], compareFn);
            const isCorrectOrder = ascending ? comparison <= 0 : comparison >= 0;
            if (!isCorrectOrder) {
                throw new Error(`Array is not sorted ${ascending ? 'ascending' : 'descending'} at index ${i}`);
            }
        }
    }
    /**
     * Helper method to get comparison result
     */
    static getComparison(a, b, compareFn) {
        if (compareFn) {
            return compareFn(a, b);
        }
        if (a < b)
            return -1;
        if (a > b)
            return 1;
        return 0;
    }
    /**
     * Assert that a mock was called in order
     */
    static assertMockCallOrder(mocks, expectedOrder) {
        const calls = [];
        mocks.forEach((mock, index) => {
            mock.mock.invocationCallOrder.forEach((callOrder) => {
                calls.push({ mock: expectedOrder[index], time: callOrder });
            });
        });
        calls.sort((a, b) => a.time - b.time);
        const actualOrder = calls.map((call) => call.mock);
        expect(actualOrder).toEqual(expectedOrder);
    }
    /**
     * Assert that a value is a valid JSON string
     */
    static assertValidJson(value) {
        try {
            JSON.parse(value);
        }
        catch {
            throw new Error(`Value is not valid JSON: ${value}`);
        }
    }
    /**
     * Assert that two objects are deeply equal ignoring specified keys
     */
    static assertDeepEqualIgnoring(actual, expected, ignoredKeys) {
        const cleanObject = (obj) => {
            if (typeof obj !== 'object' || obj === null)
                return obj;
            if (Array.isArray(obj))
                return obj.map(cleanObject);
            const cleaned = {};
            Object.keys(obj).forEach((key) => {
                if (!ignoredKeys.includes(key)) {
                    cleaned[key] = cleanObject(obj[key]);
                }
            });
            return cleaned;
        };
        expect(cleanObject(actual)).toEqual(cleanObject(expected));
    }
}
exports.AssertionHelpers = AssertionHelpers;
/**
 * Setup custom matchers
 */
function setupCustomMatchers() {
    expect.extend(exports.customMatchers);
}
/**
 * Database assertion helpers
 */
class DatabaseAssertions {
    /**
     * Assert that a record exists in the database
     */
    static async assertRecordExists(prisma, model, where) {
        const record = await prisma[model].findFirst({ where });
        if (!record) {
            throw new Error(`Expected record to exist in ${model} with conditions: ${JSON.stringify(where)}`);
        }
    }
    /**
     * Assert that a record does not exist in the database
     */
    static async assertRecordDoesNotExist(prisma, model, where) {
        const record = await prisma[model].findFirst({ where });
        if (record) {
            throw new Error(`Expected record not to exist in ${model} with conditions: ${JSON.stringify(where)}`);
        }
    }
    /**
     * Assert record count
     */
    static async assertRecordCount(prisma, model, expectedCount, where) {
        const actualCount = await prisma[model].count({ where });
        if (actualCount !== expectedCount) {
            throw new Error(`Expected ${expectedCount} records in ${model}, but found ${actualCount}`);
        }
    }
}
exports.DatabaseAssertions = DatabaseAssertions;
//# sourceMappingURL=CustomAssertions.js.map