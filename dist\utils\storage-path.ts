// jscpd:ignore-file
/**
 * Storage Path Utility
 * 
 * This utility provides functions for managing environment-specific storage paths
 * to ensure complete isolation between production and demo environments.
 */

import path from "path";
import fs from "fs";
import { logger } from "../lib/logger";
import { getEnvironment } from "../config/environment";

/**
 * Base storage directory
 */
const BASE_STORAGE_DIR: any =path.join(process.cwd(), "storage");

/**
 * Get environment-specific storage path
 * @param subPath Optional sub-path within the environment directory
 * @returns Full path to the environment-specific storage location
 */
export const getStoragePath: any =(subPath: string = ""): string => {
    const env = getEnvironment();
    const storagePath: any =path.join(BASE_STORAGE_DIR, env, subPath);
  
    // Ensure the directory exists
    ensureDirectoryExists(storagePath);
  
    return storagePath;
};

/**
 * Get environment-specific logs path
 * @returns Full path to the environment-specific logs directory
 */
export const getLogsPath: any =(): string => {
    const env = getEnvironment();
    const logsPath: any =path.join(process.cwd(), "logs", env);
  
    // Ensure the directory exists
    ensureDirectoryExists(logsPath);
  
    return logsPath;
};

/**
 * Get environment-specific uploads path
 * @param subPath Optional sub-path within the uploads directory
 * @returns Full path to the environment-specific uploads location
 */
export const getUploadsPath: any =(subPath: string = ""): string => {
    return getStoragePath(path.join("uploads", subPath));
};

/**
 * Get environment-specific temp path
 * @param subPath Optional sub-path within the temp directory
 * @returns Full path to the environment-specific temp location
 */
export const getTempPath: any =(subPath: string = ""): string => {
    return getStoragePath(path.join("temp", subPath));
};

/**
 * Get environment-specific cache path
 * @param subPath Optional sub-path within the cache directory
 * @returns Full path to the environment-specific cache location
 */
export const getCachePath: any =(subPath: string = ""): string => {
    return getStoragePath(path.join("cache", subPath));
};

/**
 * Ensure a directory exists, creating it if necessary
 * @param dirPath Directory path to ensure
 */
export const ensureDirectoryExists: any =(dirPath: string): void => {
    if (!fs.existsSync(dirPath)) {
        try {
            fs.mkdirSync(dirPath, { recursive: true });
            logger.debug(`Created directory: ${dirPath}`);
        } catch (error) {
            logger.error(`Failed to create directory: ${dirPath}`, error);
            throw error;
        }
    }
};

/**
 * Initialize storage directories for the current environment
 */
export const initializeStorage: any =(): void => {
    const env = getEnvironment();
  
    // Create base directories
    const directories: any =[
        getStoragePath(),
        getLogsPath(),
        getUploadsPath(),
        getTempPath(),
        getCachePath()
    ];
  
    // Ensure all directories exist
    directories.forEach(ensureDirectoryExists);
  
    logger.info(`Storage initialized for environment: ${env}`);
};

export default {
    getStoragePath,
    getLogsPath,
    getUploadsPath,
    getTempPath,
    getCachePath,
    ensureDirectoryExists,
    initializeStorage
};

