<div align="center">
  <h1>jest-extended</h1>

🃏💪

Additional Jest matchers

</div>

<hr />

[![Build Status](https://img.shields.io/github/actions/workflow/status/jest-community/jest-extended/ci.yaml?style=flat-square)](https://github.com/jest-community/jest-extended/actions/workflows/ci.yaml)
[![Code Coverage](https://img.shields.io/codecov/c/github/jest-community/jest-extended.svg?style=flat-square)](https://codecov.io/github/jest-community/jest-extended)
[![version](https://img.shields.io/npm/v/jest-extended.svg?style=flat-square)](https://www.npmjs.com/package/jest-extended)
[![downloads](https://img.shields.io/npm/dm/jest-extended.svg?style=flat-square)](http://npm-stat.com/charts.html?package=jest-extended&from=2017-09-14)
[![MIT License](https://img.shields.io/npm/l/jest-extended.svg?style=flat-square)](https://github.com/jest-community/jest-extended/blob/main/LICENSE)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](./CONTRIBUTING.md)
[![Examples](https://img.shields.io/badge/%F0%9F%92%A1-examples-ff615b.svg?style=flat-square)](https://github.com/jest-community/jest-extended/tree/main/examples)

## Problem

Jest is an amazing test runner and has some awesome assertion APIs built in by default. However, there are times when
having more specific matchers (assertions) would be far more convenient.

## Solution

jest-extended aims to add additional matchers to Jest's default ones making it easy to test everything 🙌

## Contributing

If you've come here to help contribute - Thanks! Take a look at the [contributing](/CONTRIBUTING.md) docs as a way of getting started.

## Installation

See the [Installation Guide](https://jest-extended.jestcommunity.dev/docs/getting-started/install) on Jest Extended documentation site.

## Setup

See the [Setup instructions](https://jest-extended.jestcommunity.dev/docs/getting-started/setup) on Jest Extended documentation site.

## Matchers

See all available [matchers and interactive repl](https://jest-extended.jestcommunity.dev/docs/matchers) on Jest Extended documentation site.

## LICENSE

[MIT](/LICENSE)
