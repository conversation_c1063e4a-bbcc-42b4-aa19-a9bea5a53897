"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandlerMiddleware = exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = void 0;
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
const AppError_1 = require("../utils/errors/AppError");
const ErrorFactory_1 = require("../utils/errors/ErrorFactory");
const environment_validator_1 = require("../utils/environment-validator");
/**
 * Error handler middleware
 * @param err Error object
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const errorHandler = (err, req, res, next) => {
    // Generate unique error ID for tracking
    const errorId = (0, uuid_1.v4)();
    // Extract request information for logging
    const requestInfo = {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get("user-agent"),
        requestId: req.requestId || "unknown",
        userId: req.user?.id || "anonymous",
        errorId
    };
    // Convert error to AppError
    const appError = err instanceof AppError_1.AppError
        ? err
        : ErrorFactory_1.ErrorFactory.handle(err);
    // Add error ID if not already present
    if (!appError.errorId) {
        appError.errorId = errorId;
    }
    // Log error with appropriate level
    if (appError.isOperational) {
        // Operational errors are expected errors (e.g. validation errors)
        logger_1.logger.warn(`[${errorId}] ${appError.code}: ${appError.message}`, {
            ...requestInfo,
            statusCode: appError.statusCode,
            details: appError.details || undefined
        });
    }
    else {
        // Programming or unknown errors need more attention
        logger_1.logger.error(`[${errorId}] ${appError.code}: ${appError.message}`, {
            ...requestInfo,
            statusCode: appError.statusCode,
            error: err.message,
            stack: err.stack
        });
    }
    // Send response
    res.status(appError.statusCode).json(appError.toResponse((0, environment_validator_1.isDevelopment)()));
};
exports.errorHandler = errorHandler;
/**
 * Not found middleware
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const notFoundHandler = (req, res, next) => {
    const error = ErrorFactory_1.ErrorFactory.notFound(`Route ${req.originalUrl}`);
    next(error);
};
exports.notFoundHandler = notFoundHandler;
/**
 * Async handler to catch errors in async routes
 * @param fn Function to wrap
 * @returns Wrapped function
 */
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};
exports.asyncHandler = asyncHandler;
// Export all middleware functions as a group
exports.errorHandlerMiddleware = {
    errorHandler: exports.errorHandler,
    notFoundHandler: exports.notFoundHandler,
    asyncHandler: exports.asyncHandler
};
exports.default = exports.errorHandlerMiddleware;
//# sourceMappingURL=errorHandler.middleware.js.map