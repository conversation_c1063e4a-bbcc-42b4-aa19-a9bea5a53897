"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const telegram_webhook_controller_ts_1 = require("../controllers/refactored/telegram-webhook.controller.ts");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// Public routes
router.post("/webhook", telegram_webhook_controller_ts_1.TelegramWebhookController.handleWebhook);
// Protected routes
router.post("/set-webhook", auth_1.authenticate, telegram_webhook_controller_ts_1.TelegramWebhookController.setWebhook);
router.delete("/webhook", auth_1.authenticate, telegram_webhook_controller_ts_1.TelegramWebhookController.deleteWebhook);
router.get("/webhook", auth_1.authenticate, telegram_webhook_controller_ts_1.TelegramWebhookController.getWebhookInfo);
router.get("/bot", auth_1.authenticate, telegram_webhook_controller_ts_1.TelegramWebhookController.getBotInfo);
router.post("/test", auth_1.authenticate, telegram_webhook_controller_ts_1.TelegramWebhookController.sendTestMessage);
exports.default = router;
//# sourceMappingURL=telegram.routes.js.map