"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const GenericService_1 = require("../../core/GenericService");
const ErrorFactory_1 = require("../../utils/errors/ErrorFactory");
const logger_1 = require("../../lib/logger");
const RepositoryFactory_1 = require("../../factories/RepositoryFactory");
/**
 * User service
 * This service handles business logic for users
 */
class UserService extends GenericService_1.GenericService {
    /**
     * Create a new user service
     */
    constructor() {
        const repositoryFactory = RepositoryFactory_1.RepositoryFactory.getInstance();
        const repository = repositoryFactory.getRepository('user');
        super(repository, 'User');
        this.userRepository = repository;
    }
    /**
     * Get users with pagination
     * @param options Query options
     * @returns Paginated users
     */
    async getUsers(options) {
        try {
            return await this.userRepository.findUsers(options);
        }
        catch (error) {
            logger_1.logger.error('Error getting users:', error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
    /**
     * Get a user by ID
     * @param id User ID
     * @returns User or null
     */
    async getUserById(id) {
        try {
            return await this.repository.findById(id);
        }
        catch (error) {
            logger_1.logger.error(`Error getting user by ID ${id}:`, error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
    /**
     * Get a user by email
     * @param email User email
     * @returns User or null
     */
    async getUserByEmail(email) {
        try {
            return await this.userRepository.findByEmail(email);
        }
        catch (error) {
            logger_1.logger.error(`Error getting user by email ${email}:`, error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
    /**
     * Create a new user
     * @param data User data
     * @returns Created user
     */
    async createUser(data) {
        try {
            // Check if email is already in use
            const existingUser = await this.getUserByEmail(data.email);
            if (existingUser) {
                throw ErrorFactory_1.ErrorFactory.conflict('Email is already in use');
            }
            // Create user
            const user = await this.repository.create({
                email: data.email,
                hashedPassword: data.hashedPassword,
                name: data.name,
                role: data.role || 'USER',
                merchantId: data.merchantId
            });
            // Log user creation
            logger_1.logger.info(`User created: ${user.id}`, {
                userId: user.id,
                email: user.email,
                role: user.role
            });
            return user;
        }
        catch (error) {
            logger_1.logger.error('Error creating user:', error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
    /**
     * Update a user
     * @param id User ID
     * @param data User data
     * @returns Updated user
     */
    async updateUser(id, data) {
        try {
            // Get user
            const user = await this.getUserById(id);
            // Check if user exists
            if (!user) {
                throw ErrorFactory_1.ErrorFactory.notFound('User', id);
            }
            // Check if email is already in use
            if (data.email && data.email !== user.email) {
                const existingUser = await this.getUserByEmail(data.email);
                if (existingUser) {
                    throw ErrorFactory_1.ErrorFactory.conflict('Email is already in use');
                }
            }
            // Update user
            const updatedUser = await this.repository.update(id, data);
            // Log user update
            logger_1.logger.info(`User updated: ${id}`, {
                userId: id,
                updatedFields: Object.keys(data)
            });
            return updatedUser;
        }
        catch (error) {
            logger_1.logger.error(`Error updating user ${id}:`, error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
    /**
     * Delete a user
     * @param id User ID
     * @returns Deleted user
     */
    async deleteUser(id) {
        try {
            // Get user
            const user = await this.getUserById(id);
            // Check if user exists
            if (!user) {
                throw ErrorFactory_1.ErrorFactory.notFound('User', id);
            }
            // Delete user
            const deletedUser = await this.repository.delete(id);
            // Log user deletion
            logger_1.logger.info(`User deleted: ${id}`, {
                userId: id
            });
            return deletedUser;
        }
        catch (error) {
            logger_1.logger.error(`Error deleting user ${id}:`, error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
    /**
     * Validate user credentials
     * @param email User email
     * @param password User password
     * @returns User if credentials are valid, null otherwise
     */
    async validateCredentials(email, password) {
        try {
            // Get user by email
            const user = await this.getUserByEmail(email);
            // Check if user exists
            if (!user) {
                return null;
            }
            // Check if password is valid
            const bcrypt = await Promise.resolve().then(() => __importStar(require('bcryptjs')));
            const isPasswordValid = await bcrypt.compare(password, user.hashedPassword);
            if (!isPasswordValid) {
                return null;
            }
            return user;
        }
        catch (error) {
            logger_1.logger.error(`Error validating credentials for ${email}:`, error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
    /**
     * Change user password
     * @param id User ID
     * @param currentPassword Current password
     * @param newPassword New password
     * @returns Updated user
     */
    async changePassword(id, currentPassword, newPassword) {
        try {
            // Get user
            const user = await this.getUserById(id);
            // Check if user exists
            if (!user) {
                throw ErrorFactory_1.ErrorFactory.notFound('User', id);
            }
            // Check if current password is valid
            const bcrypt = await Promise.resolve().then(() => __importStar(require('bcryptjs')));
            const isPasswordValid = await bcrypt.compare(currentPassword, user.hashedPassword);
            if (!isPasswordValid) {
                throw ErrorFactory_1.ErrorFactory.validation('Current password is incorrect');
            }
            // Hash new password
            const hashedPassword = await bcrypt.hash(newPassword, 10);
            // Update user
            const updatedUser = await this.repository.update(id, {
                hashedPassword
            });
            // Log password change
            logger_1.logger.info(`User password changed: ${id}`, {
                userId: id
            });
            return updatedUser;
        }
        catch (error) {
            logger_1.logger.error(`Error changing password for user ${id}:`, error);
            throw ErrorFactory_1.ErrorFactory.handle(error);
        }
    }
}
exports.UserService = UserService;
//# sourceMappingURL=user.service.js.map