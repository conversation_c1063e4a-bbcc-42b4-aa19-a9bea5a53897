"use strict";
/**
 * Base Controller
 *
 * This is a base controller class that provides common functionality
 * for all controllers in the application.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
class BaseController {
    /**
     * Send a success response
     */
    sendSuccess(res, data = {}, message = 'Success', statusCode = 200) {
        return res.status(statusCode).json({
            success: true,
            message,
            data
        });
    }
    /**
     * Send an error response
     */
    sendError(res, message = 'Error', statusCode = 500, error = null) {
        return res.status(statusCode).json({
            success: false,
            message,
            error: error ? (error.message || error) : null
        });
    }
    /**
     * Handle async controller methods
     */
    asyncHandler(fn) {
        return async (req, res, next) => {
            try {
                await fn(req, res, next);
            }
            catch (error) {
                next(error);
            }
        };
    }
    /**
     * Validate request
     */
    validateRequest(req, schema) {
        if (!schema)
            return true;
        const { error } = schema.validate(req.body);
        return error ? error.details[0].message : null;
    }
}
exports.BaseController = BaseController;
//# sourceMappingURL=BaseController.js.map