"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainEntry = toContainEntry;
const utils_1 = require("../utils");
function toContainEntry(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    // @ts-expect-error containsEntry takes an any type
    const pass = (0, utils_1.containsEntry)((a, b) => this.equals(a, b, this.customTesters), actual, expected);
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainEntry') +
                '\n\n' +
                'Expected object to not contain entry:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainEntry') +
                '\n\n' +
                'Expected object to contain entry:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
