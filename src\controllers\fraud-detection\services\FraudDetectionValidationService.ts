/**
 * Fraud Detection Validation Service
 * 
 * Handles input validation for fraud detection operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import {
  AssessTransactionRiskRequest,
  UpdateFraudConfigRequest,
  ValidationError,
  RiskLevel
} from '../types/FraudDetectionControllerTypes';

/**
 * Validation service for fraud detection operations
 */
export class FraudDetectionValidationService {
  
  /**
   * Validate transaction risk assessment request
   */
  validateAssessTransactionRisk(data: any): AssessTransactionRiskRequest {
    const errors: ValidationError[] = [];

    if (!data.transactionId) {
      errors.push({ field: 'transactionId', message: 'Transaction ID is required' });
    } else if (typeof data.transactionId !== 'string' || !this.isValidUUID(data.transactionId)) {
      errors.push({ field: 'transactionId', message: 'Invalid transaction ID format', value: data.transactionId });
    }

    if (!data.ipAddress) {
      errors.push({ field: 'ipAddress', message: 'IP address is required' });
    } else if (typeof data.ipAddress !== 'string' || !this.isValidIPAddress(data.ipAddress)) {
      errors.push({ field: 'ipAddress', message: 'Invalid IP address format', value: data.ipAddress });
    }

    if (data.userAgent !== undefined && typeof data.userAgent !== 'string') {
      errors.push({ field: 'userAgent', message: 'User agent must be a string', value: data.userAgent });
    }

    if (data.deviceId !== undefined && typeof data.deviceId !== 'string') {
      errors.push({ field: 'deviceId', message: 'Device ID must be a string', value: data.deviceId });
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors }
      });
    }

    return {
      transactionId: data.transactionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent || 'Unknown',
      deviceId: data.deviceId || 'Unknown'
    };
  }

  /**
   * Validate fraud configuration update request
   */
  validateUpdateFraudConfig(data: any): UpdateFraudConfigRequest {
    const errors: ValidationError[] = [];

    if (data.flagThreshold !== undefined) {
      if (typeof data.flagThreshold !== 'number' || data.flagThreshold < 0 || data.flagThreshold > 100) {
        errors.push({ field: 'flagThreshold', message: 'Flag threshold must be a number between 0 and 100', value: data.flagThreshold });
      }
    }

    if (data.blockThreshold !== undefined) {
      if (typeof data.blockThreshold !== 'number' || data.blockThreshold < 0 || data.blockThreshold > 100) {
        errors.push({ field: 'blockThreshold', message: 'Block threshold must be a number between 0 and 100', value: data.blockThreshold });
      }
    }

    if (data.flagThreshold !== undefined && data.blockThreshold !== undefined) {
      if (data.blockThreshold <= data.flagThreshold) {
        errors.push({ field: 'blockThreshold', message: 'Block threshold must be greater than flag threshold' });
      }
    }

    if (data.autoBlock !== undefined && typeof data.autoBlock !== 'boolean') {
      errors.push({ field: 'autoBlock', message: 'Auto block must be a boolean', value: data.autoBlock });
    }

    if (data.factorWeights !== undefined) {
      if (typeof data.factorWeights !== 'object' || data.factorWeights === null) {
        errors.push({ field: 'factorWeights', message: 'Factor weights must be an object', value: data.factorWeights });
      } else {
        Object.entries(data.factorWeights).forEach(([key, value]) => {
          if (typeof value !== 'number' || value < 0 || value > 1) {
            errors.push({ 
              field: `factorWeights.${key}`, 
              message: 'Factor weight must be a number between 0 and 1',
              value 
            });
          }
        });
      }
    }

    if (data.highRiskCountries !== undefined) {
      if (!Array.isArray(data.highRiskCountries)) {
        errors.push({ field: 'highRiskCountries', message: 'High risk countries must be an array', value: data.highRiskCountries });
      } else {
        data.highRiskCountries.forEach((country: any, index: number) => {
          if (typeof country !== 'string' || !this.isValidCountryCode(country)) {
            errors.push({ 
              field: `highRiskCountries[${index}]`, 
              message: 'Invalid country code format',
              value: country
            });
          }
        });
      }
    }

    if (data.highRiskIpRanges !== undefined) {
      if (!Array.isArray(data.highRiskIpRanges)) {
        errors.push({ field: 'highRiskIpRanges', message: 'High risk IP ranges must be an array', value: data.highRiskIpRanges });
      } else {
        data.highRiskIpRanges.forEach((ipRange: any, index: number) => {
          if (typeof ipRange !== 'string' || !this.isValidIPRange(ipRange)) {
            errors.push({ 
              field: `highRiskIpRanges[${index}]`, 
              message: 'Invalid IP range format',
              value: ipRange
            });
          }
        });
      }
    }

    if (data.maxTransactionAmount !== undefined) {
      if (typeof data.maxTransactionAmount !== 'number' || data.maxTransactionAmount <= 0) {
        errors.push({ field: 'maxTransactionAmount', message: 'Max transaction amount must be a positive number', value: data.maxTransactionAmount });
      }
    }

    if (data.maxTransactionsPerHour !== undefined) {
      if (typeof data.maxTransactionsPerHour !== 'number' || data.maxTransactionsPerHour <= 0 || !Number.isInteger(data.maxTransactionsPerHour)) {
        errors.push({ field: 'maxTransactionsPerHour', message: 'Max transactions per hour must be a positive integer', value: data.maxTransactionsPerHour });
      }
    }

    if (data.maxTransactionsPerDay !== undefined) {
      if (typeof data.maxTransactionsPerDay !== 'number' || data.maxTransactionsPerDay <= 0 || !Number.isInteger(data.maxTransactionsPerDay)) {
        errors.push({ field: 'maxTransactionsPerDay', message: 'Max transactions per day must be a positive integer', value: data.maxTransactionsPerDay });
      }
    }

    if (errors.length > 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors }
      });
    }

    const result: UpdateFraudConfigRequest = {};
    if (data.flagThreshold !== undefined) result.flagThreshold = data.flagThreshold;
    if (data.blockThreshold !== undefined) result.blockThreshold = data.blockThreshold;
    if (data.autoBlock !== undefined) result.autoBlock = data.autoBlock;
    if (data.factorWeights !== undefined) result.factorWeights = data.factorWeights;
    if (data.highRiskCountries !== undefined) result.highRiskCountries = data.highRiskCountries;
    if (data.highRiskIpRanges !== undefined) result.highRiskIpRanges = data.highRiskIpRanges;
    if (data.maxTransactionAmount !== undefined) result.maxTransactionAmount = data.maxTransactionAmount;
    if (data.maxTransactionsPerHour !== undefined) result.maxTransactionsPerHour = data.maxTransactionsPerHour;
    if (data.maxTransactionsPerDay !== undefined) result.maxTransactionsPerDay = data.maxTransactionsPerDay;

    return result;
  }

  /**
   * Validate merchant ID parameter
   */
  validateMerchantId(merchantId: any): number {
    if (!merchantId) {
      throw new AppError({
        message: 'Merchant ID is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD
      });
    }

    const parsedId = parseInt(merchantId, 10);
    if (isNaN(parsedId) || parsedId <= 0) {
      throw new AppError({
        message: 'Merchant ID must be a positive integer',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      });
    }

    return parsedId;
  }

  /**
   * Validate transaction ID parameter
   */
  validateTransactionId(transactionId: any): string {
    if (!transactionId) {
      throw new AppError({
        message: 'Transaction ID is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD
      });
    }

    if (!this.isValidUUID(transactionId)) {
      throw new AppError({
        message: 'Transaction ID must be a valid UUID',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      });
    }

    return transactionId;
  }

  /**
   * Validate date range parameters
   */
  validateDateRange(startDate?: any, endDate?: any): { start: Date; end: Date } {
    let start: Date;
    let end: Date;

    if (startDate) {
      start = new Date(startDate);
      if (isNaN(start.getTime())) {
        throw new AppError({
          message: 'Invalid start date format',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT
        });
      }
    } else {
      // Default to 30 days ago
      start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    if (endDate) {
      end = new Date(endDate);
      if (isNaN(end.getTime())) {
        throw new AppError({
          message: 'Invalid end date format',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT
        });
      }
    } else {
      // Default to now
      end = new Date();
    }

    if (start >= end) {
      throw new AppError({
        message: 'Start date must be before end date',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      });
    }

    // Limit to maximum 1 year range
    const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
    if (end.getTime() - start.getTime() > maxRange) {
      throw new AppError({
        message: 'Date range cannot exceed 1 year',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      });
    }

    return { start, end };
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: any): { page: number; limit: number; sortBy?: string; sortOrder?: 'asc' | 'desc' } {
    const page = query.page ? parseInt(query.page, 10) : 1;
    const limit = query.limit ? parseInt(query.limit, 10) : 10;

    if (isNaN(page) || page < 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      });
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      });
    }

    const result: any = { page, limit };

    if (query.sortBy) {
      const validSortFields = ['createdAt', 'score', 'level', 'isFlagged', 'isBlocked'];
      if (!validSortFields.includes(query.sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT
        });
      }
      result.sortBy = query.sortBy;
    }

    if (query.sortOrder) {
      if (!['asc', 'desc'].includes(query.sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either "asc" or "desc"',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT
        });
      }
      result.sortOrder = query.sortOrder;
    }

    return result;
  }

  /**
   * Check if string is a valid UUID
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Check if string is a valid IP address
   */
  private isValidIPAddress(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * Check if string is a valid country code
   */
  private isValidCountryCode(code: string): boolean {
    // ISO 3166-1 alpha-2 country codes (2 letters)
    return /^[A-Z]{2}$/.test(code);
  }

  /**
   * Check if string is a valid IP range (CIDR notation)
   */
  private isValidIPRange(range: string): boolean {
    const cidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;
    return cidrRegex.test(range);
  }
}
