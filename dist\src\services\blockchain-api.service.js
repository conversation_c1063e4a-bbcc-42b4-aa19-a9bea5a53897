"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockchainApiService = exports.BlockchainTransactionStatus = exports.BlockchainNetwork = void 0;
// jscpd:ignore-file
const axios_1 = __importDefault(require("axios"));
const BaseService_1 = require("../base/BaseService");
const logger_1 = require("../../utils/logger");
const config_1 = require("../../config");
const environment_1 = require("../../config/environment");
const service_config_1 = require("../../utils/service-config");
/**
 * Blockchain network types
 */
var BlockchainNetwork;
(function (BlockchainNetwork) {
    BlockchainNetwork["TRC20"] = "trc20";
    BlockchainNetwork["ERC20"] = "erc20";
    BlockchainNetwork["BEP20"] = "bep20";
    BlockchainNetwork["POLYGON"] = "polygon";
})(BlockchainNetwork || (exports.BlockchainNetwork = BlockchainNetwork = {}));
/**
 * Blockchain transaction status
 */
var BlockchainTransactionStatus;
(function (BlockchainTransactionStatus) {
    BlockchainTransactionStatus["PENDING"] = "pending";
    BlockchainTransactionStatus["CONFIRMED"] = "confirmed";
    BlockchainTransactionStatus["FAILED"] = "failed";
    BlockchainTransactionStatus["NOT_FOUND"] = "not_found";
})(BlockchainTransactionStatus || (exports.BlockchainTransactionStatus = BlockchainTransactionStatus = {}));
/**
 * Service for interacting with blockchain APIs
 */
class BlockchainApiService extends BaseService_1.BaseService {
    /**
     * Create a new BlockchainApiService instance
     */
    constructor() {
        super();
        // Initialize network configurations
        this.networkConfigs = new Map();
        this.initializeNetworkConfigs();
        // Set default retry configuration
        this.defaultRetryConfig = {
            maxRetries: 3,
            initialDelayMs: 1000,
            maxDelayMs: 10000,
            retryStatusCodes: [408, 429, 500, 502, 503, 504],
            retryErrorCodes: ["ECONNABORTED", "ETIMEDOUT", "ECONNRESET", "ECONNREFUSED"]
        };
        logger_1.logger.debug(`Initialized Blockchain API service for ${(0, environment_1.getEnvironment)()} environment`);
    }
    /**
     * Initialize network configurations
     */
    initializeNetworkConfigs() {
        // TRC20 (TRON)
        this.networkConfigs.set(BlockchainNetwork.TRC20, {
            apiKey: (0, service_config_1.getApiKey)("tron", config_1.config.blockchain.tronApiKey || ""),
            apiUrl: (0, service_config_1.getApiUrl)("tron", "https://api.trongrid.io"),
            explorerUrl: "https://tronscan.org"
        });
        // ERC20 (Ethereum)
        this.networkConfigs.set(BlockchainNetwork.ERC20, {
            apiKey: (0, service_config_1.getApiKey)("etherscan", config_1.config.blockchain.etherscanApiKey || ""),
            apiUrl: (0, service_config_1.getApiUrl)("etherscan", "https://api.etherscan.io/api"),
            explorerUrl: "https://etherscan.io"
        });
        // BEP20 (Binance Smart Chain)
        this.networkConfigs.set(BlockchainNetwork.BEP20, {
            apiKey: (0, service_config_1.getApiKey)("bscscan", config_1.config.blockchain.bscscanApiKey || ""),
            apiUrl: (0, service_config_1.getApiUrl)("bscscan", "https://api.bscscan.com/api"),
            explorerUrl: "https://bscscan.com"
        });
        // POLYGON
        this.networkConfigs.set(BlockchainNetwork.POLYGON, {
            apiKey: (0, service_config_1.getApiKey)("polygonscan", config_1.config.blockchain.polygonscanApiKey || ""),
            apiUrl: (0, service_config_1.getApiUrl)("polygonscan", "https://api.polygonscan.com/api"),
            explorerUrl: "https://polygonscan.com"
        });
        // Log service configuration (masked)
        (0, service_config_1.logServiceConfig)("tron");
        (0, service_config_1.logServiceConfig)("etherscan");
        (0, service_config_1.logServiceConfig)("bscscan");
        (0, service_config_1.logServiceConfig)("polygonscan");
    }
    /**
     * Get network configuration
     * @param network Blockchain network
     * @returns Network configuration
     */
    getNetworkConfig(network) {
        const config = this.networkConfigs.get(network);
        if (!config) {
            throw this.createError(`Unsupported blockchain network: ${network}`, 400, "BLOCKCHAIN_ERROR");
        }
        return config;
    }
    /**
     * Make a request with retry capability
     * @param config Request configuration
     * @param retryConfig Retry configuration
     * @returns Response data
     */
    async makeRequestWithRetry(config, retryConfig = this.defaultRetryConfig) {
        const { maxRetries, initialDelayMs, maxDelayMs, retryStatusCodes, retryErrorCodes } = retryConfig;
        let lastError;
        let attempt = 0;
        while (attempt < maxRetries) {
            try {
                const response = await (0, axios_1.default)(config);
                return response.data;
            }
            catch (error) {
                lastError = error;
                // Check if we should retry based on status code or error code
                const shouldRetry = (error.response && retryStatusCodes.includes(error.response.status)) ||
                    (error.code && retryErrorCodes.includes(error.code));
                if (!shouldRetry || attempt >= maxRetries - 1) {
                    break;
                }
                // Calculate exponential backoff delay
                const delay = Math.min(initialDelayMs * Math.pow(2, attempt), maxDelayMs);
                logger_1.logger.debug(`Retrying request (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);
                await new Promise(resolve => setTimeout(resolve, delay));
                attempt++;
            }
        }
        throw lastError;
    }
    /**
     * Verify a blockchain transaction
     * @param txHash Transaction hash
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address (for ERC20/BEP20/POLYGON)
     * @returns Verification result
     */
    async verifyTransaction(txHash, network, toAddress, amount, tokenAddress) {
        try {
            logger_1.logger.info(`Verifying ${network} transaction: ${txHash}`);
            // Validate inputs
            if (!txHash || !network || !toAddress) {
                return {
                    success: false,
                    message: "Missing required parameters"
                };
            }
            // Get transaction details based on network
            switch (network) {
                case BlockchainNetwork.TRC20:
                    return this.verifyTronTransaction(txHash, toAddress, amount);
                case BlockchainNetwork.ERC20:
                case BlockchainNetwork.BEP20:
                case BlockchainNetwork.POLYGON:
                    return this.verifyEVMTransaction(txHash, network, toAddress, amount, tokenAddress);
                default:
                    return {
                        success: false,
                        message: `Unsupported blockchain network: ${network}`
                    };
            }
        }
        catch (error) {
            logger_1.logger.error(`Error verifying transaction: ${error}`);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Verify a TRON transaction
     * @param txHash Transaction hash
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @returns Verification result
     */
    async verifyTronTransaction(txHash, toAddress, amount) {
        try {
            const networkConfig = this.getNetworkConfig(BlockchainNetwork.TRC20);
            const apiUrl = `${networkConfig.apiUrl}/v1/transactions/${txHash}`;
            const response = await this.makeRequestWithRetry({
                method: 'GET',
                url: apiUrl,
                headers: {
                    'TRON-PRO-API-KEY': networkConfig.apiKey
                }
            });
            // Process and return the transaction details
            return this.processTronTransaction(response, toAddress, amount);
        }
        catch (error) {
            logger_1.logger.error(`Error verifying TRON transaction: ${error}`);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Process TRON transaction response
     * @param response API response
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @returns Processed transaction
     */
    processTronTransaction(response, toAddress, amount) {
        // Implementation details for processing TRON transaction
        // This would be specific to the TRON API response format
        // For now, return a placeholder
        return {
            success: true,
            data: { hash: response.txID || '',
                from: response.from || '',
                to: response.to || '',
                value: response.amount || '0',
                timestamp: response.timestamp || 0,
                status: BlockchainTransactionStatus.CONFIRMED,
                network: BlockchainNetwork.TRC20
            }
        };
    }
    /**
     * Verify an EVM transaction (Ethereum, BSC, Polygon)
     * @param txHash Transaction hash
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address
     * @returns Verification result
     */
    async verifyEVMTransaction(txHash, network, toAddress, amount, tokenAddress) {
        try {
            const networkConfig = this.getNetworkConfig(network);
            // Different API endpoint based on whether it's a token transfer or native currency
            const apiUrl = tokenAddress
                ? `${networkConfig.apiUrl}?module=account&action=tokentx&txhash=${txHash}&apikey=${networkConfig.apiKey}`
                : `${networkConfig.apiUrl}?module=proxy&action=eth_getTransactionReceipt&txhash=${txHash}&apikey=${networkConfig.apiKey}`;
            const response = await this.makeRequestWithRetry({
                method: 'GET',
                url: apiUrl
            });
            // Process and return the transaction details
            return this.processEVMTransaction(response, network, toAddress, amount, tokenAddress);
        }
        catch (error) {
            logger_1.logger.error(`Error verifying ${network} transaction: ${error}`);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * Process EVM transaction response
     * @param response API response
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address
     * @returns Processed transaction
     */
    processEVMTransaction(response, network, toAddress, amount, tokenAddress) {
        // Implementation details for processing EVM transaction
        // This would be specific to the EVM API response format
        // For now, return a placeholder
        return {
            success: true,
            data: { hash: response.hash || '',
                from: response.from || '',
                to: response.to || '',
                value: response.value || '0',
                timestamp: response.timeStamp || 0,
                status: BlockchainTransactionStatus.CONFIRMED,
                network: network
            }
        };
    }
}
exports.BlockchainApiService = BlockchainApiService;
//# sourceMappingURL=blockchain-api.service.js.map