/**
 * Test Types and Interfaces
 *
 * Centralized type definitions for the test utility system.
 */

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
// import { BaseController } from '../../../core/BaseController';
// import { BaseService } from '../../../core/BaseService';
// import { BaseRepository } from '../../../core/BaseRepository';

// Placeholder types for missing base classes
type BaseController = any;
type BaseService = any;
type BaseRepository = any;

/**
 * Mock request interface
 */
export interface MockRequest extends Partial<Request> {
  params?: any;
  query?: any;
  body?: any;
  headers?: any;
  user?: any;
  session?: any;
  cookies?: any;
  ip?: string;
  method?: string;
  url?: string;
  originalUrl?: string;
  path?: string;
  protocol?: string;
  secure?: boolean;
  xhr?: boolean;
}

/**
 * Mock response interface
 */
export interface MockResponse extends Partial<Response> {
  status?: jest.Mock;
  json?: jest.Mock;
  send?: jest.Mock;
  end?: jest.Mock;
  redirect?: jest.Mock;
  cookie?: jest.Mock;
  clearCookie?: jest.Mock;
  locals?: any;
  statusCode?: number;
  headersSent?: boolean;
}

/**
 * Mock next function type
 */
export type MockNext = jest.Mock;

/**
 * Base test options interface
 */
export interface BaseTestOptions {
  description?: string;
  setup?: () => void | Promise<void>;
  cleanup?: () => void | Promise<void>;
  beforeEach?: () => void | Promise<void>;
  afterEach?: () => void | Promise<void>;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}

/**
 * Controller test options
 */
export interface ControllerTestOptions extends BaseTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: MockNext;
  expectedStatus?: number;
  expectedResponse?: any;
  expectedError?: any;
  controllerSetup?: (controller: BaseController) => void | Promise<void>;
  controllerCleanup?: (controller: BaseController) => void | Promise<void>;
  validateResponse?: (res: MockResponse) => void | Promise<void>;
  validateRequest?: (req: MockRequest) => void | Promise<void>;
}

/**
 * Service test options
 */
export interface ServiceTestOptions extends BaseTestOptions {
  args?: any[];
  expectedResult?: any;
  expectedError?: any;
  serviceSetup?: (service: BaseService) => void | Promise<void>;
  serviceCleanup?: (service: BaseService) => void | Promise<void>;
  mockDependencies?: Record<string, any>;
  validateResult?: (result: any) => void | Promise<void>;
  mockMethods?: Record<string, jest.Mock>;
}

/**
 * Repository test options
 */
export interface RepositoryTestOptions extends ServiceTestOptions {
  mockPrisma?: PrismaClient;
  mockTransaction?: boolean;
  mockTransactionResult?: any;
  repositorySetup?: (repository: BaseRepository) => void | Promise<void>;
  repositoryCleanup?: (repository: BaseRepository) => void | Promise<void>;
  mockQueries?: Record<string, any>;
  validateQuery?: (query: any) => void | Promise<void>;
}

/**
 * Middleware test options
 */
export interface MiddlewareTestOptions extends BaseTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: MockNext;
  expectedStatus?: number;
  expectedResponse?: any;
  expectedError?: any;
  middlewareSetup?: (req: MockRequest, res: MockResponse, next: MockNext) => void | Promise<void>;
  middlewareCleanup?: (req: MockRequest, res: MockResponse, next: MockNext) => void | Promise<void>;
  expectNextCalled?: boolean;
  expectNextCalledWith?: any;
}

/**
 * Validator test options
 */
export interface ValidatorTestOptions extends BaseTestOptions {
  req?: MockRequest;
  res?: MockResponse;
  next?: MockNext;
  expectedStatus?: number;
  expectedResponse?: any;
  expectedError?: any;
  args?: any[];
  validatorSetup?: () => void | Promise<void>;
  validatorCleanup?: () => void | Promise<void>;
  expectValidationError?: boolean;
  expectedValidationMessage?: string;
}

/**
 * Utility test options
 */
export interface UtilityTestOptions extends BaseTestOptions {
  args?: any[];
  expectedResult?: any;
  expectedError?: any;
  utilitySetup?: () => void | Promise<void>;
  utilityCleanup?: () => void | Promise<void>;
  mockGlobals?: Record<string, any>;
  restoreGlobals?: boolean;
}

/**
 * API test options
 */
export interface ApiTestOptions extends BaseTestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url?: string;
  headers?: Record<string, string>;
  body?: any;
  query?: Record<string, any>;
  expectedStatus?: number;
  expectedResponse?: any;
  expectedError?: any;
  auth?: {
    type: 'bearer' | 'basic' | 'api-key';
    token?: string;
    username?: string;
    password?: string;
    apiKey?: string;
  };
}

/**
 * Database test options
 */
export interface DatabaseTestOptions extends BaseTestOptions {
  seedData?: any[];
  cleanupData?: boolean;
  useTransaction?: boolean;
  isolateTest?: boolean;
  mockPrisma?: boolean;
  expectedQueries?: string[];
  validateData?: (data: any) => void | Promise<void>;
}

/**
 * Performance test options
 */
export interface PerformanceTestOptions extends BaseTestOptions {
  maxExecutionTime?: number;
  memoryLimit?: number;
  iterations?: number;
  warmupIterations?: number;
  measureMemory?: boolean;
  measureCpu?: boolean;
}

/**
 * Test result interface
 */
export interface TestResult {
  success: boolean;
  result?: any;
  error?: Error;
  executionTime?: number;
  memoryUsage?: number;
  metadata?: Record<string, any>;
}

/**
 * Test suite configuration
 */
export interface TestSuiteConfig {
  name: string;
  description?: string;
  setup?: () => void | Promise<void>;
  teardown?: () => void | Promise<void>;
  beforeEach?: () => void | Promise<void>;
  afterEach?: () => void | Promise<void>;
  timeout?: number;
  parallel?: boolean;
  retries?: number;
}

/**
 * Mock factory options
 */
export interface MockFactoryOptions {
  partial?: boolean;
  deep?: boolean;
  freeze?: boolean;
  seal?: boolean;
  overrides?: Record<string, any>;
}

/**
 * Data generator options
 */
export interface DataGeneratorOptions {
  count?: number;
  seed?: string | number;
  locale?: string;
  unique?: boolean;
  format?: string;
  constraints?: Record<string, any>;
}

/**
 * Test environment configuration
 */
export interface TestEnvironmentConfig {
  database?: {
    url: string;
    reset: boolean;
    seed: boolean;
  };
  redis?: {
    url: string;
    flush: boolean;
  };
  external?: {
    mockApis: boolean;
    mockServices: boolean;
  };
  logging?: {
    level: string;
    silent: boolean;
  };
}

/**
 * Test assertion helpers type
 */
export interface TestAssertions {
  toBeValidUUID: (received: string) => jest.CustomMatcherResult;
  toBeValidEmail: (received: string) => jest.CustomMatcherResult;
  toBeValidDate: (received: any) => jest.CustomMatcherResult;
  toBeValidUrl: (received: string) => jest.CustomMatcherResult;
  toHaveValidStructure: (received: any, structure: any) => jest.CustomMatcherResult;
  toMatchApiResponse: (received: any, expected: any) => jest.CustomMatcherResult;
  toBeWithinRange: (received: number, min: number, max: number) => jest.CustomMatcherResult;
  toHaveBeenCalledWithValidArgs: (
    received: jest.Mock,
    validator: Function
  ) => jest.CustomMatcherResult;
}

/**
 * Test context interface
 */
export interface TestContext {
  testName: string;
  suiteName: string;
  startTime: number;
  endTime?: number;
  metadata: Record<string, any>;
  mocks: Record<string, jest.Mock>;
  cleanup: (() => void | Promise<void>)[];
}

/**
 * Test runner configuration
 */
export interface TestRunnerConfig {
  parallel?: boolean;
  maxConcurrency?: number;
  timeout?: number;
  retries?: number;
  bail?: boolean;
  verbose?: boolean;
  coverage?: boolean;
  reporters?: string[];
}

/**
 * Mock data templates
 */
export interface MockDataTemplates {
  user: any;
  merchant: any;
  transaction: any;
  payment: any;
  subscription: any;
  notification: any;
  webhook: any;
  alert: any;
  audit: any;
  setting: any;
}

/**
 * Test fixture interface
 */
export interface TestFixture {
  name: string;
  data: any;
  dependencies?: string[];
  setup?: () => void | Promise<void>;
  teardown?: () => void | Promise<void>;
}

/**
 * Test scenario interface
 */
export interface TestScenario {
  name: string;
  description?: string;
  steps: TestStep[];
  setup?: () => void | Promise<void>;
  teardown?: () => void | Promise<void>;
  expectedOutcome?: any;
}

/**
 * Test step interface
 */
export interface TestStep {
  name: string;
  action: () => void | Promise<void>;
  validation?: () => void | Promise<void>;
  timeout?: number;
  retries?: number;
}

/**
 * Test error types
 */
export enum TestErrorType {
  SETUP_ERROR = 'SETUP_ERROR',
  EXECUTION_ERROR = 'EXECUTION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CLEANUP_ERROR = 'CLEANUP_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  ASSERTION_ERROR = 'ASSERTION_ERROR',
}

/**
 * Test error class
 */
export class TestError extends Error {
  type: TestErrorType;
  context?: TestContext;
  originalError?: Error;

  constructor(message: string, type: TestErrorType, context?: TestContext, originalError?: Error) {
    super(message);
    this.name = 'TestError';
    this.type = type;
    this.context = context;
    this.originalError = originalError;
  }
}
