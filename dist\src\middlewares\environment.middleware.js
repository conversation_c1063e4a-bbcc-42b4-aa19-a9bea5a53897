"use strict";
// jscpd:ignore-file
/**
 * Environment Middleware
 *
 * This middleware provides functions for validating environment-specific requests
 * for the production environment.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.addEnvironmentInfo = exports.validateProductionEnvironment = exports.validateEnvironment = void 0;
const logger_1 = require("../lib/logger");
const environment_1 = require("../config/environment");
const domain_1 = require("../utils/domain");
const error_middleware_1 = require("./error.middleware");
/**
 * Validate that the request is for the correct environment
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const validateEnvironment = (req, res, next) => {
    try {
        const env = (0, environment_1.getEnvironment)();
        // Add environment to request for logging
        req.environment = env;
        // Add environment header to response
        res.setHeader("X-Environment", env);
        // Check if the request has a site domain header
        const siteDomain = req.headers["x-site-domain"];
        if (siteDomain) {
            // Validate that the domain is valid for the current environment
            if (!(0, domain_1.isValidDomainForEnvironment)(siteDomain)) {
                logger_1.logger.warn(`Invalid domain for environment: ${siteDomain} in ${env} environment`, {
                    domain: siteDomain,
                    environment: env,
                    ip: req.ip,
                    path: req.path,
                    method: req.method,
                    requestId: req.requestId
                });
                return next(new error_middleware_1.AppError("Invalid domain for this environment", 403, true));
            }
        }
        // Check if the request has an environment header
        const requestEnv = req.headers["x-environment"];
        if (requestEnv && requestEnv !== env) {
            logger_1.logger.warn(`Environment mismatch: ${requestEnv} != ${env}`, {
                requestEnvironment: requestEnv,
                serverEnvironment: env,
                ip: req.ip,
                path: req.path,
                method: req.method,
                requestId: req.requestId
            });
            return next(new error_middleware_1.AppError("Environment mismatch", 403, true));
        }
        next();
    }
    catch (error) {
        logger_1.logger.error("Error validating environment", error);
        next(error);
    }
};
exports.validateEnvironment = validateEnvironment;
/**
 * Validate that the request is for the production environment
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const validateProductionEnvironment = (req, res, next) => {
    if (!(0, environment_1.isProduction)()) {
        logger_1.logger.warn(`Production-only endpoint accessed from ${(0, environment_1.getEnvironment)()} environment`, {
            environment: (0, environment_1.getEnvironment)(),
            ip: req.ip,
            path: req.path,
            method: req.method,
            requestId: req.requestId
        });
        return next(new error_middleware_1.AppError("This endpoint is only available in production", 403, true));
    }
    next();
};
exports.validateProductionEnvironment = validateProductionEnvironment;
// Demo environment validation has been removed - only production is supported
/**
 * Add environment information to the request
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const addEnvironmentInfo = (req, res, next) => {
    // Add environment to request for logging
    req.environment = (0, environment_1.getEnvironment)();
    // Add environment header to response
    res.setHeader("X-Environment", req.environment);
    // Always production mode
    res.setHeader("X-Production-Environment", "true");
    next();
};
exports.addEnvironmentInfo = addEnvironmentInfo;
exports.default = {
    validateEnvironment: exports.validateEnvironment,
    validateProductionEnvironment: exports.validateProductionEnvironment,
    addEnvironmentInfo: exports.addEnvironmentInfo
};
//# sourceMappingURL=environment.middleware.js.map