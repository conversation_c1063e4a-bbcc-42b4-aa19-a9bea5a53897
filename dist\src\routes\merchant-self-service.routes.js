"use strict";
// jscpd:ignore-file
/**
 * Merchant Self-Service Routes
 *
 * This file defines the routes for merchant self-service tools.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const merchant_self_service_controller_1 = require("../controllers/merchant-self-service.controller");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const merchantAccessMiddleware_1 = require("../middlewares/merchantAccessMiddleware");
const router = express_1.default.Router();
const merchantSelfServiceController = new merchant_self_service_controller_1.MerchantSelfServiceController();
/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Create API key
 * @access Private (Admin, Merchant)
 */
router.post("/merchants/:merchantId/api-keys", authMiddleware_1.authMiddleware, merchantAccessMiddleware_1.merchantAccessMiddleware, merchantSelfServiceController.createApiKey);
/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/api-keys
 * @desc Get merchant API keys
 * @access Private (Admin, Merchant)
 */
router.get("/merchants/:merchantId/api-keys", authMiddleware_1.authMiddleware, merchantAccessMiddleware_1.merchantAccessMiddleware, merchantSelfServiceController.getMerchantApiKeys);
/**
 * @route DELETE /api/merchant-self-service/api-keys/:apiKeyId
 * @desc Delete API key
 * @access Private (Admin, Merchant)
 */
router.delete("/api-keys/:apiKeyId", authMiddleware_1.authMiddleware, merchantSelfServiceController.deleteApiKey);
/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Create webhook
 * @access Private (Admin, Merchant)
 */
router.post("/merchants/:merchantId/webhooks", authMiddleware_1.authMiddleware, merchantAccessMiddleware_1.merchantAccessMiddleware, merchantSelfServiceController.createWebhook);
/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/webhooks
 * @desc Get merchant webhooks
 * @access Private (Admin, Merchant)
 */
router.get("/merchants/:merchantId/webhooks", authMiddleware_1.authMiddleware, merchantAccessMiddleware_1.merchantAccessMiddleware, merchantSelfServiceController.getMerchantWebhooks);
/**
 * @route PUT /api/merchant-self-service/webhooks/:webhookId
 * @desc Update webhook
 * @access Private (Admin, Merchant)
 */
router.put("/webhooks/:webhookId", authMiddleware_1.authMiddleware, merchantSelfServiceController.updateWebhook);
/**
 * @route DELETE /api/merchant-self-service/webhooks/:webhookId
 * @desc Delete webhook
 * @access Private (Admin, Merchant)
 */
router.delete("/webhooks/:webhookId", authMiddleware_1.authMiddleware, merchantSelfServiceController.deleteWebhook);
/**
 * @route POST /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Set notification preference
 * @access Private (Admin, Merchant)
 */
router.post("/merchants/:merchantId/notification-preferences", authMiddleware_1.authMiddleware, merchantAccessMiddleware_1.merchantAccessMiddleware, merchantSelfServiceController.setNotificationPreference);
/**
 * @route GET /api/merchant-self-service/merchants/:merchantId/notification-preferences
 * @desc Get merchant notification preferences
 * @access Private (Admin, Merchant)
 */
router.get("/merchants/:merchantId/notification-preferences", authMiddleware_1.authMiddleware, merchantAccessMiddleware_1.merchantAccessMiddleware, merchantSelfServiceController.getMerchantNotificationPreferences);
exports.default = router;
//# sourceMappingURL=merchant-self-service.routes.js.map