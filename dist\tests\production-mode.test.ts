// jscpd:ignore-file
/**
 * production-mode Tests
 *
 * This file contains tests for the production-mode module using the test utility.
 */

import { production-modeController } from '../controllers/production-mode.controller';
import { production-modeService } from '../services/production-mode.service';
import { production-modeRepository } from '../repositories/production-mode.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';
import { production-modeService } from '../services/production-mode.service';
import { production-modeRepository } from '../repositories/production-mode.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';


// Mock the production-modeService
jest.mock('../services/production-mode.service');

describe('production-mode Module Tests', () => {
  // Controller tests
  testControllerSuite('production-modeController', production-modeController, {
    getAll: {, description: 'should get all production-modes',
      expectedStatus: 200,
      expectedResponse: {, success: true },
    },
    getById: {, description: 'should get production-mode by ID',
      req: createMockRequest({, params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: {, success: true },
    },
    create: {, description: 'should create production-mode',
      req: createMockRequest({, body: { name: 'Test' } }),
      expectedStatus: 201,
      expectedResponse: {, success: true },
    },
    update: {, description: 'should update production-mode',
      req: createMockRequest({, params: { id: '1' }, body: {, name: 'Updated' } }),
      expectedStatus: 200,
      expectedResponse: {, success: true },
    },
    delete: {, description: 'should delete production-mode',
      req: createMockRequest({, params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: {, success: true, message: 'production-mode deleted successfully' },
    },
  });

  // Service tests
  describe('production-modeService', () => {
    let service;
    let mockRepository;

    beforeEach(() => {
      mockRepository = {
        findAll: jest.fn(),
        findById: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      };

      service = new production-modeService();
      service.production-modeRepository = mockRepository;
    });

    it('should find all production-modes', async () => {
      mockRepository.findAll.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: any =await testService(service, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockRepository.findAll).toHaveBeenCalled();
    });
  });

  // Repository tests
  describe('production-modeRepository', () => {
    let repository;
    let mockPrisma;

    beforeEach(() => {
      mockPrisma = createMockPrismaClient();
      repository = new production-modeRepository();
      repository.prisma = mockPrisma;
    });

    it('should find all production-modes', async () => {
      mockPrisma.production-mode.findMany.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: any =await testRepository(repository, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockPrisma.production-mode.findMany).toHaveBeenCalled();
    });
  });
});
