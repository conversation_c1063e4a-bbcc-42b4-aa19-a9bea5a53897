"use strict";
// jscpd:ignore-file
/**
 * Response Formatter Utility
 *
 * This utility provides standardized response formatting for the application.
 * It includes functions for formatting success and error responses.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatApiResponse = exports.sendPaginated = exports.sendAccepted = exports.sendNoContent = exports.sendCreated = exports.sendSuccess = void 0;
/**
 * Send success response
 * @param res Express response
 * @param data Response data
 * @param message Success message
 * @param statusCode HTTP status code
 * @param pagination Pagination information
 */
const sendSuccess = (res, data, message, statusCode = 200, pagination) => {
    // Create success response
    const successResponse = {
        status: 'success',
        statusCode,
        data,
        timestamp: new Date().toISOString()
    };
    // Add message if provided
    if (message) {
        successResponse.message = message;
    }
    // Add request ID if available
    if (res.locals.requestId) {
        successResponse.requestId = res.locals.requestId;
    }
    // Add pagination if provided
    if (pagination) {
        successResponse.pagination = pagination;
    }
    // Send success response
    res.status(statusCode).json(successResponse);
};
exports.sendSuccess = sendSuccess;
/**
 * Send created response
 * @param res Express response
 * @param data Response data
 * @param message Success message
 */
const sendCreated = (res, data, message = 'Resource created successfully') => {
    (0, exports.sendSuccess)(res, data, message, 201);
};
exports.sendCreated = sendCreated;
/**
 * Send no content response
 * @param res Express response
 */
const sendNoContent = (res) => {
    res.status(204).end();
};
exports.sendNoContent = sendNoContent;
/**
 * Send accepted response
 * @param res Express response
 * @param message Success message
 */
const sendAccepted = (res, message = 'Request accepted for processing') => {
    (0, exports.sendSuccess)(res, null, message, 202);
};
exports.sendAccepted = sendAccepted;
/**
 * Send paginated response
 * @param res Express response
 * @param data Response data
 * @param page Current page
 * @param limit Items per page
 * @param total Total items
 * @param message Success message
 */
const sendPaginated = (res, data, page, limit, total, message) => {
    const totalPages = Math.ceil(total / limit);
    (0, exports.sendSuccess)(res, data, message, 200, {
        page,
        limit,
        total,
        totalPages
    });
};
exports.sendPaginated = sendPaginated;
/**
 * Format API response for controllers
 * @param res Express response
 */
const formatApiResponse = (res) => {
    return {
        success: (data, message, statusCode = 200) => (0, exports.sendSuccess)(res, data, message, statusCode),
        created: (data, message) => (0, exports.sendCreated)(res, data, message),
        noContent: () => (0, exports.sendNoContent)(res),
        accepted: (message) => (0, exports.sendAccepted)(res, message),
        paginated: (data, page, limit, total, message) => (0, exports.sendPaginated)(res, data, page, limit, total, message)
    };
};
exports.formatApiResponse = formatApiResponse;
exports.default = {
    sendSuccess: exports.sendSuccess,
    sendCreated: exports.sendCreated,
    sendNoContent: exports.sendNoContent,
    sendAccepted: exports.sendAccepted,
    sendPaginated: exports.sendPaginated,
    formatApiResponse: exports.formatApiResponse
};
//# sourceMappingURL=response-formatter.js.map