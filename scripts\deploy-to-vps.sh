#!/bin/bash

# AmazingPay Flow - VPS Deployment Script
# This script automates deployment to DigitalOcean VPS with aaPanel

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🚀 AmazingPay Flow - VPS Deployment${NC}"
    echo -e "${BLUE}==================================\n${NC}"
}

print_section() {
    echo -e "${CYAN}📦 $1${NC}"
    echo "----------------------------------------"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to collect deployment information
collect_deployment_info() {
    print_section "DEPLOYMENT CONFIGURATION"
    
    echo -n "Enter your VPS IP address: "
    read VPS_IP
    
    echo -n "Enter your domain name (e.g., amazingpay.com): "
    read DOMAIN_NAME
    
    echo -n "Enter your GitHub repository URL: "
    read GITHUB_REPO
    
    echo -n "Enter PostgreSQL password for 'amazingpay' user: "
    read -s DB_PASSWORD
    echo
    
    echo -n "Enter your email for SSL certificate: "
    read SSL_EMAIL
    
    print_info "Configuration collected:"
    print_info "VPS IP: $VPS_IP"
    print_info "Domain: $DOMAIN_NAME"
    print_info "Repository: $GITHUB_REPO"
    print_info "SSL Email: $SSL_EMAIL"
    
    echo -n "Proceed with deployment? (y/n): "
    read CONFIRM
    
    if [ "$CONFIRM" != "y" ]; then
        print_error "Deployment cancelled"
        exit 1
    fi
}

# Function to create VPS setup script
create_vps_setup_script() {
    print_section "Creating VPS setup script"
    
    cat > vps-setup.sh << EOF
#!/bin/bash

# AmazingPay Flow - VPS Initial Setup Script
# Run this script on your VPS server

set -e

echo "🚀 Setting up VPS for AmazingPay Flow..."

# Update system
apt update && apt upgrade -y

# Install basic tools
apt install -y curl wget git unzip build-essential

# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Install PM2 globally
npm install -g pm2

# Create application directory
mkdir -p /www/wwwroot
cd /www/wwwroot

# Clone repository
if [ -d "amazingpay-flow" ]; then
    rm -rf amazingpay-flow
fi

git clone $GITHUB_REPO amazingpay-flow
cd amazingpay-flow

# Set permissions
chown -R www-data:www-data /www/wwwroot/amazingpay-flow
chmod -R 755 /www/wwwroot/amazingpay-flow

# Install dependencies
npm ci --production

echo "✅ VPS setup completed!"
echo "Next: Configure aaPanel and database"
EOF
    
    chmod +x vps-setup.sh
    print_success "VPS setup script created: vps-setup.sh"
}

# Function to create environment configuration
create_environment_config() {
    print_section "Creating environment configuration"
    
    cat > .env.vps.production << EOF
# AmazingPay Flow - VPS Production Environment

# Server Configuration
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
API_PREFIX=/api

# Domain Configuration
FRONTEND_URL=https://$DOMAIN_NAME
API_URL=https://$DOMAIN_NAME/api
DOMAIN=$DOMAIN_NAME
CORS_ORIGIN=https://$DOMAIN_NAME

# Database Configuration
DATABASE_URL="postgresql://amazingpay:$DB_PASSWORD@localhost:5432/amazingpay_production"
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=amazingpay
DB_PASSWORD=$DB_PASSWORD
DB_NAME=amazingpay_production
DB_SSL=false

# JWT Configuration (Generate secure secrets)
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=$DOMAIN_NAME
JWT_AUDIENCE=$DOMAIN_NAME

# Security Configuration
BCRYPT_SALT_ROUNDS=12
CSRF_ENABLED=true
CSRF_SECRET=$(openssl rand -base64 32)
XSS_PROTECTION=true
CONTENT_SECURITY_POLICY=true
HSTS=true
HSTS_MAX_AGE=31536000
FRAME_GUARD=true
NO_SNIFF=true
REFERRER_POLICY=strict-origin-when-cross-origin

# Rate Limiting (Production settings)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
AUTH_RATE_LIMIT_WINDOW_MS=3600000
AUTH_RATE_LIMIT_MAX=5
PASSWORD_RESET_RATE_LIMIT_WINDOW_MS=3600000
PASSWORD_RESET_RATE_LIMIT_MAX=2

# Logging Configuration
LOG_LEVEL=warn
LOG_FORMAT=combined
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/www/wwwroot/amazingpay-flow/logs/app.log
LOG_MAX_SIZE=50m
LOG_MAX_FILES=14

# Email Configuration (Update with your SMTP settings)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=noreply@$DOMAIN_NAME

# Monitoring Configuration
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PERFORMANCE_MONITORING=true
EOF
    
    print_success "Environment configuration created: .env.vps.production"
}

# Function to create PM2 ecosystem file
create_pm2_config() {
    print_section "Creating PM2 configuration"
    
    cat > ecosystem.vps.config.js << EOF
module.exports = {
  apps: [{
    name: 'amazingpay-flow',
    script: './dist/index.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    log_file: '/www/wwwroot/amazingpay-flow/logs/combined.log',
    out_file: '/www/wwwroot/amazingpay-flow/logs/out.log',
    error_file: '/www/wwwroot/amazingpay-flow/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF
    
    print_success "PM2 configuration created: ecosystem.vps.config.js"
}

# Function to create Nginx configuration
create_nginx_config() {
    print_section "Creating Nginx configuration"
    
    mkdir -p nginx-config
    
    cat > nginx-config/amazingpay-flow.conf << EOF
# AmazingPay Flow - Nginx Configuration for aaPanel

server {
    listen 80;
    server_name $DOMAIN_NAME;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_NAME;
    
    # SSL configuration (will be managed by aaPanel)
    # ssl_certificate and ssl_certificate_key will be added by aaPanel
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' https:; frame-ancestors 'none';";
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=auth:10m rate=1r/s;
    
    # API routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Auth routes with stricter rate limiting
    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;
        
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:3002/api/health;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Static files (if serving frontend)
    location / {
        root /www/wwwroot/amazingpay-flow/public;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Security: Block access to sensitive files
    location ~ /\.(env|git|svn) {
        deny all;
        return 404;
    }
    
    location ~ /(node_modules|src|scripts)/ {
        deny all;
        return 404;
    }
}
EOF
    
    print_success "Nginx configuration created: nginx-config/amazingpay-flow.conf"
}

# Function to create deployment script for VPS
create_vps_deployment_script() {
    print_section "Creating VPS deployment script"
    
    cat > deploy-on-vps.sh << EOF
#!/bin/bash

# AmazingPay Flow - VPS Deployment Script
# Run this script ON your VPS server after initial setup

set -e

echo "🚀 Deploying AmazingPay Flow on VPS..."

# Navigate to application directory
cd /www/wwwroot/amazingpay-flow

# Pull latest changes
git pull origin main

# Install/update dependencies
npm ci --production

# Copy environment file
cp .env.vps.production .env.production

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate deploy

# Seed database (if needed)
npx prisma db seed || echo "Seeding skipped or failed"

# Build application
npm run build

# Create logs directory
mkdir -p logs

# Stop existing PM2 processes
pm2 stop amazingpay-flow || echo "No existing process to stop"

# Start application with PM2
pm2 start ecosystem.vps.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup (run once)
pm2 startup || echo "PM2 startup already configured"

echo "✅ Deployment completed!"
echo "🌐 Application should be running on: https://$DOMAIN_NAME"
echo "🏥 Health check: https://$DOMAIN_NAME/health"
echo "📊 PM2 status: pm2 status"
EOF
    
    chmod +x deploy-on-vps.sh
    print_success "VPS deployment script created: deploy-on-vps.sh"
}

# Function to create backup script for VPS
create_vps_backup_script() {
    print_section "Creating VPS backup script"
    
    cat > backup-vps.sh << EOF
#!/bin/bash

# AmazingPay Flow - VPS Backup Script
# Run this script on your VPS to create backups

set -e

DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/www/backup/amazingpay"
APP_DIR="/www/wwwroot/amazingpay-flow"

# Create backup directory
mkdir -p \$BACKUP_DIR

echo "📦 Creating backup for \$DATE..."

# Database backup
echo "Backing up database..."
pg_dump -h localhost -U amazingpay amazingpay_production > \$BACKUP_DIR/db_\$DATE.sql
gzip \$BACKUP_DIR/db_\$DATE.sql

# Application backup
echo "Backing up application..."
tar -czf \$BACKUP_DIR/app_\$DATE.tar.gz \$APP_DIR \\
    --exclude=node_modules \\
    --exclude=logs \\
    --exclude=.git \\
    --exclude=dist

# Configuration backup
echo "Backing up configuration..."
cp \$APP_DIR/.env.production \$BACKUP_DIR/env_\$DATE.backup
cp \$APP_DIR/ecosystem.vps.config.js \$BACKUP_DIR/pm2_\$DATE.backup

# Cleanup old backups (keep 7 days)
find \$BACKUP_DIR -name "*.gz" -mtime +7 -delete
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
find \$BACKUP_DIR -name "*.backup" -mtime +7 -delete

echo "✅ Backup completed: \$BACKUP_DIR"
echo "📊 Backup size: \$(du -sh \$BACKUP_DIR | cut -f1)"
EOF
    
    chmod +x backup-vps.sh
    print_success "VPS backup script created: backup-vps.sh"
}

# Function to create deployment instructions
create_deployment_instructions() {
    print_section "Creating deployment instructions"
    
    cat > VPS_DEPLOYMENT_INSTRUCTIONS.md << EOF
# 🚀 AmazingPay Flow - VPS Deployment Instructions

## 📋 DEPLOYMENT CHECKLIST

### ✅ **STEP 1: Prepare VPS Server**

1. **Create DigitalOcean Droplet**
   - Ubuntu 22.04 LTS
   - 2 vCPUs, 4GB RAM, 80GB SSD
   - Note the IP address: **$VPS_IP**

2. **Install aaPanel**
   \`\`\`bash
   ssh root@$VPS_IP
   wget -O install.sh http://www.aapanel.com/script/install-ubuntu_6.0_en.sh && bash install.sh aapanel
   \`\`\`

3. **Access aaPanel**
   - URL: http://$VPS_IP:7800
   - Install: Nginx, PostgreSQL, Node.js 18, PM2

### ✅ **STEP 2: Setup Database**

1. **In aaPanel → Database → PostgreSQL**
   - Database: amazingpay_production
   - Username: amazingpay
   - Password: $DB_PASSWORD

### ✅ **STEP 3: Deploy Application**

1. **Upload files to VPS**
   \`\`\`bash
   scp vps-setup.sh root@$VPS_IP:/root/
   scp .env.vps.production root@$VPS_IP:/root/
   scp ecosystem.vps.config.js root@$VPS_IP:/root/
   scp deploy-on-vps.sh root@$VPS_IP:/root/
   \`\`\`

2. **Run setup on VPS**
   \`\`\`bash
   ssh root@$VPS_IP
   chmod +x /root/vps-setup.sh
   /root/vps-setup.sh
   \`\`\`

3. **Deploy application**
   \`\`\`bash
   cp /root/.env.vps.production /www/wwwroot/amazingpay-flow/
   cp /root/ecosystem.vps.config.js /www/wwwroot/amazingpay-flow/
   cp /root/deploy-on-vps.sh /www/wwwroot/amazingpay-flow/
   cd /www/wwwroot/amazingpay-flow
   chmod +x deploy-on-vps.sh
   ./deploy-on-vps.sh
   \`\`\`

### ✅ **STEP 4: Configure Domain & SSL**

1. **In aaPanel → Website → Add Site**
   - Domain: $DOMAIN_NAME
   - Document Root: /www/wwwroot/amazingpay-flow/public

2. **Configure Reverse Proxy**
   - Target: http://127.0.0.1:3002
   - Directory: /api

3. **Setup SSL**
   - Go to SSL → Let's Encrypt
   - Email: $SSL_EMAIL
   - Apply certificate

### ✅ **STEP 5: Verify Deployment**

\`\`\`bash
# Check PM2 status
pm2 status

# Test health endpoint
curl https://$DOMAIN_NAME/health

# Check logs
pm2 logs amazingpay-flow
\`\`\`

## 🌐 **YOUR DEPLOYED APPLICATION**

- **URL**: https://$DOMAIN_NAME
- **API**: https://$DOMAIN_NAME/api
- **Health**: https://$DOMAIN_NAME/health
- **Admin**: <EMAIL> / admin123!@#

## 🔧 **MANAGEMENT COMMANDS**

\`\`\`bash
# Application management
pm2 status                    # Check status
pm2 restart amazingpay-flow   # Restart app
pm2 logs amazingpay-flow      # View logs
pm2 monit                     # Monitor resources

# Backup
./backup-vps.sh               # Create backup

# Update deployment
git pull && ./deploy-on-vps.sh
\`\`\`

## 🎉 **DEPLOYMENT COMPLETE!**

Your AmazingPay Flow is now live at: **https://$DOMAIN_NAME** 🚀
EOF
    
    print_success "Deployment instructions created: VPS_DEPLOYMENT_INSTRUCTIONS.md"
}

# Main execution
main() {
    print_header
    
    collect_deployment_info
    create_vps_setup_script
    create_environment_config
    create_pm2_config
    create_nginx_config
    create_vps_deployment_script
    create_vps_backup_script
    create_deployment_instructions
    
    echo ""
    print_success "🎉 VPS deployment package created successfully!"
    echo ""
    print_info "📁 Files created:"
    print_info "   ✅ vps-setup.sh - Initial VPS setup"
    print_info "   ✅ .env.vps.production - Environment configuration"
    print_info "   ✅ ecosystem.vps.config.js - PM2 configuration"
    print_info "   ✅ nginx-config/amazingpay-flow.conf - Nginx configuration"
    print_info "   ✅ deploy-on-vps.sh - Deployment script"
    print_info "   ✅ backup-vps.sh - Backup script"
    print_info "   ✅ VPS_DEPLOYMENT_INSTRUCTIONS.md - Step-by-step guide"
    echo ""
    print_info "🚀 Next steps:"
    print_info "1. Follow VPS_DEPLOYMENT_INSTRUCTIONS.md"
    print_info "2. Create your DigitalOcean droplet"
    print_info "3. Install aaPanel and required software"
    print_info "4. Upload and run the deployment scripts"
    print_info "5. Configure domain and SSL"
    echo ""
    print_success "🎯 Your AmazingPay Flow will be live at: https://$DOMAIN_NAME"
}

# Run deployment preparation
main "\$@"
