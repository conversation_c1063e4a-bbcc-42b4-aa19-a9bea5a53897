"use strict";
// jscpd:ignore-file
/**
 * API Response Middleware
 *
 * This middleware adds response formatting functions to the response object.
 * It provides a consistent API response format across the application.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiResponseMiddleware = void 0;
const response_formatter_1 = require("../utils/response-formatter");
/**
 * Add API response formatting functions to response object
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
const apiResponseMiddleware = (req, res, next) => {
    // Add API response formatting functions to response object
    res.api = (0, response_formatter_1.formatApiResponse)(res);
    next();
};
exports.apiResponseMiddleware = apiResponseMiddleware;
exports.default = exports.apiResponseMiddleware;
//# sourceMappingURL=api-response.middleware.js.map