"use strict";
// jscpd:ignore-file
/**
 * Verification Monitoring Routes
 *
 * This file defines routes for the verification monitoring dashboard.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const verification_monitoring_controller_1 = require("../controllers/monitoring/verification-monitoring.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
const verificationMonitoringController = new verification_monitoring_controller_1.VerificationMonitoringController();
// All routes require authentication and admin authorization
router.use(auth_middleware_1.authenticate);
router.use((0, auth_middleware_1.authorize)(["admin"]));
/**
 * @route GET /api/monitoring/verification/metrics
 * @desc Get verification metrics
 * @access Private (Admin)
 */
router.get("/metrics", verificationMonitoringController.getVerificationMetrics.bind(verificationMonitoringController));
/**
 * @route GET /api/monitoring/verification/errors
 * @desc Get verification errors
 * @access Private (Admin)
 */
router.get("/errors", verificationMonitoringController.getVerificationErrors.bind(verificationMonitoringController));
/**
 * @route GET /api/monitoring/verification/methods
 * @desc Get verification method metrics
 * @access Private (Admin)
 */
router.get("/methods", verificationMonitoringController.getVerificationMethodMetrics.bind(verificationMonitoringController));
exports.default = router;
//# sourceMappingURL=verification-monitoring.routes.js.map