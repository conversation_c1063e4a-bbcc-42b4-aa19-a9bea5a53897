{"version": 3, "file": "performance-monitor.middleware.js", "sourceRoot": "", "sources": ["../../../src/middlewares/performance-monitor.middleware.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;;;;GAQG;;;AAgIH,sDAwCC;AAKD,0DAQC;AAOD,gEA2BC;AApND,0CAAuC;AACvC,0EAA8D;AA0B9D,qBAAqB;AACrB,MAAM,OAAO,GAAuB;IAChC,YAAY,EAAE,CAAC;IACf,UAAU,EAAE,CAAC;IACb,iBAAiB,EAAE,CAAC;IACpB,eAAe,EAAE,CAAC;IAClB,eAAe,EAAE,MAAM,CAAC,SAAS;IACjC,eAAe,EAAE,EAAE;IACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;CACxB,CAAC;AAEF;;;GAGG;AACI,MAAM,kBAAkB,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACtF,2DAA2D;IAC3D,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QACvD,OAAO,IAAI,EAAE,CAAC;IAClB,CAAC;IAED,oBAAoB;IACpB,MAAM,SAAS,GAAO,OAAO,CAAC,MAAM,EAAE,CAAC;IAEvC,iBAAiB;IACjB,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,0BAA0B;QACtB,MAAM,MAAM,GAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7C,MAAM,cAAc,GAAO,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QAElE,wBAAwB;QACxB,OAAO,CAAC,YAAY,EAAE,CAAC;QACvB,OAAO,CAAC,iBAAiB,IAAI,cAAc,CAAC;QAC5C,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QAC5E,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QAE5E,gCAAgC;QAChC,MAAM,OAAO,GAAO,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC;QAC1C,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;QAED,+BAA+B;QAC/B,MAAM,QAAQ,GAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEjD,wCAAwC;QACxC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG;gBAChC,YAAY,EAAE,CAAC;gBACf,UAAU,EAAE,CAAC;gBACb,iBAAiB,EAAE,CAAC;gBACpB,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,MAAM,CAAC,SAAS;aACpC,CAAC;QACN,CAAC;QAED,0BAA0B;QAC1B,MAAM,cAAc,GAAO,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC7D,cAAc,CAAC,YAAY,EAAE,CAAC;QAC9B,cAAc,CAAC,iBAAiB,IAAI,cAAc,CAAC;QACnD,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QAC1F,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QAE1F,IAAI,OAAO,EAAE,CAAC;YACV,cAAc,CAAC,UAAU,EAAE,CAAC;QAChC,CAAC;QAED,qBAAqB;QACrB,MAAM,aAAa,GAAO,IAAA,oCAAY,GAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,sCAAsC;QAC9F,IAAI,cAAc,GAAG,aAAa,EAAE,CAAC;YACjC,eAAM,CAAC,IAAI,CAAC,2BAA2B,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE;gBAC9F,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,YAAY,EAAE,cAAc;gBAC5B,SAAS,EAAE,GAAG,CAAC,SAAS;aAC3B,CAAC,CAAC;QACP,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAlEW,QAAA,kBAAkB,sBAkE7B;AAEF;;;;;GAKG;AACH,SAAS,iBAAiB,CAAC,IAAY;IACnC,+BAA+B;IAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC1C,CAAC;AAED;;;GAGG;AACH,SAAgB,qBAAqB;IACjC,MAAM,MAAM,GAAO,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;IAClD,MAAM,eAAe,GAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAClD,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACxC,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1D,2BAA2B;IAC3B,MAAM,aAAa,GAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACvF,MAAM,uBAAuB,GAAO,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YAC7C,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,OAAO;YACH,QAAQ;YACR,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,eAAe,EAAE,uBAAuB;YACxC,eAAe,EAAE,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe;YACrF,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,SAAS,EAAE,iBAAiB;SAC/B,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,+CAA+C;IAC/C,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC;IAE9D,OAAO;QACH,OAAO,EAAE;YACL,MAAM;YACN,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,eAAe;YACf,eAAe,EAAE,OAAO,CAAC,eAAe,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe;YAC3F,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,SAAS;YACT,iBAAiB,EAAE,OAAO,CAAC,YAAY,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;SAC7D;QACD,SAAS,EAAE,aAAa;KAC3B,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB;IACnC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;IACzB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;IACvB,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;IAC9B,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC;IAC3C,OAAO,CAAC,eAAe,GAAG,EAAE,CAAC;IAC7B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACnC,CAAC;AAED;;;;GAIG;AACH,SAAgB,0BAA0B,CAAC,WAAmB,EAAE,GAAG,EAAE,GAAG,IAAI;IACxE,OAAO,WAAW,CAAC,GAAG,EAAE;QACpB,MAAM,WAAW,GAAO,qBAAqB,EAAE,CAAC;QAEhD,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACvC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM;YAClC,YAAY,EAAE,WAAW,CAAC,OAAO,CAAC,YAAY;YAC9C,eAAe,EAAE,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;YAC/D,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACnD,iBAAiB,EAAE,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;SACtE,CAAC,CAAC;QAEH,gCAAgC;QAChC,MAAM,YAAY,GAAO,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,iDAAiD;QACjD,MAAM,aAAa,GAAO,WAAW,CAAC,SAAS;aAC1C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,GAAG,CAAC;aACpC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC;QACzE,CAAC;IACL,CAAC,EAAE,QAAQ,CAAC,CAAC;AACjB,CAAC;AAED,kBAAe;IACX,kBAAkB,EAAlB,0BAAkB;IAClB,qBAAqB;IACrB,uBAAuB;IACvB,0BAA0B;CAC7B,CAAC"}