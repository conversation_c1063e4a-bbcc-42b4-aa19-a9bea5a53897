export declare enum IdentityVerificationMethodEnum {
    EMAIL = "EMAIL",
    PHONE = "PHONE",
    DOCUMENT = "DOCUMENT",
    BLOCKCHAIN = "BLOCKCHAIN",
    SOCIAL = "SOCIAL"
}
export declare enum IdentityVerificationStatusEnum {
    PENDING = "PENDING",
    VERIFIED = "VERIFIED",
    REJECTED = "REJECTED",
    EXPIRED = "EXPIRED"
}
export declare enum PaymentMethodTypeEnum {
    CRYPTO = "CRYPTO",
    BINANCE_PAY = "BINANCE_PAY",
    BINANCE_C2C = "BINANCE_C2C",
    BINANCE_TRC20 = "BINANCE_TRC20",
    BANK_TRANSFER = "BANK_TRANSFER"
}
export declare enum PaymentStatusEnum {
    PENDING = "PENDING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    EXPIRED = "EXPIRED",
    REFUNDED = "REFUNDED"
}
export declare const mockPrismaClient: {};
//# sourceMappingURL=prisma.mock.d.ts.map