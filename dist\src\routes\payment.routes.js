"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const payment_controller_1 = __importDefault(require("../controllers/payment.controller"));
const transaction_analytics_controller_1 = __importDefault(require("../controllers/transaction-analytics.controller"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const router = (0, express_1.Router)();
// Admin routes
router.get("/", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), payment_controller_1.default.getAllPayments);
// Get a specific payment - accessible by admin or merchant who owns the payment
router.get("/:id", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin", "merchant"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("id").notEmpty()
]), payment_controller_1.default.getPaymentById);
// Get payments by merchant - accessible by admin or the merchant themselves
router.get("/merchant/:merchantId", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin", "merchant"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty()
]), payment_controller_1.default.getPaymentsByMerchant);
// Create a payment - this might be called from a public endpoint for a customer
// or by the merchant/admin directly
router.post("/", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("merchantId").notEmpty(),
    (0, express_validator_1.body)("amount").isNumeric(),
    (0, express_validator_1.body)("currency").notEmpty(),
    (0, express_validator_1.body)("method").notEmpty(),
    (0, express_validator_1.body)("verificationMethod").notEmpty()
]), payment_controller_1.default.createPayment);
// Update a payment status - admin only
router.put("/:id", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("id").notEmpty()
]), payment_controller_1.default.updatePayment);
// Basic Analytics endpoints
router.get("/analytics/:merchantId", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin", "merchant"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty(),
    (0, express_validator_1.query)("dateRange").optional().isString()
]), payment_controller_1.default.getMerchantAnalytics);
// Advanced Analytics endpoints
router.get("/analytics/:merchantId/advanced", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin", "merchant"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty(),
    (0, express_validator_1.query)("dateRange").optional().isString()
]), transaction_analytics_controller_1.default.getAdvancedTransactionAnalytics);
exports.default = router;
//# sourceMappingURL=payment.routes.js.map