/**
 * Blockchain Identity Verification Service
 *
 * This service provides methods for verifying user identity using blockchain technology.
 * It supports multiple blockchain networks and verification methods.
 */
export declare class BlockchainIdentityService {
    private static instance;
    private providers;
    private constructor();
    /**
   * Get singleton instance of BlockchainIdentityService
   */
    static getInstance(): BlockchainIdentityService;
    /**
   * Initialize blockchain providers for different networks
   */
    private initializeProviders;
    /**
   * Verify a wallet address ownership through signature
   * @param address Wallet address to verify
   * @param signature Signature provided by the user
   * @param message Message that was signed
   * @param network Blockchain network (ethereum, bsc, polygon, tron)
   */
    verifyWalletOwnership(address: string, signature: string, message: string, network: string): Promise<boolean>;
    /**
   * Verify Tron signature
   * @param address Tron wallet address
   * @param signature Signature provided by the user
   * @param message Message that was signed
   */
    private verifyTronSignature;
    /**
   * Generate a challenge message for the user to sign
   * @param userId User ID
   * @param timestamp Current timestamp
   */
    generateChallengeMessage(userId: string, timestamp: number): string;
    /**
   * Create a verification request in the database
   * @param userId User ID
   * @param walletAddress Wallet address
   * @param network Blockchain network
   */
    createVerificationRequest(userId: string, walletAddress: string, network: string): Promise<any>;
    /**
   * Complete a verification request
   * @param requestId Verification request ID
   * @param signature Signature provided by the user
   */
    completeVerification(requestId: string, signature: string): Promise<boolean>;
    /**
   * Get verification status for a user
   * @param userId User ID
   */
    getVerificationStatus(userId: string): Promise<any>;
}
//# sourceMappingURL=blockchain-identity.service.d.ts.map