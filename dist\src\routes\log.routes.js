"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const log_controller_1 = __importDefault(require("../controllers/log.controller"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const router = (0, express_1.Router)();
// Admin-only route to view system logs
router.get("/", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), log_controller_1.default.getAllLogs);
// Internal route to create logs
router.post("/", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("level").isIn(["info", "warning", "error"]),
    (0, express_validator_1.body)("message").notEmpty(),
    (0, express_validator_1.body)("source").notEmpty()
]), log_controller_1.default.createLog);
exports.default = router;
//# sourceMappingURL=log.routes.js.map