import { Response } from "express";
import { AppError } from "../errors/AppError";
/**
 * Response status
 */
export declare enum ResponseStatus {
    SUCCESS = "success",
    ERROR = "error",
    FAIL = "fail"
}
/**
 * Pagination metadata
 */
export interface PaginationMeta {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
}
/**
 * Response factory
 * This class provides a centralized way to create API responses
 */
export declare class ResponseFactory {
    /**
     * Send a success response
     * @param res Express response
     * @param data Response data
     * @param message Success message
     * @param statusCode HTTP status code
     */
    static success<T>(res: Response, data: T, message?: string, statusCode?: number): Response;
    /**
     * Send a paginated success response
     * @param res Express response
     * @param data Response data
     * @param pagination Pagination metadata
     * @param message Success message
     * @param statusCode HTTP status code
     */
    static paginated<T>(res: Response, data: T[], pagination: PaginationMeta, message?: string, statusCode?: number): Response;
    /**
     * Send a created response
     * @param res Express response
     * @param data Response data
     * @param message Success message
     */
    static created<T>(res: Response, data: T, message?: string): Response;
    /**
     * Send a no content response
     * @param res Express response
     */
    static noContent(res: Response): Response;
    /**
     * Send an error response
     * @param res Express response
     * @param error Error object
     */
    static error(res: Response, error: AppError | Error): Response;
    /**
     * Send a validation error response
     * @param res Express response
     * @param errors Validation errors
     * @param message Error message
     */
    static validationError(res: Response, errors: Record<string, string[]>, message?: string): Response;
    /**
     * Send an unauthorized response
     * @param res Express response
     * @param message Error message
     */
    static unauthorized(res: Response, message?: string): Response;
    /**
     * Send a forbidden response
     * @param res Express response
     * @param message Error message
     */
    static forbidden(res: Response, message?: string): Response;
    /**
     * Send a not found response
     * @param res Express response
     * @param message Error message
     */
    static notFound(res: Response, message?: string): Response;
}
export default ResponseFactory;
//# sourceMappingURL=ResponseFactory.d.ts.map