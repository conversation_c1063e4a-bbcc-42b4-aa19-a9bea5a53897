/**
 * Common Payment Routing Rules
 *
 * Implements common payment routing rules.
 */
import { IPaymentRoutingRule, PaymentRoutingContext } from "../PaymentRouter";
import { PaymentMethodType } from "../../../../types/payment-method.types";
import { PrismaClient } from "@prisma/client";
/**
 * Country-based routing rule
 *
 * Scores payment methods based on their popularity in the user's country.
 */
export declare class CountryBasedRule implements IPaymentRoutingRule {
    private prisma;
    private countryPreferences;
    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient);
    /**
   * Get the rule name
   */
    getName(): string;
    /**
   * Get the rule weight
   */
    getWeight(): number;
    /**
   * Apply the rule
   */
    apply(context: PaymentRoutingContext): Record<PaymentMethodType, number>;
}
/**
 * Amount-based routing rule
 *
 * Scores payment methods based on the transaction amount.
 */
export declare class AmountBasedRule implements IPaymentRoutingRule {
    /**
   * Get the rule name
   */
    getName(): string;
    /**
   * Get the rule weight
   */
    getWeight(): number;
    /**
   * Apply the rule
   */
    apply(context: PaymentRoutingContext): Record<PaymentMethodType, number>;
}
/**
 * Success rate routing rule
 *
 * Scores payment methods based on their historical success rate.
 */
export declare class SuccessRateRule implements IPaymentRoutingRule {
    private prisma;
    private successRates;
    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient);
    /**
   * Get the rule name
   */
    getName(): string;
    /**
   * Get the rule weight
   */
    getWeight(): number;
    /**
   * Apply the rule
   */
    apply(context: PaymentRoutingContext): Record<PaymentMethodType, number>;
    /**
   * Load success rates from database or cache
   */
    private loadSuccessRates;
}
//# sourceMappingURL=CommonRoutingRules.d.ts.map