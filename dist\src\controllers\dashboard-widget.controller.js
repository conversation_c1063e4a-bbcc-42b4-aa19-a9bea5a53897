"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardWidgetController = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
class DashboardWidgetController {
    constructor() {
        /**
         * Get all widgets for a dashboard
         */
        this.getWidgets = async (req, res) => {
            try {
                const { dashboardId } = req.params;
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                // Check if dashboard exists and user has access
                const dashboard = await prisma.dashboard.findUnique({
                    where: { id: dashboardId }
                });
                if (!dashboard) {
                    res.status(404).json({ success: false, message: 'Dashboard not found' });
                    return;
                }
                if (!dashboard.isPublic && dashboard.createdById !== userId) {
                    res.status(403).json({ success: false, message: 'Access denied' });
                    return;
                }
                const widgets = await prisma.dashboardWidget.findMany({
                    where: { dashboardId },
                    orderBy: { position: 'asc' }
                });
                res.json({
                    success: true,
                    data: widgets
                });
            }
            catch (error) {
                console.error('Error getting dashboard widgets:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting dashboard widgets'
                });
            }
        };
        /**
         * Get a widget by ID
         */
        this.getWidgetById = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                const widget = await prisma.dashboardWidget.findUnique({
                    where: { id },
                    include: { dashboard: true }
                });
                if (!widget) {
                    res.status(404).json({ success: false, message: 'Widget not found' });
                    return;
                }
                // Check if user has access to the dashboard
                if (!widget.dashboard.isPublic && widget.dashboard.createdById !== userId) {
                    res.status(403).json({ success: false, message: 'Access denied' });
                    return;
                }
                res.json({
                    success: true,
                    data: widget
                });
            }
            catch (error) {
                console.error('Error getting dashboard widget:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting dashboard widget'
                });
            }
        };
        /**
         * Create a new widget
         */
        this.createWidget = async (req, res) => {
            try {
                const { dashboardId } = req.params;
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                // Check if dashboard exists and belongs to user
                const dashboard = await prisma.dashboard.findUnique({
                    where: { id: dashboardId }
                });
                if (!dashboard) {
                    res.status(404).json({ success: false, message: 'Dashboard not found' });
                    return;
                }
                if (dashboard.createdById !== userId) {
                    res.status(403).json({ success: false, message: 'Access denied' });
                    return;
                }
                const { title, type, config, width, height, position } = req.body;
                // Get the highest position to place the new widget at the end
                const highestPosition = await prisma.dashboardWidget.findFirst({
                    where: { dashboardId },
                    orderBy: { position: 'desc' },
                    select: { position: true }
                });
                const newPosition = highestPosition ? highestPosition.position + 1 : 0;
                const widget = await prisma.dashboardWidget.create({
                    data: {
                        dashboardId,
                        title,
                        type,
                        config,
                        width: width || 1,
                        height: height || 1,
                        position: position !== undefined ? position : newPosition
                    }
                });
                res.status(201).json({
                    success: true,
                    data: widget
                });
            }
            catch (error) {
                console.error('Error creating dashboard widget:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error creating dashboard widget'
                });
            }
        };
        /**
         * Update a widget
         */
        this.updateWidget = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                // Check if widget exists and user has access
                const widget = await prisma.dashboardWidget.findUnique({
                    where: { id },
                    include: { dashboard: true }
                });
                if (!widget) {
                    res.status(404).json({ success: false, message: 'Widget not found' });
                    return;
                }
                if (widget.dashboard.createdById !== userId) {
                    res.status(403).json({ success: false, message: 'Access denied' });
                    return;
                }
                const { title, type, config, width, height, position } = req.body;
                const updatedWidget = await prisma.dashboardWidget.update({
                    where: { id },
                    data: {
                        title: title !== undefined ? title : undefined,
                        type: type !== undefined ? type : undefined,
                        config: config !== undefined ? config : undefined,
                        width: width !== undefined ? width : undefined,
                        height: height !== undefined ? height : undefined,
                        position: position !== undefined ? position : undefined
                    }
                });
                res.json({
                    success: true,
                    data: updatedWidget
                });
            }
            catch (error) {
                console.error('Error updating dashboard widget:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error updating dashboard widget'
                });
            }
        };
        /**
         * Delete a widget
         */
        this.deleteWidget = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                // Check if widget exists and user has access
                const widget = await prisma.dashboardWidget.findUnique({
                    where: { id },
                    include: { dashboard: true }
                });
                if (!widget) {
                    res.status(404).json({ success: false, message: 'Widget not found' });
                    return;
                }
                if (widget.dashboard.createdById !== userId) {
                    res.status(403).json({ success: false, message: 'Access denied' });
                    return;
                }
                await prisma.dashboardWidget.delete({
                    where: { id }
                });
                res.json({
                    success: true,
                    message: 'Widget deleted successfully'
                });
            }
            catch (error) {
                console.error('Error deleting dashboard widget:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error deleting dashboard widget'
                });
            }
        };
        /**
         * Reorder widgets
         */
        this.reorderWidgets = async (req, res) => {
            try {
                const { dashboardId } = req.params;
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                // Check if dashboard exists and belongs to user
                const dashboard = await prisma.dashboard.findUnique({
                    where: { id: dashboardId }
                });
                if (!dashboard) {
                    res.status(404).json({ success: false, message: 'Dashboard not found' });
                    return;
                }
                if (dashboard.createdById !== userId) {
                    res.status(403).json({ success: false, message: 'Access denied' });
                    return;
                }
                const { widgets } = req.body;
                if (!Array.isArray(widgets)) {
                    res.status(400).json({ success: false, message: 'Invalid widgets array' });
                    return;
                }
                // Update each widget position
                for (const widget of widgets) {
                    await prisma.dashboardWidget.update({
                        where: { id: widget.id },
                        data: { position: widget.position }
                    });
                }
                res.json({
                    success: true,
                    message: 'Widgets reordered successfully'
                });
            }
            catch (error) {
                console.error('Error reordering widgets:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error reordering widgets'
                });
            }
        };
    }
}
exports.DashboardWidgetController = DashboardWidgetController;
