{"compilerOptions": {"target": "es2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "allowJs": true, "noImplicitAny": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false}, "include": ["src/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}