// jscpd:ignore-file
import axios from 'axios';
import * as crypto from 'crypto';
import { logger } from '../utils/logger';
import { config } from '../config';
import prisma from '../lib/prisma';
import { Merchant } from '../types';

/**
 * Webhook event types
 */
export enum WebhookEventType {
  PAYMENT_CREATED = 'payment.created',
  PAYMENT_COMPLETED = 'payment.completed',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_EXPIRED = 'payment.expired',
  PAYMENT_REFUNDED = 'payment.refunded',
  VERIFICATION_SUCCEEDED = 'verification.succeeded',
  VERIFICATION_FAILED = 'verification.failed',
  ALERT_CREATED = 'alert.created',
  ALERT_UPDATED = 'alert.updated',
  ALERT_RESOLVED = 'alert.resolved',
}

/**
 * Webhook payload interface
 */
export interface WebhookPayload {
  id: string;
  event: WebhookEventType;
  created: number;
  data: Record<string, unknown>;
}

/**
 * Webhook delivery status
 */
export enum WebhookDeliveryStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  RETRYING = 'retrying',
}

/**
 * Webhook service
 */
export class WebhookService {
  private maxRetries: number;
  private retryDelay: number;
  private timeout: number;

  /**
   * Create a new webhook service
   */
  constructor() {
    this.maxRetries = config.webhook.retryAttempts || 3;
    this.retryDelay = config.webhook.retryDelay || 5000; // 5 seconds
    this.timeout = config.webhook.timeout || 10000; // 10 seconds
  }

  /**
   * Generate a signature for the webhook payload
   * @param payload Webhook payload
   * @param secret Merchant secret
   * @returns Signature
   */
  private generateSignature(payload: WebhookPayload, secret: string): string {
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(JSON.stringify(payload));
    return hmac.digest('hex');
  }

  /**
   * Create a webhook event
   * @param merchantId Merchant ID
   * @param event Event type
   * @param data Event data
   * @returns Webhook ID
   */
  public async createWebhookEvent(
    merchantId: string,
    event: WebhookEventType,
    data: Record<string, unknown>
  ): Promise<string> {
    try {
      // Get merchant
      const merchant: unknown = await prisma.merchant.findUnique({
        where: { id: merchantId },
        select: {
          id: true,
          webhookUrl: true,
          apiSecret: true,
        },
      });

      if (!merchant || !merchant.webhookUrl) {
        logger.warn('Merchant has no webhook URL configured', { merchantId });
        return '';
      }

      // Create webhook payload
      const payload: WebhookPayload = {
        id: crypto.randomUUID(),
        event,
        created: Date.now(),
        data,
      };

      // Create webhook event in database
      const webhook: unknown = await prisma.webhook.create({
        data: {
          id: payload.id,
          merchantId,
          event,
          payload: payload as unknown,
          status: WebhookDeliveryStatus.PENDING,
          url: merchant.webhookUrl,
          retryCount: 0,
          maxRetries: this.maxRetries,
        },
      });

      // Deliver webhook asynchronously
      this.deliverWebhook(webhook.id, merchant.webhookUrl, payload, merchant.apiSecret).catch(
        (error) => {
          logger.error('Error delivering webhook', { error, webhookId: webhook.id });
        }
      );

      return webhook.id;
    } catch (error) {
      logger.error('Error creating webhook event', { error, merchantId, event });
      throw new Error('Failed to create webhook event');
    }
  }

  /**
   * Deliver a webhook
   * @param webhookId Webhook ID
   * @param url Webhook URL
   * @param payload Webhook payload
   * @param secret Merchant secret
   * @param retryCount Retry count
   */
  public async deliverWebhook(
    webhookId: string,
    url: string,
    payload: WebhookPayload,
    secret: string,
    retryCount: number = 0
  ): Promise<void> {
    try {
      // Generate signature
      const signature: unknown = this.generateSignature(payload, secret);

      // Update webhook status to retrying if this is a retry
      if (retryCount > 0) {
        await prisma.webhook.update({
          where: { id: webhookId },
          data: {
            status: WebhookDeliveryStatus.RETRYING,
            retryCount,
            lastAttemptAt: new Date(),
          },
        });
      }

      // Send webhook
      const response: unknown = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Signature': signature,
          'X-Webhook-ID': webhookId,
        },
        timeout: this.timeout,
      });

      // Update webhook status
      await prisma.webhook.update({
        where: { id: webhookId },
        data: {
          status: WebhookDeliveryStatus.SUCCESS,
          statusCode: response.status,
          response: response.data,
          deliveredAt: new Date(),
        },
      });

      logger.info('Webhook delivered successfully', { webhookId, url });
    } catch (error) {
      // Log error
      logger.error('Error delivering webhook', { error, webhookId, url, retryCount });

      // Update webhook status
      await prisma.webhook.update({
        where: { id: webhookId },
        data: {
          status: WebhookDeliveryStatus.FAILED,
          statusCode: error.response?.status || 0,
          response: error.response?.data || null,
          error: (error as Error).message,
          lastAttemptAt: new Date(),
        },
      });

      // Retry if not exceeded max retries
      if (retryCount < this.maxRetries) {
        // Calculate exponential backoff delay
        const delay: unknown = this.retryDelay * Math.pow(2, retryCount);

        logger.info(`Scheduling webhook retry in ${delay}ms`, {
          webhookId,
          retryCount: retryCount + 1,
        });

        // Schedule retry
        setTimeout(() => {
          this.deliverWebhook(webhookId, url, payload, secret, retryCount + 1).catch(
            (retryError) => {
              logger.error('Error in webhook retry', { retryError, webhookId });
            }
          );
        }, delay);
      } else {
        logger.warn('Max webhook retries exceeded', { webhookId, maxRetries: this.maxRetries });
      }
    }
  }

  /**
   * Retry a failed webhook
   * @param webhookId Webhook ID
   * @returns Success status
   */
  public async retryWebhook(webhookId: string): Promise<boolean> {
    try {
      // Get webhook
      const webhook: unknown = await prisma.webhook.findUnique({
        where: { id: webhookId },
        include: {
          merchant: {
            select: { apiSecret: true },
          },
        },
      });

      if (!webhook) {
        logger.warn('Webhook not found', { webhookId });
        return false;
      }

      if (webhook.status === WebhookDeliveryStatus.SUCCESS) {
        logger.info('Webhook already delivered successfully', { webhookId });
        return true;
      }

      // Deliver webhook
      await this.deliverWebhook(
        webhook.id,
        webhook.url,
        webhook.payload as WebhookPayload,
        webhook.merchant.apiSecret,
        webhook.retryCount
      );

      return true;
    } catch (error) {
      logger.error('Error retrying webhook', { error, webhookId });
      return false;
    }
  }

  /**
   * Get webhooks for a merchant
   * @param merchantId Merchant ID
   * @param limit Limit
   * @param offset Offset
   * @returns Webhooks
   */
  public async getWebhooks(
    merchantId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<unknown[]> {
    try {
      const webhooks: unknown = await prisma.webhook.findMany({
        where: { merchantId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      });

      return webhooks;
    } catch (error) {
      logger.error('Error getting webhooks', { error, merchantId });
      throw new Error('Failed to get webhooks');
    }
  }

  /**
   * Get a webhook by ID
   * @param webhookId Webhook ID
   * @returns Webhook
   */
  public async getWebhook(webhookId: string): Promise<unknown> {
    try {
      const webhook: unknown = await prisma.webhook.findUnique({
        where: { id: webhookId },
      });

      if (!webhook) {
        throw new Error('Webhook not found');
      }

      return webhook;
    } catch (error) {
      logger.error('Error getting webhook', { error, webhookId });
      throw new Error('Failed to get webhook');
    }
  }
}
