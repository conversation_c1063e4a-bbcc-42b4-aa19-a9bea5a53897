"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const binance_pay_webhook_controller_1 = require("../controllers/binance-pay-webhook.controller");
const router = express_1.default.Router();
// Binance Pay webhook endpoint
router.post("/", binance_pay_webhook_controller_1.handleBinancePayWebhook);
exports.default = router;
//# sourceMappingURL=binance-pay-webhook.routes.js.map