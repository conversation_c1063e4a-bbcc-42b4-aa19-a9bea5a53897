// Simple database connection test script
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testDatabaseConnection(): Promise<void> {
  console.log('🔍 Testing database connection...');
  
  // Create a new PrismaClient instance
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });
  
  try {
    // Test database connection
    console.log('🔌 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Database connection successful!');

    // Print database connection info
    console.log('\n📊 Database connection info:');
    console.log(`🔗 URL: ${process.env.DATABASE_URL?.replace(/:[^:]*@/, ':****@')}`);
    console.log(`🏠 Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`🔢 Port: ${process.env.DB_PORT || '5432'}`);
    console.log(`👤 Username: ${process.env.DB_USERNAME || 'postgres'}`);
    console.log(`📚 Database: ${process.env.DB_NAME || 'amazingpay'}`);

    // Test query execution
    console.log('\n🔍 Testing query execution...');
    try {
      // Try to query the database version
      const result = await prisma.$queryRaw`SELECT version();`;
      console.log('✅ Query executed successfully!');
      console.log(`📋 PostgreSQL version: ${(result as any[])[0].version}`);
    } catch (queryError) {
      console.error('❌ Query execution failed:', queryError);
    }

  } catch (error) {
    console.error('❌ Database connection failed:', error);
    console.log('\n⚠️ Please check your database configuration in the .env file.');
    console.log('⚠️ Make sure PostgreSQL is running and accessible.');
    console.log('⚠️ You may need to create the database: CREATE DATABASE amazingpay;');
  } finally {
    try {
      await prisma.$disconnect();
      console.log('🔌 Database connection closed.');
    } catch (disconnectError) {
      console.error('❌ Error disconnecting from database:', disconnectError);
    }
  }
}

// Run the test
testDatabaseConnection()
  .catch(console.error);
