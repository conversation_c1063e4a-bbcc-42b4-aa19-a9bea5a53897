"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toIncludeAllMembers = toIncludeAllMembers;
const utils_1 = require("../utils");
function toIncludeAllMembers(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    const pass = Array.isArray(actual) &&
        Array.isArray(expected) &&
        // @ts-expect-error OK to have implicit any for this.equals
        expected.every(val => (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), actual, val));
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toIncludeAllMembers') +
                '\n\n' +
                'Expected list to not have all of the following members:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toIncludeAllMembers') +
                '\n\n' +
                'Expected list to have all of the following members:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
