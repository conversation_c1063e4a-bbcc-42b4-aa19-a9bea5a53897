"use strict";
/**
 * CRUD Controller
 *
 * This is a base controller class that provides CRUD functionality
 * for all controllers in the application.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrudController = void 0;
const BaseController_1 = require("./BaseController");
class CrudController extends BaseController_1.BaseController {
    /**
     * Get all items
     */
    async getAll(req, res, service, message = 'Items retrieved successfully') {
        try {
            const items = await service.findAll(req.query);
            return this.sendSuccess(res, items, message);
        }
        catch (error) {
            return this.sendError(res, 'Error retrieving items', 500, error);
        }
    }
    /**
     * Get item by ID
     */
    async getById(req, res, service, message = 'Item retrieved successfully') {
        try {
            const { id } = req.params;
            const item = await service.findById(id);
            if (!item) {
                return this.sendError(res, 'Item not found', 404);
            }
            return this.sendSuccess(res, item, message);
        }
        catch (error) {
            return this.sendError(res, 'Error retrieving item', 500, error);
        }
    }
    /**
     * Create item
     */
    async create(req, res, service, validationSchema = null, message = 'Item created successfully') {
        try {
            // Validate request
            const validationError = this.validateRequest(req, validationSchema);
            if (validationError) {
                return this.sendError(res, validationError, 400);
            }
            const item = await service.create(req.body);
            return this.sendSuccess(res, item, message, 201);
        }
        catch (error) {
            return this.sendError(res, 'Error creating item', 500, error);
        }
    }
    /**
     * Update item
     */
    async update(req, res, service, validationSchema = null, message = 'Item updated successfully') {
        try {
            // Validate request
            const validationError = this.validateRequest(req, validationSchema);
            if (validationError) {
                return this.sendError(res, validationError, 400);
            }
            const { id } = req.params;
            const item = await service.update(id, req.body);
            if (!item) {
                return this.sendError(res, 'Item not found', 404);
            }
            return this.sendSuccess(res, item, message);
        }
        catch (error) {
            return this.sendError(res, 'Error updating item', 500, error);
        }
    }
    /**
     * Delete item
     */
    async delete(req, res, service, message = 'Item deleted successfully') {
        try {
            const { id } = req.params;
            const result = await service.delete(id);
            if (!result) {
                return this.sendError(res, 'Item not found', 404);
            }
            return this.sendSuccess(res, {}, message);
        }
        catch (error) {
            return this.sendError(res, 'Error deleting item', 500, error);
        }
    }
}
exports.CrudController = CrudController;
//# sourceMappingURL=CrudController.js.map