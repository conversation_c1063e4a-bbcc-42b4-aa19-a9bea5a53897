"use strict";
// jscpd:ignore-file
/**
 * Merchant Relationship Routes
 *
 * This file defines the routes for merchant relationship management.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const merchant_relationship_controller_1 = require("../controllers/merchant-relationship.controller");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const roleMiddleware_1 = require("../middlewares/roleMiddleware");
const router = express_1.default.Router();
const merchantRelationshipController = new merchant_relationship_controller_1.MerchantRelationshipController();
/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/communications
 * @desc Send communication to merchant
 * @access Private (Admin)
 */
router.post("/merchants/:merchantId/communications", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantRelationshipController.sendCommunication);
/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/communications
 * @desc Get merchant communications
 * @access Private (Admin, Merchant)
 */
router.get("/merchants/:merchantId/communications", authMiddleware_1.authMiddleware, merchantRelationshipController.getMerchantCommunications);
/**
 * @route PUT /api/merchant-relationship/communications/:communicationId/read
 * @desc Mark communication as read
 * @access Private (Merchant)
 */
router.put("/communications/:communicationId/read", authMiddleware_1.authMiddleware, merchantRelationshipController.markCommunicationAsRead);
/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/support-tickets
 * @desc Create support ticket
 * @access Private (Admin, Merchant)
 */
router.post("/merchants/:merchantId/support-tickets", authMiddleware_1.authMiddleware, merchantRelationshipController.createSupportTicket);
/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/support-tickets
 * @desc Get merchant support tickets
 * @access Private (Admin, Merchant)
 */
router.get("/merchants/:merchantId/support-tickets", authMiddleware_1.authMiddleware, merchantRelationshipController.getMerchantSupportTickets);
/**
 * @route POST /api/merchant-relationship/support-tickets/:ticketId/messages
 * @desc Add message to support ticket
 * @access Private (Admin, Merchant)
 */
router.post("/support-tickets/:ticketId/messages", authMiddleware_1.authMiddleware, merchantRelationshipController.addMessageToSupportTicket);
/**
 * @route PUT /api/merchant-relationship/support-tickets/:ticketId/status
 * @desc Update support ticket status
 * @access Private (Admin)
 */
router.put("/support-tickets/:ticketId/status", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantRelationshipController.updateSupportTicketStatus);
/**
 * @route POST /api/merchant-relationship/merchants/:merchantId/onboarding
 * @desc Initialize merchant onboarding
 * @access Private (Admin)
 */
router.post("/merchants/:merchantId/onboarding", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantRelationshipController.initializeOnboarding);
/**
 * @route GET /api/merchant-relationship/merchants/:merchantId/onboarding
 * @desc Get merchant onboarding
 * @access Private (Admin, Merchant)
 */
router.get("/merchants/:merchantId/onboarding", authMiddleware_1.authMiddleware, merchantRelationshipController.getMerchantOnboarding);
/**
 * @route PUT /api/merchant-relationship/onboarding-steps/:stepId/status
 * @desc Update onboarding step status
 * @access Private (Admin, Merchant)
 */
router.put("/onboarding-steps/:stepId/status", authMiddleware_1.authMiddleware, merchantRelationshipController.updateOnboardingStepStatus);
exports.default = router;
//# sourceMappingURL=merchant-relationship.routes.js.map