"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantEventsService = exports.merchantEvents = void 0;
// jscpd:ignore-file
const events_1 = require("events");
const logger_1 = require("../lib/logger");
/**
 * Event emitter for merchant events
 */
exports.merchantEvents = new events_1.EventEmitter();
/**
 * Merchant events service
 */
class MerchantEventsService {
    /**
   * Emit merchant created event
   * @param merchant Merchant data
   */
    static emitMerchantCreated(merchant) {
        try {
            exports.merchantEvents.emit("merchant.created", {
                id: merchant.id,
                name: merchant.name,
                email: merchant.email,
                status: merchant.status,
                createdAt: merchant.createdAt
            });
            logger_1.logger.info(`Emitted merchant.created event for merchant ${merchant.id}`);
        }
        catch (error) {
            logger_1.logger.error("Error emitting merchant.created event:", error);
        }
    }
    /**
   * Emit merchant updated event
   * @param merchant Merchant data
   */
    static emitMerchantUpdated(merchant) {
        try {
            exports.merchantEvents.emit("merchant.updated", {
                id: merchant.id,
                name: merchant.name,
                email: merchant.email,
                status: merchant.status,
                updatedAt: merchant.updatedAt
            });
            logger_1.logger.info(`Emitted merchant.updated event for merchant ${merchant.id}`);
        }
        catch (error) {
            logger_1.logger.error("Error emitting merchant.updated event:", error);
        }
    }
    /**
   * Emit merchant status updated event
   * @param merchantId Merchant ID
   * @param status New status
   * @param updatedBy User who updated the status
   * @param reason Reason for status update
   */
    static emitMerchantStatusUpdated(merchantId, status, updatedBy, reason) {
        try {
            const event = {
                id: merchantId,
                status,
                updatedAt: new Date(),
                updatedBy,
                reason
            };
            exports.merchantEvents.emit("merchant.status.updated", event);
            logger_1.logger.info(`Emitted merchant.status.updated event for merchant ${merchantId}`, {
                status,
                updatedBy
            });
        }
        catch (error) {
            logger_1.logger.error("Error emitting merchant.status.updated event:", error);
        }
    }
}
exports.MerchantEventsService = MerchantEventsService;
//# sourceMappingURL=merchant-events.service.js.map