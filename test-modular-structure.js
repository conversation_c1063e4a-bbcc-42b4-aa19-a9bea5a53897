/**
 * Comprehensive Modular Structure Test
 *
 * Tests all our restructured modules to ensure they work correctly.
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Test configurations
const testConfigs = [
  {
    name: 'Identity Verification Service',
    path: 'src/services/identity-verification/index.ts',
    description: 'Core identity verification functionality',
  },
  {
    name: 'Reporting Service',
    path: 'src/services/reporting/index.ts',
    description: 'Advanced reporting and analytics',
  },
  {
    name: 'Fraud Detection Service',
    path: 'src/services/fraud-detection/index.ts',
    description: 'Fraud detection and risk assessment',
  },
  {
    name: 'Test Utilities',
    path: 'src/tests/utils/index.ts',
    description: 'Comprehensive testing framework',
  },
  {
    name: 'Alert Aggregation Controller',
    path: 'src/controllers/alert-aggregation/index.ts',
    description: 'Alert management and aggregation',
  },
  {
    name: 'Identity Verification Controller',
    path: 'src/controllers/identity-verification/index.ts',
    description: 'Identity verification HTTP endpoints',
  },
  {
    name: 'Admin Controller',
    path: 'src/controllers/admin/index.ts',
    description: 'Admin management system',
  },
  {
    name: 'Fraud Detection Controller',
    path: 'src/controllers/fraud-detection/index.ts',
    description: 'Fraud detection HTTP endpoints',
  },
];

/**
 * Test TypeScript compilation for a module
 */
function testTypeScriptCompilation(modulePath) {
  return new Promise((resolve, reject) => {
    const tsc = spawn('npx', ['tsc', '--noEmit', '--skipLibCheck', modulePath], {
      stdio: 'pipe',
      shell: true,
    });

    let stdout = '';
    let stderr = '';

    tsc.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    tsc.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    tsc.on('close', (code) => {
      resolve({
        success: code === 0,
        stdout,
        stderr,
        exitCode: code,
      });
    });

    tsc.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Test module imports
 */
function testModuleImports(modulePath) {
  try {
    if (!fs.existsSync(modulePath)) {
      return {
        success: false,
        error: 'Module file does not exist',
      };
    }

    const content = fs.readFileSync(modulePath, 'utf8');

    // Check for proper exports
    const hasExports = content.includes('export') || content.includes('module.exports');

    // Check for proper imports
    const hasImports = content.includes('import') || content.includes('require');

    // Check for TypeScript syntax
    const hasTypeScript =
      content.includes('interface') || content.includes('type ') || content.includes(': ');

    return {
      success: true,
      hasExports,
      hasImports,
      hasTypeScript,
      lineCount: content.split('\n').length,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Test module structure
 */
function testModuleStructure(modulePath) {
  const moduleDir = path.dirname(modulePath);
  const expectedStructure = {
    types: false,
    services: false,
    mappers: false,
    controllers: false,
    utils: false,
    methods: false,
    core: false,
  };

  try {
    if (!fs.existsSync(moduleDir)) {
      return {
        success: false,
        error: 'Module directory does not exist',
      };
    }

    const items = fs.readdirSync(moduleDir);

    items.forEach((item) => {
      const itemPath = path.join(moduleDir, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        if (item === 'types') expectedStructure.types = true;
        if (item === 'services') expectedStructure.services = true;
        if (item === 'mappers') expectedStructure.mappers = true;
        if (item === 'controllers') expectedStructure.controllers = true;
        if (item === 'utils') expectedStructure.utils = true;
        if (item === 'methods') expectedStructure.methods = true;
        if (item === 'core') expectedStructure.core = true;
      }
    });

    return {
      success: true,
      structure: expectedStructure,
      fileCount: items.filter((item) => {
        const itemPath = path.join(moduleDir, item);
        return fs.statSync(itemPath).isFile();
      }).length,
      dirCount: items.filter((item) => {
        const itemPath = path.join(moduleDir, item);
        return fs.statSync(itemPath).isDirectory();
      }).length,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Run comprehensive tests
 */
async function runTests() {
  console.log('🧪 COMPREHENSIVE MODULAR STRUCTURE TESTING\n');
  console.log('=' * 60);

  const results = [];
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;

  for (const config of testConfigs) {
    console.log(`\n📦 Testing: ${config.name}`);
    console.log(`📄 Description: ${config.description}`);
    console.log(`📁 Path: ${config.path}`);
    console.log('-'.repeat(50));

    const testResult = {
      name: config.name,
      path: config.path,
      tests: {},
    };

    // Test 1: Module file existence
    totalTests++;
    console.log('🔍 Test 1: Module file existence...');
    const fileExists = fs.existsSync(config.path);
    if (fileExists) {
      console.log('  ✅ Module file exists');
      testResult.tests.fileExists = { success: true };
      passedTests++;
    } else {
      console.log('  ❌ Module file does not exist');
      testResult.tests.fileExists = { success: false, error: 'File not found' };
      failedTests++;
      continue; // Skip other tests if file doesn't exist
    }

    // Test 2: Module imports and exports
    totalTests++;
    console.log('🔍 Test 2: Module imports and exports...');
    const importTest = testModuleImports(config.path);
    if (importTest.success) {
      console.log(`  ✅ Module structure valid (${importTest.lineCount} lines)`);
      console.log(`  📤 Has exports: ${importTest.hasExports ? '✅' : '❌'}`);
      console.log(`  📥 Has imports: ${importTest.hasImports ? '✅' : '❌'}`);
      console.log(`  🔷 TypeScript syntax: ${importTest.hasTypeScript ? '✅' : '❌'}`);
      testResult.tests.imports = importTest;
      passedTests++;
    } else {
      console.log(`  ❌ Module structure invalid: ${importTest.error}`);
      testResult.tests.imports = importTest;
      failedTests++;
    }

    // Test 3: Module directory structure
    totalTests++;
    console.log('🔍 Test 3: Module directory structure...');
    const structureTest = testModuleStructure(config.path);
    if (structureTest.success) {
      console.log(`  ✅ Directory structure valid`);
      console.log(`  📁 Files: ${structureTest.fileCount}, Directories: ${structureTest.dirCount}`);

      const structure = structureTest.structure;
      if (structure.types) console.log('  📋 Types directory: ✅');
      if (structure.services) console.log('  🔧 Services directory: ✅');
      if (structure.mappers) console.log('  🗺️  Mappers directory: ✅');
      if (structure.controllers) console.log('  🎮 Controllers directory: ✅');
      if (structure.utils) console.log('  🛠️  Utils directory: ✅');
      if (structure.methods) console.log('  ⚙️  Methods directory: ✅');
      if (structure.core) console.log('  🏗️  Core directory: ✅');

      testResult.tests.structure = structureTest;
      passedTests++;
    } else {
      console.log(`  ❌ Directory structure invalid: ${structureTest.error}`);
      testResult.tests.structure = structureTest;
      failedTests++;
    }

    // Test 4: TypeScript compilation
    totalTests++;
    console.log('🔍 Test 4: TypeScript compilation...');
    try {
      const compilationTest = await testTypeScriptCompilation(config.path);
      if (compilationTest.success) {
        console.log('  ✅ TypeScript compilation successful');
        testResult.tests.compilation = { success: true };
        passedTests++;
      } else {
        console.log('  ❌ TypeScript compilation failed');
        if (compilationTest.stderr) {
          console.log(`  📝 Errors: ${compilationTest.stderr.substring(0, 200)}...`);
        }
        testResult.tests.compilation = {
          success: false,
          error: compilationTest.stderr,
          exitCode: compilationTest.exitCode,
        };
        failedTests++;
      }
    } catch (error) {
      console.log(`  ❌ TypeScript compilation error: ${error.message}`);
      testResult.tests.compilation = { success: false, error: error.message };
      failedTests++;
    }

    results.push(testResult);
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));

  console.log(`\n🎯 Overall Results:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${passedTests} ✅`);
  console.log(`   Failed: ${failedTests} ❌`);
  console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  console.log(`\n📦 Module Results:`);
  results.forEach((result) => {
    const moduleTests = Object.values(result.tests);
    const modulePassed = moduleTests.filter((test) => test.success).length;
    const moduleTotal = moduleTests.length;
    const moduleRate = ((modulePassed / moduleTotal) * 100).toFixed(1);

    console.log(
      `   ${result.name}: ${modulePassed}/${moduleTotal} (${moduleRate}%) ${
        moduleRate === '100.0' ? '✅' : '❌'
      }`
    );
  });

  // Recommendations
  console.log(`\n🚀 RECOMMENDATIONS:`);
  if (failedTests === 0) {
    console.log('🎉 All tests passed! Your modular structure is ready for production.');
    console.log('✅ All modules are properly structured and compile successfully.');
    console.log('✅ TypeScript compilation is working correctly.');
    console.log('✅ Module imports and exports are properly configured.');
  } else {
    console.log('⚠️  Some tests failed. Please review the following:');

    results.forEach((result) => {
      const failedModuleTests = Object.entries(result.tests).filter(([_, test]) => !test.success);
      if (failedModuleTests.length > 0) {
        console.log(`\n❌ ${result.name}:`);
        failedModuleTests.forEach(([testName, test]) => {
          console.log(`   - ${testName}: ${test.error || 'Failed'}`);
        });
      }
    });
  }

  console.log('\n🔧 NEXT STEPS:');
  console.log('1. Fix any compilation errors shown above');
  console.log('2. Ensure all modules have proper exports');
  console.log('3. Run unit tests for individual modules');
  console.log('4. Test integration between modules');
  console.log('5. Deploy to staging environment');

  return {
    totalTests,
    passedTests,
    failedTests,
    successRate: (passedTests / totalTests) * 100,
    results,
  };
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests()
    .then((summary) => {
      console.log(`\n✨ Testing complete! Success rate: ${summary.successRate.toFixed(1)}%`);
      process.exit(summary.failedTests === 0 ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Testing failed:', error);
      process.exit(1);
    });
}

module.exports = { runTests, testTypeScriptCompilation, testModuleImports, testModuleStructure };
