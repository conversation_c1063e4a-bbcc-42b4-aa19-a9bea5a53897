"use strict";
// jscpd:ignore-file
/**
 * Enhanced Risk Engine Routes
 *
 * This file defines the routes for the enhanced risk engine.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const enhanced_risk_engine_controller_1 = require("../controllers/enhanced-risk-engine.controller");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const roleMiddleware_1 = require("../middlewares/roleMiddleware");
const router = express_1.default.Router();
const enhancedRiskEngineController = new enhanced_risk_engine_controller_1.EnhancedRiskEngineController();
/**
 * @route POST /api/risk/assess
 * @desc Assess transaction risk
 * @access Private (Admin, Merchant)
 */
router.post("/assess", authMiddleware_1.authMiddleware, enhancedRiskEngineController.assessTransactionRisk);
/**
 * @route GET /api/risk/transaction/:transactionId
 * @desc Get risk assessment for a transaction
 * @access Private (Admin, Merchant)
 */
router.get("/transaction/:transactionId", authMiddleware_1.authMiddleware, enhancedRiskEngineController.getTransactionRiskAssessment);
/**
 * @route GET /api/risk/config/:merchantId
 * @desc Get merchant risk configuration
 * @access Private (Admin, Merchant)
 */
router.get("/config/:merchantId", authMiddleware_1.authMiddleware, enhancedRiskEngineController.getMerchantRiskConfig);
/**
 * @route PUT /api/risk/config/:merchantId
 * @desc Update merchant risk configuration
 * @access Private (Admin)
 */
router.put("/config/:merchantId", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), enhancedRiskEngineController.updateMerchantRiskConfig);
/**
 * @route GET /api/risk/statistics/:merchantId
 * @desc Get risk statistics
 * @access Private (Admin, Merchant)
 */
router.get("/statistics/:merchantId", authMiddleware_1.authMiddleware, enhancedRiskEngineController.getRiskStatistics);
exports.default = router;
//# sourceMappingURL=enhanced-risk-engine.routes.js.map