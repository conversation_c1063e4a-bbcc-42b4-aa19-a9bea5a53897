/**
 * Mock Factories
 *
 * Factory functions for creating mock objects used in tests.
 */
import { PrismaClient } from '@prisma/client';
import { MockRequest, MockResponse, MockNext, MockFactoryOptions } from '../core/TestTypes';
/**
 * Create a mock request object
 */
export declare function createMockRequest(options?: {
    params?: any;
    query?: any;
    body?: any;
    headers?: any;
    user?: any;
    session?: any;
    cookies?: any;
    ip?: string;
    method?: string;
    url?: string;
    originalUrl?: string;
    path?: string;
    protocol?: string;
    secure?: boolean;
    xhr?: boolean;
}): MockRequest;
/**
 * Create a mock response object
 */
export declare function createMockResponse(options?: {
    statusCode?: number;
    locals?: any;
    headersSent?: boolean;
}): MockResponse;
/**
 * Create a mock next function
 */
export declare function createMockNext(): MockNext;
/**
 * Create a mock database model
 */
export declare function createMockModel(modelName?: string): any;
/**
 * Create a mock Prisma client
 */
export declare function createMockPrismaClient(options?: MockFactoryOptions): PrismaClient;
/**
 * Create a mock JWT token
 */
export declare function createMockJwtToken(payload?: any, options?: {
    expiresIn?: string;
    issuer?: string;
    audience?: string;
}): string;
/**
 * Create mock API response
 */
export declare function createMockApiResponse(data: any, options?: {
    status?: number;
    message?: string;
    success?: boolean;
    pagination?: any;
    metadata?: any;
}): any;
/**
 * Create mock error response
 */
export declare function createMockErrorResponse(error: string | Error, options?: {
    status?: number;
    code?: string;
    details?: any;
}): any;
/**
 * Create mock file upload
 */
export declare function createMockFileUpload(options?: {
    filename?: string;
    mimetype?: string;
    size?: number;
    buffer?: Buffer;
}): any;
/**
 * Create mock WebSocket
 */
export declare function createMockWebSocket(): any;
/**
 * Reset all mocks in an object
 */
export declare function resetMocks(obj: any): void;
/**
 * Clear all mocks in an object
 */
export declare function clearMocks(obj: any): void;
//# sourceMappingURL=MockFactories.d.ts.map