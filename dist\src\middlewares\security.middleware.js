"use strict";
// jscpd:ignore-file
/**
 * Security Middleware
 *
 * This middleware provides additional security features for the API.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityMiddleware = exports.requestValidation = exports.environmentBanner = exports.sqlInjectionProtection = exports.cacheControl = exports.permissionsPolicy = exports.referrerPolicy = exports.xssFilter = exports.frameGuard = exports.noSniff = exports.hsts = exports.csrfProtection = exports.contentSecurityPolicy = void 0;
const logger_1 = require("../lib/logger");
const error_middleware_1 = require("./error.middleware");
const csrf_1 = require("../utils/csrf");
const environment_validator_1 = require("../utils/environment-validator");
/**
 * Content Security Policy middleware
 * Sets CSP headers to prevent XSS attacks
 */
const contentSecurityPolicy = (req, res, next) => {
    // Set Content-Security-Policy header
    res.setHeader("Content-Security-Policy", "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: blob:; " +
        "font-src 'self' data:; " +
        "connect-src 'self' https://api.amazingpayme.com; " +
        "frame-src 'self'; " +
        "object-src 'none'; " +
        "base-uri 'self'; " +
        "form-action 'self';");
    next();
};
exports.contentSecurityPolicy = contentSecurityPolicy;
/**
 * CSRF Protection middleware
 * Validates CSRF tokens for mutating requests
 */
const csrfProtection = (req, res, next) => {
    // Skip CSRF check for GET, HEAD, OPTIONS requests
    if (["GET", "HEAD", "OPTIONS"].includes(req.method)) {
        return next();
    }
    // Skip CSRF check for API endpoints that use JWT authentication
    if (req.path.startsWith("/api/") && req.headers.authorization) {
        return next();
    }
    // Extract CSRF token
    const csrfToken = (0, csrf_1.extractCsrfToken)(req);
    if (!csrfToken) {
        logger_1.logger.warn("CSRF token missing", {
            ip: req.ip,
            path: req.path,
            method: req.method,
            requestId: req.requestId
        });
        return next((0, error_middleware_1.createForbiddenError)("CSRF token missing", "CSRF_TOKEN_MISSING"));
    }
    // Get user ID from session or request
    const userId = req.user?.id; // Fixed: using id instead of userId || "anonymous";
    // Validate CSRF token
    if (!(0, csrf_1.validateCsrfToken)(csrfToken, userId)) {
        logger_1.logger.warn("Invalid CSRF token", {
            ip: req.ip,
            path: req.path,
            method: req.method,
            requestId: req.requestId,
            userId
        });
        return next((0, error_middleware_1.createForbiddenError)("Invalid CSRF token", "INVALID_CSRF_TOKEN"));
    }
    next();
};
exports.csrfProtection = csrfProtection;
/**
 * HTTP Strict Transport Security middleware
 * Forces browsers to use HTTPS
 */
const hsts = (req, res, next) => {
    // Set Strict-Transport-Security header
    res.setHeader("Strict-Transport-Security", "max-age = 31536000; includeSubDomains; preload");
    next();
};
exports.hsts = hsts;
/**
 * X-Content-Type-Options middleware
 * Prevents MIME type sniffing
 */
const noSniff = (req, res, next) => {
    // Set X-Content-Type-Options header
    res.setHeader("X-Content-Type-Options", "nosniff");
    next();
};
exports.noSniff = noSniff;
/**
 * X-Frame-Options middleware
 * Prevents clickjacking
 */
const frameGuard = (req, res, next) => {
    // Set X-Frame-Options header
    res.setHeader("X-Frame-Options", "SAMEORIGIN");
    next();
};
exports.frameGuard = frameGuard;
/**
 * X-XSS-Protection middleware
 * Enables browser XSS filtering
 */
const xssFilter = (req, res, next) => {
    // Set X-XSS-Protection header
    res.setHeader("X-XSS-Protection", "1; mode = block");
    next();
};
exports.xssFilter = xssFilter;
/**
 * Referrer-Policy middleware
 * Controls the Referer header
 */
const referrerPolicy = (req, res, next) => {
    // Set Referrer-Policy header
    res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
    next();
};
exports.referrerPolicy = referrerPolicy;
/**
 * Permissions-Policy middleware
 * Restricts browser features
 */
const permissionsPolicy = (req, res, next) => {
    // Set Permissions-Policy header
    res.setHeader("Permissions-Policy", "camera=(), microphone=(), geolocation=(), interest-cohort=()");
    next();
};
exports.permissionsPolicy = permissionsPolicy;
/**
 * Cache Control middleware
 * Sets cache control headers to prevent sensitive information caching
 */
const cacheControl = (req, res, next) => {
    // Set Cache-Control header for API routes
    if (req.path.startsWith("/api/")) {
        // No caching for API routes by default
        res.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate");
        res.setHeader("Pragma", "no-cache");
        res.setHeader("Expires", "0");
    }
    next();
};
exports.cacheControl = cacheControl;
/**
 * SQL Injection Protection middleware
 * Basic protection against SQL injection attacks
 */
const sqlInjectionProtection = (req, res, next) => {
    // Check for common SQL injection patterns in query parameters
    const sqlInjectionPattern = /('|"|;|--|\/\*|\*\/|@@|@|char|nchar|varchar|nvarchar|alter|begin|cast|create|cursor|declare|delete|drop|end|exec|execute|fetch|insert|kill|open|select|sys|sysobjects|syscolumns|table|update|union|waitfor)/i;
    // Check query parameters
    const queryParams = req.query;
    for (const key in queryParams) {
        if (typeof queryParams[key] === "string" && sqlInjectionPattern.test(queryParams[key])) {
            logger_1.logger.warn("Possible SQL injection attempt detected in query parameters", {
                ip: req.ip,
                path: req.path,
                method: req.method,
                requestId: req.requestId,
                parameter: key,
                value: queryParams[key]
            });
            return next((0, error_middleware_1.createBadRequestError)("Invalid input", "INVALID_INPUT"));
        }
    }
    next();
};
exports.sqlInjectionProtection = sqlInjectionProtection;
/**
 * Environment Banner middleware
 * Adds environment information to response headers
 */
const environmentBanner = (req, res, next) => {
    // Add environment information to response headers
    if ((0, environment_validator_1.isDemo)()) {
        res.setHeader("X-Environment", "demo");
        res.setHeader("X-Demo-Mode", "true");
    }
    else if (!(0, environment_validator_1.isProduction)()) {
        res.setHeader("X-Environment", process.env.NODE_ENV || "development");
    }
    next();
};
exports.environmentBanner = environmentBanner;
/**
 * Request Validation middleware
 * Validates request parameters for common security issues
 */
const requestValidation = (req, res, next) => {
    // Check for overly large payloads
    const contentLength = parseInt(req.headers["content-length"] || "0", 10);
    if (contentLength > 1024 * 1024) { // 1MB
        logger_1.logger.warn("Request payload too large", {
            ip: req.ip,
            path: req.path,
            method: req.method,
            requestId: req.requestId,
            contentLength
        });
        return next((0, error_middleware_1.createBadRequestError)("Request payload too large", "PAYLOAD_TOO_LARGE"));
    }
    next();
};
exports.requestValidation = requestValidation;
/**
 * Apply all security middleware
 */
exports.securityMiddleware = [
    exports.contentSecurityPolicy,
    exports.csrfProtection,
    exports.hsts,
    exports.noSniff,
    exports.frameGuard,
    exports.xssFilter,
    exports.referrerPolicy,
    exports.permissionsPolicy,
    exports.cacheControl,
    exports.sqlInjectionProtection,
    exports.environmentBanner,
    exports.requestValidation
];
exports.default = exports.securityMiddleware;
//# sourceMappingURL=security.middleware.js.map