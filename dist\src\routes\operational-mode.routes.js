"use strict";
// jscpd:ignore-file
/**
 * Operational Mode Routes
 *
 * Routes for operational mode operations.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const enhanced_auth_middleware_1 = require("../middlewares/enhanced-auth.middleware");
const audit_middleware_1 = require("../middlewares/audit.middleware");
const operational_mode_controller_1 = __importDefault(require("../controllers/operational-mode.controller"));
const router = (0, express_1.Router)();
// Routes requiring authentication
router.use(enhanced_auth_middleware_1.enhancedAuthenticate);
// Get current operational mode
router.get("/", (0, enhanced_auth_middleware_1.requirePermission)("settings", "view"), operational_mode_controller_1.default.getCurrentMode);
// Set operational mode
router.post("/mode", (0, enhanced_auth_middleware_1.requirePermission)("settings", "update"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("mode").isString().notEmpty().withMessage("Mode is required")
]), (0, audit_middleware_1.auditLog)("operational_mode", "update"), operational_mode_controller_1.default.setOperationalMode);
// Enable or disable the system
router.post("/status", (0, enhanced_auth_middleware_1.requirePermission)("settings", "update"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("enabled").isBoolean().withMessage("Enabled must be a boolean")
]), (0, audit_middleware_1.auditLog)("operational_mode", "update"), operational_mode_controller_1.default.setSystemEnabled);
exports.default = router;
//# sourceMappingURL=operational-mode.routes.js.map