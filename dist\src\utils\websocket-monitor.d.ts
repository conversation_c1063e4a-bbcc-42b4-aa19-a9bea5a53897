import { Server as WebSocketServer } from 'socket.io';
interface WebSocketStats {
    connections: {
        total: number;
        active: number;
        disconnected: number;
    };
    messages: {
        sent: number;
        received: number;
        errors: number;
    };
    rooms: {
        count: number;
        active: Map<string, number>;
    };
    events: {
        count: number;
        types: Map<string, number>;
    };
    errors: {
        count: number;
        lastError: string | null;
    };
    performance: {
        avgResponseTime: number;
        peakConnections: number;
        lastHealthCheck: Date;
    };
    messageHistory: Array<{
        type: 'sent' | 'received' | 'error';
        event?: string;
        timestamp: Date;
        size?: number;
        room?: string;
    }>;
}
export declare class WebSocketMonitor {
    private static instance;
    private io;
    private stats;
    private monitoringInterval;
    private historyLimit;
    /**
     * Private constructor to enforce singleton pattern
     */
    private constructor();
    /**
     * Get the singleton instance
     */
    static getInstance(): WebSocketMonitor;
    /**
     * Initialize the WebSocket monitor with a socket.io server instance
     */
    initialize(io: WebSocketServer): void;
    /**
     * Set up event listeners for socket.io events
     */
    private setupEventListeners;
    /**
     * Start periodic monitoring
     */
    private startMonitoring;
    /**
     * Check the health of the WebSocket server
     */
    private checkHealth;
    /**
     * Get the current WebSocket stats
     */
    getStats(): WebSocketStats;
    /**
     * Stop monitoring
     */
    stop(): void;
}
export {};
//# sourceMappingURL=websocket-monitor.d.ts.map