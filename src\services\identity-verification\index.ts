/**
 * Identity Verification Module
 * 
 * Centralized exports for the identity verification system.
 */

// Core exports
export { IdentityVerificationService } from "./core/IdentityVerificationService";
export { IdentityVerificationError } from "./core/IdentityVerificationError";
export * from "./core/IdentityVerificationTypes";

// Method exports
export { EthereumSignatureVerification } from "./methods/EthereumSignatureVerification";

// Utility exports
export { BlockchainUtils } from "./utils/BlockchainUtils";

// Re-export Prisma enums for convenience
export { IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from "@prisma/client";

/**
 * Default export - main service class
 */
export { IdentityVerificationService as default } from "./core/IdentityVerificationService";
