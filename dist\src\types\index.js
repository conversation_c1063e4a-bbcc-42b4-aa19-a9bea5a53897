"use strict";
/**
 * Core Type Definitions
 *
 * This file contains core type definitions used throughout the application.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationStatus = exports.AlertStatus = exports.AlertSeverity = exports.AlertType = exports.VerificationMethodType = exports.VerificationStatus = exports.TransactionStatus = exports.PaymentMethodType = exports.MerchantStatus = exports.UserRole = void 0;
// User Types
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["MERCHANT"] = "MERCHANT";
    UserRole["CUSTOMER"] = "CUSTOMER";
    UserRole["SUPPORT"] = "SUPPORT";
})(UserRole || (exports.UserRole = UserRole = {}));
// Merchant Types
var MerchantStatus;
(function (MerchantStatus) {
    MerchantStatus["PENDING"] = "PENDING";
    MerchantStatus["ACTIVE"] = "ACTIVE";
    MerchantStatus["SUSPENDED"] = "SUSPENDED";
    MerchantStatus["INACTIVE"] = "INACTIVE";
})(MerchantStatus || (exports.MerchantStatus = MerchantStatus = {}));
// Payment Method Types
var PaymentMethodType;
(function (PaymentMethodType) {
    PaymentMethodType["CRYPTO"] = "CRYPTO";
    PaymentMethodType["CARD"] = "CARD";
    PaymentMethodType["BANK_TRANSFER"] = "BANK_TRANSFER";
    PaymentMethodType["MOBILE_MONEY"] = "MOBILE_MONEY";
    PaymentMethodType["WALLET"] = "WALLET";
    PaymentMethodType["OTHER"] = "OTHER";
})(PaymentMethodType || (exports.PaymentMethodType = PaymentMethodType = {}));
// Transaction Types
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["PENDING"] = "PENDING";
    TransactionStatus["PROCESSING"] = "PROCESSING";
    TransactionStatus["COMPLETED"] = "COMPLETED";
    TransactionStatus["FAILED"] = "FAILED";
    TransactionStatus["EXPIRED"] = "EXPIRED";
    TransactionStatus["REFUNDED"] = "REFUNDED";
    TransactionStatus["CANCELLED"] = "CANCELLED";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
// Verification Types
var VerificationStatus;
(function (VerificationStatus) {
    VerificationStatus["PENDING"] = "PENDING";
    VerificationStatus["IN_PROGRESS"] = "IN_PROGRESS";
    VerificationStatus["VERIFIED"] = "VERIFIED";
    VerificationStatus["FAILED"] = "FAILED";
    VerificationStatus["EXPIRED"] = "EXPIRED";
})(VerificationStatus || (exports.VerificationStatus = VerificationStatus = {}));
var VerificationMethodType;
(function (VerificationMethodType) {
    VerificationMethodType["BLOCKCHAIN"] = "BLOCKCHAIN";
    VerificationMethodType["API"] = "API";
    VerificationMethodType["MANUAL"] = "MANUAL";
    VerificationMethodType["BINANCE"] = "BINANCE";
    VerificationMethodType["OTHER"] = "OTHER";
})(VerificationMethodType || (exports.VerificationMethodType = VerificationMethodType = {}));
// Alert Types
var AlertType;
(function (AlertType) {
    AlertType["SECURITY"] = "SECURITY";
    AlertType["PAYMENT"] = "PAYMENT";
    AlertType["SYSTEM"] = "SYSTEM";
    AlertType["FRAUD"] = "FRAUD";
    AlertType["VERIFICATION"] = "VERIFICATION";
    AlertType["OTHER"] = "OTHER";
})(AlertType || (exports.AlertType = AlertType = {}));
var AlertSeverity;
(function (AlertSeverity) {
    AlertSeverity["LOW"] = "LOW";
    AlertSeverity["MEDIUM"] = "MEDIUM";
    AlertSeverity["HIGH"] = "HIGH";
    AlertSeverity["CRITICAL"] = "CRITICAL";
})(AlertSeverity || (exports.AlertSeverity = AlertSeverity = {}));
var AlertStatus;
(function (AlertStatus) {
    AlertStatus["OPEN"] = "OPEN";
    AlertStatus["IN_PROGRESS"] = "IN_PROGRESS";
    AlertStatus["RESOLVED"] = "RESOLVED";
    AlertStatus["CLOSED"] = "CLOSED";
    AlertStatus["IGNORED"] = "IGNORED";
})(AlertStatus || (exports.AlertStatus = AlertStatus = {}));
var MigrationStatus;
(function (MigrationStatus) {
    MigrationStatus["PENDING"] = "PENDING";
    MigrationStatus["APPLIED"] = "APPLIED";
    MigrationStatus["FAILED"] = "FAILED";
})(MigrationStatus || (exports.MigrationStatus = MigrationStatus = {}));
// Export all types
exports.default = {
    UserRole,
    MerchantStatus,
    PaymentMethodType,
    TransactionStatus,
    VerificationStatus,
    VerificationMethodType,
    AlertType,
    AlertSeverity,
    AlertStatus,
    MigrationStatus,
};
