"use strict";
// jscpd:ignore-file
/**
 * Transactions Module
 *
 * This module handles transaction management.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const ModuleFactory_1 = require("../../factories/ModuleFactory");
const logger_1 = require("../../utils/logger");
/**
 * Transactions Module
 */
class TransactionsModule extends ModuleFactory_1.BaseModule {
    /**
     * Constructor
     */
    constructor() {
        super('TransactionsModule');
    }
    /**
     * Initialize the module
     */
    initialize() {
        logger_1.logger.info('Initializing TransactionsModule');
        // Get controllers
        const transactionController = this.controllerFactory.getController('transaction');
        // Set up routes
        this.router.get('/', transactionController.getAll);
        this.router.get('/:id', transactionController.getById);
        this.router.post('/', transactionController.create);
        this.router.put('/:id', transactionController.update);
        this.router.delete('/:id', transactionController.delete);
        this.router.get('/statistics', transactionController.getStatistics);
        this.router.get('/merchant/:merchantId', transactionController.getByMerchantId);
        this.router.get('/date-range', transactionController.getByDateRange);
        logger_1.logger.info('TransactionsModule initialized');
    }
}
// Export the module
exports.default = new TransactionsModule();
//# sourceMappingURL=transactions.module.js.map