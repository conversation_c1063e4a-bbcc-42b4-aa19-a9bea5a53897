/**
 * Import Update Script
 * 
 * Updates all import statements to use the new modular structure.
 */

const fs = require('fs');
const path = require('path');

// Define import mappings
const importMappings = {
  // Services
  '../services/identity-verification.service': '../services/identity-verification',
  '../services/advanced-report.service': '../services/reporting',
  '../services/fraud-detection.service': '../services/fraud-detection',
  './services/identity-verification.service': './services/identity-verification',
  './services/advanced-report.service': './services/reporting',
  './services/fraud-detection.service': './services/fraud-detection',
  
  // Controllers
  '../controllers/admin.controller': '../controllers/admin',
  '../controllers/fraud-detection.controller': '../controllers/fraud-detection',
  '../controllers/alert-aggregation.controller': '../controllers/alert-aggregation',
  '../controllers/identity-verification.controller': '../controllers/identity-verification',
  './controllers/admin.controller': './controllers/admin',
  './controllers/fraud-detection.controller': './controllers/fraud-detection',
  './controllers/alert-aggregation.controller': './controllers/alert-aggregation',
  './controllers/identity-verification.controller': './controllers/identity-verification',
  
  // Test utilities
  '../tests/utils/TestUtility': '../tests/utils/test-utilities',
  './tests/utils/TestUtility': './tests/utils/test-utilities',
  '../utils/TestUtility': '../tests/utils/test-utilities',
  './utils/TestUtility': './tests/utils/test-utilities',
};

// Class name mappings for specific imports
const classNameMappings = {
  'AdvancedReportService': 'ReportService',
  'TestUtility': 'TestUtilitySystem'
};

/**
 * Update imports in a file
 */
function updateImportsInFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;

  // Update import paths
  Object.entries(importMappings).forEach(([oldPath, newPath]) => {
    const oldImportRegex = new RegExp(`from ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
    if (oldImportRegex.test(content)) {
      content = content.replace(oldImportRegex, `from '${newPath}'`);
      updated = true;
      console.log(`  ✅ Updated import: ${oldPath} → ${newPath}`);
    }
  });

  // Update class names in imports
  Object.entries(classNameMappings).forEach(([oldName, newName]) => {
    const importRegex = new RegExp(`import\\s*{([^}]*\\b${oldName}\\b[^}]*)}`, 'g');
    content = content.replace(importRegex, (match, importList) => {
      if (importList.includes(oldName)) {
        const updatedImportList = importList.replace(new RegExp(`\\b${oldName}\\b`, 'g'), newName);
        updated = true;
        console.log(`  ✅ Updated class name: ${oldName} → ${newName}`);
        return `import {${updatedImportList}}`;
      }
      return match;
    });
  });

  // Update default imports
  const defaultImportRegex = /import\s+(\w+)\s+from\s+['"]([^'"]+)['"];?/g;
  content = content.replace(defaultImportRegex, (match, importName, importPath) => {
    if (importMappings[importPath]) {
      updated = true;
      console.log(`  ✅ Updated default import: ${importPath} → ${importMappings[importPath]}`);
      return `import ${importName} from '${importMappings[importPath]}';`;
    }
    return match;
  });

  if (updated) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }

  return false;
}

/**
 * Recursively find all TypeScript files
 */
function findTypeScriptFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules, dist, and .git directories
      if (!['node_modules', 'dist', '.git', '.next'].includes(item)) {
        findTypeScriptFiles(fullPath, files);
      }
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Main execution
 */
function main() {
  console.log('🔄 Starting import update process...\n');
  
  const projectRoot = process.cwd();
  const tsFiles = findTypeScriptFiles(projectRoot);
  
  console.log(`📁 Found ${tsFiles.length} TypeScript files\n`);
  
  let updatedFiles = 0;
  
  tsFiles.forEach(filePath => {
    const relativePath = path.relative(projectRoot, filePath);
    
    // Skip our new modular files to avoid circular updates
    if (relativePath.includes('/identity-verification/') ||
        relativePath.includes('/reporting/') ||
        relativePath.includes('/fraud-detection/') ||
        relativePath.includes('/admin/') ||
        relativePath.includes('/alert-aggregation/') ||
        relativePath.includes('/test-utilities/')) {
      return;
    }
    
    console.log(`📄 Checking: ${relativePath}`);
    
    if (updateImportsInFile(filePath)) {
      updatedFiles++;
      console.log(`  ✅ Updated imports in ${relativePath}`);
    } else {
      console.log(`  ⏭️  No updates needed`);
    }
    
    console.log('');
  });
  
  console.log(`\n🎉 Import update complete!`);
  console.log(`📊 Updated ${updatedFiles} files out of ${tsFiles.length} total files`);
  
  if (updatedFiles > 0) {
    console.log('\n✅ All imports have been updated to use the new modular structure!');
    console.log('\n🚀 Next steps:');
    console.log('1. Run TypeScript compilation to check for errors');
    console.log('2. Run tests to ensure functionality is preserved');
    console.log('3. Remove old monolithic files after verification');
  } else {
    console.log('\n✅ All imports are already up to date!');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { updateImportsInFile, findTypeScriptFiles, importMappings, classNameMappings };
