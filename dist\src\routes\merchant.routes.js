"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_validator_1 = require("express-validator");
const ControllerProvider_1 = __importDefault(require("../core/ControllerProvider"));
const RouteProvider_1 = __importDefault(require("../core/RouteProvider"));
const merchantController = ControllerProvider_1.default.getMerchantController();
// Create a route builder for merchant routes
const routeBuilder = RouteProvider_1.default.createRouteBuilder("merchant", "/api/merchants", "Merchant management routes");
// Define merchant routes
routeBuilder.addRoute({
    method: "GET",
    path: "/",
    description: "Get all merchants",
    middleware: ["authenticate", "isAdmin"],
    handler: merchantController.getAllMerchants
});
routeBuilder.addRoute({
    method: "GET",
    path: "/:id",
    description: "Get merchant by ID",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        (0, express_validator_1.param)("id").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: merchantController.getMerchantById
});
routeBuilder.addRoute({
    method: "PUT",
    path: "/:id",
    description: "Update merchant",
    middleware: ["authenticate", "isResourceOwner"],
    validation: [
        (0, express_validator_1.param)("id").isUUID().withMessage("Invalid merchant ID format"),
        (0, express_validator_1.body)("name").optional().isString().withMessage("Name must be a string"),
        (0, express_validator_1.body)("contactEmail").optional().isEmail().withMessage("Invalid email format"),
        (0, express_validator_1.body)("contactPhone").optional().isString().withMessage("Contact phone must be a string"),
        (0, express_validator_1.body)("status").optional().isIn(["ACTIVE", "INACTIVE", "SUSPENDED"]).withMessage("Invalid status")
    ],
    handler: merchantController.updateMerchant
});
routeBuilder.addRoute({
    method: "DELETE",
    path: "/:id",
    description: "Delete merchant",
    middleware: ["authenticate", "isAdmin"],
    validation: [
        (0, express_validator_1.param)("id").isUUID().withMessage("Invalid merchant ID format")
    ],
    handler: merchantController.deleteMerchant
});
// Export the router
exports.default = routeBuilder.build();
//# sourceMappingURL=merchant.routes.js.map