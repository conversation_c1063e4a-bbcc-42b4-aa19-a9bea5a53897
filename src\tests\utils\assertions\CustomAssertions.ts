/**
 * Custom Assertions
 * 
 * Custom Jest matchers for common test assertions.
 */

import { TestAssertions } from '../core/TestTypes';

/**
 * Custom Jest matchers
 */
export const customMatchers: TestAssertions = {
  /**
   * Check if a string is a valid UUID
   */
  toBeValidUUID(received: string): jest.CustomMatcherResult {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = typeof received === 'string' && uuidRegex.test(received);

    return {
      message: () =>
        pass
          ? `Expected ${received} not to be a valid UUID`
          : `Expected ${received} to be a valid UUID`,
      pass,
    };
  },

  /**
   * Check if a string is a valid email
   */
  toBeValidEmail(received: string): jest.CustomMatcherResult {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = typeof received === 'string' && emailRegex.test(received);

    return {
      message: () =>
        pass
          ? `Expected ${received} not to be a valid email`
          : `Expected ${received} to be a valid email`,
      pass,
    };
  },

  /**
   * Check if a value is a valid date
   */
  toBeValidDate(received: any): jest.CustomMatcherResult {
    const pass = received instanceof Date && !isNaN(received.getTime());

    return {
      message: () =>
        pass
          ? `Expected ${received} not to be a valid date`
          : `Expected ${received} to be a valid date`,
      pass,
    };
  },

  /**
   * Check if a string is a valid URL
   */
  toBeValidUrl(received: string): jest.CustomMatcherResult {
    let pass = false;
    
    try {
      new URL(received);
      pass = true;
    } catch {
      pass = false;
    }

    return {
      message: () =>
        pass
          ? `Expected ${received} not to be a valid URL`
          : `Expected ${received} to be a valid URL`,
      pass,
    };
  },

  /**
   * Check if an object has a valid structure
   */
  toHaveValidStructure(received: any, structure: any): jest.CustomMatcherResult {
    const pass = validateStructure(received, structure);

    return {
      message: () =>
        pass
          ? `Expected object not to have valid structure`
          : `Expected object to have valid structure`,
      pass,
    };
  },

  /**
   * Check if response matches API response format
   */
  toMatchApiResponse(received: any, expected: any): jest.CustomMatcherResult {
    const hasRequiredFields = 
      typeof received === 'object' &&
      received !== null &&
      'success' in received &&
      'status' in received &&
      'data' in received;

    const dataMatches = expected ? 
      JSON.stringify(received.data) === JSON.stringify(expected) : 
      true;

    const pass = hasRequiredFields && dataMatches;

    return {
      message: () =>
        pass
          ? `Expected response not to match API format`
          : `Expected response to match API format with required fields: success, status, data`,
      pass,
    };
  },

  /**
   * Check if a number is within a range
   */
  toBeWithinRange(received: number, min: number, max: number): jest.CustomMatcherResult {
    const pass = typeof received === 'number' && received >= min && received <= max;

    return {
      message: () =>
        pass
          ? `Expected ${received} not to be within range ${min}-${max}`
          : `Expected ${received} to be within range ${min}-${max}`,
      pass,
    };
  },

  /**
   * Check if a mock was called with valid arguments
   */
  toHaveBeenCalledWithValidArgs(received: jest.Mock, validator: Function): jest.CustomMatcherResult {
    const calls = received.mock.calls;
    const pass = calls.length > 0 && calls.every(call => validator(...call));

    return {
      message: () =>
        pass
          ? `Expected mock not to have been called with valid arguments`
          : `Expected mock to have been called with valid arguments`,
      pass,
    };
  },
};

/**
 * Validate object structure recursively
 */
function validateStructure(obj: any, structure: any): boolean {
  if (typeof structure !== 'object' || structure === null) {
    return typeof obj === typeof structure;
  }

  if (Array.isArray(structure)) {
    if (!Array.isArray(obj)) return false;
    if (structure.length === 0) return true;
    return obj.every(item => validateStructure(item, structure[0]));
  }

  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  for (const key in structure) {
    if (!(key in obj)) {
      return false;
    }

    if (!validateStructure(obj[key], structure[key])) {
      return false;
    }
  }

  return true;
}

/**
 * Additional assertion helpers
 */
export class AssertionHelpers {
  /**
   * Assert that an array contains unique elements
   */
  static assertUniqueArray<T>(array: T[], keyExtractor?: (item: T) => any): void {
    const keys = keyExtractor ? array.map(keyExtractor) : array;
    const uniqueKeys = new Set(keys);
    
    if (keys.length !== uniqueKeys.size) {
      throw new Error('Array contains duplicate elements');
    }
  }

  /**
   * Assert that an object has all required properties
   */
  static assertRequiredProperties(obj: any, requiredProps: string[]): void {
    const missingProps = requiredProps.filter(prop => !(prop in obj));
    
    if (missingProps.length > 0) {
      throw new Error(`Missing required properties: ${missingProps.join(', ')}`);
    }
  }

  /**
   * Assert that a value is one of the allowed values
   */
  static assertOneOf<T>(value: T, allowedValues: T[]): void {
    if (!allowedValues.includes(value)) {
      throw new Error(`Value ${value} is not one of allowed values: ${allowedValues.join(', ')}`);
    }
  }

  /**
   * Assert that a string matches a pattern
   */
  static assertMatchesPattern(value: string, pattern: RegExp, message?: string): void {
    if (!pattern.test(value)) {
      throw new Error(message || `Value "${value}" does not match pattern ${pattern}`);
    }
  }

  /**
   * Assert that an async function throws
   */
  static async assertAsyncThrows(
    fn: () => Promise<any>,
    expectedError?: string | RegExp | Error
  ): Promise<void> {
    let thrownError: any = null;

    try {
      await fn();
    } catch (error) {
      thrownError = error;
    }

    if (!thrownError) {
      throw new Error('Expected function to throw, but it did not');
    }

    if (expectedError) {
      if (typeof expectedError === 'string') {
        expect(thrownError.message).toContain(expectedError);
      } else if (expectedError instanceof RegExp) {
        expect(thrownError.message).toMatch(expectedError);
      } else if (expectedError instanceof Error) {
        expect(thrownError).toEqual(expectedError);
      }
    }
  }

  /**
   * Assert that an async function does not throw
   */
  static async assertAsyncDoesNotThrow(fn: () => Promise<any>): Promise<void> {
    try {
      await fn();
    } catch (error) {
      throw new Error(`Expected function not to throw, but it threw: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * Assert that a value is within a percentage of another value
   */
  static assertWithinPercentage(actual: number, expected: number, percentage: number): void {
    const tolerance = Math.abs(expected * percentage / 100);
    const difference = Math.abs(actual - expected);
    
    if (difference > tolerance) {
      throw new Error(`Expected ${actual} to be within ${percentage}% of ${expected}, but difference was ${difference}`);
    }
  }

  /**
   * Assert that an array is sorted
   */
  static assertArraySorted<T>(
    array: T[],
    compareFn?: (a: T, b: T) => number,
    ascending: boolean = true
  ): void {
    for (let i = 1; i < array.length; i++) {
      const comparison = compareFn ? compareFn(array[i - 1], array[i]) : 
        array[i - 1] < array[i] ? -1 : array[i - 1] > array[i] ? 1 : 0;
      
      const isCorrectOrder = ascending ? comparison <= 0 : comparison >= 0;
      
      if (!isCorrectOrder) {
        throw new Error(`Array is not sorted ${ascending ? 'ascending' : 'descending'} at index ${i}`);
      }
    }
  }

  /**
   * Assert that a mock was called in order
   */
  static assertMockCallOrder(mocks: jest.Mock[], expectedOrder: string[]): void {
    const calls: { mock: string; time: number }[] = [];
    
    mocks.forEach((mock, index) => {
      mock.mock.invocationCallOrder.forEach(callOrder => {
        calls.push({ mock: expectedOrder[index], time: callOrder });
      });
    });
    
    calls.sort((a, b) => a.time - b.time);
    const actualOrder = calls.map(call => call.mock);
    
    expect(actualOrder).toEqual(expectedOrder);
  }

  /**
   * Assert that a value is a valid JSON string
   */
  static assertValidJson(value: string): void {
    try {
      JSON.parse(value);
    } catch {
      throw new Error(`Value is not valid JSON: ${value}`);
    }
  }

  /**
   * Assert that two objects are deeply equal ignoring specified keys
   */
  static assertDeepEqualIgnoring(actual: any, expected: any, ignoredKeys: string[]): void {
    const cleanObject = (obj: any): any => {
      if (typeof obj !== 'object' || obj === null) return obj;
      if (Array.isArray(obj)) return obj.map(cleanObject);
      
      const cleaned: any = {};
      Object.keys(obj).forEach(key => {
        if (!ignoredKeys.includes(key)) {
          cleaned[key] = cleanObject(obj[key]);
        }
      });
      return cleaned;
    };

    expect(cleanObject(actual)).toEqual(cleanObject(expected));
  }
}

/**
 * Setup custom matchers
 */
export function setupCustomMatchers(): void {
  expect.extend(customMatchers);
}

/**
 * Database assertion helpers
 */
export class DatabaseAssertions {
  /**
   * Assert that a record exists in the database
   */
  static async assertRecordExists(
    prisma: any,
    model: string,
    where: any
  ): Promise<void> {
    const record = await prisma[model].findFirst({ where });
    
    if (!record) {
      throw new Error(`Expected record to exist in ${model} with conditions: ${JSON.stringify(where)}`);
    }
  }

  /**
   * Assert that a record does not exist in the database
   */
  static async assertRecordDoesNotExist(
    prisma: any,
    model: string,
    where: any
  ): Promise<void> {
    const record = await prisma[model].findFirst({ where });
    
    if (record) {
      throw new Error(`Expected record not to exist in ${model} with conditions: ${JSON.stringify(where)}`);
    }
  }

  /**
   * Assert record count
   */
  static async assertRecordCount(
    prisma: any,
    model: string,
    expectedCount: number,
    where?: any
  ): Promise<void> {
    const actualCount = await prisma[model].count({ where });
    
    if (actualCount !== expectedCount) {
      throw new Error(`Expected ${expectedCount} records in ${model}, but found ${actualCount}`);
    }
  }
}
