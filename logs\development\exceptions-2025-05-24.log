{"date":"Sat May 24 2025 03:55:34 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":14988.531},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":115131,"external":2196405,"heapTotal":32423936,"heapUsed":12958184,"rss":60010496},"pid":396,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:58:45 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15179.609},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":115131,"external":2196405,"heapTotal":31899648,"heapUsed":18072144,"rss":57335808},"pid":8584,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:59:13 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15207.343},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85253,"external":2197016,"heapTotal":30326784,"heapUsed":19180032,"rss":56365056},"pid":12776,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\websocket.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:59:25 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15218.843},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85253,"external":2197016,"heapTotal":30326784,"heapUsed":18086912,"rss":55525376},"pid":12628,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\websocket.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:59:37 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15231.093},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85253,"external":2197016,"heapTotal":30326784,"heapUsed":18145560,"rss":55476224},"pid":11816,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\websocket.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:00:54 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/transaction-monitor.service.ts(57,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(61,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(90,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(91,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(173,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(227,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(273,46): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(279,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(334,28): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(396,25): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/transaction-monitor.service.ts(57,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(61,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(90,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(91,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(173,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(227,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(273,46): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(279,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(334,28): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(396,25): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\transaction-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15307.812},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83853,"external":2196405,"heapTotal":32161792,"heapUsed":17920144,"rss":57192448},"pid":7504,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/transaction-monitor.service.ts(57,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(61,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(90,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(91,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(173,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(227,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(273,46): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(279,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(334,28): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(396,25): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\transaction-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\transaction-monitor.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:01:27 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15341.187},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84976,"external":2197016,"heapTotal":30588928,"heapUsed":19991752,"rss":57667584},"pid":760,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:01:37 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15350.734},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84976,"external":2197016,"heapTotal":30326784,"heapUsed":18189496,"rss":55824384},"pid":5480,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:01:48 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15361.953},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84976,"external":2197016,"heapTotal":30588928,"heapUsed":18205720,"rss":55865344},"pid":7980,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
