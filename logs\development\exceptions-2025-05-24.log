{"date":"Sat May 24 2025 03:55:34 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":14988.531},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":115131,"external":2196405,"heapTotal":32423936,"heapUsed":12958184,"rss":60010496},"pid":396,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-32208677082901094.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:58:45 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15179.609},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":115131,"external":2196405,"heapTotal":31899648,"heapUsed":18072144,"rss":57335808},"pid":8584,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/alert-monitor.service.ts(21,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(30,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(37,18): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(46,14): error TS1131: Property or signature expected.\r\nsrc/services/alert-monitor.service.ts(79,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(86,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(91,28): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(98,24): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(121,42): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(123,10): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(127,46): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(129,14): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(130,10): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,12): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\r\nsrc/services/alert-monitor.service.ts(130,30): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(131,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(136,18): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(137,13): error TS1003: Identifier expected.\r\nsrc/services/alert-monitor.service.ts(137,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,48): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(137,50): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(146,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(151,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(151,33): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(151,47): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(152,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(154,26): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(154,74): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(157,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(157,63): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(160,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(160,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(163,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(163,70): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(166,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(166,61): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(169,23): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(169,46): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,19): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(171,58): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(172,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(181,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(181,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(181,50): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(181,64): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(183,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(183,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(195,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(262,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(268,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(268,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(268,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(270,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,81): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(270,83): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(282,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(323,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(329,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(329,57): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(329,71): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(331,25): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,89): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(331,91): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(343,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(383,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(389,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(389,48): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(389,62): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(391,21): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,83): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(391,85): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(403,11): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(443,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(448,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(448,41): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(448,55): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(449,13): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,19): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(451,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(452,25): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(453,26): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(454,15): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,18): error TS1359: Identifier expected. 'const' is a reserved word that cannot be used here.\r\nsrc/services/alert-monitor.service.ts(457,24): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,33): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(457,36): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(460,29): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(461,37): error TS1136: Property assignment expected.\r\nsrc/services/alert-monitor.service.ts(514,11): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(529,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(529,13): error TS1434: Unexpected keyword or identifier.\r\nsrc/services/alert-monitor.service.ts(530,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(531,13): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(532,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(533,14): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(534,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(535,16): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(536,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(537,6): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(537,20): error TS1109: Expression expected.\r\nsrc/services/alert-monitor.service.ts(538,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(538,18): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(538,35): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,15): error TS1005: ':' expected.\r\nsrc/services/alert-monitor.service.ts(539,28): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(539,73): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,17): error TS1005: ',' expected.\r\nsrc/services/alert-monitor.service.ts(542,53): error TS1005: ';' expected.\r\nsrc/services/alert-monitor.service.ts(570,5): error TS1128: Declaration or statement expected.\r\nsrc/services/alert-monitor.service.ts(571,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\alert-monitor.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:59:13 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15207.343},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85253,"external":2197016,"heapTotal":30326784,"heapUsed":19180032,"rss":56365056},"pid":12776,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\websocket.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:59:25 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15218.843},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85253,"external":2197016,"heapTotal":30326784,"heapUsed":18086912,"rss":55525376},"pid":12628,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\websocket.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 03:59:37 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15231.093},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85253,"external":2197016,"heapTotal":30326784,"heapUsed":18145560,"rss":55476224},"pid":11816,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/websocket.service.ts(31,20): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(55,21): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(56,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(57,22): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,19): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/websocket.service.ts(72,24): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(72,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,46): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(72,48): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(73,27): error TS1005: ':' expected.\r\nsrc/services/websocket.service.ts(73,35): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(74,33): error TS1136: Property assignment expected.\r\nsrc/services/websocket.service.ts(75,23): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(90,17): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(90,19): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,13): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(95,15): error TS1005: 'try' expected.\r\nsrc/services/websocket.service.ts(98,9): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(98,10): error TS1128: Declaration or statement expected.\r\nsrc/services/websocket.service.ts(378,17): error TS1005: ')' expected.\r\nsrc/services/websocket.service.ts(383,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,34): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(383,47): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,20): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,24): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,26): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(392,32): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,1): error TS1005: ',' expected.\r\nsrc/services/websocket.service.ts(411,2): error TS1005: ')' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\websocket.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\websocket.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6730487670675016.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:00:54 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/transaction-monitor.service.ts(57,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(61,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(90,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(91,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(173,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(227,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(273,46): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(279,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(334,28): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(396,25): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/transaction-monitor.service.ts(57,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(61,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(90,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(91,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(173,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(227,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(273,46): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(279,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(334,28): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(396,25): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\transaction-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15307.812},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83853,"external":2196405,"heapTotal":32161792,"heapUsed":17920144,"rss":57192448},"pid":7504,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/transaction-monitor.service.ts(57,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(61,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(90,25): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(91,27): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(173,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(227,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(273,46): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(279,36): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(334,28): error TS1136: Property assignment expected.\r\nsrc/services/transaction-monitor.service.ts(396,25): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\transaction-monitor.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\transaction-monitor.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:01:27 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15341.187},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84976,"external":2197016,"heapTotal":30588928,"heapUsed":19991752,"rss":57667584},"pid":760,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:01:37 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15350.734},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84976,"external":2197016,"heapTotal":30326784,"heapUsed":18189496,"rss":55824384},"pid":5480,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:01:48 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15361.953},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84976,"external":2197016,"heapTotal":30588928,"heapUsed":18205720,"rss":55865344},"pid":7980,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/monitoring.service.ts(19,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(28,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(40,18): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(50,14): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(61,15): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(70,9): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(80,22): error TS1131: Property or signature expected.\r\nsrc/services/monitoring.service.ts(251,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(253,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(255,24): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(337,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(344,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(416,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(423,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(498,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(501,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(507,26): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(645,25): error TS1136: Property assignment expected.\r\nsrc/services/monitoring.service.ts(649,26): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\monitoring.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-9829817821100029.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:06:48 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/payment.service.ts(14,28): error TS1005: ';' expected.\r\nsrc/services/payment.service.ts(60,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/payment.service.ts(14,28): error TS1005: ';' expected.\r\nsrc/services/payment.service.ts(60,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\payment.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15662.156},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83703,"external":2196405,"heapTotal":32161792,"heapUsed":16250152,"rss":58699776},"pid":10440,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/payment.service.ts(14,28): error TS1005: ';' expected.\r\nsrc/services/payment.service.ts(60,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\payment.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\payment.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:07:22 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/payment.service.ts(60,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/payment.service.ts(60,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\payment.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15696.25},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83672,"external":2197016,"heapTotal":30588928,"heapUsed":18453776,"rss":57450496},"pid":11724,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/payment.service.ts(60,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\payment.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\payment.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:07:35 GMT+0300 (Eastern European Summer Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\Amazing pay flow\\src\\services\\payment.service.ts","F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts","F:\\Amazing pay flow\\src\\index.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../data/transactions.data'\nRequire stack:\n- F:\\Amazing pay flow\\src\\services\\payment.service.ts\n- F:\\Amazing pay flow\\src\\services\\monitoring.service.ts\n- F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts\n- F:\\Amazing pay flow\\src\\index.ts\nError: Cannot find module '../data/transactions.data'\nRequire stack:\n- F:\\Amazing pay flow\\src\\services\\payment.service.ts\n- F:\\Amazing pay flow\\src\\services\\monitoring.service.ts\n- F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts\n- F:\\Amazing pay flow\\src\\index.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\payment.service.ts:3:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)","os":{"loadavg":[0,0,0],"uptime":15708.875},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":93464,"external":2206609,"heapTotal":30851072,"heapUsed":18867928,"rss":56250368},"pid":7564,"uid":null,"version":"v20.19.1"},"stack":"Error: Cannot find module '../data/transactions.data'\nRequire stack:\n- F:\\Amazing pay flow\\src\\services\\payment.service.ts\n- F:\\Amazing pay flow\\src\\services\\monitoring.service.ts\n- F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts\n- F:\\Amazing pay flow\\src\\index.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\payment.service.ts:3:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":1,"file":"F:\\Amazing pay flow\\src\\services\\payment.service.ts","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false}]}
{"date":"Sat May 24 2025 04:08:37 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/middlewares/apiResponseMiddleware.ts(326,50): error TS1005: ';' expected.\r\nsrc/middlewares/apiResponseMiddleware.ts(328,14): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/middlewares/apiResponseMiddleware.ts(326,50): error TS1005: ';' expected.\r\nsrc/middlewares/apiResponseMiddleware.ts(328,14): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\apiResponseMiddleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15771.453},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83071,"external":2197016,"heapTotal":30326784,"heapUsed":18780992,"rss":55779328},"pid":5132,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/middlewares/apiResponseMiddleware.ts(326,50): error TS1005: ';' expected.\r\nsrc/middlewares/apiResponseMiddleware.ts(328,14): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\apiResponseMiddleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\middlewares\\apiResponseMiddleware.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:08:55 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/middlewares/apiResponseMiddleware.ts(326,50): error TS1005: ';' expected.\r\nsrc/middlewares/apiResponseMiddleware.ts(328,14): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/middlewares/apiResponseMiddleware.ts(326,50): error TS1005: ';' expected.\r\nsrc/middlewares/apiResponseMiddleware.ts(328,14): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\apiResponseMiddleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15788.796},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":83071,"external":2197016,"heapTotal":30326784,"heapUsed":17940152,"rss":57077760},"pid":11936,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/middlewares/apiResponseMiddleware.ts(326,50): error TS1005: ';' expected.\r\nsrc/middlewares/apiResponseMiddleware.ts(328,14): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\apiResponseMiddleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\middlewares\\apiResponseMiddleware.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:09:33 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/controllers/auth.controller.ts(55,32): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(101,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(125,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(148,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(172,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(205,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(238,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(316,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(317,25): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(318,17): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(318,27): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(319,34): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(323,14): error TS1005: ',' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/controllers/auth.controller.ts(55,32): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(101,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(125,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(148,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(172,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(205,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(238,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(316,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(317,25): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(318,17): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(318,27): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(319,34): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(323,14): error TS1005: ',' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\controllers\\auth.controller.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15826.75},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":100962,"external":2196661,"heapTotal":34283520,"heapUsed":17634688,"rss":63766528},"pid":12500,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/controllers/auth.controller.ts(55,32): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(101,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(125,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(148,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(172,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(205,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(238,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(316,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(317,25): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(318,17): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(318,27): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(319,34): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(323,14): error TS1005: ',' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\controllers\\auth.controller.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\controllers\\auth.controller.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:09:46 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/controllers/auth.controller.ts(55,32): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(101,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(125,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(148,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(172,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(205,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(238,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(316,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(317,25): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(318,17): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(318,27): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(319,34): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(323,14): error TS1005: ',' expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/controllers/auth.controller.ts(55,32): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(101,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(125,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(148,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(172,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(205,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(238,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(316,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(317,25): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(318,17): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(318,27): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(319,34): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(323,14): error TS1005: ',' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\controllers\\auth.controller.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15839.968},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":100962,"external":2196661,"heapTotal":34283520,"heapUsed":16975520,"rss":61718528},"pid":3720,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/controllers/auth.controller.ts(55,32): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(101,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(125,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(148,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(172,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(205,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(238,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(316,13): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(317,25): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(318,17): error TS1005: ',' expected.\r\nsrc/controllers/auth.controller.ts(318,27): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(319,34): error TS1136: Property assignment expected.\r\nsrc/controllers/auth.controller.ts(323,14): error TS1005: ',' expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\controllers\\auth.controller.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\controllers\\auth.controller.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:10:26 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/middlewares/auth.middleware.ts(66,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(66,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(68,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,10): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(72,7): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(82,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(94,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(94,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(96,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(97,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(98,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(105,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(117,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(117,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(119,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(120,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(121,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(128,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(140,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(140,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(142,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(143,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(144,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(151,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(165,17): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(165,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(167,26): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(168,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(169,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(170,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,14): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(178,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(195,17): error TS1005: ')' expected.\r\nsrc/middlewares/auth.middleware.ts(196,21): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(196,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(198,36): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(199,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(200,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(201,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,18): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(208,11): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(211,5): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(212,1): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/middlewares/auth.middleware.ts(66,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(66,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(68,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,10): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(72,7): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(82,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(94,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(94,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(96,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(97,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(98,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(105,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(117,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(117,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(119,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(120,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(121,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(128,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(140,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(140,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(142,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(143,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(144,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(151,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(165,17): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(165,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(167,26): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(168,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(169,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(170,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,14): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(178,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(195,17): error TS1005: ')' expected.\r\nsrc/middlewares/auth.middleware.ts(196,21): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(196,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(198,36): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(199,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(200,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(201,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,18): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(208,11): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(211,5): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(212,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\auth.middleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15880.046},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":104959,"external":2197232,"heapTotal":34283520,"heapUsed":18082752,"rss":61796352},"pid":12776,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/middlewares/auth.middleware.ts(66,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(66,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(68,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,10): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(72,7): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(82,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(94,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(94,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(96,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(97,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(98,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(105,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(117,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(117,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(119,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(120,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(121,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(128,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(140,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(140,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(142,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(143,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(144,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(151,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(165,17): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(165,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(167,26): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(168,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(169,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(170,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,14): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(178,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(195,17): error TS1005: ')' expected.\r\nsrc/middlewares/auth.middleware.ts(196,21): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(196,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(198,36): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(199,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(200,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(201,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,18): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(208,11): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(211,5): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(212,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\auth.middleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\middlewares\\auth.middleware.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:10:39 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/middlewares/auth.middleware.ts(66,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(66,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(68,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,10): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(72,7): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(82,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(94,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(94,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(96,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(97,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(98,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(105,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(117,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(117,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(119,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(120,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(121,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(128,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(140,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(140,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(142,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(143,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(144,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(151,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(165,17): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(165,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(167,26): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(168,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(169,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(170,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,14): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(178,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(195,17): error TS1005: ')' expected.\r\nsrc/middlewares/auth.middleware.ts(196,21): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(196,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(198,36): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(199,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(200,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(201,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,18): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(208,11): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(211,5): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(212,1): error TS1128: Declaration or statement expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/middlewares/auth.middleware.ts(66,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(66,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(68,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,10): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(72,7): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(82,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(94,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(94,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(96,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(97,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(98,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(105,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(117,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(117,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(119,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(120,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(121,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(128,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(140,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(140,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(142,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(143,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(144,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(151,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(165,17): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(165,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(167,26): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(168,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(169,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(170,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,14): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(178,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(195,17): error TS1005: ')' expected.\r\nsrc/middlewares/auth.middleware.ts(196,21): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(196,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(198,36): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(199,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(200,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(201,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,18): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(208,11): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(211,5): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(212,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\auth.middleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":15892.921},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":104959,"external":2196661,"heapTotal":34545664,"heapUsed":17109752,"rss":61632512},"pid":10140,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/middlewares/auth.middleware.ts(66,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(66,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(67,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(68,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(69,10): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(72,7): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(82,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(94,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(94,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(95,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(96,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(97,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(98,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(99,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(105,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(117,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(117,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(118,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(119,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(120,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(121,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(122,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(128,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(140,13): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(140,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(141,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(142,22): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(143,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(144,19): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,9): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(145,10): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(151,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(165,17): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(165,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(166,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(167,26): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(168,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(169,23): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(170,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,13): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(171,14): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(178,1): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(195,17): error TS1005: ')' expected.\r\nsrc/middlewares/auth.middleware.ts(196,21): error TS1005: '}' expected.\r\nsrc/middlewares/auth.middleware.ts(196,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,21): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(197,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(198,36): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(199,30): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(200,25): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(201,27): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,17): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(202,18): error TS1472: 'catch' or 'finally' expected.\r\nsrc/middlewares/auth.middleware.ts(208,11): error TS1005: ',' expected.\r\nsrc/middlewares/auth.middleware.ts(211,5): error TS1128: Declaration or statement expected.\r\nsrc/middlewares/auth.middleware.ts(212,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\middlewares\\auth.middleware.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\middlewares\\auth.middleware.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-4309526566273576.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:20:36 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(53,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(161,45): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(53,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(161,45): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16490.203},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85215,"external":2196405,"heapTotal":32161792,"heapUsed":16155832,"rss":60272640},"pid":3720,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(53,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(161,45): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:21:09 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(161,45): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(161,45): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16523.578},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85163,"external":2197016,"heapTotal":30588928,"heapUsed":18653496,"rss":57585664},"pid":12468,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(161,45): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:21:21 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16535.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85068,"external":2197016,"heapTotal":30588928,"heapUsed":18737520,"rss":57782272},"pid":908,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:21:34 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16548.453},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":85068,"external":2197016,"heapTotal":30588928,"heapUsed":18447528,"rss":56299520},"pid":6556,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(207,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:21:46 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16560.531},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84973,"external":2197016,"heapTotal":30588928,"heapUsed":18688952,"rss":56393728},"pid":12644,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:21:59 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16573.015},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84973,"external":2197016,"heapTotal":30588928,"heapUsed":18739768,"rss":57860096},"pid":10128,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(228,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(238,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:22:08 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(239,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(258,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(280,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(306,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(239,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(258,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(280,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(306,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16582.593},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84880,"external":2197016,"heapTotal":30588928,"heapUsed":18490488,"rss":56549376},"pid":12428,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(239,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(258,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(280,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(306,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:22:20 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16594.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84787,"external":2197016,"heapTotal":30588928,"heapUsed":18741680,"rss":57978880},"pid":12312,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(257,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:22:32 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16606.484},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84692,"external":2197016,"heapTotal":30588928,"heapUsed":18637264,"rss":56279040},"pid":13044,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:22:45 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16618.953},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84692,"external":2197016,"heapTotal":30588928,"heapUsed":18845216,"rss":57708544},"pid":6652,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(279,31): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(305,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(386,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(404,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(428,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(457,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(504,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:22:57 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(306,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(306,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16631.125},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84599,"external":2197016,"heapTotal":30588928,"heapUsed":18704016,"rss":56827904},"pid":9748,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(306,43): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:23:43 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16677.203},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84504,"external":2197016,"heapTotal":30588928,"heapUsed":18845056,"rss":56242176},"pid":12752,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(387,39): error TS1131: Property or signature expected.\r\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:23:55 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16689.046},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84409,"external":2197016,"heapTotal":30588928,"heapUsed":18419160,"rss":56373248},"pid":5400,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(405,27): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(429,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(430,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(458,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(505,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:24:07 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(430,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(431,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(459,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(430,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(431,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(459,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16701.218},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84316,"external":2197016,"heapTotal":30588928,"heapUsed":18626096,"rss":56512512},"pid":13016,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(430,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(431,26): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(459,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:24:21 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(459,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(459,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16715.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84130,"external":2197016,"heapTotal":30588928,"heapUsed":18578792,"rss":56389632},"pid":12900,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(459,25): error TS1136: Property assignment expected.\r\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:24:33 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16727.296},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":84037,"external":2197016,"heapTotal":30588928,"heapUsed":18732440,"rss":58126336},"pid":4508,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/services/verification.service.ts(506,27): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\verification.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\services\\verification.service.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:24:46 GMT+0300 (Eastern European Summer Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\Amazing pay flow\\src\\services\\blockchain\\binance-api.service.ts","F:\\Amazing pay flow\\src\\services\\verification.service.ts","F:\\Amazing pay flow\\src\\services\\monitoring.service.ts","F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts","F:\\Amazing pay flow\\src\\index.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../../shared/modules/services/ServiceError'\nRequire stack:\n- F:\\Amazing pay flow\\src\\services\\blockchain\\binance-api.service.ts\n- F:\\Amazing pay flow\\src\\services\\verification.service.ts\n- F:\\Amazing pay flow\\src\\services\\monitoring.service.ts\n- F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts\n- F:\\Amazing pay flow\\src\\index.ts\nError: Cannot find module '../../shared/modules/services/ServiceError'\nRequire stack:\n- F:\\Amazing pay flow\\src\\services\\blockchain\\binance-api.service.ts\n- F:\\Amazing pay flow\\src\\services\\verification.service.ts\n- F:\\Amazing pay flow\\src\\services\\monitoring.service.ts\n- F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts\n- F:\\Amazing pay flow\\src\\index.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\blockchain\\binance-api.service.ts:5:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)","os":{"loadavg":[0,0,0],"uptime":16740.062},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":172818,"external":3449600,"heapTotal":33734656,"heapUsed":17188488,"rss":62410752},"pid":10696,"uid":null,"version":"v20.19.1"},"stack":"Error: Cannot find module '../../shared/modules/services/ServiceError'\nRequire stack:\n- F:\\Amazing pay flow\\src\\services\\blockchain\\binance-api.service.ts\n- F:\\Amazing pay flow\\src\\services\\verification.service.ts\n- F:\\Amazing pay flow\\src\\services\\monitoring.service.ts\n- F:\\Amazing pay flow\\src\\middlewares\\monitoring.middleware.ts\n- F:\\Amazing pay flow\\src\\index.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Function.Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\blockchain\\binance-api.service.ts:5:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":1,"file":"F:\\Amazing pay flow\\src\\services\\blockchain\\binance-api.service.ts","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false}]}
{"date":"Sat May 24 2025 04:25:29 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16783.281},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":34258944,"heapUsed":15175776,"rss":63967232},"pid":10552,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:25:51 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16805.531},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":35045376,"heapUsed":14451600,"rss":63569920},"pid":2176,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:26:16 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16830.203},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":34521088,"heapUsed":14493344,"rss":61726720},"pid":11436,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:26:29 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16843.328},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":34783232,"heapUsed":15271440,"rss":62210048},"pid":600,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:26:42 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16855.781},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":34521088,"heapUsed":15194120,"rss":61804544},"pid":1672,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:26:54 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16868.453},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":34258944,"heapUsed":15119608,"rss":63455232},"pid":13116,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:27:04 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16877.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":34783232,"heapUsed":14650888,"rss":62169088},"pid":11900,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
{"date":"Sat May 24 2025 04:27:17 GMT+0300 (Eastern European Summer Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\nError: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","os":{"loadavg":[0,0,0],"uptime":16890.781},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","src/index.ts"],"cwd":"F:\\Amazing pay flow","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":136184,"external":3410228,"heapTotal":34521088,"heapUsed":14675456,"rss":61751296},"pid":11800,"uid":null,"version":"v20.19.1"},"stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/utils/verification-error-handler.ts(25,11): error TS1131: Property or signature expected.\r\nsrc/utils/verification-error-handler.ts(81,13): error TS1136: Property assignment expected.\r\n\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._compile (F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js:521:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js:71:20)\n    at Object.nodeDevHook [as .ts] (F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)","trace":[{"column":7,"file":"F:\\Amazing pay flow\\src\\utils\\verification-error-handler.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":25,"file":"F:\\Amazing pay flow\\node_modules\\source-map-support\\source-map-support.js","function":"Module._compile","line":521,"method":"_compile","native":false},{"column":33,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"Module.m._compile","line":69,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions..jsx.require.extensions..js","line":114,"method":".js","native":false},{"column":20,"file":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-8705144012743571.js","function":"require.extensions.<computed>","line":71,"method":"<computed>","native":false},{"column":13,"file":"F:\\Amazing pay flow\\node_modules\\ts-node-dev\\lib\\hook.js","function":"Object.nodeDevHook [as .ts]","line":63,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false}]}
