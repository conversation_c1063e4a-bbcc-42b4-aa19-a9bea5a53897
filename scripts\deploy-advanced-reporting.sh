#!/bin/bash

# Advanced Reporting Deployment Script
# This script deploys the advanced reporting system to production

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="amazingpay-flow"
DEPLOY_DIR="/opt/amazingpay"
BACKUP_DIR="/opt/amazingpay/backups"
LOG_FILE="/var/log/amazingpay/deploy.log"
SERVICE_NAME="amazingpay-flow"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        error "npm is not installed"
    fi
    
    # Check if PM2 is installed
    if ! command -v pm2 &> /dev/null; then
        warning "PM2 is not installed. Installing PM2..."
        npm install -g pm2
    fi
    
    # Check if PostgreSQL is running
    if ! systemctl is-active --quiet postgresql; then
        error "PostgreSQL is not running"
    fi
    
    success "Prerequisites check completed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Create timestamp for backup
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_NAME="backup_${TIMESTAMP}"
    
    # Backup application files
    if [ -d "$DEPLOY_DIR/current" ]; then
        log "Backing up application files..."
        tar -czf "$BACKUP_DIR/${BACKUP_NAME}_app.tar.gz" -C "$DEPLOY_DIR" current
    fi
    
    # Backup database
    log "Backing up database..."
    pg_dump amazingpay_production > "$BACKUP_DIR/${BACKUP_NAME}_db.sql"
    
    # Backup reports directory
    if [ -d "/var/lib/amazingpay/reports" ]; then
        log "Backing up reports..."
        tar -czf "$BACKUP_DIR/${BACKUP_NAME}_reports.tar.gz" -C "/var/lib/amazingpay" reports
    fi
    
    success "Backup created: $BACKUP_NAME"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    # Create deployment directories
    mkdir -p "$DEPLOY_DIR/releases"
    mkdir -p "$DEPLOY_DIR/shared/logs"
    mkdir -p "/var/lib/amazingpay/reports"
    
    # Create release directory
    RELEASE_DIR="$DEPLOY_DIR/releases/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$RELEASE_DIR"
    
    # Copy application files
    log "Copying application files..."
    cp -r . "$RELEASE_DIR/"
    
    # Install dependencies
    log "Installing dependencies..."
    cd "$RELEASE_DIR"
    npm ci --production
    
    # Build application
    log "Building application..."
    npm run build
    
    # Copy environment configuration
    log "Setting up environment configuration..."
    cp .env.production "$RELEASE_DIR/.env"
    
    # Create symlinks for shared resources
    ln -sfn "$DEPLOY_DIR/shared/logs" "$RELEASE_DIR/logs"
    ln -sfn "/var/lib/amazingpay/reports" "$RELEASE_DIR/reports"
    
    # Update current symlink
    ln -sfn "$RELEASE_DIR" "$DEPLOY_DIR/current"
    
    success "Application deployed to $RELEASE_DIR"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    cd "$DEPLOY_DIR/current"
    
    # Run Prisma migrations
    npx prisma migrate deploy
    
    # Seed advanced reporting data
    log "Seeding advanced reporting data..."
    npx ts-node prisma/seeds/advanced-reporting-seed.ts
    
    success "Database migrations completed"
}

# Update system configuration
update_system_config() {
    log "Updating system configuration..."
    
    # Create systemd service file
    cat > /tmp/amazingpay-flow.service << EOF
[Unit]
Description=AmazingPay Flow Application
After=network.target postgresql.service

[Service]
Type=simple
User=amazingpay
WorkingDirectory=$DEPLOY_DIR/current
ExecStart=/usr/bin/node dist/src/index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3002

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=amazingpay-flow

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DEPLOY_DIR /var/lib/amazingpay /var/log/amazingpay

[Install]
WantedBy=multi-user.target
EOF

    # Install service file (requires sudo)
    sudo mv /tmp/amazingpay-flow.service /etc/systemd/system/
    sudo systemctl daemon-reload
    sudo systemctl enable amazingpay-flow
    
    # Create log rotation configuration
    cat > /tmp/amazingpay-logrotate << EOF
/var/log/amazingpay/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 amazingpay amazingpay
    postrotate
        systemctl reload amazingpay-flow
    endscript
}
EOF

    sudo mv /tmp/amazingpay-logrotate /etc/logrotate.d/amazingpay
    
    success "System configuration updated"
}

# Start services
start_services() {
    log "Starting services..."
    
    # Stop existing service if running
    if systemctl is-active --quiet amazingpay-flow; then
        log "Stopping existing service..."
        sudo systemctl stop amazingpay-flow
    fi
    
    # Start the service
    log "Starting AmazingPay Flow service..."
    sudo systemctl start amazingpay-flow
    
    # Wait for service to start
    sleep 5
    
    # Check service status
    if systemctl is-active --quiet amazingpay-flow; then
        success "Service started successfully"
    else
        error "Failed to start service"
    fi
}

# Run health checks
run_health_checks() {
    log "Running health checks..."
    
    # Wait for application to be ready
    sleep 10
    
    # Check basic health endpoint
    if curl -f http://localhost:3002/health > /dev/null 2>&1; then
        success "Basic health check passed"
    else
        error "Basic health check failed"
    fi
    
    # Check advanced reporting health
    if curl -f -H "Authorization: Bearer test-token" http://localhost:3002/api/health/reports > /dev/null 2>&1; then
        log "Advanced reporting health check passed"
    else
        warning "Advanced reporting health check failed (may require valid token)"
    fi
    
    # Check database connectivity
    cd "$DEPLOY_DIR/current"
    if npx prisma db execute --stdin <<< "SELECT 1;" > /dev/null 2>&1; then
        success "Database connectivity check passed"
    else
        error "Database connectivity check failed"
    fi
    
    success "Health checks completed"
}

# Cleanup old releases
cleanup_old_releases() {
    log "Cleaning up old releases..."
    
    # Keep only the last 5 releases
    cd "$DEPLOY_DIR/releases"
    ls -t | tail -n +6 | xargs -r rm -rf
    
    success "Old releases cleaned up"
}

# Main deployment function
main() {
    log "Starting Advanced Reporting deployment..."
    
    check_root
    check_prerequisites
    create_backup
    deploy_application
    run_migrations
    update_system_config
    start_services
    run_health_checks
    cleanup_old_releases
    
    success "Advanced Reporting deployment completed successfully!"
    log "Application is running at http://localhost:3002"
    log "Dashboard available at http://localhost:3002/dashboard/reports"
    log "Health check: http://localhost:3002/health"
    log "API documentation: http://localhost:3002/api/docs"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        log "Rolling back to previous release..."
        # Implementation for rollback
        error "Rollback functionality not implemented yet"
        ;;
    "status")
        log "Checking service status..."
        systemctl status amazingpay-flow
        ;;
    "logs")
        log "Showing recent logs..."
        journalctl -u amazingpay-flow -n 50 --no-pager
        ;;
    "health")
        log "Running health checks..."
        run_health_checks
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|status|logs|health}"
        exit 1
        ;;
esac
