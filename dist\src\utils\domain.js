"use strict";
// jscpd:ignore-file
/**
 * Domain Utility
 * Re-exports from shared DomainUtils to eliminate duplication
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidDomainForEnvironment = exports.getWebSocketDomain = exports.getAdminDomain = exports.getApiDomain = exports.getSiteDomain = exports.getBaseDomain = void 0;
const utils_1 = require("../utils");
const environment_1 = require("../config/environment");
/**
 * Get the base site domain
 * @returns Base site domain
 */
const getBaseDomain = () => {
    return utils_1.DomainUtils.getBaseDomain((0, environment_1.getEnvironment)());
};
exports.getBaseDomain = getBaseDomain;
/**
 * Get site domain
 * @returns Production site domain
 */
const getSiteDomain = () => {
    return utils_1.DomainUtils.getSiteDomain((0, environment_1.getEnvironment)());
};
exports.getSiteDomain = getSiteDomain;
/**
 * Get API domain
 * @returns Production API domain
 */
const getApiDomain = () => {
    return utils_1.DomainUtils.getApiDomain((0, environment_1.getEnvironment)());
};
exports.getApiDomain = getApiDomain;
/**
 * Get admin domain
 * @returns Production admin domain
 */
const getAdminDomain = () => {
    return utils_1.DomainUtils.getAdminDomain((0, environment_1.getEnvironment)());
};
exports.getAdminDomain = getAdminDomain;
/**
 * Get WebSocket domain
 * @returns Production WebSocket domain
 */
const getWebSocketDomain = () => {
    return utils_1.DomainUtils.getWebSocketDomain((0, environment_1.getEnvironment)());
};
exports.getWebSocketDomain = getWebSocketDomain;
/**
 * Check if a domain is valid for production
 * @param domain Domain to check
 * @returns True if the domain is valid for the environment
 */
const isValidDomainForEnvironment = (domain) => {
    return utils_1.DomainUtils.isValidDomainForEnvironment(domain, (0, environment_1.getEnvironment)());
};
exports.isValidDomainForEnvironment = isValidDomainForEnvironment;
exports.default = {
    getBaseDomain: exports.getBaseDomain,
    getSiteDomain: exports.getSiteDomain,
    getApiDomain: exports.getApiDomain,
    getAdminDomain: exports.getAdminDomain,
    getWebSocketDomain: exports.getWebSocketDomain,
    isValidDomainForEnvironment: exports.isValidDomainForEnvironment
};
//# sourceMappingURL=domain.js.map