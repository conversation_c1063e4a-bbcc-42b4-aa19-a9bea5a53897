"use strict";
// jscpd:ignore-file
/**
 * RBAC Service
 *
 * Handles role-based access control operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RBACService = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
const role_model_1 = require("../models/role.model");
const permission_model_1 = require("../models/permission.model");
class RBACService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * Initialize RBAC system with default roles and permissions
     */
    async initialize() {
        try {
            logger_1.logger.info('Initializing RBAC system...');
            // Create system permissions
            for (const permission of permission_model_1.SYSTEM_PERMISSIONS) {
                await this.prisma.permission.upsert({
                    where: { name: permission.name },
                    update: {},
                    create: {
                        name: permission.name,
                        description: permission.description,
                        resource: permission.resource,
                        action: permission.action,
                        isActive: true,
                        isSystem: true,
                    },
                });
            }
            logger_1.logger.info('Created system permissions');
            // Create system roles
            for (const [key, role] of Object.entries(role_model_1.SYSTEM_ROLES)) {
                const createdRole = await this.prisma.role.upsert({
                    where: { name: role.name },
                    update: {},
                    create: {
                        name: role.name,
                        type: role.type,
                        description: role.description,
                        isActive: role.isActive ?? true,
                        isSystem: role.isSystem ?? true,
                    },
                });
                // Connect permissions to role
                if (role.permissions.includes('*')) {
                    // Special case: all permissions
                    const allPermissions = await this.prisma.permission.findMany();
                    await this.prisma.role.update({
                        where: { id: createdRole.id },
                        data: {
                            permissions: {
                                connect: allPermissions.map((p) => ({ id: p.id })),
                            },
                        },
                    });
                }
                else {
                    // Connect specific permissions
                    const permissions = await this.prisma.permission.findMany({
                        where: {
                            OR: role.permissions.map((p) => {
                                const [resource, action] = p.split(':');
                                return {
                                    resource,
                                    action,
                                };
                            }),
                        },
                    });
                    await this.prisma.role.update({
                        where: { id: createdRole.id },
                        data: {
                            permissions: {
                                connect: permissions.map((p) => ({ id: p.id })),
                            },
                        },
                    });
                }
            }
            logger_1.logger.info('Created system roles');
            // Migrate existing admin users to RBAC system
            const adminUsers = await this.prisma.user.findMany({
                where: { role: 'ADMIN' },
            });
            const adminRole = await this.prisma.role.findUnique({
                where: { name: 'Admin' },
            });
            if (adminRole) {
                for (const user of adminUsers) {
                    await this.prisma.user.update({
                        where: { id: user.id },
                        data: {
                            roles: {
                                connect: { id: adminRole.id },
                            },
                        },
                    });
                }
            }
            logger_1.logger.info('Migrated existing admin users to RBAC system');
        }
        catch (error) {
            logger_1.logger.error('Error initializing RBAC system:', error);
            throw error;
        }
    }
    /**
     * Check if a user has a specific permission
     * @param userId User ID
     * @param resource Resource name
     * @param action Action name
     * @returns Whether the user has the permission
     */
    async hasPermission(userId, resource, action) {
        try {
            // Get user with roles and permissions
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                include: {
                    roles: {
                        include: { permissions: true },
                    },
                },
            });
            if (!user || !user.roles || user.roles.length === 0) {
                return false;
            }
            // Check if user has any role with the required permission
            for (const role of user.roles) {
                // Super admin has all permissions
                if (role.type === 'super_admin') {
                    return true;
                }
                // Check if role has the specific permission
                const hasPermission = role.permissions.some((p) => p.resource === resource && p.action === action);
                if (hasPermission) {
                    return true;
                }
            }
            return false;
        }
        catch (error) {
            logger_1.logger.error('Error checking permission:', error);
            return false;
        }
    }
    /**
     * Get all permissions for a user
     * @param userId User ID
     * @returns Array of permission strings in format "resource:action"
     */
    async getUserPermissions(userId) {
        try {
            // Get user with roles and permissions
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                include: {
                    roles: {
                        include: { permissions: true },
                    },
                },
            });
            if (!user || !user.roles || user.roles.length === 0) {
                return [];
            }
            const permissions = new Set();
            for (const role of user.roles) {
                // Super admin has all permissions
                if (role.type === 'super_admin') {
                    const allPermissions = await this.prisma.permission.findMany();
                    allPermissions.forEach((p) => {
                        permissions.add(`${p.resource}:${p.action}`);
                    });
                    break;
                }
                // Add role permissions
                role.permissions.forEach((p) => {
                    permissions.add(`${p.resource}:${p.action}`);
                });
            }
            return Array.from(permissions);
        }
        catch (error) {
            logger_1.logger.error('Error getting user permissions:', error);
            return [];
        }
    }
}
exports.RBACService = RBACService;
// Export a singleton instance
exports.default = new RBACService(new client_1.PrismaClient());
