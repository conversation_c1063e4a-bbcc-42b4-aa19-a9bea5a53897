"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const advanced_report_controller_1 = require("../controllers/advanced-report.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = express_1.default.Router();
const reportController = new advanced_report_controller_1.AdvancedReportController();
// Apply authentication middleware to all routes
router.use(auth_middleware_1.authMiddleware);
// Report generation
router.post('/generate', reportController.generateReport);
// Report templates
router.get('/templates', reportController.getReportTemplates);
router.get('/templates/:id', reportController.getReportTemplateById);
router.post('/templates', reportController.createReportTemplate);
router.put('/templates/:id', reportController.updateReportTemplate);
router.delete('/templates/:id', reportController.deleteReportTemplate);
// Scheduled reports
router.get('/scheduled', reportController.getScheduledReports);
router.get('/scheduled/:id', reportController.getScheduledReportById);
router.post('/scheduled', reportController.createScheduledReport);
router.put('/scheduled/:id', reportController.updateScheduledReport);
router.delete('/scheduled/:id', reportController.deleteScheduledReport);
router.post('/scheduled/:id/run', reportController.runScheduledReport);
// Saved reports
router.get('/saved', reportController.getSavedReports);
router.get('/saved/:id', reportController.getSavedReportById);
router.get('/saved/:id/download', reportController.downloadSavedReport);
router.delete('/saved/:id', reportController.deleteSavedReport);
exports.default = router;
