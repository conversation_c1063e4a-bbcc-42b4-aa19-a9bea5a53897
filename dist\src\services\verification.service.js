"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationService = void 0;
// jscpd:ignore-file
const BaseService_1 = require("../shared/modules/services/BaseService");
const logger_1 = require("../utils/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
const payment_service_1 = require("./payment.service");
const binance_api_service_1 = require("./blockchain/binance-api.service");
const blockchain_api_service_1 = require("./blockchain/blockchain-api.service");
const types_1 = require("../types");
/**
 * Verification service
 */
class VerificationService extends BaseService_1.BaseService {
    /**
     * Create a new verification service
     */
    constructor() {
        super();
        this.paymentService = new payment_service_1.PaymentService();
        this.binanceApiService = new binance_api_service_1.BinanceApiService();
        this.blockchainApiService = new blockchain_api_service_1.BlockchainApiService();
    }
    /**
     * Verify a payment
     * @param paymentId Payment ID
     * @param method Verification method
     * @param data Verification data
     * @returns Verification result
     */
    async verifyPayment(paymentId, method, data) {
        try {
            // Get payment
            const payment = await prisma_1.default.transaction.findUnique({
                where: { id: paymentId },
            });
            if (!payment) {
                throw new Error('Payment not found');
            }
            // Verify payment based on method
            let verificationResult;
            switch (method) {
                case types_1.VerificationMethod.BINANCE_TRC20:
                    verificationResult = await this.verifyBinanceTRC20({
                        paymentId,
                        merchantId: payment.merchantId,
                        amount: payment.amount,
                        currency: payment.currency,
                        walletAddress: data.walletAddress,
                        apiKey: data.apiKey,
                        apiSecret: data.apiSecret,
                        network: 'TRC20',
                        txHash: data.txHash,
                    });
                    break;
                case types_1.VerificationMethod.BINANCE_C2C:
                    verificationResult = await this.verifyBinanceC2C({
                        paymentId,
                        merchantId: payment.merchantId,
                        amount: payment.amount,
                        currency: payment.currency,
                        note: data.note,
                        orderNumber: data.orderNumber,
                    });
                    break;
                case types_1.VerificationMethod.BINANCE_PAY:
                    verificationResult = await this.verifyBinancePay({
                        paymentId,
                        merchantId: payment.merchantId,
                        amount: payment.amount,
                        currency: payment.currency,
                        transactionId: data.transactionId,
                        apiKey: data.apiKey,
                        apiSecret: data.apiSecret,
                    });
                    break;
                case types_1.VerificationMethod.BLOCKCHAIN:
                    verificationResult = await this.verifyBlockchain({
                        paymentId,
                        merchantId: payment.merchantId,
                        amount: payment.amount,
                        currency: payment.currency,
                        walletAddress: data.walletAddress,
                        network: data.network,
                        transactionHash: data.txHash,
                    });
                    break;
                case types_1.VerificationMethod.MANUAL:
                    verificationResult = await this.verifyManual({
                        paymentId,
                        merchantId: payment.merchantId,
                        amount: payment.amount,
                        currency: payment.currency,
                        reference: data.reference,
                        notes: data.notes,
                    });
                    break;
                default:
                    throw new Error('Verification method not supported');
            }
            // Update payment verification status
            if (verificationResult.success) {
                await this.updateVerificationStatus(paymentId, types_1.VerificationStatus.VERIFIED, {
                    method,
                    ...verificationResult.details,
                });
            }
            else {
                await this.updateVerificationStatus(paymentId, types_1.VerificationStatus.FAILED, {
                    method,
                    reason: verificationResult.message,
                    ...verificationResult.details,
                });
            }
            return verificationResult;
        }
        catch (error) {
            logger_1.logger.error('Error verifying payment', { error, paymentId, method });
            return {
                success: false,
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Verify a Binance TRC20 payment
     * @param data Verification data
     * @returns Verification result
     */
    async verifyBinanceTRC20(data) {
        try {
            // Create Binance API service with merchant credentials
            const binanceApiService = new binance_api_service_1.BinanceApiService({
                apiKey: data.apiKey,
                apiSecret: data.apiSecret,
            });
            // Verify transaction
            const verificationResult = await binanceApiService.verifyTRC20Transaction(data.walletAddress, data.amount, data.txHash, data.currency);
            return verificationResult;
        }
        catch (error) {
            logger_1.logger.error('Error verifying Binance TRC20 payment', { error, data });
            return {
                success: false,
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Verify a Binance C2C payment
     * @param data Verification data
     * @returns Verification result
     */
    async verifyBinanceC2C(data) {
        try {
            // For now, we'll simulate a successful verification if the note contains the payment ID
            const isVerified = data.note.includes(data.paymentId);
            if (isVerified) {
                return {
                    success: true,
                    status: 'verified',
                    message: 'Payment verified successfully',
                    details: {
                        note: data.note,
                        orderNumber: data.orderNumber,
                        verifiedAt: new Date().toISOString(),
                    },
                };
            }
            else {
                return {
                    success: false,
                    status: 'failed',
                    message: 'Payment verification failed: note does not match',
                    details: { note: data.note },
                };
            }
        }
        catch (error) {
            logger_1.logger.error('Error verifying Binance C2C payment', { error, data });
            return {
                success: false,
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Verify a Binance Pay payment
     * @param data Verification data
     * @returns Verification result
     */
    async verifyBinancePay(data) {
        try {
            // For now, we'll simulate a successful verification if the transaction ID is provided
            const isVerified = !!data.transactionId;
            if (isVerified) {
                return {
                    success: true,
                    status: 'verified',
                    message: 'Payment verified successfully',
                    details: {
                        transactionId: data.transactionId,
                        verifiedAt: new Date().toISOString(),
                    },
                };
            }
            else {
                return {
                    success: false,
                    status: 'failed',
                    message: 'Payment verification failed: transaction ID not provided',
                };
            }
        }
        catch (error) {
            logger_1.logger.error('Error verifying Binance Pay payment', { error, data });
            return {
                success: false,
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Verify a blockchain payment
     * @param data Verification data
     * @returns Verification result
     */
    async verifyBlockchain(data) {
        try {
            if (!data.transactionHash) {
                return {
                    success: false,
                    status: 'failed',
                    message: 'Transaction hash is required',
                };
            }
            // Determine which blockchain verification method to use
            let verificationResult;
            switch (data.network.toLowerCase()) {
                case 'trc20':
                    verificationResult = await this.blockchainApiService.verifyTRC20Transaction(data.transactionHash, data.walletAddress, data.amount, data.currency);
                    break;
                case 'erc20':
                    verificationResult = await this.blockchainApiService.verifyERC20Transaction(data.transactionHash, data.walletAddress, data.amount, data.currency);
                    break;
                case 'bep20':
                    verificationResult = await this.blockchainApiService.verifyBEP20Transaction(data.transactionHash, data.walletAddress, data.amount, data.currency);
                    break;
                case 'polygon':
                    verificationResult = await this.blockchainApiService.verifyPolygonTransaction(data.transactionHash, data.walletAddress, data.amount, data.currency);
                    break;
                default:
                    return {
                        success: false,
                        status: 'failed',
                        message: `Unsupported, network: ${data.network}`,
                    };
            }
            return verificationResult;
        }
        catch (error) {
            logger_1.logger.error('Error verifying blockchain payment', { error, data });
            return {
                success: false,
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Verify a manual payment
     * @param data Verification data
     * @returns Verification result
     */
    async verifyManual(data) {
        try {
            // For manual verification, we'll always return success
            return {
                success: true,
                status: 'verified',
                message: 'Payment verified manually',
                details: {
                    reference: data.reference,
                    notes: data.notes,
                    verifiedAt: new Date().toISOString(),
                    verifiedBy: 'manual',
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Error verifying manual payment', { error, data });
            return {
                success: false,
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get verification status
     * @param paymentId Payment ID
     * @returns Verification status
     */
    async getVerificationStatus(paymentId) {
        try {
            const payment = await prisma_1.default.transaction.findUnique({
                where: { id: paymentId },
                select: { verificationStatus: true },
            });
            if (!payment) {
                throw new Error('Payment not found');
            }
            return payment.verificationStatus || types_1.VerificationStatus.PENDING;
        }
        catch (error) {
            logger_1.logger.error('Error getting verification status', { error, paymentId });
            throw new Error('Failed to get verification status');
        }
    }
    /**
     * Update verification status
     * @param paymentId Payment ID
     * @param status Verification status
     * @param details Verification details
     */
    async updateVerificationStatus(paymentId, status, details) {
        try {
            // Get payment to determine verification method
            const payment = await prisma_1.default.transaction.findUnique({
                where: { id: paymentId },
            });
            if (!payment) {
                throw new Error('Payment not found');
            }
            // Get verification method from payment or details
            const method = details?.method || payment.verificationMethod || types_1.VerificationMethod.MANUAL;
            // Update payment verification status
            await this.paymentService.updateVerificationStatus(paymentId, status, details, method);
            // Create verification history entry
            await prisma_1.default.verificationHistory.create({
                data: {
                    paymentId,
                    status,
                    method,
                    details: details || {},
                },
            });
        }
        catch (error) {
            logger_1.logger.error('Error updating verification status', { error, paymentId, status });
            throw new Error('Failed to update verification status');
        }
    }
    /**
     * Get verification history
     * @param paymentId Payment ID
     * @returns Verification history
     */
    async getVerificationHistory(paymentId) {
        try {
            const history = await prisma_1.default.verificationHistory.findMany({
                where: { paymentId },
                orderBy: { createdAt: 'desc' },
            });
            return history.map((entry) => ({
                status: entry.status,
                method: entry.method,
                details: entry.details,
                createdAt: entry.createdAt.toISOString(),
            }));
        }
        catch (error) {
            logger_1.logger.error('Error getting verification history', { error, paymentId });
            throw new Error('Failed to get verification history');
        }
    }
}
exports.VerificationService = VerificationService;
