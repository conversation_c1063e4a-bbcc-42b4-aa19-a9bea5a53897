/**
 * Report Service
 * 
 * Main orchestrator for the reporting system.
 */

import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import {
  ReportType,
  ExportFormat,
  ReportGenerationRequest,
  ReportGenerationResult,
  ReportSizeEstimate,
  ReportError,
  ReportErrorCode,
  IReportGenerator,
  IReportExporter
} from './ReportTypes';
import { logger } from '../../../lib/logger';

/**
 * Main report service class
 */
export class ReportService {
  private prisma: PrismaClient;
  private reportsDir: string;
  private generators: Map<ReportType, IReportGenerator>;
  private exporters: Map<ExportFormat, IReportExporter>;

  constructor(prisma: PrismaClient, reportsDir?: string) {
    this.prisma = prisma;
    this.reportsDir = reportsDir || path.join(__dirname, '../../../../reports');
    this.generators = new Map();
    this.exporters = new Map();

    // Ensure reports directory exists
    this.ensureReportsDirectory();
  }

  /**
   * Register a report generator
   */
  registerGenerator(generator: IReportGenerator): void {
    this.generators.set(generator.getType(), generator);
    logger.info(`Registered report generator for type: ${generator.getType()}`);
  }

  /**
   * Register a report exporter
   */
  registerExporter(exporter: IReportExporter): void {
    this.exporters.set(exporter.getFormat(), exporter);
    logger.info(`Registered report exporter for format: ${exporter.getFormat()}`);
  }

  /**
   * Generate a report
   */
  async generateReport(request: ReportGenerationRequest): Promise<ReportGenerationResult> {
    try {
      // Validate request
      this.validateRequest(request);

      // Get generator and exporter
      const generator = this.getGenerator(request.type);
      const exporter = this.getExporter(request.format);

      // Validate parameters
      generator.validateParameters(request.parameters);

      // Generate filename
      const filename = this.generateFilename(request);
      const filePath = path.join(this.reportsDir, filename);

      // Generate data
      logger.info(`Generating ${request.type} report with ${request.format} format`);
      const data = await generator.generateData(request.parameters);

      // Export data
      await exporter.export(data, filePath);

      // Get file stats
      const fileStats = fs.statSync(filePath);

      // Create saved report record
      const savedReport = await this.createSavedReportRecord({
        name: request.name || `${request.type.toLowerCase()}_report`,
        type: request.type,
        format: request.format,
        filePath: filePath.replace(/\\/g, '/'), // Normalize path
        fileSize: fileStats.size,
        parameters: request.parameters,
        rowCount: data.length
      });

      logger.info(`Report generated successfully: ${savedReport.id}`);

      return {
        id: savedReport.id,
        filePath,
        rowCount: data.length,
        format: request.format,
        fileSize: fileStats.size
      };

    } catch (error) {
      logger.error('Error generating report:', error);
      
      if (error instanceof ReportError) {
        throw error;
      }

      throw new ReportError(
        `Failed to generate report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ReportErrorCode.GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Estimate report size
   */
  async estimateReportSize(type: ReportType, parameters: any): Promise<ReportSizeEstimate> {
    try {
      const generator = this.getGenerator(type);
      
      // For now, use a simple estimation based on record count
      // In a real implementation, this would be more sophisticated
      const sampleData = await generator.generateData({ ...parameters, limit: 1 });
      
      // Estimate total records (simplified)
      const estimatedRecords = 1000; // This should be calculated based on actual query
      const avgRecordSize = JSON.stringify(sampleData[0] || {}).length;
      const estimatedSizeBytes = estimatedRecords * avgRecordSize;
      
      return {
        recordCount: estimatedRecords,
        estimatedSizeBytes,
        recommendStreaming: estimatedSizeBytes > 100 * 1024 * 1024, // 100MB threshold
        estimatedDuration: Math.ceil(estimatedRecords / 1000) * 1000 // 1 second per 1000 records
      };

    } catch (error) {
      logger.error('Error estimating report size:', error);
      
      // Return conservative estimate
      return {
        recordCount: 0,
        estimatedSizeBytes: 0,
        recommendStreaming: false,
        estimatedDuration: 5000
      };
    }
  }

  /**
   * Get available report types
   */
  getAvailableTypes(): ReportType[] {
    return Array.from(this.generators.keys());
  }

  /**
   * Get available export formats
   */
  getAvailableFormats(): ExportFormat[] {
    return Array.from(this.exporters.keys());
  }

  /**
   * Get report by ID
   */
  async getReportById(id: string) {
    try {
      const report = await this.prisma.savedReport.findUnique({
        where: { id }
      });

      if (!report) {
        throw new ReportError('Report not found', ReportErrorCode.FILE_NOT_FOUND, 404);
      }

      return report;
    } catch (error) {
      if (error instanceof ReportError) {
        throw error;
      }

      logger.error('Error getting report by ID:', error);
      throw new ReportError('Failed to retrieve report', ReportErrorCode.GENERATION_FAILED, 500);
    }
  }

  /**
   * Delete report
   */
  async deleteReport(id: string): Promise<void> {
    try {
      const report = await this.getReportById(id);

      // Delete file if it exists
      if (fs.existsSync(report.filePath)) {
        fs.unlinkSync(report.filePath);
      }

      // Delete database record
      await this.prisma.savedReport.delete({
        where: { id }
      });

      logger.info(`Report deleted: ${id}`);
    } catch (error) {
      logger.error('Error deleting report:', error);
      throw new ReportError('Failed to delete report', ReportErrorCode.GENERATION_FAILED, 500);
    }
  }

  /**
   * Validate report generation request
   */
  private validateRequest(request: ReportGenerationRequest): void {
    if (!request.type) {
      throw new ReportError('Report type is required', ReportErrorCode.INVALID_PARAMETERS);
    }

    if (!request.format) {
      throw new ReportError('Export format is required', ReportErrorCode.INVALID_PARAMETERS);
    }

    if (!this.generators.has(request.type)) {
      throw new ReportError(`Unsupported report type: ${request.type}`, ReportErrorCode.UNSUPPORTED_TYPE);
    }

    if (!this.exporters.has(request.format)) {
      throw new ReportError(`Unsupported export format: ${request.format}`, ReportErrorCode.UNSUPPORTED_FORMAT);
    }
  }

  /**
   * Get report generator
   */
  private getGenerator(type: ReportType): IReportGenerator {
    const generator = this.generators.get(type);
    if (!generator) {
      throw new ReportError(`No generator found for type: ${type}`, ReportErrorCode.UNSUPPORTED_TYPE);
    }
    return generator;
  }

  /**
   * Get report exporter
   */
  private getExporter(format: ExportFormat): IReportExporter {
    const exporter = this.exporters.get(format);
    if (!exporter) {
      throw new ReportError(`No exporter found for format: ${format}`, ReportErrorCode.UNSUPPORTED_FORMAT);
    }
    return exporter;
  }

  /**
   * Generate filename for report
   */
  private generateFilename(request: ReportGenerationRequest): string {
    const reportName = request.name || `${request.type.toLowerCase()}_report`;
    const timestamp = dayjs().format('YYYYMMDD_HHmmss');
    const exporter = this.getExporter(request.format);
    return `${reportName}_${timestamp}.${exporter.getFileExtension()}`;
  }

  /**
   * Ensure reports directory exists
   */
  private ensureReportsDirectory(): void {
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
      logger.info(`Created reports directory: ${this.reportsDir}`);
    }
  }

  /**
   * Create saved report record
   */
  private async createSavedReportRecord(data: any) {
    return await this.prisma.savedReport.create({
      data: {
        id: uuidv4(),
        name: data.name,
        description: data.description,
        filePath: data.filePath,
        fileType: data.format,
        templateId: data.templateId,
        parameters: JSON.stringify(data.parameters),
        createdById: data.parameters.userId,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      }
    });
  }
}
