"use strict";
// jscpd:ignore-file
/**
 * RBAC Middleware
 *
 * Middleware for role-based access control.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.attachPermissions = exports.requireRole = exports.requireAnyPermission = exports.requirePermission = void 0;
const rbac_service_1 = require("../services/rbac.service");
const client_1 = require("@prisma/client");
const logger_1 = require("../lib/logger");
const error_middleware_1 = require("./error.middleware");
const prisma = new client_1.PrismaClient();
const rbacService = new rbac_service_1.RBACService(prisma);
/**
 * Middleware to check if user has required permission
 */
const requirePermission = (resource, action) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new error_middleware_1.AppError({
                    message: "Unauthorized",
                    type: ErrorType.AUTHENTICATION,
                    code: ErrorCode.INVALID_CREDENTIALS
                }));
            }
            const hasPermission = await rbacService.hasPermission(req.user.id, resource, action);
            if (!hasPermission) {
                logger_1.logger.warn(`User ${req.user.id} attempted to access ${resource}:${action} without permission`);
                return next(new error_middleware_1.AppError({
                    message: "Forbidden: Insufficient permissions",
                    type: ErrorType.AUTHORIZATION,
                    code: ErrorCode.FORBIDDEN
                }));
            }
            next();
        }
        catch (error) {
            logger_1.logger.error("Error in RBAC middleware:", error);
            next(new error_middleware_1.AppError({
                message: "Internal server error",
                type: ErrorType.INTERNAL,
                code: ErrorCode.INTERNAL_SERVER_ERROR
            }));
        }
    };
};
exports.requirePermission = requirePermission;
/**
 * Middleware to check if user has any of the required permissions
 */
const requireAnyPermission = (permissions) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new error_middleware_1.AppError({
                    message: "Unauthorized",
                    type: ErrorType.AUTHENTICATION,
                    code: ErrorCode.INVALID_CREDENTIALS
                }));
            }
            for (const { resource, action } of permissions) {
                const hasPermission = await rbacService.hasPermission(req.user.id, resource, action);
                if (hasPermission) {
                    return next();
                }
            }
            logger_1.logger.warn(`User ${req.user.id} attempted to access a route without required permissions`);
            return next(new error_middleware_1.AppError({
                message: "Forbidden: Insufficient permissions",
                type: ErrorType.AUTHORIZATION,
                code: ErrorCode.FORBIDDEN
            }));
        }
        catch (error) {
            logger_1.logger.error("Error in RBAC middleware:", error);
            next(new error_middleware_1.AppError({
                message: "Internal server error",
                type: ErrorType.INTERNAL,
                code: ErrorCode.INTERNAL_SERVER_ERROR
            }));
        }
    };
};
exports.requireAnyPermission = requireAnyPermission;
/**
 * Middleware to check if user has required role
 */
const requireRole = (roleTypes) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new error_middleware_1.AppError({
                    message: "Unauthorized",
                    type: ErrorType.AUTHENTICATION,
                    code: ErrorCode.INVALID_CREDENTIALS
                }));
            }
            const user = await prisma.user.findUnique({
                where: { id: req.user.id },
                include: { roles: true }
            });
            if (!user) {
                return next(new error_middleware_1.AppError({
                    message: "Unauthorized",
                    type: ErrorType.AUTHENTICATION,
                    code: ErrorCode.INVALID_CREDENTIALS
                }));
            }
            const hasRole = user.roles.some(role => roleTypes.includes(role.type));
            if (!hasRole) {
                logger_1.logger.warn(`User ${req.user.id} attempted to access a route without required role`);
                return next(new error_middleware_1.AppError({
                    message: "Forbidden: Insufficient role",
                    type: ErrorType.AUTHORIZATION,
                    code: ErrorCode.FORBIDDEN
                }));
            }
            next();
        }
        catch (error) {
            logger_1.logger.error("Error in RBAC middleware:", error);
            next(new error_middleware_1.AppError({
                message: "Internal server error",
                type: ErrorType.INTERNAL,
                code: ErrorCode.INTERNAL_SERVER_ERROR
            }));
        }
    };
};
exports.requireRole = requireRole;
/**
 * Middleware to attach user permissions to request
 */
const attachPermissions = async (req, res, next) => {
    try {
        if (req.user) {
            const permissions = await rbacService.getUserPermissions(req.user.id);
            req.user.permissions = permissions;
        }
        next();
    }
    catch (error) {
        logger_1.logger.error("Error attaching permissions:", error);
        next();
    }
};
exports.attachPermissions = attachPermissions;
//# sourceMappingURL=rbac.middleware.js.map