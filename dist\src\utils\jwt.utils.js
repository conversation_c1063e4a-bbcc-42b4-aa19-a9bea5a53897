"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyToken = exports.generateRefreshToken = exports.generateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config");
const logger_1 = require("../lib/logger");
const error_middleware_1 = require("../middlewares/error.middleware");
/**
 * Generate a JWT token
 * @param payload Token payload
 * @returns JWT token
 */
const generateToken = (payload) => {
    if (!config_1.config.jwt.secret) {
        const error = new Error('JWT_SECRET is not defined');
        logger_1.logger.error('JWT generation failed:', error);
        throw new error_middleware_1.AppError('Authentication service configuration error', 500, false);
    }
    // Generate a unique token ID
    const tokenId = generateTokenId();
    // Get current environment
    const environment = process.env.NODE_ENV || 'development';
    return jsonwebtoken_1.default.sign({
        ...payload,
        environment, // Include environment in the token
        jti: tokenId,
    }, config_1.config.jwt.secret, {
        expiresIn: config_1.config.jwt.expiresIn,
        algorithm: 'HS256',
        issuer: `amazingpay-api-${environment}`, // Environment-specific issuer
        audience: `amazingpay-client-${environment}`, // Environment-specific audience
        notBefore: 0, // Token is valid immediately
    });
};
exports.generateToken = generateToken;
/**
 * Generate a refresh token
 * @param userId User ID
 * @returns Refresh token
 */
const generateRefreshToken = (userId) => {
    if (!config_1.config.jwt.secret) {
        const error = new Error('JWT_SECRET is not defined');
        logger_1.logger.error('JWT refresh token generation failed:', error);
        throw new error_middleware_1.AppError('Authentication service configuration error', 500, false);
    }
    // Generate a unique token ID
    const tokenId = generateTokenId();
    // Get current environment
    const environment = process.env.NODE_ENV || 'development';
    return jsonwebtoken_1.default.sign({
        userId,
        type: 'refresh',
        environment, // Include environment in the token
        jti: tokenId,
    }, config_1.config.jwt.secret, {
        expiresIn: config_1.config.jwt.refreshExpiresIn,
        algorithm: 'HS256',
        issuer: `amazingpay-api-${environment}`, // Environment-specific issuer
        audience: `amazingpay-client-${environment}`, // Environment-specific audience
    });
};
exports.generateRefreshToken = generateRefreshToken;
/**
 * Verify a JWT token
 * @param token JWT token
 * @returns Token payload
 */
const verifyToken = (token) => {
    if (!config_1.config.jwt.secret) {
        const error = new Error('JWT_SECRET is not defined');
        logger_1.logger.error('JWT verification failed:', error);
        throw new error_middleware_1.AppError('Authentication service configuration error', 500, false);
    }
    // Get current environment
    const environment = process.env.NODE_ENV || 'development';
    try {
        // Verify the token
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret, {
            algorithms: ['HS256'],
            issuer: `amazingpay-api-${environment}`, // Environment-specific issuer
            audience: `amazingpay-client-${environment}`, // Environment-specific audience
        });
        // Verify that the token was issued for this environment
        if (decoded.environment !== environment) {
            logger_1.logger.warn(`JWT token environment mismatch: token=${decoded.environment}, current=${environment}`);
            throw new error_middleware_1.AppError('Invalid token for this environment', 401, true);
        }
        return decoded;
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                logger_1.logger.warn('JWT token expired');
                throw new error_middleware_1.AppError('Token expired', 401, true);
            }
            else {
                logger_1.logger.warn('JWT verification failed:', error);
                throw new error_middleware_1.AppError('Invalid token', 401, true);
            }
        }
        logger_1.logger.error('Unexpected JWT verification error:', error);
        throw new error_middleware_1.AppError('Authentication failed', 401, true);
    }
};
exports.verifyToken = verifyToken;
/**
 * Generate a unique token ID
 * @returns Unique token ID
 */
const generateTokenId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
};
