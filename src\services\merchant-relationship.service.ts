// jscpd:ignore-file
/**
 * Merchant Relationship Service
 *
 * This service provides functionality for managing merchant relationships,
 * including communications, support tickets, and onboarding.
 */

import { BaseService, ServiceError } from "./base.service";
import {
    MerchantCommunication,
    MerchantSupportTicket,
    MerchantSupportMessage,
    MerchantOnboarding,
    MerchantOnboardingStep
} from "@prisma/client";
import { logger } from "../utils/logger";
import { ApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { EmailService } from "./email.service";
import { NotificationService } from "./notification.service";
import { Merchant, User } from '../types';


/**
 * Support ticket priority
 */
export enum SupportTicketPriority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

/**
 * Support ticket status
 */
export enum SupportTicketStatus {
  OPEN = "OPEN",
  IN_PROGRESS = "IN_PROGRESS",
  RESOLVED = "RESOLVED",
  CLOSED = "CLOSED",
}

/**
 * Communication type
 */
export enum CommunicationType {
  EMAIL = "EMAIL",
  SMS = "SMS",
  IN_APP = "IN_APP",
  PUSH = "PUSH",
}

/**
 * Communication status
 */
export enum CommunicationStatus {
  PENDING = "PENDING",
  SENT = "SENT",
  DELIVERED = "DELIVERED",
  READ = "READ",
  FAILED = "FAILED",
}

/**
 * Onboarding status
 */
export enum OnboardingStatus {
  NOT_STARTED = "NOT_STARTED",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
}

/**
 * Onboarding step status
 */
export enum OnboardingStepStatus {
  NOT_STARTED = "NOT_STARTED",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  SKIPPED = "SKIPPED",
}

/**
 * Support ticket with messages
 */
export interface SupportTicketWithMessages extends MerchantSupportTicket {
  messages: MerchantSupportMessage[];
}

/**
 * Onboarding with steps
 */
export interface OnboardingWithSteps extends MerchantOnboarding {
  steps: MerchantOnboardingStep[];
}

/**
 * Merchant relationship service
 */
export class MerchantRelationshipService extends BaseService {
    private emailService: EmailService;
    private notificationService: NotificationService;

    constructor() {
        super();
        this.emailService = new EmailService();
        this.notificationService = new NotificationService();
    }

    /**
   * Send communication to merchant
   * @param merchantId Merchant ID
   * @param type Communication type
   * @param subject Communication subject
   * @param content Communication content
   * @returns Created communication
   */
    async sendCommunication(
        merchantId: string,
        type: CommunicationType,
        subject: string,
        content: string
    ): Promise<MerchantCommunication> {
        try {
            // Check if merchant exists
            const merchant: any = await this.prisma.merchant.findUnique({
                where: { id: merchantId },
                include: { user: true
                }
            });

            if (!merchant) {
                throw this.genericError("Merchant not found", 404, ApiErrorCode.NOT_FOUND);
            }

            // Create communication record
            const communication: any = await this.prisma.merchantCommunication.create({
                data: {
                    merchantId,
                    type,
                    subject,
                    content,
                    status: CommunicationStatus.PENDING
                }
            });

            // Send communication based on type
            let status: any = CommunicationStatus.SENT;

            try {
                switch (type) {
                case CommunicationType.EMAIL:
                    await this.emailService.sendEmail(merchant.email, subject, content);
                    break;
                case CommunicationType.SMS:
                    // Implement SMS sending
                    break;
                case CommunicationType.IN_APP:
                    await this.notificationService.sendNotification(merchant.id // Fixed: using id instead of userId, subject, content);
                    break;
                case CommunicationType.PUSH:
                    // Implement push notification
                    break;
                }
            } catch (error) {
                logger.error(`Failed to send ${type} communication to merchant ${merchantId}:`, error);
                status = CommunicationStatus.FAILED;
            }

            // Update communication status
            const updatedCommunication: any = await this.prisma.merchantCommunication.update({
                where: { id: communication.id },
                data: { status }
            });

            logger.info(`Sent ${type} communication to merchant ${merchantId}`);
            return updatedCommunication;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error sending communication to merchant:", error);
            throw this.genericError("Failed to send communication to merchant", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Get merchant communications
   * @param merchantId Merchant ID
   * @returns List of communications
   */
    async getMerchantCommunications(merchantId: string): Promise<MerchantCommunication[]> {
        try {
            return await this.prisma.merchantCommunication.findMany({
                where: { merchantId },
                orderBy: { sentAt: "desc" }
            });
        } catch (error) {
            logger.error("Error getting merchant communications:", error);
            throw this.genericError("Failed to get merchant communications", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Mark communication as read
   * @param communicationId Communication ID
   * @returns Updated communication
   */
    async markCommunicationAsRead(communicationId: string): Promise<MerchantCommunication> {
        try {
            return await this.prisma.merchantCommunication.update({
                where: { id: communicationId },
                data: { status: CommunicationStatus.READ,
                    readAt: new Date()
                }
            });
        } catch (error) {
            logger.error("Error marking communication as read:", error);
            throw this.genericError("Failed to mark communication as read", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Create support ticket
   * @param merchantId Merchant ID
   * @param subject Ticket subject
   * @param description Ticket description
   * @param priority Ticket priority
   * @param category Ticket category
   * @returns Created ticket
   */
    async createSupportTicket(
        merchantId: string,
        subject: string,
        description: string,
        priority: SupportTicketPriority = SupportTicketPriority.MEDIUM,
        category?: string
    ): Promise<SupportTicketWithMessages> {
        try {
            // Check if merchant exists
            const merchant: any = await this.prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError("Merchant not found", 404, ApiErrorCode.NOT_FOUND);
            }

            // Create ticket
            const ticket: any = await this.prisma.merchantSupportTicket.create({
                data: {
                    merchantId,
                    subject,
                    description,
                    status: SupportTicketStatus.OPEN,
                    priority,
                    category
                }
            });

            // Create initial message
            const message: any = await this.prisma.merchantSupportMessage.create({
                data: { ticketId: ticket.id,
                    senderId: merchantId,
                    senderType: "MERCHANT",
                    content: description
                }
            });

            logger.info(`Created support ticket for merchant ${merchantId}`);
            return {
                ...ticket,
                messages: [message]
            };
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error creating support ticket:", error);
            throw this.genericError("Failed to create support ticket", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Get merchant support tickets
   * @param merchantId Merchant ID
   * @returns List of tickets
   */
    async getMerchantSupportTickets(merchantId: string): Promise<SupportTicketWithMessages[]> {
        try {
            const tickets: any = await this.prisma.merchantSupportTicket.findMany({
                where: { merchantId },
                orderBy: { createdAt: "desc" },
                include: { messages: {
                        orderBy: { createdAt: "asc" }
                    }
                }
            });

            return tickets;
        } catch (error) {
            logger.error("Error getting merchant support tickets:", error);
            throw this.genericError("Failed to get merchant support tickets", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Add message to support ticket
   * @param ticketId Ticket ID
   * @param senderId Sender ID
   * @param senderType Sender type
   * @param content Message content
   * @returns Created message
   */
    async addMessageToSupportTicket(
        ticketId: string,
        senderId: string,
        senderType: "MERCHANT" | "ADMIN" | "SYSTEM",
        content: string
    ): Promise<MerchantSupportMessage> {
        try {
            // Check if ticket exists
            const ticket: any = await this.prisma.merchantSupportTicket.findUnique({
                where: { id: ticketId }
            });

            if (!ticket) {
                throw this.genericError("Support ticket not found", 404, ApiErrorCode.NOT_FOUND);
            }

            // Create message
            const message: any = await this.prisma.merchantSupportMessage.create({
                data: {
                    ticketId,
                    senderId,
                    senderType,
                    content
                }
            });

            // Update ticket status if it's closed
            if (ticket.status === SupportTicketStatus.CLOSED) {
                await this.prisma.merchantSupportTicket.update({
                    where: { id: ticketId },
                    data: { status: SupportTicketStatus.OPEN }
                });
            }

            logger.info(`Added message to support ticket ${ticketId}`);
            return message;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error adding message to support ticket:", error);
            throw this.genericError("Failed to add message to support ticket", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Update support ticket status
   * @param ticketId Ticket ID
   * @param status New status
   * @param assignedTo User ID to assign ticket to
   * @returns Updated ticket
   */
    async updateSupportTicketStatus(
        ticketId: string,
        status: SupportTicketStatus,
        assignedTo?: string
    ): Promise<MerchantSupportTicket> {
        try {
            // Check if ticket exists
            const ticket: any = await this.prisma.merchantSupportTicket.findUnique({
                where: { id: ticketId }
            });

            if (!ticket) {
                throw this.genericError("Support ticket not found", 404, ApiErrorCode.NOT_FOUND);
            }

            // Update ticket
            const updatedTicket: any = await this.prisma.merchantSupportTicket.update({
                where: { id: ticketId },
                data: {
                    status,
                    assignedTo,
                    resolvedAt: status === SupportTicketStatus.RESOLVED ? new Date() : undefined
                }
            });

            // Add system message about status change
            await this.prisma.merchantSupportMessage.create({
                data: {
                    ticketId,
                    senderId: "SYSTEM",
                    senderType: "SYSTEM",
                    content: `Ticket status changed to ${status}${assignedTo ? ` and assigned to ${assignedTo}` : ""}`
                }
            });

            logger.info(`Updated support ticket ${ticketId} status to ${status}`);
            return updatedTicket;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error updating support ticket status:", error);
            throw this.genericError("Failed to update support ticket status", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Initialize merchant onboarding
   * @param merchantId Merchant ID
   * @returns Created onboarding
   */
    async initializeOnboarding(merchantId: string): Promise<OnboardingWithSteps> {
        try {
            // Check if merchant exists
            const merchant: any = await this.prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError("Merchant not found", 404, ApiErrorCode.NOT_FOUND);
            }

            // Check if onboarding already exists
            const existingOnboarding: any = await this.prisma.merchantOnboarding.findUnique({
                where: { merchantId }
            });

            if (existingOnboarding) {
                throw this.genericError("Onboarding already initialized for this merchant", 400, ApiErrorCode.DUPLICATE_ENTITY);
            }

            // Create onboarding
            const onboarding: any = await this.prisma.merchantOnboarding.create({
                data: {
                    merchantId,
                    status: OnboardingStatus.NOT_STARTED,
                    currentStep: "profile",
                    progress: 0
                }
            });

            // Define onboarding steps
            const steps: any = [
                {
                    name: "profile",
                    description: "Complete your merchant profile",
                    status: OnboardingStepStatus.NOT_STARTED,
                    order: 1
                },
                {
                    name: "payment-methods",
                    description: "Set up your payment methods",
                    status: OnboardingStepStatus.NOT_STARTED,
                    order: 2
                },
                {
                    name: "api-integration",
                    description: "Integrate with our API",
                    status: OnboardingStepStatus.NOT_STARTED,
                    order: 3
                },
                {
                    name: "test-payment",
                    description: "Make a test payment",
                    status: OnboardingStepStatus.NOT_STARTED,
                    order: 4
                },
                {
                    name: "go-live",
                    description: "Go live with your integration",
                    status: OnboardingStepStatus.NOT_STARTED,
                    order: 5
                }
            ];

            // Create onboarding steps
            const createdSteps: any = await Promise.all(
                steps.map(step => this.prisma.merchantOnboardingStep.create({
                        data: { onboardingId: onboarding.id,
                            ...step
                        }
                    })
                )
            );

            logger.info(`Initialized onboarding for merchant ${merchantId}`);
            return {
                ...onboarding,
                steps: createdSteps
            };
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error initializing merchant onboarding:", error);
            throw this.genericError("Failed to initialize merchant onboarding", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Get merchant onboarding
   * @param merchantId Merchant ID
   * @returns Onboarding with steps
   */
    async getMerchantOnboarding(merchantId: string): Promise<OnboardingWithSteps | null> {
        try {
            const onboarding: any = await this.prisma.merchantOnboarding.findUnique({
                where: { merchantId },
                include: { steps: {
                        orderBy: { order: "asc" }
                    }
                }
            });

            return onboarding;
        } catch (error) {
            logger.error("Error getting merchant onboarding:", error);
            throw this.genericError("Failed to get merchant onboarding", 500, ApiErrorCode.SERVER_ERROR);
        }
    }

    /**
   * Update onboarding step status
   * @param stepId Step ID
   * @param status New status
   * @returns Updated onboarding
   */
    async updateOnboardingStepStatus(
        stepId: string,
        status: OnboardingStepStatus
    ): Promise<OnboardingWithSteps> {
        try {
            // Check if step exists
            const step: any = await this.prisma.merchantOnboardingStep.findUnique({
                where: { id: stepId },
                include: { onboarding: true
                }
            });

            if (!step) {
                throw this.genericError("Onboarding step not found", 404, ApiErrorCode.NOT_FOUND);
            }

            // Update step
            const updatedStep: any = await this.prisma.merchantOnboardingStep.update({
                where: { id: stepId },
                data: {
                    status,
                    startedAt: status === OnboardingStepStatus.IN_PROGRESS ? new Date() : step.startedAt,
                    completedAt: status === OnboardingStepStatus.COMPLETED ? new Date() : step.completedAt
                }
            });

            // Get all steps for this onboarding
            const steps: any = await this.prisma.merchantOnboardingStep.findMany({
                where: { onboardingId: step.onboardingId },
                orderBy: { order: "asc" }
            });

            // Calculate progress
            const completedSteps: any = steps.filter(s => s.status === OnboardingStepStatus.COMPLETED || s.status === OnboardingStepStatus.SKIPPED
            ).length;
            const progress: any = (completedSteps / steps.length) * 100;

            // Determine current step
            let currentStep: any = steps.find(s => s.status === OnboardingStepStatus.IN_PROGRESS
            )?.name;

            if (!currentStep) {
                const nextStep: any = steps.find(s => s.status === OnboardingStepStatus.NOT_STARTED
                );
                currentStep = nextStep?.name || step.onboarding.currentStep;
            }

            // Determine overall status
            let onboardingStatus: any = OnboardingStatus.IN_PROGRESS;
            if (progress === 100) {
                onboardingStatus = OnboardingStatus.COMPLETED;
            } else if (progress === 0) {
                onboardingStatus = OnboardingStatus.NOT_STARTED;
            }

            // Update onboarding
            const updatedOnboarding: any = await this.prisma.merchantOnboarding.update({
                where: { id: step.onboardingId },
                data: { status: onboardingStatus,
                    currentStep,
                    progress,
                    completedAt: onboardingStatus === OnboardingStatus.COMPLETED ? new Date() : null
                },
                include: { steps: {
                        orderBy: { order: "asc" }
                    }
                }
            });

            logger.info(`Updated onboarding step ${stepId} status to ${status}`);
            return updatedOnboarding;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error updating onboarding step status:", error);
            throw this.genericError("Failed to update onboarding step status", 500, ApiErrorCode.SERVER_ERROR);
        }
    }
}
