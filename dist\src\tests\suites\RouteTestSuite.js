"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteTestSuite = void 0;
const RouteTestHelper_1 = require("../helpers/RouteTestHelper");
const RouteRegistry_1 = require("../../core/RouteRegistry");
const RouteVersionManager_1 = require("../../core/RouteVersionManager");
const RouteMonitor_1 = require("../../core/RouteMonitor");
const RouteHealthChecker_1 = require("../../core/RouteHealthChecker");
const logger_1 = require("../../lib/logger");
/**
 * Route test suite
 * This class provides a suite of tests for routes
 */
class RouteTestSuite {
    /**
     * Create a new route test suite
     * @param app Express application
     */
    constructor(app) {
        this.app = app;
        this.routeTestHelper = new RouteTestHelper_1.RouteTestHelper(app);
        this.routeRegistry = RouteRegistry_1.RouteRegistry.getInstance();
        this.routeVersionManager = RouteVersionManager_1.RouteVersionManager.getInstance();
        this.routeMonitor = RouteMonitor_1.RouteMonitor.getInstance();
        this.routeHealthChecker = RouteHealthChecker_1.RouteHealthChecker.getInstance();
    }
    /**
     * Run all tests
     */
    async runAllTests() {
        await this.testRouteRegistry();
        await this.testRouteVersionManager();
        await this.testRouteMonitor();
        await this.testRouteHealthChecker();
        await this.testExplorerRoutes();
        await this.testHealthCheckRoutes();
    }
    /**
     * Test route registry
     */
    async testRouteRegistry() {
        logger_1.logger.info("Testing route registry...");
        // Create mock router
        const router = this.routeTestHelper.createMockRouter("test", "/api/test", (req, res) => {
            res.json({ success: true });
        });
        // Register router
        this.app.use("/api/test", router);
        // Test route
        await this.routeTestHelper.testUnauthenticatedRoute("get", "/api/test", 200, { success: true });
        // Test route registry
        expect(this.routeRegistry.has("test")).toBe(true);
        expect(this.routeRegistry.getMetadata("test").path).toBe("/api/test");
        logger_1.logger.info("Route registry tests passed");
    }
    /**
     * Test route version manager
     */
    async testRouteVersionManager() {
        logger_1.logger.info("Testing route version manager...");
        // Create mock versioned router
        const router = this.routeTestHelper.createMockVersionedRouter("v1", "test", "/test", (req, res) => {
            res.json({ success: true, version: "v1" });
        });
        // Register router
        this.app.use("/api/v1/test", router);
        // Test route
        await this.routeTestHelper.testUnauthenticatedRoute("get", "/api/v1/test", 200, { success: true, version: "v1" });
        // Test route version manager
        expect(this.routeVersionManager.hasVersionedRoute("v1", "test")).toBe(true);
        expect(this.routeVersionManager.getVersions()).toContain("v1");
        logger_1.logger.info("Route version manager tests passed");
    }
    /**
     * Test route monitor
     */
    async testRouteMonitor() {
        logger_1.logger.info("Testing route monitor...");
        // Reset metrics
        this.routeTestHelper.resetRouteMetrics();
        // Create mock router
        const router = this.routeTestHelper.createMockRouter("monitor-test", "/api/monitor-test", (req, res) => {
            res.json({ success: true });
        });
        // Register router
        this.app.use("/api/monitor-test", router);
        // Apply monitoring middleware
        this.app.use(this.routeMonitor.createMonitoringMiddleware());
        // Test route
        await this.routeTestHelper.testUnauthenticatedRoute("get", "/api/monitor-test", 200, { success: true });
        // Test route monitor
        const metrics = this.routeMonitor.getMetrics("monitor-test");
        expect(metrics.hits).toBeGreaterThan(0);
        logger_1.logger.info("Route monitor tests passed");
    }
    /**
     * Test route health checker
     */
    async testRouteHealthChecker() {
        logger_1.logger.info("Testing route health checker...");
        // Create health check router
        const router = this.routeHealthChecker.createHealthCheckRouter();
        // Register router
        this.app.use("/api/health", router);
        // Test route
        await this.routeTestHelper.testUnauthenticatedRoute("get", "/api/health", 200);
        // Test route health checker
        const health = this.routeHealthChecker.checkSystemHealth();
        expect(health.status).toBeDefined();
        logger_1.logger.info("Route health checker tests passed");
    }
    /**
     * Test explorer routes
     */
    async testExplorerRoutes() {
        logger_1.logger.info("Testing explorer routes...");
        // Test route
        await this.routeTestHelper.testUnauthenticatedRoute("get", "/api/explorer", 200);
        logger_1.logger.info("Explorer routes tests passed");
    }
    /**
     * Test health check routes
     */
    async testHealthCheckRoutes() {
        logger_1.logger.info("Testing health check routes...");
        // Test route
        await this.routeTestHelper.testUnauthenticatedRoute("get", "/api/health", 200);
        logger_1.logger.info("Health check routes tests passed");
    }
}
exports.RouteTestSuite = RouteTestSuite;
exports.default = RouteTestSuite;
//# sourceMappingURL=RouteTestSuite.js.map