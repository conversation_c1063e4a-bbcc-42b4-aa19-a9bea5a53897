"use strict";
// jscpd:ignore-file
/**
 * Database Query Optimization Utility
 * Re-exports from shared DatabaseUtils to eliminate duplication
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabaseOptimization = exports.executeQueryWithTimeoutAndRetry = exports.optimizeFindManyQuery = exports.setupQueryPerformanceMonitoring = exports.clearRecentSlowQueries = exports.getRecentSlowQueries = exports.logSlowQuery = void 0;
const utils_1 = require("../utils");
const logger_1 = require("../lib/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
/**
 * Log a slow query
 * @param metrics Query metrics
 */
const logSlowQuery = (metrics) => {
    utils_1.DatabaseUtils.logSlowQuery(metrics, logger_1.logger);
};
exports.logSlowQuery = logSlowQuery;
/**
 * Get recent slow queries
 * @returns Recent slow queries
 */
const getRecentSlowQueries = () => {
    return utils_1.DatabaseUtils.getRecentSlowQueries();
};
exports.getRecentSlowQueries = getRecentSlowQueries;
/**
 * Clear recent slow queries
 */
const clearRecentSlowQueries = () => {
    utils_1.DatabaseUtils.clearRecentSlowQueries();
};
exports.clearRecentSlowQueries = clearRecentSlowQueries;
/**
 * Set up query performance monitoring
 * @param prismaClient Prisma client instance
 */
const setupQueryPerformanceMonitoring = (prismaClient = prisma_1.default) => {
    utils_1.DatabaseUtils.setupQueryPerformanceMonitoring(prismaClient, logger_1.logger);
};
exports.setupQueryPerformanceMonitoring = setupQueryPerformanceMonitoring;
/**
 * Optimize a findMany query by adding pagination and limiting fields
 * @param model Model name
 * @param args Query arguments
 * @param defaultPageSize Default page size
 * @param maxPageSize Maximum page size
 * @returns Optimized query arguments
 */
const optimizeFindManyQuery = (model, args = {}, defaultPageSize = 20, maxPageSize = 100) => {
    return utils_1.DatabaseUtils.optimizeFindManyQuery(model, args, defaultPageSize, maxPageSize, logger_1.logger);
};
exports.optimizeFindManyQuery = optimizeFindManyQuery;
/**
 * Execute a query with timeout and retry
 * @param queryFn Function that executes the query
 * @param timeout Timeout in milliseconds
 * @param retries Number of retries
 * @returns Query result
 */
const executeQueryWithTimeoutAndRetry = async (queryFn, timeout = 5000, retries = 3) => {
    return utils_1.DatabaseUtils.executeQueryWithTimeoutAndRetry(queryFn, timeout, retries, logger_1.logger);
};
exports.executeQueryWithTimeoutAndRetry = executeQueryWithTimeoutAndRetry;
/**
 * Initialize database optimization
 */
const initializeDatabaseOptimization = () => {
    utils_1.DatabaseUtils.initializeDatabaseOptimization(prisma_1.default, logger_1.logger);
};
exports.initializeDatabaseOptimization = initializeDatabaseOptimization;
exports.default = {
    logSlowQuery: exports.logSlowQuery,
    getRecentSlowQueries: exports.getRecentSlowQueries,
    clearRecentSlowQueries: exports.clearRecentSlowQueries,
    setupQueryPerformanceMonitoring: exports.setupQueryPerformanceMonitoring,
    optimizeFindManyQuery: exports.optimizeFindManyQuery,
    executeQueryWithTimeoutAndRetry: exports.executeQueryWithTimeoutAndRetry,
    initializeDatabaseOptimization: exports.initializeDatabaseOptimization
};
//# sourceMappingURL=db-optimization.js.map