/**
 * Alerting Utility
 *
 * This utility provides functions for sending alerts when critical issues occur.
 */
import { AlertSeverity, AlertType } from '../types/alert.types';
/**
 * Send an alert via the specified channels
 * @param alert Alert data
 * @param channels Alert channels
 * @returns Promise that resolves when the alert is sent
 */
export declare const sendAlert: any;
/**
 * Get recent alerts
 * @returns Recent alerts
 */
export declare const getRecentAlerts: any;
/**
 * Clear recent alerts
 */
export declare const clearRecentAlerts: any;
/**
 * Send a system alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
export declare const sendSystemAlert: any;
/**
 * Send a database alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
export declare const sendDatabaseAlert: any;
/**
 * Send a security alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
export declare const sendSecurityAlert: any;
declare const _default: {
    sendAlert: any;
    getRecentAlerts: any;
    clearRecentAlerts: any;
    sendSystemAlert: any;
    sendDatabaseAlert: any;
    sendSecurityAlert: any;
    AlertSeverity: typeof AlertSeverity;
    AlertType: typeof AlertType;
    AlertChannel: typeof import("../types/alert.types").AlertNotificationMethod;
};
export default _default;
//# sourceMappingURL=alerting.d.ts.map