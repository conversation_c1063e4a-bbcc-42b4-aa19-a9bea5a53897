"use strict";
// jscpd:ignore-file
/**
 * Fee Management Controller
 *
 * This controller handles fee management-related API endpoints.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeeManagementController = void 0;
const BaseController_1 = require("./base/BaseController");
const fee_management_service_1 = require("../services/fee-management.service");
const logger_1 = require("../lib/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
/**
 * Fee management controller
 */
class FeeManagementController extends BaseController_1.BaseController {
    constructor() {
        super();
        /**
         * Calculate fee for a transaction
         * @param req Request
         * @param res Response
         */
        this.calculateFee = async (req, res) => {
            try {
                const { merchantId } = req.params;
                const { amount, currency, paymentMethodId } = req.body;
                // Validate required parameters
                if (!merchantId || !amount || !currency) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                // Calculate fee
                const result = await this.feeManagementService.calculateFee(merchantId, parseFloat(amount), currency, paymentMethodId);
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error calculating fee:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to calculate fee', error.statusCode || 500);
            }
        };
        /**
         * Create fee tier
         * @param req Request
         * @param res Response
         */
        this.createFeeTier = async (req, res) => {
            try {
                const feeTier = req.body;
                // Validate required parameters
                if (!feeTier || !feeTier.name || !feeTier.type || !feeTier.calculationMethod) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                // Create fee tier
                const result = await this.feeManagementService.createFeeTier(feeTier);
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error creating fee tier:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to create fee tier', error.statusCode || 500);
            }
        };
        /**
         * Get fee tiers
         * @param req Request
         * @param res Response
         */
        this.getFeeTiers = async (req, res) => {
            try {
                const { type, merchantId, paymentMethodType } = req.query;
                // Get fee tiers
                const result = await this.feeManagementService.getFeeTiers(type, merchantId, paymentMethodType);
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error getting fee tiers:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to get fee tiers', error.statusCode || 500);
            }
        };
        /**
         * Get fee tier by ID
         * @param req Request
         * @param res Response
         */
        this.getFeeTierById = async (req, res) => {
            try {
                const { tierId } = req.params;
                // Validate required parameters
                if (!tierId) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                const result = await prisma_1.default.feeTier.findUnique({
                    where: { id: tierId },
                });
                if (!result) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Fee tier not found', 404);
                    return;
                }
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error getting fee tier:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to get fee tier', error.statusCode || 500);
            }
        };
        /**
         * Update fee tier
         * @param req Request
         * @param res Response
         */
        this.updateFeeTier = async (req, res) => {
            try {
                const { tierId } = req.params;
                const updates = req.body;
                // Validate required parameters
                if (!tierId || !updates) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                // Update fee tier
                const result = await this.feeManagementService.updateFeeTier(tierId, updates);
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error updating fee tier:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to update fee tier', error.statusCode || 500);
            }
        };
        /**
         * Delete fee tier
         * @param req Request
         * @param res Response
         */
        this.deleteFeeTier = async (req, res) => {
            try {
                const { tierId } = req.params;
                // Validate required parameters
                if (!tierId) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                await this.feeManagementService.deleteFeeTier(tierId);
                apiResponseMiddleware_1.ApiResponse.success(res, { message: 'Fee tier deleted successfully' });
            }
            catch (error) {
                logger_1.logger.error('Error deleting fee tier:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to delete fee tier', error.statusCode || 500);
            }
        };
        /**
         * Get fee calculations for merchant
         * @param req Request
         * @param res Response
         */
        this.getMerchantFeeCalculations = async (req, res) => {
            try {
                const { merchantId } = req.params;
                const { startDate, endDate } = req.query;
                // Validate required parameters
                if (!merchantId) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                // Build query
                const whereClause = { merchantId };
                if (startDate) {
                    whereClause.createdAt = {
                        ...(whereClause.createdAt || {}),
                        gte: new Date(startDate),
                    };
                }
                if (endDate) {
                    whereClause.createdAt = {
                        ...(whereClause.createdAt || {}),
                        lte: new Date(endDate),
                    };
                }
                // Get fee calculations
                const result = await prisma_1.default.feeCalculation.findMany({
                    where: whereClause,
                    include: {
                        feeTier: true,
                        paymentMethod: {
                            select: {
                                id: true,
                                name: true,
                                type: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                });
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error getting merchant fee calculations:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to get merchant fee calculations', error.statusCode || 500);
            }
        };
        /**
         * Create merchant fee discount
         * @param req Request
         * @param res Response
         */
        this.createMerchantFeeDiscount = async (req, res) => {
            try {
                const { merchantId } = req.params;
                const { discountType, discountValue, startDate, endDate, reason } = req.body;
                // Validate required parameters
                if (!merchantId || !discountType || discountValue === undefined || !startDate) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                // Create merchant fee discount
                const result = await prisma_1.default.merchantFeeDiscount.create({
                    data: {
                        merchantId,
                        discountType,
                        discountValue: parseFloat(discountValue),
                        startDate: new Date(startDate),
                        endDate: endDate ? new Date(endDate) : null,
                        reason,
                    },
                });
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error creating merchant fee discount:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to create merchant fee discount', error.statusCode || 500);
            }
        };
        /**
         * Get merchant fee discounts
         * @param req Request
         * @param res Response
         */
        this.getMerchantFeeDiscounts = async (req, res) => {
            try {
                const { merchantId } = req.params;
                // Validate required parameters
                if (!merchantId) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                const result = await prisma_1.default.merchantFeeDiscount.findMany({
                    where: { merchantId },
                    orderBy: { createdAt: 'desc' },
                });
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error getting merchant fee discounts:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to get merchant fee discounts', error.statusCode || 500);
            }
        };
        /**
         * Update merchant fee discount
         * @param req Request
         * @param res Response
         */
        this.updateMerchantFeeDiscount = async (req, res) => {
            try {
                const { discountId } = req.params;
                const { discountValue, endDate, reason, isActive } = req.body;
                // Validate required parameters
                if (!discountId) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                // Build update data
                const updateData = {};
                if (discountValue !== undefined) {
                    updateData.discountValue = parseFloat(discountValue);
                }
                if (endDate !== undefined) {
                    updateData.endDate = endDate ? new Date(endDate) : null;
                }
                if (reason !== undefined) {
                    updateData.reason = reason;
                }
                if (isActive !== undefined) {
                    updateData.isActive = isActive;
                }
                // Update merchant fee discount
                const result = await prisma_1.default.merchantFeeDiscount.update({
                    where: { id: discountId },
                    data: updateData,
                });
                apiResponseMiddleware_1.ApiResponse.success(res, result);
            }
            catch (error) {
                logger_1.logger.error('Error updating merchant fee discount:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to update merchant fee discount', error.statusCode || 500);
            }
        };
        /**
         * Delete merchant fee discount
         * @param req Request
         * @param res Response
         */
        this.deleteMerchantFeeDiscount = async (req, res) => {
            try {
                const { discountId } = req.params;
                // Validate required parameters
                if (!discountId) {
                    apiResponseMiddleware_1.ApiResponse.error(res, 'Missing required parameters', 400);
                    return;
                }
                // Delete merchant fee discount
                await prisma_1.default.merchantFeeDiscount.delete({
                    where: { id: discountId },
                });
                apiResponseMiddleware_1.ApiResponse.success(res, { message: 'Merchant fee discount deleted successfully' });
            }
            catch (error) {
                logger_1.logger.error('Error deleting merchant fee discount:', error);
                apiResponseMiddleware_1.ApiResponse.error(res, error.message || 'Failed to delete merchant fee discount', error.statusCode || 500);
            }
        };
        this.feeManagementService = new fee_management_service_1.FeeManagementService();
    }
}
exports.FeeManagementController = FeeManagementController;
exports.default = new FeeManagementController();
