"use strict";
// jscpd:ignore-file
/**
 * RBAC Initializer
 *
 * Service for initializing the RBAC system with predefined roles and permissions.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RBACInitializer = void 0;
const RoleTemplates_1 = require("../../config/rbac/RoleTemplates");
const PermissionGroups_1 = require("../../config/rbac/PermissionGroups");
const logger_1 = require("../../lib/logger");
/**
 * RBAC initializer service
 */
class RBACInitializer {
    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
   * Initialize the RBAC system
   */
    async initialize() {
        logger_1.logger.info("Initializing RBAC system");
        try {
            // Check if the required models exist in the database
            const hasRbacModels = await this.checkRbacModelsExist();
            if (hasRbacModels) {
                // Initialize permissions
                await this.initializePermissions();
                // Initialize roles
                await this.initializeRoles();
                logger_1.logger.info("RBAC system initialized successfully");
            }
            else {
                logger_1.logger.warn("RBAC models not found in database. Skipping RBAC initialization.");
                logger_1.logger.info("The application will continue to run with limited RBAC functionality.");
            }
        }
        catch (error) {
            logger_1.logger.error("Error initializing RBAC system:", error);
            logger_1.logger.warn("Continuing without RBAC initialization");
        }
    }
    /**
   * Check if RBAC models exist in the database
   */
    async checkRbacModelsExist() {
        try {
            // Check if the required models exist in the Prisma client
            const hasPermissionModel = !!this.prisma.permission;
            const hasRoleModel = !!this.prisma.roleModel;
            const hasRolePermissionModel = !!this.prisma.rolePermission;
            const hasUserRoleModel = !!this.prisma.userRole;
            const allModelsExist = hasPermissionModel && hasRoleModel && hasRolePermissionModel && hasUserRoleModel;
            if (!allModelsExist) {
                logger_1.logger.warn("Some RBAC models are missing from the Prisma client:");
                if (!hasPermissionModel)
                    logger_1.logger.warn("- Permission model is missing");
                if (!hasRoleModel)
                    logger_1.logger.warn("- RoleModel model is missing");
                if (!hasRolePermissionModel)
                    logger_1.logger.warn("- RolePermission model is missing");
                if (!hasUserRoleModel)
                    logger_1.logger.warn("- UserRole model is missing");
            }
            return allModelsExist;
        }
        catch (error) {
            logger_1.logger.error("Error checking RBAC models:", error);
            return false;
        }
    }
    /**
   * Initialize permissions
   */
    async initializePermissions() {
        logger_1.logger.info("Initializing permissions");
        try {
            // Parse permissions into resource and action
            const permissionData = PermissionGroups_1.ALL_PERMISSIONS.map((permission));
            {
                const [resource, action] = permission.split(":");
                return {
                    resource,
                    action,
                    description: `${action} ${resource}`
                };
            }
            ;
            // Check if the permission model exists
            if (this.prisma.permission) {
                // Create permissions in database
                for (const permission of permissionData) {
                    await this.prisma.permission.upsert({
                        where: { resource_action: {
                                resource: permission.resource,
                                action: permission.action
                            }
                        },
                        update: { description: permission.description
                        },
                        create: { resource: permission.resource,
                            action: permission.action,
                            description: permission.description
                        }
                    });
                }
                logger_1.logger.info(`Initialized ${permissionData.length} permissions`);
            }
            else {
                logger_1.logger.warn("Permission model not found in Prisma schema. Skipping permission initialization.");
            }
        }
        catch (error) {
            logger_1.logger.error("Error initializing permissions:", error);
            logger_1.logger.warn("Continuing without initializing permissions");
        }
    }
    /**
   * Initialize roles
   */
    async initializeRoles() {
        logger_1.logger.info("Initializing roles");
        try {
            // Check if the role model exists
            if (this.prisma.roleModel) {
                // Create roles in database
                for (const [key, template] of Object.entries(RoleTemplates_1.ROLE_TEMPLATES)) {
                    await this.createOrUpdateRole(template);
                }
                logger_1.logger.info(`Initialized ${Object.keys(RoleTemplates_1.ROLE_TEMPLATES).length} roles`);
            }
            else {
                logger_1.logger.warn("RoleModel not found in Prisma schema. Skipping role initialization.");
            }
        }
        catch (error) {
            logger_1.logger.error("Error initializing roles:", error);
            logger_1.logger.warn("Continuing without initializing roles");
        }
    }
    /**
   * Create or update a role
   *
   * @param template Role template
   */
    async createOrUpdateRole(template) {
        try {
            // Check if the role model exists
            if (!this.prisma.roleModel) {
                logger_1.logger.warn(`RoleModel not found in Prisma schema. Skipping role creation for ${template.name}.`);
                return;
            }
            // Find or create role
            const role = await this.prisma.roleModel.upsert({
                where: { type: template.type
                },
                update: { name: template.name,
                    description: template.description,
                    isSystem: template.isSystem || false
                },
                create: { type: template.type,
                    name: template.name,
                    description: template.description,
                    isSystem: template.isSystem || false
                }
            });
            // Check if the permission model exists
            if (!this.prisma.permission) {
                logger_1.logger.warn(`Permission model not found in Prisma schema. Skipping permission assignment for role ${template.name}.`);
                return;
            }
            // Get permissions
            const permissions = await this.prisma.permission.findMany({
                where: { OR: template.permissions.map((permission)) }
            });
            {
                const [resource, action] = permission.split(":");
                return {
                    resource,
                    action
                };
            }
        }
        finally // Check if the rolePermission model exists
         {
        }
    }
    ;
    // Check if the rolePermission model exists
    if(, prisma, rolePermission) {
        logger_1.logger.warn(`RolePermission model not found in Prisma schema. Skipping permission assignment for role ${template.name}.`);
        return;
    }
}
exports.RBACInitializer = RBACInitializer;
// Clear existing role permissions
await this.prisma.rolePermission.deleteMany({
    where: { roleId: role.id
    }
});
// Create role permissions
for (const permission of permissions) {
    await this.prisma.rolePermission.create({
        data: { roleId: role.id,
            permissionId: permission.id
        }
    });
}
logger_1.logger.info(`Created/updated role: ${template.name} with ${permissions.length} permissions`);
try { }
catch (error) {
    logger_1.logger.error(`Error creating/updating role ${template.name}:`, error);
    // Don't throw the error, just log it and continue
}
async;
createSuperAdmin(email, string, password, string, name, string);
Promise < void  > {
    try: {
        logger: logger_1.logger, : .info(`Creating super admin user: ${email}`),
        : .prisma.roleModel
    }
};
{
    logger_1.logger.warn("RoleModel not found in Prisma schema. Skipping super admin creation.");
    return;
}
// Find super admin role
const superAdminRole = await this.prisma.roleModel.findUnique({
    where: { type: "super_admin"
    }
});
if (!superAdminRole) {
    logger_1.logger.warn("Super admin role not found. Skipping super admin creation.");
    return;
}
// Check if the user model exists
if (!this.prisma.user) {
    logger_1.logger.warn("User model not found in Prisma schema. Skipping super admin creation.");
    return;
}
// Create user
const user = await this.prisma.user.upsert({
    where: {
        email
    },
    update: {
        name,
        isActive: true
    },
    create: {
        email,
        hashedPassword: await this.hashPassword(password),
        name,
        role: "ADMIN",
        isActive: true
    }
});
// Check if the userRole model exists
if (!this.prisma.userRole) {
    logger_1.logger.warn("UserRole model not found in Prisma schema. Skipping super admin role assignment.");
    return;
}
// Assign super admin role
await this.prisma.userRole.upsert({
    where: { userId_roleId: {
            userId: user.id,
            roleId: superAdminRole.id
        }
    },
    update: {},
    create: { userId: user.id,
        roleId: superAdminRole.id
    }
});
logger_1.logger.info(`Super admin user created: ${email}`);
try { }
catch (error) {
    logger_1.logger.error("Error creating super admin user:", error);
    // Don't throw the error, just log it and continue
}
async;
hashPassword(password, string);
Promise < string > {
    // In a real implementation, this would use bcrypt or similar
    // For now, we'll just return the password
    return: password
};
//# sourceMappingURL=RBACInitializer.js.map