/**
 * Security Audit Configuration and Tests
 *
 * Comprehensive security validation for production deployment
 */
export interface SecurityAuditResult {
    category: string;
    test: string;
    status: 'PASS' | 'FAIL' | 'WARNING';
    message: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    recommendation?: string;
}
export declare class SecurityAuditor {
    private results;
    /**
     * Run comprehensive security audit
     */
    runAudit(): Promise<SecurityAuditResult[]>;
    /**
     * Audit cryptographic implementations
     */
    private auditCryptography;
    /**
     * Audit input validation mechanisms
     */
    private auditInputValidation;
    /**
     * Audit authentication mechanisms
     */
    private auditAuthentication;
    /**
     * Audit authorization mechanisms
     */
    private auditAuthorization;
    /**
     * Audit data protection mechanisms
     */
    private auditDataProtection;
    /**
     * Audit error handling
     */
    private auditErrorHandling;
    /**
     * Audit logging and monitoring
     */
    private auditLogging;
    /**
     * Audit dependencies and third-party components
     */
    private auditDependencies;
    /**
     * Add audit result
     */
    private addResult;
    /**
     * Validate Ethereum address format
     */
    private isValidEthereumAddress;
    /**
     * Generate security audit report
     */
    generateReport(): string;
}
export { SecurityAuditor };
//# sourceMappingURL=security-audit.d.ts.map