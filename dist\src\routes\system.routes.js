"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const system_controller_1 = __importDefault(require("../controllers/system.controller"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const router = (0, express_1.Router)();
// Admin-only routes for system settings
router.get("/", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), system_controller_1.default.getAllSettings);
router.get("/:key", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("key").notEmpty()
]), system_controller_1.default.getSettingByKey);
router.put("/:key", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("key").notEmpty(),
    (0, express_validator_1.body)("value").notEmpty()
]), system_controller_1.default.updateSetting);
router.post("/", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("key").notEmpty(),
    (0, express_validator_1.body)("value").notEmpty()
]), system_controller_1.default.createSetting);
exports.default = router;
//# sourceMappingURL=system.routes.js.map