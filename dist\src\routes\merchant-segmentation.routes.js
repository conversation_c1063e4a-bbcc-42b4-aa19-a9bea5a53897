"use strict";
// jscpd:ignore-file
/**
 * Merchant Segmentation Routes
 *
 * This file defines the routes for merchant segmentation.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const merchant_segmentation_controller_1 = require("../controllers/merchant-segmentation.controller");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const roleMiddleware_1 = require("../middlewares/roleMiddleware");
const router = express_1.default.Router();
const merchantSegmentationController = new merchant_segmentation_controller_1.MerchantSegmentationController();
/**
 * @route POST /api/merchant-segmentation/categories
 * @desc Create a new merchant category
 * @access Private (Admin)
 */
router.post("/categories", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.createCategory);
/**
 * @route GET /api/merchant-segmentation/categories
 * @desc Get all merchant categories
 * @access Private (Admin)
 */
router.get("/categories", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.getAllCategories);
/**
 * @route POST /api/merchant-segmentation/categories/:categoryId/merchants/:merchantId
 * @desc Add merchant to category
 * @access Private (Admin)
 */
router.post("/categories/:categoryId/merchants/:merchantId", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.addMerchantToCategory);
/**
 * @route POST /api/merchant-segmentation/segments
 * @desc Create a new merchant segment
 * @access Private (Admin)
 */
router.post("/segments", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.createSegment);
/**
 * @route GET /api/merchant-segmentation/segments
 * @desc Get all merchant segments
 * @access Private (Admin)
 */
router.get("/segments", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.getAllSegments);
/**
 * @route POST /api/merchant-segmentation/segments/:segmentId/apply
 * @desc Apply segment to matching merchants
 * @access Private (Admin)
 */
router.post("/segments/:segmentId/apply", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.applySegment);
/**
 * @route POST /api/merchant-segmentation/performance-tiers
 * @desc Create a new merchant performance tier
 * @access Private (Admin)
 */
router.post("/performance-tiers", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.createPerformanceTier);
/**
 * @route POST /api/merchant-segmentation/performance-tiers/:tierId/apply
 * @desc Apply performance tier to qualifying merchants
 * @access Private (Admin)
 */
router.post("/performance-tiers/:tierId/apply", authMiddleware_1.authMiddleware, (0, roleMiddleware_1.roleMiddleware)(["ADMIN"]), merchantSegmentationController.applyPerformanceTier);
exports.default = router;
//# sourceMappingURL=merchant-segmentation.routes.js.map