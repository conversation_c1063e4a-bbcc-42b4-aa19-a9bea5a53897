/**
 * Admin Authorization Service
 *
 * Handles authorization logic for admin operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { AuthorizationContext, PermissionResult } from '../types/AdminControllerTypes';

/**
 * Authorization service for admin operations
 */
export class AdminAuthorizationService {
  private readonly superAdminRoles = ['SUPER_ADMIN'];
  private readonly adminRoles = ['ADMIN', 'SUPER_ADMIN'];
  private readonly managerRoles = ['MANAGER', 'ADMIN', 'SUPER_ADMIN'];

  /**
   * Check if user is authorized for the given action
   */
  async checkPermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource, action } = context;

    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated',
      };
    }

    // Check role-based permissions
    const rolePermission = this.checkRolePermission(user.role, resource, action);
    if (!rolePermission.allowed) {
      return rolePermission;
    }

    // Check resource-specific permissions
    const resourcePermission = await this.checkResourcePermission(context);
    if (!resourcePermission.allowed) {
      return resourcePermission;
    }

    return { allowed: true };
  }

  /**
   * Check role-based permissions
   */
  private checkRolePermission(
    userRole: string,
    resource: string,
    action: string
  ): PermissionResult {
    switch (resource) {
      case 'dashboard':
        return this.checkDashboardPermission(userRole, action);
      case 'admin-users':
        return this.checkAdminUserPermission(userRole, action);
      case 'roles':
        return this.checkRoleManagementPermission(userRole, action);
      case 'permissions':
        return this.checkPermissionPermission(userRole, action);
      case 'system':
        return this.checkSystemPermission(userRole, action);
      default:
        return {
          allowed: false,
          reason: `Unknown resource: ${resource}`,
        };
    }
  }

  /**
   * Check dashboard permissions
   */
  private checkDashboardPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'read':
        if (this.managerRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Manager role required for dashboard access',
          requiredRole: 'MANAGER',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }

  /**
   * Check admin user permissions
   */
  private checkAdminUserPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'read':
        if (this.adminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for viewing admin users',
          requiredRole: 'ADMIN',
        };
      case 'create':
      case 'update':
      case 'delete':
        if (this.superAdminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Super Admin role required for admin user management',
          requiredRole: 'SUPER_ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }

  /**
   * Check role management permissions
   */
  private checkRoleManagementPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'read':
        if (this.adminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for viewing roles',
          requiredRole: 'ADMIN',
        };
      case 'create':
      case 'update':
      case 'delete':
        if (this.superAdminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Super Admin role required for role management',
          requiredRole: 'SUPER_ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }

  /**
   * Check permission permissions
   */
  private checkPermissionPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'read':
        if (this.adminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for viewing permissions',
          requiredRole: 'ADMIN',
        };
      case 'create':
      case 'update':
      case 'delete':
        if (this.superAdminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Super Admin role required for permission management',
          requiredRole: 'SUPER_ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }

  /**
   * Check system permissions
   */
  private checkSystemPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'read':
      case 'manage':
        if (this.superAdminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Super Admin role required for system management',
          requiredRole: 'SUPER_ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }

  /**
   * Check resource-specific permissions
   */
  private async checkResourcePermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource, action, resourceId } = context;

    // For admin operations, we generally don't have additional resource-specific checks
    // beyond role-based permissions, but this can be extended for specific use cases

    // Example: Prevent users from deleting themselves
    if (resource === 'admin-users' && action === 'delete' && resourceId === user.id) {
      return {
        allowed: false,
        reason: 'Cannot delete your own admin account',
      };
    }

    return { allowed: true };
  }

  /**
   * Require super admin role
   */
  requireSuperAdmin(userRole?: string): void {
    if (!userRole || !this.superAdminRoles.includes(userRole)) {
      throw new AppError({
        message: 'Super Admin role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require admin role
   */
  requireAdmin(userRole?: string): void {
    if (!userRole || !this.adminRoles.includes(userRole)) {
      throw new AppError({
        message: 'Admin role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require manager role or higher
   */
  requireManager(userRole?: string): void {
    if (!userRole || !this.managerRoles.includes(userRole)) {
      throw new AppError({
        message: 'Manager role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy: Record<string, number> = {
      MANAGER: 1,
      ADMIN: 2,
      SUPER_ADMIN: 3,
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  }

  /**
   * Get user permissions for a resource
   */
  getUserPermissions(userRole: string, resource: string): string[] {
    const permissions: string[] = [];

    switch (resource) {
      case 'dashboard':
        if (this.managerRoles.includes(userRole)) {
          permissions.push('read');
        }
        break;
      case 'admin-users':
        if (this.adminRoles.includes(userRole)) {
          permissions.push('read');
        }
        if (this.superAdminRoles.includes(userRole)) {
          permissions.push('create', 'update', 'delete');
        }
        break;
      case 'roles':
      case 'permissions':
        if (this.adminRoles.includes(userRole)) {
          permissions.push('read');
        }
        if (this.superAdminRoles.includes(userRole)) {
          permissions.push('create', 'update', 'delete');
        }
        break;
      case 'system':
        if (this.superAdminRoles.includes(userRole)) {
          permissions.push('read', 'manage');
        }
        break;
    }

    return permissions;
  }

  /**
   * Validate authorization context
   */
  validateAuthorizationContext(context: AuthorizationContext): void {
    if (!context.user) {
      throw new AppError({
        message: 'User context is required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }

    if (!context.resource) {
      throw new AppError({
        message: 'Resource is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!context.action) {
      throw new AppError({
        message: 'Action is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }
  }

  /**
   * Create authorization context from request
   */
  createAuthorizationContext(
    user: any,
    resource: string,
    action: string,
    resourceId?: string
  ): AuthorizationContext {
    return {
      user: {
        id: user?.id,
        role: user?.role,
        merchantId: user?.merchantId,
      },
      resource,
      action,
      resourceId,
    };
  }

  /**
   * Handle authorization error
   */
  handleAuthorizationError(result: PermissionResult): never {
    const message = result.reason || 'Access denied';

    throw new AppError({
      message,
      type: ErrorType.AUTHENTICATION,
      code: ErrorCode.INVALID_CREDENTIALS,
      details: {
        requiredRole: result.requiredRole,
        requiredPermissions: result.requiredPermissions,
      },
    });
  }
}
