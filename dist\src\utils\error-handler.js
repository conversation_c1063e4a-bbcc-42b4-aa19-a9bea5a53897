"use strict";
// jscpd:ignore-file
/**
 * Error Handler Utility
 *
 * This utility provides centralized error handling for the application.
 * It includes functions for handling errors in controllers, services, and middleware.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handle404Error = exports.globalErrorHandler = exports.handleServiceError = exports.handleControllerError = void 0;
const logger_1 = require("../lib/logger");
const app_error_1 = require("./app-error");
const environment_validator_1 = require("./environment-validator");
/**
 * Handle controller errors
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
const handleControllerError = (err, req, res, next) => {
    // Log error
    logger_1.logger.error('Controller error:', {
        path: req.path,
        method: req.method,
        error: err.message,
        stack: err.stack,
        requestId: req.id
    });
    // Create error response
    const errorResponse = {
        status: 'error',
        statusCode: err instanceof app_error_1.AppError ? err.statusCode : 500,
        message: err.message || 'Internal server error',
        timestamp: new Date().toISOString(),
        path: req.originalUrl || req.url
    };
    // Add request ID if available
    if (req.id) {
        errorResponse.requestId = req.id;
    }
    // Add error code if available
    if (err instanceof app_error_1.AppError && err.code) {
        errorResponse.code = err.code;
    }
    // Add error details if available
    if (err instanceof app_error_1.AppError && err.details) {
        errorResponse.details = err.details;
    }
    // Add stack trace in development
    if (!(0, environment_validator_1.isProduction)()) {
        errorResponse.stack = err.stack;
    }
    // Send error response
    res.status(errorResponse.statusCode).json(errorResponse);
};
exports.handleControllerError = handleControllerError;
const handleServiceError = (err, serviceName, methodName, params) => {
    // Log error
    logger_1.logger.error(`Service error in ${serviceName}.${methodName}:`, {
        error: err.message,
        stack: err.stack,
        params: params ? JSON.stringify(params) : undefined
    });
    // Rethrow AppError
    if (err instanceof app_error_1.AppError) {
        throw err;
    }
    // Wrap other errors in AppError
    throw new app_error_1.AppError(err.message || 'Internal server error', 500, 'INTERNAL_SERVER_ERROR', { serviceName, methodName, originalError: err.message });
};
exports.handleServiceError = handleServiceError;
/**
 * Global error handler middleware
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
const globalErrorHandler = (err, req, res, next) => {
    // Log error
    logger_1.logger.error('Global error handler:', {
        path: req.path,
        method: req.method,
        error: err.message,
        stack: err.stack,
        requestId: req.id
    });
    // Create error response
    const errorResponse = {
        status: 'error',
        statusCode: err instanceof app_error_1.AppError ? err.statusCode : 500,
        message: err.message || 'Internal server error',
        timestamp: new Date().toISOString(),
        path: req.originalUrl || req.url
    };
    // Add request ID if available
    if (req.id) {
        errorResponse.requestId = req.id;
    }
    // Add error code if available
    if (err instanceof app_error_1.AppError && err.code) {
        errorResponse.code = err.code;
    }
    // Add error details if available
    if (err instanceof app_error_1.AppError && err.details) {
        errorResponse.details = err.details;
    }
    // Add stack trace in development
    if (!(0, environment_validator_1.isProduction)()) {
        errorResponse.stack = err.stack;
        errorResponse.error = err.name;
    }
    // Send error response
    res.status(errorResponse.statusCode).json(errorResponse);
};
exports.globalErrorHandler = globalErrorHandler;
const handle404Error = (req, res, next) => {
    // Create error response
    const errorResponse = {
        status: 'error',
        statusCode: 404,
        message: `Route not found: ${req.method} ${req.originalUrl || req.url}`,
        timestamp: new Date().toISOString(),
        path: req.originalUrl || req.url
    };
    // Add request ID if available
    if (req.id) {
        errorResponse.requestId = req.id;
    }
    // Log error
    logger_1.logger.warn('Route not found:', {
        path: req.path,
        method: req.method,
        requestId: req.id
    });
    // Send error response
    res.status(404).json(errorResponse);
};
exports.handle404Error = handle404Error;
exports.default = {
    handleControllerError: exports.handleControllerError,
    handleServiceError: exports.handleServiceError,
    globalErrorHandler: exports.globalErrorHandler,
    handle404Error: exports.handle404Error
};
//# sourceMappingURL=error-handler.js.map