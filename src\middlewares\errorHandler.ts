// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { AppError } from "../utils/appError";
import { logger } from "../lib/logger";
import { ErrorFactory, ErrorType } from "../utils/errors/ErrorFactory";
import { AppError } from "../utils/appError";
import { logger } from "../lib/logger";
import { ErrorFactory, ErrorType } from "../utils/errors/ErrorFactory";

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Error handler middleware
 * This middleware handles all errors in the application
 */
export const errorHandler: any = (
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Convert error to AppError
  const error: any = err instanceof AppError ? err : ErrorFactory.handle(err);
  
  // Log error
  if (error.statusCode >= 500) {
    logger.error(`[${error.type || 'ERROR'}] ${(error as Error).message}`, {
      error,
      request: { method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userId: req.user?.id
      }
    });
  } else {
    logger.warn(`[${error.type || 'ERROR'}] ${(error as Error).message}`, {
      error,
      request: { method: req.method,
        url: req.originalUrl
      }
    });
  }
  
  // Prepare response
  const response: any = {
    success: false,
    message: (error as Error).message,
    type: error.type,
    ...(error.details && { details: error.details }),
    ...(process.env.NODE_ENV === 'development' && {
      stack: error.stack,
      originalError: error.originalError
    })
  };
  
  // Send response
  res.status(error.statusCode).json(response);
};

/**
 * Not found middleware
 * This middleware handles 404 errors
 */
export const notFoundHandler: any = (req: Request, res: Response, next: NextFunction) => {
  const error: any = ErrorFactory.notFound('Route', req.originalUrl);
  next(error);
};

/**
 * Validation error handler
 * This function creates a validation error from validation errors
 * @param errors Validation errors
 * @returns AppError
 */
export const validationErrorHandler: any = (errors: Record<string, string[]>): AppError => {
  const message = 'Validation failed';
  return ErrorFactory.validation(message, errors);
};

/**
 * Database error handler
 * This function handles database errors
 * @param error Database error
 * @returns AppError
 */
export const databaseErrorHandler: any = (error: Error): AppError => {
  // Handle specific database errors
  if (error.name === 'PrismaClientKnownRequestError') {
    // @ts-ignore - PrismaClientKnownRequestError has a code property
    const code = error.code;
    
    // Handle unique constraint violations
    if (code === 'P2002') {
      // @ts-ignore - PrismaClientKnownRequestError has a meta property
      const fields = error.meta?.target as string[];
      const fieldNames: any = fields.join(', ');
      
      return ErrorFactory.conflict(`Unique constraint violation: ${fieldNames} already exists`);
    }
    
    // Handle foreign key constraint violations
    if (code === 'P2003') {
      // @ts-ignore - PrismaClientKnownRequestError has a meta property
      const field = error.meta?.field_name as string;
      
      return ErrorFactory.validation(`Foreign key constraint violation: ${field} does not exist`);
    }
    
    // Handle record not found
    if (code === 'P2001') {
      // @ts-ignore - PrismaClientKnownRequestError has a meta property
      const model = error.meta?.model as string;
      
      return ErrorFactory.notFound(model);
    }
  }
  
  // Default database error
  return ErrorFactory.database('Database operation failed', error);
};

/**
 * Authentication error handler
 * This function handles authentication errors
 * @param message Error message
 * @returns AppError
 */
export const authenticationErrorHandler: any = (message = 'Authentication failed'): AppError => {
  return ErrorFactory.authentication(message);
};

/**
 * Authorization error handler
 * This function handles authorization errors
 * @param message Error message
 * @returns AppError
 */
export const authorizationErrorHandler: any = (message = 'Authorization failed'): AppError => {
  return ErrorFactory.authorization(message);
};
