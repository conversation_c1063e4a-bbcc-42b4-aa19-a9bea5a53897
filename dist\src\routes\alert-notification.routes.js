"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const alert_notification_controller_1 = __importDefault(require("../controllers/alert-notification.controller"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
/**
 * @route   GET /api/alert-notifications/config
 * @desc    Get alert notification configuration
 * @access  Admin
 */
router.get("/config", auth_1.authenticateJWT, auth_1.isAdmin, alert_notification_controller_1.default.getConfig);
/**
 * @route   PUT /api/alert-notifications/config
 * @desc    Update alert notification configuration
 * @access  Admin
 */
router.put("/config", auth_1.authenticateJWT, auth_1.isAdmin, alert_notification_controller_1.default.updateConfig);
/**
 * @route   POST /api/alert-notifications/test
 * @desc    Test alert notification
 * @access  Admin
 */
router.post("/test", auth_1.authenticateJWT, auth_1.isAdmin, alert_notification_controller_1.default.testNotification);
/**
 * @route   POST /api/alert-notifications/initialize
 * @desc    Initialize alert notification service
 * @access  Admin
 */
router.post("/initialize", auth_1.authenticateJWT, auth_1.isAdmin, alert_notification_controller_1.default.initialize);
exports.default = router;
//# sourceMappingURL=alert-notification.routes.js.map