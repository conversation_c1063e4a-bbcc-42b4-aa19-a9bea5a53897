"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toBeAfter = toBeAfter;
function toBeAfter(actual, after) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, matcherHint } = this.utils;
    const pass = actual instanceof Date && actual > after;
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toBeAfter', 'received', '') +
                '\n\n' +
                `Expected date to be after ${printReceived(after)} but received:\n` +
                `  ${printReceived(actual)}`
            : matcherHint('.toBeAfter', 'received', '') +
                '\n\n' +
                `Expected date to be after ${printReceived(after)} but received:\n` +
                `  ${printReceived(actual)}`,
    };
}
