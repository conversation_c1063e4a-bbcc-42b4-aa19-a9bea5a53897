{"version": 3, "file": "security-test.js", "sourceRoot": "", "sources": ["../../../src/tests/security-test.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;;;;;;;;;;GAcG;;;;;AAEH,kDAA0B;AAC1B,+BAA8B;AAE9B,gBAAgB;AAChB,MAAM,QAAQ,GAAU,uBAAuB,CAAC;AAEhD,yBAAyB;AACzB,KAAK,UAAU,gBAAgB;IAC3B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAE1C,IAAI,WAAW,GAAU,CAAC,CAAC;IAC3B,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,gCAAgC;IAChC,KAAK,UAAU,OAAO,CAAC,IAAY,EAAE,MAA2B;QAC5D,UAAU,EAAE,CAAC;QACb,IAAI,CAAC;YACD,MAAM,MAAM,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACjC,WAAW,EAAE,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACnC,OAAO,CAAC,KAAK,CAAC,aAAc,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,MAAM,OAAO,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QAC9C,MAAM,QAAQ,GAAQ,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,SAAS,CAAC,CAAC;QAE5D,6BAA6B;QAC7B,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QAC/E,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC9D,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QAC7D,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QACrE,IAAA,aAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,OAAO,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QAC7C,IAAI,CAAC;YACD,oDAAoD;YACpD,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,iBAAiB,EAAE;gBAC3C,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,UAAU;aACvB,CAAC,CAAC;YAEH,2DAA2D;YAC3D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,kCAAkC;YAClC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,MAAM,OAAO,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QAC5C,IAAI,CAAC;YACD,6CAA6C;YAC7C,MAAM,UAAU,GAAU,iCAAiC,CAAC;YAE5D,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,iBAAiB,UAAU,EAAE,CAAC,CAAC;YAE1D,0DAA0D;YAC1D,mFAAmF;QACvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,+CAA+C;YAC/C,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,MAAM,OAAO,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QACtD,IAAI,CAAC;YACD,uDAAuD;YACvD,MAAM,mBAAmB,GAAU,aAAa,CAAC;YAEjD,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,iBAAiB,mBAAmB,EAAE,CAAC,CAAC;YAEnE,8EAA8E;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,+CAA+C;YAC/C,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,MAAM,OAAO,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;QAC/C,kDAAkD;QAC9C,MAAM,QAAQ,GAAS,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAU,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,QAAQ,CAAC,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,aAAa,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,SAAS,GAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEnD,8DAA8D;QAC9D,MAAM,WAAW,GAAQ,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;QAC1E,IAAA,aAAM,EAAC,WAAW,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,MAAM,OAAO,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QAC5C,IAAI,CAAC;YACD,gEAAgE;YAChE,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,CAAC,CAAC;YAE7C,+DAA+D;YAC/D,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,qCAAqC;YACrC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,MAAM,OAAO,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QACzC,IAAI,CAAC;YACD,6DAA6D;YAC7D,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,EAAE;gBACzC,OAAO,EAAE,EAAE,aAAa,EAAE,0BAA0B;iBACnD;aACJ,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,qCAAqC;YACrC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,MAAM,OAAO,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QAC7C,uDAAuD;QACnD,MAAM,UAAU,GAAU,mLAAmL,CAAC;QAE9M,IAAI,CAAC;YACD,6DAA6D;YAC7D,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,EAAE;gBACzC,OAAO,EAAE,EAAE,aAAa,EAAE,UAAU,UAAU,EAAE;iBAC/C;aACJ,CAAC,CAAC;YAEH,4DAA4D;YAC5D,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,qCAAqC;YACrC,IAAA,aAAM,EAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,gBAAgB;IAChB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IACxC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,WAAW,WAAW,IAAI,UAAU,KAAK,CAAC,WAAW,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAEtG,4BAA4B;IAC5B,MAAM,SAAS,GAAQ,WAAW,KAAK,UAAU,CAAC;IAElD,IAAI,SAAS,EAAE,CAAC;QACZ,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IACjD,CAAC;SAAM,CAAC;QACJ,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAClD,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,6DAA6D;AAC7D,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC1B,gBAAgB,EAAE;SACb,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;IAAI,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACA,IAAA,CAAC,CAAD,CAAC,AAAD;IAAA,OAAM,EAAA,CAAC,CAAD,CAAC,AAAD;IAAA,CAAC,KAAK,EAAE,EAAE;QACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAA;IAAC,CAAC;AACX,CAAC;AAED,kBAAe,gBAAgB,CAAC"}