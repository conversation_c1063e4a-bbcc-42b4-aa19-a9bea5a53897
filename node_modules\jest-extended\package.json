{"name": "jest-extended", "version": "5.0.3", "description": "Additional Jest matchers", "main": "dist/index.js", "types": "types/index.d.ts", "files": ["dist", "types/index.d.ts", "all.js"], "scripts": {"clean": "node clean.js", "prebuild": "yarn clean", "build": "tsc && tsc-alias", "lint": "eslint .", "lint:fix": "yarn lint --fix", "prepare": "husky", "prepublishOnly": "yarn build", "precommit": "lint-staged", "test": "jest --color=true", "test:clearCache": "yarn test --clearCache", "test:updateSnapshot": "yarn test --updateSnapshot", "test:coverage": "yarn test --coverage", "test:watch": "yarn test --watch", "typecheck": "tsc --noEmit", "dev:docs": "cd website && yarn start"}, "keywords": ["jest", "matchers", "extend", "extended", "test", "testing", "assertions"], "author": "<PERSON> <<EMAIL>> (mattphillips.io)", "license": "MIT", "repository": "jest-community/jest-extended", "devDependencies": {"@changesets/cli": "^2.26.0", "@eslint/compat": "^1.2.0", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.24.0", "@jest/expect-utils": "^29.0.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-import-resolver-typescript": "^4.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jest": "^28.0.0", "eslint-plugin-prettier": "^5.2.0", "globals": "^16.0.0", "husky": "^9.0.0", "jest": "^29.0.0", "jest-serializer-ansi-escapes": "^4.0.0", "jest-watch-typeahead": "^2.0.0", "lint-staged": "~15.5.0", "prettier": "^3.0.0", "ts-jest": "^29.0.0", "tsc-alias": "^1.8.0", "typescript": "^5.0.0"}, "dependencies": {"jest-diff": "^29.0.0"}, "engines": {"node": "^18.12.0 || ^20.9.0 || ^22.11.0 || >=23.0.0"}, "lint-staged": {"*.js": "eslint --fix", "*.md": "prettier --write"}, "jest": {"preset": "ts-jest", "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"tsconfig": "tsconfig.test.json"}]}, "transformIgnorePatterns": ["/node_modules/"], "testEnvironment": "node", "testPathIgnorePatterns": [".idea", "examples", "/node_modules/", "/fixtures/", "/dist/"], "coveragePathIgnorePatterns": ["/node_modules/"], "snapshotFormat": {"escapeString": false, "printBasicPrototype": false}, "snapshotSerializers": ["jest-serializer-ansi-escapes"], "moduleNameMapper": {"src/(.*)": "<rootDir>/src/$1"}, "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "prettier": {"arrowParens": "avoid", "printWidth": 120, "singleQuote": true, "trailingComma": "all"}, "peerDependencies": {"jest": ">=27.2.5"}, "peerDependenciesMeta": {"jest": {"optional": true}}, "packageManager": "yarn@1.22.22"}