"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentMethodType = void 0;
// jscpd:ignore-file
/**
 * Payment method types
 */
var PaymentMethodType;
(function (PaymentMethodType) {
    PaymentMethodType["BINANCE_PAY"] = "binance_pay";
    PaymentMethodType["BINANCE_C2C"] = "binance_c2c";
    PaymentMethodType["BINANCE_TRC20"] = "binance_trc20";
    PaymentMethodType["BINANCE_TRC20_DIRECT"] = "binance_trc20_direct";
    PaymentMethodType["USDT"] = "usdt";
    PaymentMethodType["USDC"] = "usdc";
    PaymentMethodType["CRYPTO_TRANSFER"] = "crypto_transfer";
})(PaymentMethodType || (exports.PaymentMethodType = PaymentMethodType = {}));
//# sourceMappingURL=paymentMethodTypes.js.map