// jscpd:ignore-file
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import { createServer } from 'http';
import path from 'path';
import { monitorRequest, initializeMonitoring } from './utils/monitoring';
import { AlertMonitorService } from './services/alert-monitor.service';
import { AlertAggregationService } from './services/alert-aggregation.service';
import { VerificationAlertJob } from './jobs/verification-alert.job';
import { WebSocketService } from './services/websocket.service';
import { TransactionMonitorService } from './services/transaction-monitor.service';
import { monitoringMiddleware } from './middlewares/monitoring.middleware';
import { apiResponseMiddleware, errorHandlerMiddleware } from './middlewares/apiResponseMiddleware';
import { VerificationRealtimeService } from './services/websocket/verification-realtime.service';
import { Middleware } from './types/express';

// Import routes
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import merchantRoutes from './routes/merchant.routes';
import paymentMethodRoutes from './routes/payment-method.routes';
import verificationMethodRoutes from './routes/verification-method.routes';
import transactionRoutes from './routes/transaction.routes';
import paymentPageRoutes from './routes/payment-page.routes';
import subscriptionRoutes from './routes/subscription.routes';
import monitoringRoutes from './routes/monitoring.routes';
import binanceRoutes from './routes/binance.routes';
import binancePayWebhookRoutes from './routes/binance-pay-webhook.routes';
import locationRoutes from './routes/location.routes';
import verificationRoutes from './routes/verification.routes';
import webhookRoutes from './routes/webhook.routes';
import alertRoutes from './routes/alert.routes';
import alertAggregationRoutes from './routes/alert-aggregation.routes';
import alertAnalyticsRoutes from './routes/alert-analytics.routes';
import emailRoutes from './routes/email.routes';
import smsRoutes from './routes/sms.routes';
import telegramRoutes from './routes/telegram.routes';
import notificationRoutes from './routes/notification.routes';
import pushNotificationRoutes from './routes/push-notification.routes';
import exampleRoutes from './routes/example.routes';
import paymentVerificationRoutes from './routes/payment-verification.routes';
import fraudDetectionRoutes from './routes/fraud-detection.routes';
import paymentRecommendationRoutes from './routes/payment-recommendation.routes';
import identityVerificationRoutes from './routes/identity-verification.routes';
import enhancedRiskEngineRoutes from './routes/enhanced-risk-engine.routes';
import merchantSegmentationRoutes from './routes/merchant-segmentation.routes';
import merchantRelationshipRoutes from './routes/merchant-relationship.routes';
import merchantSelfServiceRoutes from './routes/merchant-self-service.routes';
import websocketMonitoringRoutes from './routes/websocket-monitoring.routes';
import advancedReportRoutes from './routes/advanced-report.routes';
import dashboardRoutes from './routes/dashboard.routes';

// All monitoring and service imports are already at the top of the file

// Load environment variables
dotenv.config();

// Initialize Prisma client
export const prisma: any = new PrismaClient();

// Test database connection
const testDatabaseConnection: any = async () => {
  try {
    await prisma.$connect();
  } catch (error) {
    console.error('Database connection failed:', error);
    console.error('Please check your database configuration in .env file');
    process.exit(1);
  }
};

// Create Express app and HTTP server
const app: any = express();
const httpServer = createServer(app);
const PORT: any = process.env.PORT || 3002; // Use port 3002 as default

// Middleware
app.use(
  cors({
    origin: [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:5176',
      'http://localhost:5177',
      'http://localhost:3000',
      'http://localhost:3002',
      'https://amazingpayme.com',
      '*',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Site-Domain'],
  })
);
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
  })
);
app.use(morgan('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Initialize monitoring
const cleanupMonitoring: any = initializeMonitoring();

// Add monitoring middleware
app.use(monitorRequest);
app.use(monitoringMiddleware);

// Add API response middleware
app.use(apiResponseMiddleware);

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/merchants', merchantRoutes);
app.use('/api/payment-methods', paymentMethodRoutes);
app.use('/api/verification-methods', verificationMethodRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/payment-pages', paymentPageRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/monitoring', monitoringRoutes);
app.use('/api/monitoring/websocket', websocketMonitoringRoutes);
app.use('/api/binance', binanceRoutes);
app.use('/webhook/binance-pay', binancePayWebhookRoutes);
app.use('/api/location', locationRoutes);
app.use('/api/verification', verificationRoutes);
app.use('/api/webhooks', webhookRoutes);
app.use('/api/alerts', alertRoutes);
app.use('/api/alerts', alertAggregationRoutes);
app.use('/api/alerts', alertAnalyticsRoutes);
app.use('/api/email', emailRoutes);
app.use('/api/sms', smsRoutes);
app.use('/api/telegram', telegramRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/push', pushNotificationRoutes);
app.use('/api/examples', exampleRoutes);
app.use('/api/payment-verification', paymentVerificationRoutes);
app.use('/api/fraud-detection', fraudDetectionRoutes);
app.use('/api/payment-recommendation', paymentRecommendationRoutes);
app.use('/api/identity-verification', identityVerificationRoutes);
app.use('/api/risk', enhancedRiskEngineRoutes);
app.use('/api/merchant-segmentation', merchantSegmentationRoutes);
app.use('/api/merchant-relationship', merchantRelationshipRoutes);
app.use('/api/merchant-self-service', merchantSelfServiceRoutes);
app.use('/api/advanced-reports', advancedReportRoutes);
app.use('/api/dashboards', dashboardRoutes);
app.use('/dashboard/reports', dashboardRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.success({ status: 'ok' }, 'Server is healthy and running');
});

// Error handling middleware
app.use(errorHandlerMiddleware);

// Initialize WebSocket server
const io: any = WebSocketService.initialize(httpServer);

// Initialize verification WebSocket service
import verificationWebSocketService from './services/websocket/verificationWebSocketService';
verificationWebSocketService.initialize(httpServer);

// Initialize verification real-time service
const verificationRealtimeService: any = VerificationRealtimeService.getInstance();
verificationRealtimeService.initialize(io);

// Initialize WebSocket monitor
import websocketMonitor from './utils/websocket-monitor';

websocketMonitor.initialize(io);

// Initialize transaction monitor service
const transactionMonitor: any = TransactionMonitorService.getInstance();
transactionMonitor.initialize().catch((error) => {
  console.error('Failed to initialize transaction monitor service:', error);
});

// Start server
const startServer: any = async () => {
  // Test database connection first
  await testDatabaseConnection();

  // Log all routes

  app._router.stack.forEach((middleware: any) => {
    if (middleware.route) {
      // Routes registered directly on the app
      console.log(`Route: ${middleware.route.path}`);
    } else if (middleware.name === 'router') {
      // Router middleware
      middleware.handle.stack.forEach((handler) => {
        if (handler.route) {
          const path: any = handler.route.path;
          const methods: any = Object.keys(handler.route.methods);
        }
      });
    }
  });

  return httpServer.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`🔗 API available at http://localhost:${PORT}/api`);
    console.log(`🩺 Health check at http://localhost:${PORT}/health`);

    // Initialize and start alert monitor
    const alertMonitor: any = new AlertMonitorService();
    alertMonitor.start();
    (global as any).alertMonitor = alertMonitor;

    // Initialize and start alert aggregation service
    const alertAggregation: any = new AlertAggregationService();
    alertAggregation.start();
    (global as any).alertAggregation = alertAggregation;

    // Initialize and start verification alert job
    const verificationAlertJob: any = new VerificationAlertJob();
    verificationAlertJob.start();
    (global as any).verificationAlertJob = verificationAlertJob;
  });
};

// Start the server
startServer().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  // Close server
  httpServer.close(() => {});

  // Cleanup monitoring
  cleanupMonitoring();

  // Shutdown transaction monitor
  transactionMonitor.shutdown();

  // Shutdown alert monitor
  if ((global as any).alertMonitor) {
    (global as any).alertMonitor.stop();
  }

  // Shutdown alert aggregation service
  if ((global as any).alertAggregation) {
    (global as any).alertAggregation.stop();
  }

  // Shutdown verification alert job
  if ((global as any).verificationAlertJob) {
    (global as any).verificationAlertJob.stop();
  }

  // Disconnect Prisma
  await prisma.$disconnect();

  process.exit(0);
});

export default app;
