"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorMiddleware = exports.errorHandler = exports.createTooManyRequestsError = exports.createServerError = exports.createValidationError = exports.createConflictError = exports.createNotFoundError = exports.createForbiddenError = exports.createUnauthorizedError = exports.createBadRequestError = exports.AppError = void 0;
const logger_1 = require("../utils/logger");
/**
 * Custom application error class
 * Used for standardized error handling throughout the application
 */
class AppError extends Error {
    /**
     * Create a new AppError
     * @param message Error message
     * @param statusCode HTTP status code (default: 500)
     * @param isOperational Whether the error is operational (expected) or programming (unexpected)
     * @param code Error code for client-side error handling
     * @param details Additional error details
     */
    constructor(message, statusCode = 500, isOperational = true, code = 'INTERNAL_SERVER_ERROR', details) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.code = code;
        this.details = details;
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
/**
 * Factory methods for common error types
 */
/**
 * Create a bad request error (400)
 */
const createBadRequestError = (message = 'Bad request', code = 'BAD_REQUEST', details) => {
    return new AppError(message, 400, true, code, details);
};
exports.createBadRequestError = createBadRequestError;
/**
 * Create an unauthorized error (401)
 */
const createUnauthorizedError = (message = 'Unauthorized', code = 'UNAUTHORIZED', details) => {
    return new AppError(message, 401, true, code, details);
};
exports.createUnauthorizedError = createUnauthorizedError;
/**
 * Create a forbidden error (403)
 */
const createForbiddenError = (message = 'Forbidden', code = 'FORBIDDEN', details) => {
    return new AppError(message, 403, true, code, details);
};
exports.createForbiddenError = createForbiddenError;
/**
 * Create a not found error (404)
 */
const createNotFoundError = (message = 'Not found', code = 'NOT_FOUND', details) => {
    return new AppError(message, 404, true, code, details);
};
exports.createNotFoundError = createNotFoundError;
/**
 * Create a conflict error (409)
 */
const createConflictError = (message = 'Conflict', code = 'CONFLICT', details) => {
    return new AppError(message, 409, true, code, details);
};
exports.createConflictError = createConflictError;
/**
 * Create a validation error (422)
 */
const createValidationError = (message = 'Validation error', code = 'VALIDATION_ERROR', details) => {
    return new AppError(message, 422, true, code, details);
};
exports.createValidationError = createValidationError;
/**
 * Create a server error (500)
 */
const createServerError = (message = 'Internal server error', code = 'INTERNAL_SERVER_ERROR', details) => {
    return new AppError(message, 500, false, code, details);
};
exports.createServerError = createServerError;
/**
 * Create a too many requests error (429)
 */
const createTooManyRequestsError = (message = 'Too many requests, please try again later', code = 'RATE_LIMIT_EXCEEDED', details) => {
    return new AppError(message, 429, true, code, details);
};
exports.createTooManyRequestsError = createTooManyRequestsError;
const errorHandler = (err, req, res, next) => {
    // Default to 500 internal server error
    let statusCode = 500;
    let message = 'Internal Server Error';
    let isOperational = false;
    let errorCode = 'INTERNAL_SERVER_ERROR';
    let details = undefined;
    // Handle AppError instances
    if (err instanceof AppError) {
        statusCode = err.statusCode;
        message = err.message;
        isOperational = err.isOperational;
        errorCode = err.code;
        details = err.details;
    }
    else if (Array.isArray(err) && err.length > 0 && 'msg' in err[0]) {
        // Handle express-validator validation errors
        statusCode = 422;
        message = 'Validation failed';
        isOperational = true;
        errorCode = 'VALIDATION_ERROR';
        details = err.map((e) => ({
            field: e.param,
            message: e.msg,
            value: e.value,
        }));
    }
    else if (err.name === 'JsonWebTokenError') {
        // Handle JWT errors
        statusCode = 401;
        message = 'Invalid authentication token';
        isOperational = true;
        errorCode = 'INVALID_TOKEN';
    }
    else if (err.name === 'TokenExpiredError') {
        // Handle JWT expiration
        statusCode = 401;
        message = 'Authentication token expired';
        isOperational = true;
        errorCode = 'TOKEN_EXPIRED';
    }
    else if (err.name === 'PrismaClientKnownRequestError') {
        // Handle Prisma known errors
        statusCode = 400;
        message = 'Database operation failed';
        isOperational = true;
        errorCode = 'DATABASE_ERROR';
        // Extract more details from Prisma error
        const prismaError = err;
        if (prismaError.code) {
            // Handle specific Prisma error codes
            switch (prismaError.code) {
                case 'P2002':
                    message = 'A unique constraint would be violated.';
                    errorCode = 'UNIQUE_CONSTRAINT_VIOLATION';
                    details = { fields: prismaError.meta?.target };
                    break;
                case 'P2003':
                    message = 'Foreign key constraint failed.';
                    errorCode = 'FOREIGN_KEY_CONSTRAINT_VIOLATION';
                    details = { field: prismaError.meta?.field_name };
                    break;
                case 'P2025':
                    message = 'Record not found.';
                    errorCode = 'RECORD_NOT_FOUND';
                    break;
                default:
                    details = { code: prismaError.code };
            }
        }
    }
    else if (err.name === 'PrismaClientValidationError') {
        // Handle Prisma validation errors
        statusCode = 400;
        message = 'Invalid data provided';
        isOperational = true;
        errorCode = 'VALIDATION_ERROR';
    }
    else if (err.name === 'SyntaxError' && err.status === 400 && 'body' in err) {
        // Handle JSON parsing errors
        statusCode = 400;
        message = 'Invalid JSON in request body';
        isOperational = true;
        errorCode = 'INVALID_JSON';
    }
    // Collect request information for logging
    const requestInfo = {
        method: req.method,
        path: req.path,
        ip: req.ip,
        requestId: req.requestId,
        userId: req.user?.id, // Fixed: using id instead of userId
        userRole: req.user?.role,
    };
    // Log error details
    if (isOperational) {
        logger_1.logger.warn(`[${statusCode}] ${errorCode}: ${message}`, {
            ...requestInfo,
            errorCode,
            details,
        });
    }
    else {
        logger_1.logger.error(`[${statusCode}] ${errorCode}: ${message}`, {
            ...requestInfo,
            errorCode,
            error: err,
            stack: err.stack,
        });
    }
    // Send response to client
    res.status(statusCode).json({
        status: 'error',
        code: errorCode,
        message,
        details: details,
        requestId: req.requestId,
        // Only include stack trace in development
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
    });
};
exports.errorHandler = errorHandler;
// Export error middleware
exports.errorMiddleware = {
    errorHandler: exports.errorHandler,
    AppError,
    createBadRequestError: exports.createBadRequestError,
    createUnauthorizedError: exports.createUnauthorizedError,
    createForbiddenError: exports.createForbiddenError,
    createNotFoundError: exports.createNotFoundError,
    createConflictError: exports.createConflictError,
    createValidationError: exports.createValidationError,
    createServerError: exports.createServerError,
    createTooManyRequestsError: exports.createTooManyRequestsError,
};
exports.default = exports.errorMiddleware;
