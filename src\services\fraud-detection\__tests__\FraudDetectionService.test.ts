/**
 * Unit Tests for Fraud Detection Service
 *
 * Comprehensive test suite covering all functionality of the FraudDetectionService
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { FraudDetectionService } from '../core/FraudDetectionService';
import { FraudDetectionError } from '../core/FraudDetectionError';

// Mock dependencies
jest.mock('@prisma/client');

describe('FraudDetectionService', () => {
  let service: FraudDetectionService;
  let mockPrisma: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    // Create mock Prisma client
    mockPrisma = {
      riskAssessment: {
        create: jest.fn(),
        findUnique: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        count: jest.fn(),
        aggregate: jest.fn(),
      },
      transaction: {
        findUnique: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
      },
      fraudDetectionConfig: {
        findUnique: jest.fn(),
        upsert: jest.fn(),
      },
      merchant: {
        findUnique: jest.fn(),
      },
    } as any;

    // Initialize service with mock
    service = new FraudDetectionService(mockPrisma);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('assessTransactionRisk', () => {
    const validTransactionContext = {
      transaction: {
        id: testUtils.mockUUID(),
        amount: 1000,
        currency: 'USD',
        merchantId: testUtils.mockUUID(),
        userId: testUtils.mockUUID(),
      },
      merchant: {
        id: testUtils.mockUUID(),
        name: 'Test Merchant',
      },
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0 Test Browser',
      deviceId: 'device-123',
      timestamp: new Date(),
    };

    it('should assess low risk for normal transaction', async () => {
      // Arrange
      const mockRiskAssessment = {
        id: testUtils.mockUUID(),
        transactionId: validTransactionContext.transaction.id,
        score: 25,
        level: 'LOW',
        factors: [],
        isFlagged: false,
        isBlocked: false,
        confidence: 0.9,
        createdAt: new Date(),
      };

      mockPrisma.riskAssessment.create.mockResolvedValue(mockRiskAssessment);

      // Act
      const result = await service.assessTransactionRisk(validTransactionContext);

      // Assert
      expect(result).toBeDefined();
      expect(result.transactionId).toBe(validTransactionContext.transaction.id);
      expect(result.riskScore.score).toBe(25);
      expect(result.riskScore.level).toBe('LOW');
      expect(result.isFlagged).toBe(false);
      expect(result.isBlocked).toBe(false);
      expect(result.recommendedAction).toBe('Process normally');

      // Verify database call
      expect(mockPrisma.riskAssessment.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          transactionId: validTransactionContext.transaction.id,
          score: expect.any(Number),
          level: expect.any(String),
          factors: expect.any(Array),
          confidence: expect.any(Number),
        }),
      });
    });

    it('should assess high risk for suspicious transaction', async () => {
      // Arrange
      const suspiciousContext = {
        ...validTransactionContext,
        transaction: {
          ...validTransactionContext.transaction,
          amount: 50000, // High amount
        },
        ipAddress: '*******', // Suspicious IP
      };

      const mockRiskAssessment = {
        id: testUtils.mockUUID(),
        transactionId: suspiciousContext.transaction.id,
        score: 85,
        level: 'HIGH',
        factors: ['high_amount', 'suspicious_ip'],
        isFlagged: true,
        isBlocked: false,
        confidence: 0.95,
        createdAt: new Date(),
      };

      mockPrisma.riskAssessment.create.mockResolvedValue(mockRiskAssessment);

      // Act
      const result = await service.assessTransactionRisk(suspiciousContext);

      // Assert
      expect(result.riskScore.score).toBeGreaterThan(80);
      expect(result.riskScore.level).toBe('HIGH');
      expect(result.isFlagged).toBe(true);
      expect(result.recommendedAction).toBe('Manual review required');
      expect(result.riskScore.factors).toContain('high_amount');
    });

    it('should block critical risk transactions', async () => {
      // Arrange
      const criticalContext = {
        ...validTransactionContext,
        transaction: {
          ...validTransactionContext.transaction,
          amount: 100000, // Very high amount
        },
        ipAddress: '0.0.0.0', // Invalid IP
        userAgent: 'Bot/1.0', // Bot user agent
      };

      const mockRiskAssessment = {
        id: testUtils.mockUUID(),
        transactionId: criticalContext.transaction.id,
        score: 95,
        level: 'CRITICAL',
        factors: ['very_high_amount', 'invalid_ip', 'bot_user_agent'],
        isFlagged: true,
        isBlocked: true,
        confidence: 0.98,
        createdAt: new Date(),
      };

      mockPrisma.riskAssessment.create.mockResolvedValue(mockRiskAssessment);

      // Act
      const result = await service.assessTransactionRisk(criticalContext);

      // Assert
      expect(result.riskScore.score).toBeGreaterThan(90);
      expect(result.riskScore.level).toBe('CRITICAL');
      expect(result.isBlocked).toBe(true);
      expect(result.recommendedAction).toBe('Block transaction');
    });

    it('should handle missing transaction data', async () => {
      // Arrange
      const invalidContext = {
        ...validTransactionContext,
        transaction: null as any,
      };

      // Act & Assert
      await expect(service.assessTransactionRisk(invalidContext)).rejects.toThrow(
        FraudDetectionError
      );

      expect(mockPrisma.riskAssessment.create).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      mockPrisma.riskAssessment.create.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.assessTransactionRisk(validTransactionContext)).rejects.toThrow(
        FraudDetectionError
      );
    });
  });

  describe('getFlaggedTransactions', () => {
    it('should return flagged transactions with pagination', async () => {
      // Arrange
      const mockFlaggedTransactions = [
        {
          id: testUtils.mockUUID(),
          transactionId: testUtils.mockUUID(),
          score: 85,
          level: 'HIGH',
          isFlagged: true,
          createdAt: new Date(),
        },
        {
          id: testUtils.mockUUID(),
          transactionId: testUtils.mockUUID(),
          score: 90,
          level: 'CRITICAL',
          isFlagged: true,
          createdAt: new Date(),
        },
      ];

      mockPrisma.riskAssessment.findMany.mockResolvedValue(mockFlaggedTransactions);
      mockPrisma.riskAssessment.count.mockResolvedValue(2);

      // Act
      const result = await service.getFlaggedTransactions({ page: 1, limit: 10 });

      // Assert
      expect(result.transactions).toEqual(mockFlaggedTransactions);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);

      expect(mockPrisma.riskAssessment.findMany).toHaveBeenCalledWith({
        where: { isFlagged: true },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 10,
      });
    });

    it('should filter by risk level', async () => {
      // Arrange
      const mockHighRiskTransactions = [
        {
          id: testUtils.mockUUID(),
          score: 85,
          level: 'HIGH',
          isFlagged: true,
        },
      ];

      mockPrisma.riskAssessment.findMany.mockResolvedValue(mockHighRiskTransactions);
      mockPrisma.riskAssessment.count.mockResolvedValue(1);

      // Act
      await service.getFlaggedTransactions({
        page: 1,
        limit: 10,
        riskLevel: 'HIGH',
      });

      // Assert
      expect(mockPrisma.riskAssessment.findMany).toHaveBeenCalledWith({
        where: {
          isFlagged: true,
          level: 'HIGH',
        },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 10,
      });
    });

    it('should handle empty results', async () => {
      // Arrange
      mockPrisma.riskAssessment.findMany.mockResolvedValue([]);
      mockPrisma.riskAssessment.count.mockResolvedValue(0);

      // Act
      const result = await service.getFlaggedTransactions();

      // Assert
      expect(result.transactions).toEqual([]);
      expect(result.total).toBe(0);
    });
  });

  describe('updateRiskAssessment', () => {
    it('should successfully update risk assessment', async () => {
      // Arrange
      const assessmentId = testUtils.mockUUID();
      const updates = {
        isFlagged: false,
        reason: 'False positive - manual review completed',
      };

      const mockUpdatedAssessment = {
        id: assessmentId,
        ...updates,
        updatedAt: new Date(),
      };

      mockPrisma.riskAssessment.update.mockResolvedValue(mockUpdatedAssessment);

      // Act
      const result = await service.updateRiskAssessment(assessmentId, updates);

      // Assert
      expect(result).toEqual(mockUpdatedAssessment);
      expect(mockPrisma.riskAssessment.update).toHaveBeenCalledWith({
        where: { id: assessmentId },
        data: {
          ...updates,
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should throw error for non-existent assessment', async () => {
      // Arrange
      const assessmentId = testUtils.mockUUID();
      mockPrisma.riskAssessment.update.mockRejectedValue(new Error('Record not found'));

      // Act & Assert
      await expect(
        service.updateRiskAssessment(assessmentId, { isFlagged: false })
      ).rejects.toThrow(FraudDetectionError);
    });
  });

  describe('getStatistics', () => {
    it('should return fraud detection statistics', async () => {
      // Arrange
      const merchantId = testUtils.mockUUID();
      const dateFrom = new Date('2024-01-01');
      const dateTo = new Date('2024-01-31');

      mockPrisma.riskAssessment.count
        .mockResolvedValueOnce(1000) // total
        .mockResolvedValueOnce(50) // flagged
        .mockResolvedValueOnce(10); // blocked

      mockPrisma.riskAssessment.aggregate.mockResolvedValue({
        _avg: { score: 35.5 },
      });

      // Act
      const result = await service.getStatistics(merchantId, dateFrom, dateTo);

      // Assert
      expect(result).toEqual({
        totalAssessments: 1000,
        flaggedTransactions: 50,
        blockedTransactions: 10,
        averageRiskScore: 35.5,
        flaggedRate: 5.0,
        blockedRate: 1.0,
      });

      // Verify database calls
      expect(mockPrisma.riskAssessment.count).toHaveBeenCalledTimes(3);
      expect(mockPrisma.riskAssessment.aggregate).toHaveBeenCalledTimes(1);
    });

    it('should handle zero assessments', async () => {
      // Arrange
      mockPrisma.riskAssessment.count.mockResolvedValue(0);
      mockPrisma.riskAssessment.aggregate.mockResolvedValue({ _avg: { score: null } });

      // Act
      const result = await service.getStatistics();

      // Assert
      expect(result.totalAssessments).toBe(0);
      expect(result.averageRiskScore).toBe(0);
      expect(result.flaggedRate).toBe(0);
      expect(result.blockedRate).toBe(0);
    });
  });

  describe('Risk Calculation', () => {
    it('should calculate risk based on amount', () => {
      // Test different amounts
      const lowAmountContext = {
        ...validTransactionContext,
        transaction: { ...validTransactionContext.transaction, amount: 100 },
      };
      const highAmountContext = {
        ...validTransactionContext,
        transaction: { ...validTransactionContext.transaction, amount: 10000 },
      };

      // This would test internal risk calculation logic
      // Implementation depends on actual risk calculation algorithm
    });

    it('should consider IP address reputation', () => {
      // Test different IP addresses
      const normalIP = { ...validTransactionContext, ipAddress: '***********' };
      const suspiciousIP = { ...validTransactionContext, ipAddress: '*******' };

      // This would test IP-based risk factors
    });

    it('should analyze user agent patterns', () => {
      // Test different user agents
      const normalUA = {
        ...validTransactionContext,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      };
      const botUA = { ...validTransactionContext, userAgent: 'Bot/1.0' };

      // This would test user agent analysis
    });
  });

  describe('Configuration Management', () => {
    it('should update fraud detection configuration', async () => {
      // Arrange
      const merchantId = testUtils.mockUUID();
      const config = {
        riskThresholds: {
          low: 30,
          medium: 60,
          high: 80,
          critical: 90,
        },
        autoBlock: true,
        reviewRequired: true,
      };

      const mockConfig = {
        id: testUtils.mockUUID(),
        merchantId,
        config,
        updatedAt: new Date(),
      };

      mockPrisma.fraudDetectionConfig.upsert.mockResolvedValue(mockConfig);

      // Act
      const result = await service.updateConfiguration(merchantId, config);

      // Assert
      expect(result).toEqual(mockConfig);
      expect(mockPrisma.fraudDetectionConfig.upsert).toHaveBeenCalledWith({
        where: { merchantId },
        update: { config, updatedAt: expect.any(Date) },
        create: { merchantId, config },
      });
    });

    it('should get fraud detection configuration', async () => {
      // Arrange
      const merchantId = testUtils.mockUUID();
      const mockConfig = {
        id: testUtils.mockUUID(),
        merchantId,
        config: {
          riskThresholds: { low: 30, medium: 60, high: 80, critical: 90 },
          autoBlock: true,
        },
      };

      mockPrisma.fraudDetectionConfig.findUnique.mockResolvedValue(mockConfig);

      // Act
      const result = await service.getConfiguration(merchantId);

      // Assert
      expect(result).toEqual(mockConfig);
      expect(mockPrisma.fraudDetectionConfig.findUnique).toHaveBeenCalledWith({
        where: { merchantId },
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid transaction context', async () => {
      const invalidContexts = [
        null,
        undefined,
        {},
        { transaction: null },
        { transaction: {}, merchant: null },
      ];

      for (const context of invalidContexts) {
        await expect(service.assessTransactionRisk(context as any)).rejects.toThrow(
          FraudDetectionError
        );
      }
    });

    it('should handle network timeouts', async () => {
      // Arrange
      mockPrisma.riskAssessment.create.mockImplementation(
        () => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100))
      );

      // Act & Assert
      await expect(service.assessTransactionRisk(validTransactionContext)).rejects.toThrow(
        FraudDetectionError
      );
    });
  });
});
