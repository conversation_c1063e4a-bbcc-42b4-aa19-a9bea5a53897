"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainEntries = toContainEntries;
const utils_1 = require("../utils");
function toContainEntries(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    // @ts-expect-error containsEntry takes an any type
    const pass = expected.every(entry => (0, utils_1.containsEntry)((a, b) => this.equals(a, b, this.customTesters), actual, entry));
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainEntries') +
                '\n\n' +
                'Expected object to not contain all of the given entries:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainEntries') +
                '\n\n' +
                'Expected object to contain all of the given entries:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
