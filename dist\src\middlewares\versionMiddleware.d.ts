import { Request, Response, NextFunction } from "express";
/**
 * Version middleware
 * This middleware handles API versioning
 */
export declare function versionMiddleware(req: Request, res: Response, next: NextFunction): void;
/**
 * Version header middleware
 * This middleware adds version headers to responses
 */
export declare function versionHeaderMiddleware(req: Request, res: Response, next: NextFunction): void;
export default versionMiddleware;
//# sourceMappingURL=versionMiddleware.d.ts.map