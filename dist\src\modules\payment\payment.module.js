"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentModule = void 0;
const module_1 = require("../../core/module");
const utils_1 = require("../../utils");
const auth_middleware_1 = require("../../middlewares/auth.middleware");
/**
 * Payment Module
 * This module provides payment functionality with zero duplication
 */
class PaymentModule {
    /**
     * Create a new payment module
     */
    constructor() {
        this.moduleRegistry = new module_1.ModuleRegistry();
        this.container = new module_1.Container();
        // Create module factory
        this.moduleFactory = new module_1.ModuleFactory('payment', 'Payment');
        // Get router, repository, service, and controller from factory
        const { router, repository, service, controller } = this.moduleFactory.build();
        // Configure router
        router
            .addRoute('get', '/:id', controller.getById)
            .addRoute('post', '/', controller.create)
            .addRoute('put', '/:id', controller.update)
            .addRoute('delete', '/:id', controller.delete)
            .addRoute('get', '/merchant/:merchantId', controller.getByMerchantId)
            .addRoute('post', '/:id/verify', controller.verifyPayment)
            .addMiddleware(auth_middleware_1.authMiddleware);
        // Add custom repository methods
        this.moduleFactory.addRepositoryMethod('findByMerchantId', async (merchantId, options = {}) => {
            try {
                return await repository.findByFieldWithPagination('merchantId', merchantId, options);
            }
            catch (error) {
                utils_1.logger.error(`Error finding payments by merchant ID ${merchantId}:`, error);
                throw error;
            }
        });
        // Add custom service methods
        this.moduleFactory.addServiceMethod('verifyPayment', async (id, verificationData) => {
            try {
                // Get payment
                const payment = await service.getById(id);
                // Check if payment exists
                if (!payment) {
                    throw utils_1.ErrorFactory.notFound('Payment', id);
                }
                // Verify payment
                // Implementation would depend on the specific verification process
                const isVerified = true; // Placeholder
                // Update payment status
                const updatedPayment = await service.update(id, {
                    status: isVerified ? 'COMPLETED' : 'FAILED',
                    verifiedAt: new Date(),
                    updatedAt: new Date()
                });
                utils_1.logger.info(`Payment verified: ${id}`, {
                    paymentId: id,
                    isVerified
                });
                return {
                    success: true,
                    payment: updatedPayment,
                    isVerified
                };
            }
            catch (error) {
                utils_1.logger.error(`Error verifying payment ${id}:`, error);
                throw utils_1.ErrorFactory.handle(error);
            }
        });
        // Add custom controller methods
        this.moduleFactory.addControllerMethod('getByMerchantId', async (req, res) => {
            try {
                // Check authorization
                const { userRole, merchantId } = req.user;
                // Get merchant ID from params
                const { merchantId: requestedMerchantId } = req.params;
                // Check if user has permission to view these payments
                if (userRole !== 'ADMIN' && merchantId !== requestedMerchantId) {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to view these payments');
                }
                // Parse pagination parameters
                const limit = parseInt(req.query.limit) || 10;
                const page = parseInt(req.query.page) || 1;
                const offset = (page - 1) * limit;
                // Get payments
                const payments = await service.findByMerchantId(requestedMerchantId, { limit, offset });
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: payments
                });
            }
            catch (error) {
                utils_1.logger.error(`Error getting payments by merchant ID:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while getting payments'
                });
            }
        });
        this.moduleFactory.addControllerMethod('verifyPayment', async (req, res) => {
            try {
                // Check authorization
                const { userRole } = req.user;
                // Only admins and merchants can verify payments
                if (userRole !== 'ADMIN' && userRole !== 'MERCHANT') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to verify payments');
                }
                // Get payment ID from params
                const { id } = req.params;
                // Get verification data from request body
                const verificationData = req.body;
                // Verify payment
                const result = await service.verifyPayment(id, verificationData);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                utils_1.logger.error(`Error verifying payment:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while verifying payment'
                });
            }
        });
        this.moduleFactory.addControllerMethod('getPaymentStats', async (req, res) => {
            try {
                // Check authorization
                const { userRole, merchantId } = req.user;
                // Only admins can view all payment stats
                const targetMerchantId = userRole === 'ADMIN' ? undefined : merchantId;
                // Get payment stats
                const stats = await service.getPaymentStats(targetMerchantId);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: stats
                });
            }
            catch (error) {
                utils_1.logger.error(`Error getting payment stats:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while getting payment stats'
                });
            }
        });
        // Create module
        this.module = {
            name: 'payment',
            router,
            repository,
            service,
            controller,
            dependencies: [],
            initialize: async () => {
                utils_1.logger.info('Initializing payment module');
                // Register dependencies
                this.container.registerSingleton('paymentRepository', () => repository);
                this.container.registerSingleton('paymentService', () => service);
                this.container.registerSingleton('paymentController', () => controller);
                utils_1.logger.info('Payment module initialized');
            }
        };
        // Register the module
        this.moduleRegistry.registerModule(this.module);
    }
    /**
     * Get the module
     * @returns Payment module
     */
    getModule() {
        return this.module;
    }
}
exports.PaymentModule = PaymentModule;
//# sourceMappingURL=payment.module.js.map