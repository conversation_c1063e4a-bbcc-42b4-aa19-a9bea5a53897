"use strict";
// jscpd:ignore-file
/**
 * Enhanced Payment Routes
 *
 * Routes for enhanced payment operations.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const enhanced_auth_middleware_1 = require("../middlewares/enhanced-auth.middleware");
const enhanced_payment_controller_1 = __importDefault(require("../controllers/enhanced-payment.controller"));
const router = (0, express_1.Router)();
// Public routes (no authentication required)
router.post("/process", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("merchantId").notEmpty(),
    (0, express_validator_1.body)("amount").isNumeric(),
    (0, express_validator_1.body)("currency").notEmpty(),
    (0, express_validator_1.body)("paymentMethodType").notEmpty()
]), enhanced_payment_controller_1.default.processPayment);
// Get payment method details
router.get("/methods/:type", (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("type").notEmpty()
]), enhanced_payment_controller_1.default.getPaymentMethodDetails);
// Routes requiring authentication
router.use(enhanced_auth_middleware_1.enhancedAuthenticate);
// Get available payment methods for a merchant
router.get("/methods/merchant/:merchantId", (0, enhanced_auth_middleware_1.requirePermission)("payment_methods", "view"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty(),
    (0, express_validator_1.query)("currency").optional()
]), enhanced_payment_controller_1.default.getAvailablePaymentMethods);
// Get all payment methods
router.get("/methods", (0, enhanced_auth_middleware_1.requirePermission)("payment_methods", "view"), enhanced_payment_controller_1.default.getAllPaymentMethods);
exports.default = router;
//# sourceMappingURL=enhanced-payment.routes.js.map