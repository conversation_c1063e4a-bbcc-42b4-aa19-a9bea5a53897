{"version": 3, "file": "prisma.mock.d.ts", "sourceRoot": "", "sources": ["../../../../src/tests/mocks/prisma.mock.ts"], "names": [], "mappings": "AAOA,oBAAY,8BAA8B;IACxC,KAAK,UAAU;IACf,KAAK,UAAU;IACf,QAAQ,aAAa;IACrB,UAAU,eAAe;IACzB,MAAM,WAAW;CAClB;AAED,oBAAY,8BAA8B;IACxC,OAAO,YAAY;IACnB,QAAQ,aAAa;IACrB,QAAQ,aAAa;IACrB,OAAO,YAAY;CACpB;AAED,oBAAY,qBAAqB;IAC/B,MAAM,WAAW;IACjB,WAAW,gBAAgB;IAC3B,WAAW,gBAAgB;IAC3B,aAAa,kBAAkB;IAC/B,aAAa,kBAAkB;CAChC;AAGD,oBAAY,iBAAiB;IAC3B,OAAO,YAAY;IACnB,SAAS,cAAc;IACvB,MAAM,WAAW;IACjB,OAAO,YAAY;IACnB,QAAQ,aAAa;CACtB;AAGD,eAAO,MAAM,gBAAgB,IAC5B,CAAA"}