"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const alert_analytics_controller_ts_1 = require("../controllers/refactored/alert-analytics.controller.ts");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// Alert analytics routes
router.get("/analytics/count-by-status", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getAlertCountByStatus);
router.get("/analytics/count-by-severity", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getAlertCountBySeverity);
router.get("/analytics/count-by-type", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getAlertCountByType);
router.get("/analytics/count-by-day", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getAlertCountByDay);
router.get("/analytics/count-by-hour", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getAlertCountByHour);
router.get("/analytics/top-merchants", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getTopMerchantsByAlertCount);
router.get("/analytics/resolution-time", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getAlertResolutionTimeStats);
router.get("/analytics/trends", auth_1.authenticate, alert_analytics_controller_ts_1.AlertAnalyticsController.getAlertTrends);
exports.default = router;
//# sourceMappingURL=alert-analytics.routes.js.map