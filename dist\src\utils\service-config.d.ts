/**
 * Service Configuration Utility
 *
 * This utility provides functions for managing environment-specific third-party service configurations
 * to ensure complete isolation between production and demo environments.
 */
/**
 * Get environment-specific service configuration
 * @param serviceName Name of the service
 * @param configKey Configuration key
 * @param defaultValue Default value if not found
 * @returns Environment-specific configuration value
 */
export declare const getServiceConfig: any;
/**
 * Get environment-specific API key
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API key
 */
export declare const getApiKey: any;
/**
 * Get environment-specific API secret
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API secret
 */
export declare const getApiSecret: any;
/**
 * Get environment-specific API URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API URL
 */
export declare const getApiUrl: any;
/**
 * Get environment-specific webhook URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific webhook URL
 */
export declare const getWebhookUrl: any;
/**
 * Get environment-specific callback URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific callback URL
 */
export declare const getCallbackUrl: any;
/**
 * Get environment-specific service credentials
 * @param serviceName Name of the service
 * @returns Environment-specific service credentials
 */
export declare const getServiceCredentials: any;
/**
 * Log service configuration for debugging
 * @param serviceName Name of the service
 */
export declare const logServiceConfig: any;
declare const _default: {
    getServiceConfig: any;
    getApiKey: any;
    getApiSecret: any;
    getApiUrl: any;
    getWebhookUrl: any;
    getCallbackUrl: any;
    getServiceCredentials: any;
    logServiceConfig: any;
};
export default _default;
//# sourceMappingURL=service-config.d.ts.map