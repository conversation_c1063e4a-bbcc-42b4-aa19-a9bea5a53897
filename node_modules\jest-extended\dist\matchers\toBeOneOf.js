"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toBeOneOf = toBeOneOf;
const utils_1 = require("../utils");
function toBeOneOf(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    // @ts-expect-error OK to have implicit any for this.equals
    const pass = (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), expected, actual);
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toBeOneOf') +
                '\n\n' +
                'Expected value to not be in list:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toBeOneOf') +
                '\n\n' +
                'Expected value to be in list:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
