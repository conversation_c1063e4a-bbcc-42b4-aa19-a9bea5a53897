"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const payment_recommendation_controller_1 = require("../controllers/payment-recommendation.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const role_middleware_1 = require("../middlewares/role.middleware");
const router = express_1.default.Router();
const paymentRecommendationController = new payment_recommendation_controller_1.PaymentRecommendationController();
/**
 * @route GET /api/payment-recommendation
 * @desc Get payment method recommendations
 * @access Private (Merchant, Admin)
 */
router.get('/', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['MERCHANT', 'ADMIN']), paymentRecommendationController.getRecommendations);
/**
 * @route GET /api/payment-recommendation/transaction/:transactionId
 * @desc Get payment method recommendation for a specific transaction
 * @access Private (Merchant, Admin)
 */
router.get('/transaction/:transactionId', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['MERCHANT', 'ADMIN']), paymentRecommendationController.getTransactionRecommendation);
/**
 * @route GET /api/payment-recommendation/weights/:merchantId
 * @desc Get merchant recommendation weights
 * @access Private (Merchant, Admin)
 */
router.get('/weights/:merchantId', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['MERCHANT', 'ADMIN']), paymentRecommendationController.getRecommendationWeights);
/**
 * @route PUT /api/payment-recommendation/weights/:merchantId
 * @desc Update merchant recommendation weights
 * @access Private (Merchant, Admin)
 */
router.put('/weights/:merchantId', auth_middleware_1.authMiddleware, (0, role_middleware_1.roleMiddleware)(['MERCHANT', 'ADMIN']), paymentRecommendationController.updateRecommendationWeights);
exports.default = router;
//# sourceMappingURL=payment-recommendation.routes.js.map