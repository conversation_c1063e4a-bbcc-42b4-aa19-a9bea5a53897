/**
 * Custom Assertions
 *
 * Custom Jest matchers for common test assertions.
 */
import { TestAssertions } from '../core/TestTypes';
/**
 * Custom Jest matchers
 */
export declare const customMatchers: TestAssertions;
/**
 * Additional assertion helpers
 */
export declare class AssertionHelpers {
    /**
     * Assert that an array contains unique elements
     */
    static assertUniqueArray<T>(array: T[], keyExtractor?: (item: T) => any): void;
    /**
     * Assert that an object has all required properties
     */
    static assertRequiredProperties(obj: any, requiredProps: string[]): void;
    /**
     * Assert that a value is one of the allowed values
     */
    static assertOneOf<T>(value: T, allowedValues: T[]): void;
    /**
     * Assert that a string matches a pattern
     */
    static assertMatchesPattern(value: string, pattern: RegExp, message?: string): void;
    /**
     * Assert that an async function throws
     */
    static assertAsyncThrows(fn: () => Promise<any>, expectedError?: string | RegExp | Error): Promise<void>;
    /**
     * Assert that an async function does not throw
     */
    static assertAsyncDoesNotThrow(fn: () => Promise<any>): Promise<void>;
    /**
     * Assert that a value is within a percentage of another value
     */
    static assertWithinPercentage(actual: number, expected: number, percentage: number): void;
    /**
     * Assert that an array is sorted
     */
    static assertArraySorted<T>(array: T[], compareFn?: (a: T, b: T) => number, ascending?: boolean): void;
    /**
     * Helper method to get comparison result
     */
    private static getComparison;
    /**
     * Assert that a mock was called in order
     */
    static assertMockCallOrder(mocks: jest.Mock[], expectedOrder: string[]): void;
    /**
     * Assert that a value is a valid JSON string
     */
    static assertValidJson(value: string): void;
    /**
     * Assert that two objects are deeply equal ignoring specified keys
     */
    static assertDeepEqualIgnoring(actual: any, expected: any, ignoredKeys: string[]): void;
}
/**
 * Setup custom matchers
 */
export declare function setupCustomMatchers(): void;
/**
 * Database assertion helpers
 */
export declare class DatabaseAssertions {
    /**
     * Assert that a record exists in the database
     */
    static assertRecordExists(prisma: any, model: string, where: any): Promise<void>;
    /**
     * Assert that a record does not exist in the database
     */
    static assertRecordDoesNotExist(prisma: any, model: string, where: any): Promise<void>;
    /**
     * Assert record count
     */
    static assertRecordCount(prisma: any, model: string, expectedCount: number, where?: any): Promise<void>;
}
//# sourceMappingURL=CustomAssertions.d.ts.map