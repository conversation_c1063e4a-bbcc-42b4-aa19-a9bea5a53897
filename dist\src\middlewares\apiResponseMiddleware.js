"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiErrorCode = void 0;
exports.apiResponseMiddleware = apiResponseMiddleware;
exports.errorHandlerMiddleware = errorHandlerMiddleware;
const uuid_1 = require("uuid");
/**
 * API error codes
 */
var ApiErrorCode;
(function (ApiErrorCode) {
    /**
     * Generic error
     */
    ApiErrorCode["GENERIC_ERROR"] = "GENERIC_ERROR";
    /**
     * Authentication error
     */
    ApiErrorCode["AUTHENTICATION_ERROR"] = "AUTHENTICATION_ERROR";
    /**
     * Authorization error
     */
    ApiErrorCode["AUTHORIZATION_ERROR"] = "AUTHORIZATION_ERROR";
    /**
     * Validation error
     */
    ApiErrorCode["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    /**
     * Resource not found
     */
    ApiErrorCode["NOT_FOUND"] = "NOT_FOUND";
    /**
     * Duplicate resource
     */
    ApiErrorCode["DUPLICATE"] = "DUPLICATE";
    /**
     * Rate limit exceeded
     */
    ApiErrorCode["RATE_LIMIT_EXCEEDED"] = "RATE_LIMIT_EXCEEDED";
    /**
     * Server error
     */
    ApiErrorCode["SERVER_ERROR"] = "SERVER_ERROR";
    /**
     * Database error
     */
    ApiErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
    /**
     * External service error
     */
    ApiErrorCode["EXTERNAL_SERVICE_ERROR"] = "EXTERNAL_SERVICE_ERROR";
    /**
     * Payment processing error
     */
    ApiErrorCode["PAYMENT_ERROR"] = "PAYMENT_ERROR";
    /**
     * Blockchain error
     */
    ApiErrorCode["BLOCKCHAIN_ERROR"] = "BLOCKCHAIN_ERROR";
    /**
     * Webhook error
     */
    ApiErrorCode["WEBHOOK_ERROR"] = "WEBHOOK_ERROR";
    /**
     * Notification error
     */
    ApiErrorCode["NOTIFICATION_ERROR"] = "NOTIFICATION_ERROR";
})(ApiErrorCode || (exports.ApiErrorCode = ApiErrorCode = {}));
/**
 * Middleware to add standardized API response methods to Express Response object
 */
function apiResponseMiddleware(req, res, next) {
    // Generate a unique request ID
    const requestId = (0, uuid_1.v4)();
    // Add success response method
    res.success = function (data, message, meta) {
        const response = {
            success: true,
            data,
            message,
            meta,
            timestamp: new Date().toISOString(),
            requestId,
        };
        this.json(response);
    };
    // Add paginated response method
    res.paginated = function (data, pagination, message, meta) {
        const response = {
            success: true,
            data,
            pagination,
            message,
            meta,
            timestamp: new Date().toISOString(),
            requestId,
        };
        this.json(response);
    };
    // Add error response method
    res.error = function (error, errorCode = ApiErrorCode.GENERIC_ERROR, statusCode = 500, validationErrors, meta) {
        const response = {
            success: false,
            error,
            errorCode,
            validationErrors,
            meta,
            timestamp: new Date().toISOString(),
            requestId,
        };
        this.status(statusCode).json(response);
    };
    // Add validation error response method
    res.validationError = function (validationErrors, message, meta) {
        const response = {
            success: false,
            error: message || 'Validation failed',
            errorCode: ApiErrorCode.VALIDATION_ERROR,
            validationErrors,
            meta,
            timestamp: new Date().toISOString(),
            requestId,
        };
        this.status(400).json(response);
    };
    // Add not found response method
    res.notFound = function (resource, id, meta) {
        const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
        const response = {
            success: false,
            error: message,
            errorCode: ApiErrorCode.NOT_FOUND,
            meta,
            timestamp: new Date().toISOString(),
            requestId,
        };
        this.status(404).json(response);
    };
    next();
}
/**
 * Error handling middleware for standardized API error responses
 */
function errorHandlerMiddleware(err, req, res, next) {
    console.error('API Error:', err);
    // Handle different types of errors
    if (err.name === 'ValidationError') {
        // Handle validation errors (e.g., from Joi or express-validator)
        const validationErrors = {};
        if (err.errors) {
            // Handle mongoose validation errors
            Object.keys(err.errors).forEach((key) => {
                validationErrors[key] = [err.errors[key].message];
            });
        }
        else if (err.details) {
            // Handle Joi validation errors
            err.details.forEach((detail) => {
                const key = detail.path.join('.');
                if (!validationErrors[key]) {
                    validationErrors[key] = [];
                }
                validationErrors[key].push(detail.message);
            });
        }
        else {
            // Generic validation error
            validationErrors['_error'] = [err.message];
        }
        res.validationError(validationErrors);
    }
    else if (err.name === 'NotFoundError' || err.statusCode === 404) {
        // Handle not found errors
        res.notFound(err.resource || 'Resource', err.id);
    }
    else if (err.name === 'UnauthorizedError' || err.statusCode === 401) {
        // Handle authentication errors
        res.error('Authentication required', ApiErrorCode.AUTHENTICATION_ERROR, 401);
    }
    else if (err.name === 'ForbiddenError' || err.statusCode === 403) {
        // Handle authorization errors
        res.error('Access denied', ApiErrorCode.AUTHORIZATION_ERROR, 403);
    }
    else if (err.name === 'ConflictError' || err.statusCode === 409) {
        // Handle conflict errors
        res.error(err.message || 'Resource conflict', ApiErrorCode.DUPLICATE, 409);
    }
    else if (err.name === 'PaymentError') {
        // Handle payment errors
        res.error(err.message || 'Payment processing error', ApiErrorCode.PAYMENT_ERROR, 400);
    }
    else if (err.name === 'BlockchainError') {
        // Handle blockchain errors
        res.error(err.message || 'Blockchain error', ApiErrorCode.BLOCKCHAIN_ERROR, 400);
    }
    else {
        // Handle generic errors
        const statusCode = err.statusCode || 500;
        const errorCode = statusCode >= 500 ? ApiErrorCode.SERVER_ERROR : ApiErrorCode.GENERIC_ERROR;
        res.error(err.message || 'Internal server error', errorCode, statusCode);
    }
}
