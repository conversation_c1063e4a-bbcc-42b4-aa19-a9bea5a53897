"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
// jscpd:ignore-file
const dotenv_1 = __importDefault(require("dotenv"));
const logger_1 = require("../lib/logger");
// Load environment variables from .env file
dotenv_1.default.config();
/**
 * Validate required environment variables
 */
const validateEnv = () => {
    const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET'];
    const missingEnvVars = requiredEnvVars.filter((envVar) => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
        const errorMessage = `Missing required environment variables: ${missingEnvVars.join(', ')}`;
        if (process.env.NODE_ENV === 'production') {
            throw new Error(errorMessage);
        }
        else {
            logger_1.logger.warn(errorMessage);
            logger_1.logger.warn('Using default values for development. DO NOT use these in production!');
        }
    }
};
// Validate environment variables
validateEnv();
/**
 * Application configuration
 */
exports.config = {
    // Server configuration
    server: {
        port: process.env.PORT || 3002, // Use port 3002 as default
        host: process.env.HOST || 'localhost',
        env: process.env.NODE_ENV || 'development',
        apiPrefix: process.env.API_PREFIX || '/api',
    },
    // App configuration
    app: {
        url: process.env.FRONTEND_URL || 'http://localhost:5173',
        apiUrl: process.env.API_URL || 'http://localhost:3002/api',
        domain: process.env.DOMAIN || 'amazingpayme.com',
    },
    // Database configuration
    database: {
        url: process.env.DATABASE_URL,
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME || 'Amazingpay',
    },
    // JWT configuration
    jwt: {
        secret: process.env.JWT_SECRET,
        expiresIn: process.env.JWT_EXPIRES_IN || '1d',
        refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    },
    // CORS configuration
    cors: {
        origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
        methods: process.env.CORS_METHODS || 'GET,HEAD,PUT,PATCH,POST,DELETE',
        preflightContinue: process.env.CORS_PREFLIGHT_CONTINUE === 'true' ? true : false,
        optionsSuccessStatus: parseInt(process.env.CORS_OPTIONS_SUCCESS_STATUS || '204', 10),
        credentials: true,
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Site-Domain', 'X-Request-ID'],
        exposedHeaders: [
            'X-Request-ID',
            'X-RateLimit-Limit',
            'X-RateLimit-Remaining',
            'X-RateLimit-Reset',
        ],
        maxAge: parseInt(process.env.CORS_MAX_AGE || '86400', 10), // 24 hours
    },
    // Binance API configuration
    binance: {
        apiUrl: process.env.BINANCE_API_URL || 'https://api.binance.com',
        testApiUrl: process.env.BINANCE_TEST_API_URL || 'https://testnet.binance.vision',
    },
    // Blockchain API configuration
    blockchain: {
        tronApiKey: process.env.TRON_API_KEY || '',
        etherscanApiKey: process.env.ETHERSCAN_API_KEY || '',
        bscscanApiKey: process.env.BSCSCAN_API_KEY || '',
        polygonscanApiKey: process.env.POLYGONSCAN_API_KEY || '',
    },
    // Email configuration
    email: {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
            user: process.env.EMAIL_USER || '',
            pass: process.env.EMAIL_PASS || '',
        },
        from: process.env.EMAIL_FROM || '<EMAIL>',
    },
    // SMS configuration
    sms: {
        provider: process.env.SMS_PROVIDER || 'twilio',
        accountSid: process.env.TWILIO_ACCOUNT_SID || '',
        authToken: process.env.TWILIO_AUTH_TOKEN || '',
        from: process.env.TWILIO_PHONE_NUMBER || '',
    },
    // Telegram configuration
    telegram: {
        botToken: process.env.TELEGRAM_BOT_TOKEN || '',
        webhookUrl: process.env.TELEGRAM_WEBHOOK_URL || '',
        adminChatId: process.env.TELEGRAM_ADMIN_CHAT_ID || '',
    },
    // Push notification configuration
    push: {
        publicKey: process.env.VAPID_PUBLIC_KEY || '',
        privateKey: process.env.VAPID_PRIVATE_KEY || '',
        contactEmail: process.env.VAPID_CONTACT_EMAIL || '<EMAIL>',
    },
    // Rate limiting configuration
    rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
        max: parseInt(process.env.RATE_LIMIT_MAX || '100'), // limit each IP to 100 requests per windowMs
        authWindowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS || '3600000'), // 1 hour
        authMax: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5'), // limit each IP to 5 login attempts per hour
        passwordResetWindowMs: parseInt(process.env.PASSWORD_RESET_RATE_LIMIT_WINDOW_MS || '3600000'), // 1 hour
        passwordResetMax: parseInt(process.env.PASSWORD_RESET_RATE_LIMIT_MAX || '3'), // limit each IP to 3 password reset attempts per hour
        sensitiveOperationWindowMs: parseInt(process.env.SENSITIVE_OPERATION_RATE_LIMIT_WINDOW_MS || '86400000'), // 24 hours
        sensitiveOperationMax: parseInt(process.env.SENSITIVE_OPERATION_RATE_LIMIT_MAX || '5'), // limit each IP to 5 sensitive operations per day
    },
    // Logging configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined',
        directory: process.env.LOG_DIRECTORY || 'logs',
        maxSize: parseInt(process.env.LOG_MAX_SIZE || '10485760'), // 10MB
        maxFiles: parseInt(process.env.LOG_MAX_FILES || '30'), // 30 days
        rotationInterval: process.env.LOG_ROTATION_INTERVAL || 'daily',
    },
    // Security configuration
    security: {
        bcryptSaltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS || '10'),
        csrfEnabled: process.env.CSRF_ENABLED !== 'false',
        csrfSecret: process.env.CSRF_SECRET || 'amazingpay-csrf-secret',
        csrfTokenExpiration: parseInt(process.env.CSRF_TOKEN_EXPIRATION || '3600000'), // 1 hour
        xssProtection: process.env.XSS_PROTECTION !== 'false',
        contentSecurityPolicy: process.env.CONTENT_SECURITY_POLICY !== 'false',
        hsts: process.env.HSTS !== 'false',
        hstsMaxAge: parseInt(process.env.HSTS_MAX_AGE || '31536000'), // 1 year
        frameGuard: process.env.FRAME_GUARD !== 'false',
        noSniff: process.env.NO_SNIFF !== 'false',
        referrerPolicy: process.env.REFERRER_POLICY || 'strict-origin-when-cross-origin',
    },
    // Monitoring configuration
    monitoring: {
        enabled: process.env.MONITORING_ENABLED !== 'false',
        healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '300000'), // 5 minutes
        maxRecentRequests: parseInt(process.env.MAX_RECENT_REQUESTS || '100'),
        maxErrorRequests: parseInt(process.env.MAX_ERROR_REQUESTS || '50'),
        slowResponseThreshold: parseInt(process.env.SLOW_RESPONSE_THRESHOLD || '1000'), // 1 second
        cleanupInterval: parseInt(process.env.MONITORING_CLEANUP_INTERVAL || '86400000'), // 24 hours
        retentionDays: parseInt(process.env.MONITORING_RETENTION_DAYS || '30'), // 30 days
    },
    // Payment configuration
    payment: {
        defaultCurrency: process.env.DEFAULT_CURRENCY || 'USDT',
        defaultExpirationMinutes: parseInt(process.env.DEFAULT_EXPIRATION_MINUTES || '30'),
    },
    // Webhook configuration
    webhook: {
        retryAttempts: parseInt(process.env.WEBHOOK_RETRY_ATTEMPTS || '3'),
        retryDelay: parseInt(process.env.WEBHOOK_RETRY_DELAY || '5000'), // 5 seconds
        timeout: parseInt(process.env.WEBHOOK_TIMEOUT || '10000'), // 10 seconds
    },
};
