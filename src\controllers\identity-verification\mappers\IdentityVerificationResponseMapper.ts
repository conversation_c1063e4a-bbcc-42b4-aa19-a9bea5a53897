/**
 * Identity Verification Response Mapper
 *
 * Handles response formatting for identity verification operations.
 */

import { Response } from 'express';
import {
  SuccessResponse,
  ErrorResponse,
  VerificationResponse,
  ClaimResponse,
  VerificationStatsResponse,
  SupportedNetworksResponse,
} from '../types/IdentityVerificationControllerTypes';
import { AppError } from '../../../utils/errors/AppError';

/**
 * Response mapper for identity verification
 */
export class IdentityVerificationResponseMapper {
  /**
   * Send success response
   */
  static sendSuccess<T>(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId || 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId || 'unknown',
      };

      statusCode = statusCode || error.statusCode || 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message || 'Internal server error',
          code: 'INTERNAL_SERVER_ERROR',
          type: 'INTERNAL',
        },
        timestamp: new Date(),
        requestId: res.locals.requestId || 'unknown',
      };

      statusCode = statusCode || 500;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send verification result response
   */
  static sendVerificationResult(res: Response, result: any, message?: string): void {
    const statusCode = result.success ? 200 : 400;

    this.sendSuccess(
      res,
      result,
      message || (result.success ? 'Verification completed successfully' : 'Verification failed'),
      statusCode
    );
  }

  /**
   * Send verification response
   */
  static sendVerification(
    res: Response,
    verification: VerificationResponse,
    message?: string
  ): void {
    this.sendSuccess(res, verification, message || 'Verification retrieved successfully');
  }

  /**
   * Send verifications list response
   */
  static sendVerificationsList(
    res: Response,
    verifications: VerificationResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);

    this.sendSuccess(res, verifications, `Retrieved ${verifications.length} verifications`, 200, {
      page,
      limit,
      total,
      totalPages,
    });
  }

  /**
   * Send claim response
   */
  static sendClaim(
    res: Response,
    claim: ClaimResponse,
    message?: string,
    statusCode: number = 200
  ): void {
    this.sendSuccess(res, claim, message || 'Claim processed successfully', statusCode);
  }

  /**
   * Send claim created response
   */
  static sendClaimCreated(res: Response, claim: ClaimResponse): void {
    this.sendClaim(res, claim, 'Claim added successfully', 201);
  }

  /**
   * Send verification statistics response
   */
  static sendVerificationStats(res: Response, stats: VerificationStatsResponse): void {
    this.sendSuccess(res, stats, 'Verification statistics retrieved successfully');
  }

  /**
   * Send supported networks response
   */
  static sendSupportedNetworks(res: Response, networks: SupportedNetworksResponse): void {
    this.sendSuccess(res, networks, 'Supported networks retrieved successfully');
  }

  /**
   * Send blockchain verification request created response
   */
  static sendBlockchainVerificationRequestCreated(res: Response, request: any): void {
    this.sendSuccess(res, request, 'Blockchain verification request created successfully', 201);
  }

  /**
   * Send blockchain verification completed response
   */
  static sendBlockchainVerificationCompleted(res: Response, isVerified: boolean): void {
    if (isVerified) {
      this.sendSuccess(res, { verified: true }, 'Blockchain identity verified successfully');
    } else {
      this.sendSuccess(res, { verified: false }, 'Blockchain identity verification failed', 400);
    }
  }

  /**
   * Send verification expiration check response
   */
  static sendVerificationExpirationCheck(res: Response, count: number): void {
    this.sendSuccess(res, { count }, `Found ${count} expired verifications`);
  }

  /**
   * Send verification expiration set response
   */
  static sendVerificationExpirationSet(res: Response, verification: VerificationResponse): void {
    this.sendSuccess(res, verification, 'Verification expiration set successfully');
  }

  /**
   * Send ENS domain verification response
   */
  static sendENSDomainVerification(res: Response, result: any): void {
    this.sendVerificationResult(res, result, 'ENS domain verification completed');
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: any[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: 'VALIDATION' as any,
      code: 'INVALID_INPUT' as any,
      details: { errors },
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: 'AUTHENTICATION' as any,
      code: 'INVALID_CREDENTIALS' as any,
      details: { requiredRole },
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: 'NOT_FOUND' as any,
      code: 'RESOURCE_NOT_FOUND' as any,
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      type: 'INTERNAL' as any,
      code: 'INTERNAL_SERVER_ERROR' as any,
    });

    this.sendError(res, error, 500);
  }

  /**
   * Format pagination metadata
   */
  static formatPagination(
    page: number,
    limit: number,
    total: number
  ): {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } {
    const totalPages = Math.ceil(total / limit);

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: any, res: Response, next: Function) => {
      Promise.resolve(fn(req, res, next)).catch((error) => next(error));
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res: Response): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Response-Time', Date.now());
  }

  /**
   * Log response for debugging
   */
  static logResponse(method: string, url: string, statusCode: number, responseTime: number): void {
    console.log(`${method} ${url} - ${statusCode} - ${responseTime}ms`);
  }

  /**
   * Transform verification data for response
   */
  static transformVerification(verification: any): VerificationResponse {
    return {
      id: verification.id,
      type: verification.type,
      status: verification.status,
      address: verification.address,
      userId: verification.userId,
      merchantId: verification.merchantId,
      metadata: verification.metadata,
      createdAt: verification.createdAt,
      updatedAt: verification.updatedAt,
      expiresAt: verification.expiresAt,
    };
  }

  /**
   * Transform claim data for response
   */
  static transformClaim(claim: any): ClaimResponse {
    return {
      id: claim.id,
      verificationId: claim.verificationId,
      type: claim.type,
      value: claim.value,
      issuer: claim.issuer,
      isRevoked: claim.isRevoked,
      createdAt: claim.createdAt,
      updatedAt: claim.updatedAt,
    };
  }

  /**
   * Transform verification statistics for response
   */
  static transformVerificationStats(stats: any): VerificationStatsResponse {
    return {
      totalVerifications: stats.totalVerifications || 0,
      verifiedCount: stats.verifiedCount || 0,
      pendingCount: stats.pendingCount || 0,
      failedCount: stats.failedCount || 0,
      expiredCount: stats.expiredCount || 0,
      verificationsByType: stats.verificationsByType || {},
      verificationsByNetwork: stats.verificationsByNetwork || {},
    };
  }
}
