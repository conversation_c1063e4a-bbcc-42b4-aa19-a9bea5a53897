"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringService = void 0;
// jscpd:ignore-file
const logger_1 = require("../utils/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
const payment_service_1 = require("./payment.service");
const verification_service_1 = require("./verification.service");
const webhook_service_1 = require("./webhook.service");
/**
 * Monitoring service
 */
class MonitoringService {
    /**
     * Create a new monitoring service
     */
    constructor() {
        this.startTime = Date.now();
        this.requestCounts = {};
        this.responseTimes = {};
        this.errorCounts = {};
    }
    /**
     * Get system uptime in seconds
     * @returns Uptime in seconds
     */
    getUptime() {
        return Math.floor((Date.now() - this.startTime) / 1000);
    }
    /**
     * Track API request
     * @param endpoint Endpoint
     * @param startTime Start time
     * @param statusCode Status code
     */
    trackApiRequest(endpoint, startTime, statusCode) {
        const responseTime = Date.now() - startTime;
        // Initialize counters if needed
        if (!this.requestCounts[endpoint]) {
            this.requestCounts[endpoint] = 0;
            this.responseTimes[endpoint] = [];
            this.errorCounts[endpoint] = 0;
        }
        // Increment request count
        this.requestCounts[endpoint]++;
        // Add response time
        this.responseTimes[endpoint].push(responseTime);
        // Track errors
        if (statusCode >= 400) {
            this.errorCounts[endpoint]++;
        }
        // Log request
        logger_1.logger.debug('API request tracked', {
            endpoint,
            responseTime,
            statusCode,
        });
    }
    /**
     * Get all monitoring metrics
     * @returns Monitoring metrics
     */
    async getMetrics() {
        try {
            // Get system metrics
            const systemHealth = await this.getSystemHealthMetrics();
            // Get payment metrics
            const payments = await this.getPaymentMetrics();
            // Get verification metrics
            const verification = await this.getVerificationMetrics();
            // Get webhook metrics
            const webhooks = await this.getWebhookMetrics();
            // Get merchant metrics
            const merchants = await this.getMerchantMetrics();
            // Get API metrics
            const api = this.getApiMetrics();
            // Get time-based metrics
            const timeBasedMetrics = await this.getTimeBasedMetrics();
            return {
                systemHealth,
                payments,
                verification,
                webhooks,
                merchants,
                api,
                timeBasedMetrics,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting monitoring metrics', { error });
            throw new Error('Failed to get monitoring metrics');
        }
    }
    /**
     * Get system health metrics
     * @returns System health metrics
     */
    async getSystemHealthMetrics() {
        try {
            // Get memory usage
            const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
            // Get CPU usage (simplified)
            const cpuUsage = process.cpuUsage().user / 1000000; // seconds
            // Get active connections (simplified)
            const activeConnections = Object.keys(this.requestCounts).reduce((sum, key) => sum + this.requestCounts[key], 0);
            // Get API latency
            const apiLatency = this.getAverageResponseTime();
            // Determine system health status
            let status = 'healthy';
            if (memoryUsage > 1024 || cpuUsage > 10 || apiLatency > 1000) {
                status = 'degraded';
            }
            if (memoryUsage > 2048 || cpuUsage > 20 || apiLatency > 5000) {
                status = 'unhealthy';
            }
            return {
                status,
                uptime: this.getUptime(),
                memoryUsage,
                cpuUsage,
                activeConnections,
                apiLatency,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting system health metrics', { error });
            return {
                status: 'degraded',
                uptime: this.getUptime(),
                memoryUsage: 0,
                cpuUsage: 0,
                activeConnections: 0,
                apiLatency: 0,
            };
        }
    }
    /**
     * Get payment metrics
     * @returns Payment metrics
     */
    async getPaymentMetrics() {
        try {
            // Get payment counts by status
            const paymentCounts = await prisma_1.default.transaction.groupBy({
                by: ['status'],
                _count: {
                    id: true,
                },
                _avg: {
                    amount: true,
                },
                _sum: {
                    amount: true,
                },
            });
            // Initialize metrics
            const metrics = {
                total: 0,
                completed: 0,
                failed: 0,
                pending: 0,
                expired: 0,
                refunded: 0,
                successRate: 0,
                averageAmount: 0,
                totalVolume: 0,
            };
            // Calculate metrics
            paymentCounts.forEach((count) => {
                const countValue = count._count.id;
                metrics.total += countValue;
                switch (count.status) {
                    case payment_service_1.PaymentStatus.COMPLETED:
                        metrics.completed += countValue;
                        break;
                    case payment_service_1.PaymentStatus.FAILED:
                        metrics.failed += countValue;
                        break;
                    case payment_service_1.PaymentStatus.PENDING:
                        metrics.pending += countValue;
                        break;
                    case payment_service_1.PaymentStatus.EXPIRED:
                        metrics.expired += countValue;
                        break;
                    case payment_service_1.PaymentStatus.REFUNDED:
                        metrics.refunded += countValue;
                        break;
                }
                // Add to total volume
                if (count._sum.amount) {
                    metrics.totalVolume += count._sum.amount;
                }
            });
            // Calculate success rate
            if (metrics.total > 0) {
                metrics.successRate = (metrics.completed / metrics.total) * 100;
            }
            // Calculate average amount
            if (metrics.total > 0) {
                metrics.averageAmount = metrics.totalVolume / metrics.total;
            }
            return metrics;
        }
        catch (error) {
            logger_1.logger.error('Error getting payment metrics', { error });
            return {
                total: 0,
                completed: 0,
                failed: 0,
                pending: 0,
                expired: 0,
                refunded: 0,
                successRate: 0,
                averageAmount: 0,
                totalVolume: 0,
            };
        }
    }
    /**
     * Get verification metrics
     * @returns Verification metrics
     */
    async getVerificationMetrics() {
        try {
            // Get verification counts by status
            const verificationCounts = await prisma_1.default.transaction.groupBy({
                by: ['verificationStatus'],
                _count: {
                    id: true,
                },
            });
            // Get verification method distribution
            const methodDistribution = await prisma_1.default.transaction.groupBy({
                by: ['verificationMethod'],
                _count: {
                    id: true,
                },
            });
            // Initialize metrics
            const metrics = {
                total: 0,
                verified: 0,
                failed: 0,
                pending: 0,
                successRate: 0,
                averageTime: 0,
                methodDistribution: {},
            };
            // Calculate metrics
            verificationCounts.forEach((count) => {
                const countValue = count._count.id;
                metrics.total += countValue;
                switch (count.verificationStatus) {
                    case verification_service_1.VerificationStatus.VERIFIED:
                        metrics.verified += countValue;
                        break;
                    case verification_service_1.VerificationStatus.FAILED:
                        metrics.failed += countValue;
                        break;
                    case verification_service_1.VerificationStatus.PENDING:
                        metrics.pending += countValue;
                        break;
                }
            });
            // Calculate success rate
            if (metrics.total > 0) {
                metrics.successRate = (metrics.verified / metrics.total) * 100;
            }
            // Calculate method distribution
            methodDistribution.forEach((method) => {
                if (method.verificationMethod) {
                    metrics.methodDistribution[method.verificationMethod] = method._count.id;
                }
            });
            // Calculate average verification time (simplified)
            metrics.averageTime = 5000; // 5 seconds (placeholder)
            return metrics;
        }
        catch (error) {
            logger_1.logger.error('Error getting verification metrics', { error });
            return {
                total: 0,
                verified: 0,
                failed: 0,
                pending: 0,
                successRate: 0,
                averageTime: 0,
                methodDistribution: {},
            };
        }
    }
    /**
     * Get webhook metrics
     * @returns Webhook metrics
     */
    async getWebhookMetrics() {
        try {
            // Get webhook counts by status
            const webhookCounts = await prisma_1.default.webhook.groupBy({
                by: ['status'],
                _count: {
                    id: true,
                },
            });
            // Get webhook event distribution
            const eventDistribution = await prisma_1.default.webhook.groupBy({
                by: ['event'],
                _count: {
                    id: true,
                },
            });
            // Initialize metrics
            const metrics = {
                total: 0,
                delivered: 0,
                failed: 0,
                pending: 0,
                retrying: 0,
                deliveryRate: 0,
                averageLatency: 0,
                eventDistribution: {},
            };
            // Calculate metrics
            webhookCounts.forEach((count) => {
                const countValue = count._count.id;
                metrics.total += countValue;
                switch (count.status) {
                    case webhook_service_1.WebhookDeliveryStatus.SUCCESS:
                        metrics.delivered += countValue;
                        break;
                    case webhook_service_1.WebhookDeliveryStatus.FAILED:
                        metrics.failed += countValue;
                        break;
                    case webhook_service_1.WebhookDeliveryStatus.PENDING:
                        metrics.pending += countValue;
                        break;
                    case webhook_service_1.WebhookDeliveryStatus.RETRYING:
                        metrics.retrying += countValue;
                        break;
                }
            });
            // Calculate delivery rate
            if (metrics.total > 0) {
                metrics.deliveryRate = (metrics.delivered / metrics.total) * 100;
            }
            // Calculate event distribution
            eventDistribution.forEach((event) => {
                metrics.eventDistribution[event.event] = event._count.id;
            });
            // Calculate average latency (simplified)
            metrics.averageLatency = 500; // 500ms (placeholder)
            return metrics;
        }
        catch (error) {
            logger_1.logger.error('Error getting webhook metrics', { error });
            return {
                total: 0,
                delivered: 0,
                failed: 0,
                pending: 0,
                retrying: 0,
                deliveryRate: 0,
                averageLatency: 0,
                eventDistribution: {},
            };
        }
    }
    /**
     * Get merchant metrics
     * @returns Merchant metrics
     */
    async getMerchantMetrics() {
        try {
            // Get merchant counts
            const totalMerchants = await prisma_1.default.merchant.count();
            const activeMerchants = await prisma_1.default.merchant.count({
                where: { isActive: true },
            });
            const verifiedMerchants = await prisma_1.default.merchant.count({
                where: { isVerified: true },
            });
            // Get transaction counts per merchant
            const transactionsPerMerchant = await prisma_1.default.transaction.groupBy({
                by: ['merchantId'],
                _count: {
                    id: true,
                },
            });
            // Calculate average transactions per merchant
            let totalTransactions = 0;
            transactionsPerMerchant.forEach((merchant) => {
                totalTransactions += merchant._count.id;
            });
            const averageTransactionsPerMerchant = totalMerchants > 0 ? totalTransactions / totalMerchants : 0;
            return {
                total: totalMerchants,
                active: activeMerchants,
                inactive: totalMerchants - activeMerchants,
                verified: verifiedMerchants,
                unverified: totalMerchants - verifiedMerchants,
                averageTransactionsPerMerchant,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting merchant metrics', { error });
            return {
                total: 0,
                active: 0,
                inactive: 0,
                verified: 0,
                unverified: 0,
                averageTransactionsPerMerchant: 0,
            };
        }
    }
    /**
     * Get API metrics
     * @returns API metrics
     */
    getApiMetrics() {
        try {
            // Calculate total requests
            const totalRequests = Object.values(this.requestCounts).reduce((sum, count) => sum + count, 0);
            // Calculate total errors
            const totalErrors = Object.values(this.errorCounts).reduce((sum, count) => sum + count, 0);
            // Calculate error rate
            const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
            // Calculate average response time
            const averageResponseTime = this.getAverageResponseTime();
            // Calculate requests per minute
            const uptime = this.getUptime();
            const requestsPerMinute = uptime > 0 ? (totalRequests / uptime) * 60 : 0;
            // Calculate endpoint distribution
            const endpointDistribution = {};
            Object.keys(this.requestCounts).forEach((endpoint) => {
                endpointDistribution[endpoint] = this.requestCounts[endpoint];
            });
            return {
                totalRequests,
                successfulRequests: totalRequests - totalErrors,
                failedRequests: totalErrors,
                averageResponseTime,
                requestsPerMinute,
                errorRate,
                endpointDistribution,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting API metrics', { error });
            return {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                averageResponseTime: 0,
                requestsPerMinute: 0,
                errorRate: 0,
                endpointDistribution: {},
            };
        }
    }
    /**
     * Get time-based metrics
     * @returns Time-based metrics
     */
    async getTimeBasedMetrics() {
        try {
            // Get current date
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            // Get hourly transactions for today
            const hourlyTransactions = {};
            for (let i = 0; i < 24; i++) {
                const hour = i.toString().padStart(2, '0');
                hourlyTransactions[hour] = 0;
            }
            // Get daily transactions for the last 7 days
            const dailyTransactions = {};
            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const dateString = date.toISOString().split('T')[0];
                dailyTransactions[dateString] = 0;
            }
            // Get weekly transactions for the last 4 weeks
            const weeklyTransactions = {};
            for (let i = 3; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i * 7);
                const weekString = `Week ${i + 1}`;
                weeklyTransactions[weekString] = 0;
            }
            // Get monthly transactions for the last 6 months
            const monthlyTransactions = {};
            for (let i = 5; i >= 0; i--) {
                const date = new Date(today);
                date.setMonth(date.getMonth() - i);
                const monthString = date.toISOString().split('T')[0].substring(0, 7);
                monthlyTransactions[monthString] = 0;
            }
            // Get transactions for the last 6 months
            const transactions = await prisma_1.default.transaction.findMany({
                where: {
                    createdAt: {
                        gte: new Date(today.getFullYear(), today.getMonth() - 5, 1),
                    },
                },
                select: {
                    createdAt: true,
                },
            });
            // Populate metrics
            transactions.forEach((transaction) => {
                const date = new Date(transaction.createdAt);
                const hour = date.getHours().toString().padStart(2, '0');
                const dateString = date.toISOString().split('T')[0];
                const monthString = dateString.substring(0, 7);
                // Add to hourly transactions if today
                if (date >= today) {
                    hourlyTransactions[hour]++;
                }
                // Add to daily transactions if in the last 7 days
                if (date >= new Date(today.getFullYear(), today.getMonth(), today.getDate() - 6)) {
                    dailyTransactions[dateString] = (dailyTransactions[dateString] || 0) + 1;
                }
                // Add to weekly transactions
                const weekDiff = Math.floor((today.getTime() - date.getTime()) / (7 * 24 * 60 * 60 * 1000));
                if (weekDiff < 4) {
                    const weekString = `Week ${4 - weekDiff}`;
                    weeklyTransactions[weekString]++;
                }
                // Add to monthly transactions
                if (monthString in monthlyTransactions) {
                    monthlyTransactions[monthString]++;
                }
            });
            return {
                hourlyTransactions,
                dailyTransactions,
                weeklyTransactions,
                monthlyTransactions,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting time-based metrics', { error });
            return {
                hourlyTransactions: {},
                dailyTransactions: {},
                weeklyTransactions: {},
                monthlyTransactions: {},
            };
        }
    }
    /**
     * Get average response time across all endpoints
     * @returns Average response time in milliseconds
     */
    getAverageResponseTime() {
        let totalTime = 0;
        let totalRequests = 0;
        Object.keys(this.responseTimes).forEach((endpoint) => {
            this.responseTimes[endpoint].forEach((time) => {
                totalTime += time;
                totalRequests++;
            });
        });
        return totalRequests > 0 ? totalTime / totalRequests : 0;
    }
}
exports.MonitoringService = MonitoringService;
