
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  isActive: 'isActive',
  twoFactorEnabled: 'twoFactorEnabled',
  twoFactorSecret: 'twoFactorSecret',
  lastLogin: 'lastLogin',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MerchantScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  businessName: 'businessName',
  businessType: 'businessType',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  website: 'website',
  country: 'country',
  address: 'address',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  verificationStatus: 'verificationStatus',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AdminScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  department: 'department',
  permissions: 'permissions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  permissions: 'permissions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  merchantId: 'merchantId',
  userId: 'userId',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  type: 'type',
  paymentMethod: 'paymentMethod',
  reference: 'reference',
  description: 'description',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AlertScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  transactionId: 'transactionId',
  type: 'type',
  severity: 'severity',
  status: 'status',
  message: 'message',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AlertAggregationRuleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  conditions: 'conditions',
  actions: 'actions',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AlertCorrelationRuleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  conditions: 'conditions',
  actions: 'actions',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentMethodScalarFieldEnum = {
  id: 'id',
  merchantId: 'merchantId',
  customerId: 'customerId',
  type: 'type',
  name: 'name',
  isDefault: 'isDefault',
  isActive: 'isActive',
  details: 'details',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemSettingScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  updatedById: 'updatedById',
  updatedAt: 'updatedAt',
  createdAt: 'createdAt'
};

exports.Prisma.ReportScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  format: 'format',
  filters: 'filters',
  recordCount: 'recordCount',
  filePath: 'filePath',
  createdAt: 'createdAt'
};

exports.Prisma.ReportTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  config: 'config',
  isSystem: 'isSystem',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ScheduledReportScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  templateId: 'templateId',
  schedule: 'schedule',
  cronExpression: 'cronExpression',
  parameters: 'parameters',
  recipients: 'recipients',
  exportFormat: 'exportFormat',
  isActive: 'isActive',
  lastRunAt: 'lastRunAt',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SavedReportScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  templateId: 'templateId',
  parameters: 'parameters',
  filePath: 'filePath',
  fileType: 'fileType',
  createdById: 'createdById',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt'
};

exports.Prisma.ReportRunScalarFieldEnum = {
  id: 'id',
  scheduledReportId: 'scheduledReportId',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  filePath: 'filePath',
  fileType: 'fileType',
  error: 'error',
  metadata: 'metadata'
};

exports.Prisma.DashboardScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  layout: 'layout',
  isPublic: 'isPublic',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DashboardWidgetScalarFieldEnum = {
  id: 'id',
  dashboardId: 'dashboardId',
  type: 'type',
  title: 'title',
  config: 'config',
  position: 'position',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WebhookScalarFieldEnum = {
  id: 'id',
  merchantId: 'merchantId',
  event: 'event',
  payload: 'payload',
  status: 'status',
  url: 'url',
  retryCount: 'retryCount',
  maxRetries: 'maxRetries',
  statusCode: 'statusCode',
  response: 'response',
  error: 'error',
  lastAttemptAt: 'lastAttemptAt',
  deliveredAt: 'deliveredAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VerificationHistoryScalarFieldEnum = {
  id: 'id',
  paymentId: 'paymentId',
  status: 'status',
  method: 'method',
  details: 'details',
  createdAt: 'createdAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  level: 'level',
  message: 'message',
  source: 'source',
  details: 'details',
  createdAt: 'createdAt'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  phone: 'phone',
  address: 'address',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  planId: 'planId',
  status: 'status',
  amount: 'amount',
  currency: 'currency',
  interval: 'interval',
  startDate: 'startDate',
  endDate: 'endDate',
  nextBillingDate: 'nextBillingDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  merchantId: 'merchantId',
  type: 'type',
  channel: 'channel',
  subject: 'subject',
  message: 'message',
  priority: 'priority',
  status: 'status',
  metadata: 'metadata',
  templateId: 'templateId',
  sentAt: 'sentAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationDeliveryScalarFieldEnum = {
  id: 'id',
  notificationId: 'notificationId',
  channel: 'channel',
  recipient: 'recipient',
  status: 'status',
  attempts: 'attempts',
  lastAttemptAt: 'lastAttemptAt',
  deliveredAt: 'deliveredAt',
  error: 'error',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  channel: 'channel',
  subject: 'subject',
  body: 'body',
  variables: 'variables',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserNotificationPreferenceScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  channel: 'channel',
  type: 'type',
  enabled: 'enabled',
  settings: 'settings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MerchantNotificationPreferenceScalarFieldEnum = {
  id: 'id',
  merchantId: 'merchantId',
  channel: 'channel',
  type: 'type',
  enabled: 'enabled',
  settings: 'settings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PushSubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  merchantId: 'merchantId',
  endpoint: 'endpoint',
  subscription: 'subscription',
  deviceInfo: 'deviceInfo',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IdentityVerificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  merchantId: 'merchantId',
  address: 'address',
  method: 'method',
  status: 'status',
  verificationData: 'verificationData',
  verifiedAt: 'verifiedAt',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IdentityVerificationClaimScalarFieldEnum = {
  id: 'id',
  identityVerificationId: 'identityVerificationId',
  claimType: 'claimType',
  claimValue: 'claimValue',
  isVerified: 'isVerified',
  verifiedAt: 'verifiedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RiskAssessmentScalarFieldEnum = {
  id: 'id',
  transactionId: 'transactionId',
  score: 'score',
  level: 'level',
  factors: 'factors',
  isFlagged: 'isFlagged',
  isBlocked: 'isBlocked',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FraudDetectionConfigScalarFieldEnum = {
  id: 'id',
  merchantId: 'merchantId',
  flagThreshold: 'flagThreshold',
  blockThreshold: 'blockThreshold',
  autoBlock: 'autoBlock',
  factorWeights: 'factorWeights',
  highRiskCountries: 'highRiskCountries',
  highRiskIpRanges: 'highRiskIpRanges',
  maxTransactionAmount: 'maxTransactionAmount',
  maxTransactionsPerHour: 'maxTransactionsPerHour',
  maxTransactionsPerDay: 'maxTransactionsPerDay',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.IdentityVerificationMethodEnum = exports.$Enums.IdentityVerificationMethodEnum = {
  ETHEREUM_SIGNATURE: 'ETHEREUM_SIGNATURE',
  ERC1484: 'ERC1484',
  ERC725: 'ERC725',
  ENS: 'ENS',
  POLYGON_ID: 'POLYGON_ID',
  WORLDCOIN: 'WORLDCOIN',
  UNSTOPPABLE_DOMAINS: 'UNSTOPPABLE_DOMAINS',
  BRIGHTID: 'BRIGHTID'
};

exports.IdentityVerificationStatusEnum = exports.$Enums.IdentityVerificationStatusEnum = {
  PENDING: 'PENDING',
  VERIFIED: 'VERIFIED',
  FAILED: 'FAILED',
  EXPIRED: 'EXPIRED',
  REVOKED: 'REVOKED',
  REJECTED: 'REJECTED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Merchant: 'Merchant',
  Admin: 'Admin',
  Role: 'Role',
  Transaction: 'Transaction',
  Alert: 'Alert',
  AlertAggregationRule: 'AlertAggregationRule',
  AlertCorrelationRule: 'AlertCorrelationRule',
  PaymentMethod: 'PaymentMethod',
  SystemSetting: 'SystemSetting',
  Report: 'Report',
  ReportTemplate: 'ReportTemplate',
  ScheduledReport: 'ScheduledReport',
  SavedReport: 'SavedReport',
  ReportRun: 'ReportRun',
  Dashboard: 'Dashboard',
  DashboardWidget: 'DashboardWidget',
  Webhook: 'Webhook',
  VerificationHistory: 'VerificationHistory',
  AuditLog: 'AuditLog',
  Customer: 'Customer',
  Subscription: 'Subscription',
  Notification: 'Notification',
  NotificationDelivery: 'NotificationDelivery',
  NotificationTemplate: 'NotificationTemplate',
  UserNotificationPreference: 'UserNotificationPreference',
  MerchantNotificationPreference: 'MerchantNotificationPreference',
  PushSubscription: 'PushSubscription',
  IdentityVerification: 'IdentityVerification',
  IdentityVerificationClaim: 'IdentityVerificationClaim',
  RiskAssessment: 'RiskAssessment',
  FraudDetectionConfig: 'FraudDetectionConfig'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
