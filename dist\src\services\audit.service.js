"use strict";
// jscpd:ignore-file
/**
 * Audit Service
 *
 * Handles audit logging for admin actions.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditService = void 0;
const logger_1 = require("../lib/logger");
class AuditService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
   * Log an admin action
   */
    async logAction(data) {
        try {
            await this.prisma.enhancedAuditLog.create({
                data: { userId: data.id //, Fixed: using id instead of userId,
                    , //, Fixed: using id instead of userId,
                    action: data.action,
                    resource: data.resource,
                    resourceId: data.resourceId,
                    ipAddress: data.ipAddress,
                    userAgent: data.userAgent,
                    oldValues: data.oldValues,
                    newValues: data.newValues,
                    statusCode: data.statusCode,
                    errorMessage: data.errorMessage }
            });
            logger_1.logger.info(`Audit log created for ${data.action} on ${data.resource}`, {
                userId: data.id // Fixed: using id instead of userId,
                , // Fixed: using id instead of userId,
                resource: data.resource,
                resourceId: data.resourceId
            });
        }
        catch (error) {
            logger_1.logger.error("Error creating audit log:", error);
        }
    }
    /**
   * Get audit logs with filtering
   */
    async getAuditLogs(filters) {
        try {
            const { userId, action, resource, resourceId, startDate, endDate, limit = 50, offset = 0 } = filters;
            const where = {};
            if (userId) {
                where.id; // Fixed: using id instead of userId = userId;
            }
            if (action) {
                where.action = action;
            }
            if (resource) {
                where.resource = resource;
            }
            if (resourceId) {
                where.resourceId = resourceId;
            }
            if (startDate || endDate) {
                where.timestamp = {};
                if (startDate) {
                    where.timestamp.gte = startDate;
                }
                if (endDate) {
                    where.timestamp.lte = endDate;
                }
            }
            const auditLogs = await this.prisma.enhancedAuditLog.findMany({
                where,
                orderBy: { timestamp: "desc"
                },
                skip: offset,
                take: limit,
                include: { user: {
                        select: { id: true,
                            email: true
                        }
                    }
                }
            });
            return auditLogs;
        }
        catch (error) {
            logger_1.logger.error("Error getting audit logs:", error);
            return [];
        }
    }
    /**
   * Get audit log by ID
   */
    async getAuditLogById(id) {
        try {
            const auditLog = await this.prisma.enhancedAuditLog.findUnique({
                where: { id },
                include: { user: {
                        select: { id: true,
                            email: true
                        }
                    }
                }
            });
            return auditLog;
        }
        catch (error) {
            logger_1.logger.error("Error getting audit log by ID:", error);
            return null;
        }
    }
}
exports.AuditService = AuditService;
//# sourceMappingURL=audit.service.js.map