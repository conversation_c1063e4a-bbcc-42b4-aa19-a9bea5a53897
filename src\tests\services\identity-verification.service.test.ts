// jscpd:ignore-file
/**
 * identity-verification.service Tests
 *
 * This file contains tests for the identity-verification.service module using the test utility.
 */

import { identity-verification.serviceController } from '../controllers/identity-verification.service.controller';
import { identity-verification.serviceService } from '../services/identity-verification.service.service';
import { identity-verification.serviceRepository } from '../repositories/identity-verification.service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';
import { identity-verification.serviceService } from '../services/identity-verification.service.service';
import { identity-verification.serviceRepository } from '../repositories/identity-verification.service.repository';
import { testControllerSuite, createMockRequest, createMockResponse, testService, testRepository, createMockPrismaClient } from './tests/utils/test-utilities';
import { Repository } from '../types/database';


// Mock the identity-verification.serviceService
jest.mock('../services/identity-verification.service.service');

describe('identity-verification.service Module Tests', () => {
  // Controller tests
  testControllerSuite('identity-verification.serviceController', identity-verification.serviceController, {
    getAll: {, description: 'should get all identity-verification.services',
      expectedStatus: 200,
      expectedResponse: {, success: true },
    },
    getById: {, description: 'should get identity-verification.service by ID',
      req: createMockRequest({, params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: {, success: true },
    },
    create: {, description: 'should create identity-verification.service',
      req: createMockRequest({, body: { name: 'Test' } }),
      expectedStatus: 201,
      expectedResponse: {, success: true },
    },
    update: {, description: 'should update identity-verification.service',
      req: createMockRequest({, params: { id: '1' }, body: {, name: 'Updated' } }),
      expectedStatus: 200,
      expectedResponse: {, success: true },
    },
    delete: {, description: 'should delete identity-verification.service',
      req: createMockRequest({, params: { id: '1' } }),
      expectedStatus: 200,
      expectedResponse: {, success: true, message: 'identity-verification.service deleted successfully' },
    },
  });

  // Service tests
  describe('identity-verification.serviceService', () => {
    let service;
    let mockRepository;

    beforeEach(() => {
      mockRepository = {
        findAll: jest.fn(),
        findById: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      };

      service = new identity-verification.serviceService();
      service.identity-verification.serviceRepository = mockRepository;
    });

    it('should find all identity-verification.services', async () => {
      mockRepository.findAll.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: any =await testService(service, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockRepository.findAll).toHaveBeenCalled();
    });
  });

  // Repository tests
  describe('identity-verification.serviceRepository', () => {
    let repository;
    let mockPrisma;

    beforeEach(() => {
      mockPrisma = createMockPrismaClient();
      repository = new identity-verification.serviceRepository();
      repository.prisma = mockPrisma;
    });

    it('should find all identity-verification.services', async () => {
      mockPrisma.identity-verification.service.findMany.mockResolvedValue([{ id: '1', name: 'Test' }]);

      const result: any =await testRepository(repository, 'findAll');

      expect(result).toEqual([{ id: '1', name: 'Test' }]);
      expect(mockPrisma.identity-verification.service.findMany).toHaveBeenCalled();
    });
  });
});
