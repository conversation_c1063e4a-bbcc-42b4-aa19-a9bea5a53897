/**
 * Generate a JWT token
 * @param payload - Data to encode in the token
 * @param expiresIn - Token expiration time (default: 1 day)
 * @returns The generated JWT token
 */
export declare const generateToken: (payload: Record<string, any>, expiresIn?: string) => string;
/**
 * Verify a JWT token
 * @param token - The JWT token to verify
 * @returns The decoded token payload
 * @throws AppError if token is invalid or expired
 */
export declare const verifyToken: (token: string) => Record<string, any>;
/**
 * Extract token from authorization header
 * @param authHeader - The authorization header value
 * @returns The extracted token
 * @throws AppError if header format is invalid
 */
export declare const extractTokenFromHeader: (authHeader: string | undefined) => string;
/**
 * Generate a refresh token
 * @param userId - User ID to encode in the token
 * @param expiresIn - Token expiration time (default: 7 days)
 * @returns The generated refresh token
 */
export declare const generateRefreshToken: (userId: string, expiresIn?: string) => string;
/**
 * Verify a refresh token
 * @param token - The refresh token to verify
 * @returns The decoded token payload
 * @throws AppError if token is invalid or expired
 */
export declare const verifyRefreshToken: (token: string) => Record<string, any>;
//# sourceMappingURL=jwt.d.ts.map