const express = require('express');
const app = express();
const port = process.env.PORT || 3002;

// Basic middleware
app.use(express.json());

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'AmazingPay Flow Server is running!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Fee management test endpoint
app.get('/api/fee-management/test', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Fee management system is working!',
    timestamp: new Date().toISOString()
  });
});

// Basic payment endpoint
app.post('/api/payments', (req, res) => {
  res.json({
    status: 'success',
    message: 'Payment endpoint is working!',
    data: {
      id: 'test-payment-' + Date.now(),
      amount: req.body.amount || 100,
      currency: req.body.currency || 'USD',
      status: 'pending'
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    status: 'error',
    message: 'Something went wrong!'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    status: 'error',
    message: 'Endpoint not found'
  });
});

// Start server
app.listen(port, () => {
  console.log(`🚀 AmazingPay Flow Test Server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/api/health`);
  console.log(`💰 Fee management: http://localhost:${port}/api/fee-management/test`);
  console.log(`💳 Payments: http://localhost:${port}/api/payments`);
});
