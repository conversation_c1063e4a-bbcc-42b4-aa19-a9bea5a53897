{"version": 3, "file": "ApiAnalyticsService.js", "sourceRoot": "", "sources": ["../../../../src/services/analytics/ApiAnalyticsService.ts"], "names": [], "mappings": ";;;;;;AAEA,6CAA0C;AAC1C,4EAA6C;AAmB7C;;;GAGG;AACH,MAAa,mBAAmB;IAY9B;;OAEG;IACH;QAZA,eAAe;QACP,gBAAW,GAAwB,EAAE,CAAC;QAE9C,cAAc;QACN,eAAU,GAAW,GAAG,CAAC;QA4M/B,kBAAkB;QACZ,UAAK,GAAQ,MAAM,uBAAM,CAAC,YAAY,CAAC,KAAK,CAAC;YACjD,KAAK;SACN,CAAC,CAAC;QAEH,WAAW;QACL,SAAI,GAAQ,MAAM,uBAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YACnD,KAAK;YACL,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;aAC3B;YACD,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,GAAG;YACzB,IAAI,EAAE,MAAM,CAAC,MAAM,IAAI,CAAC;SACzB,CAAC,CAAC;QA/MH,+BAA+B;QAC/B,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAElB,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YAClC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC3D,CAAC;QAED,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,GAAY,EAAE,GAAa,EAAE,YAAoB;QACnE,eAAe;QACf,MAAM,KAAK,GAAsB;YAC/B,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,YAAY;YACZ,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;YAC7B,QAAQ,EAAG,GAAW,CAAC,IAAI,EAAE,IAAI;YACjC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,SAAS,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,aAAa;YAC7C,UAAU,EAAG,GAAW,CAAC,UAAU;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,sBAAsB;QACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7B,4BAA4B;QAC5B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/C,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW;QACvB,0BAA0B;QAC1B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,cAAc;QACd,MAAM,MAAM,GAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QAE1C,eAAe;QACf,IAAI,CAAC,WAAW,GAAG,AAAD,CAAE;QAEpB,IAAI,CAAC;YACH,0BAA0B;YAC1B,MAAM,uBAAM,CAAC,YAAY,CAAC,UAAU,CAAC;gBACnC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACzB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,qCAAqC;sBAArC,qCAAqC;oBACtD,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,SAAS,EAAE,KAAK,CAAC,SAAS;iBAC3B,CAAC,CAAC;aACJ,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,WAAW,MAAM,CAAC,MAAM,uBAAuB,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAE7D,4BAA4B;YAC5B,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YAEpD,oBAAoB;YACpB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBAClD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC5D,eAAM,CAAC,IAAI,CAAC,4CAA4C,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACrB,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YACzD,wBAAwB;YACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,oBAAoB;YACpB,MAAM,SAAS,GAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;YAElC,4BAA4B;YAC5B,MAAM,WAAW,GAAQ,GAAG,CAAC,GAAG,CAAC;YAEjC,sBAAsB;YACtB,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;gBACpB,0BAA0B;gBAC1B,MAAM,YAAY,GAAQ,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAEjD,gBAAgB;gBAChB,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;gBAE1C,2BAA2B;gBAC3B,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACtC,CAAC,CAAC;YAEF,WAAW;YACX,IAAI,EAAE,CAAC;QACT,CAAC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,YAAY,CAAC,MAWzB;QAIC,sBAAsB;QACtB,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,cAAc;QACd,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,KAAK,CAAC,SAAS,GAAG;gBAChB,GAAG,KAAK,CAAC,SAAS;gBAClB,GAAG,EAAE,MAAM,CAAC,SAAS;aACtB,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,KAAK,CAAC,SAAS,GAAG;gBAChB,GAAG,KAAK,CAAC,SAAS;gBAClB,GAAG,EAAE,MAAM,CAAC,OAAO;aACpB,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,GAAG;gBACX,QAAQ,EAAE,MAAM,CAAC,IAAI;aACtB,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC/B,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QACvC,CAAC;QAED,IAAI,MAAM,CAAC,EAAE,CAAC,uCAAuC;;YACnD,KAAK,CAAC,EAAE,CAAA,CAAC,sFAAsF;IACjG,CAAC;IAED,EAAE,CAAE,MAAM,EAAC,QAAQ;QACjB,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,EAAE,CAAE,MAAM,EAAC,UAAU;QACnB,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;IACvC,CAAC;CAcE;AA/NP,kDA+NO;AAEH,OAAO;IACL,IAAI;IACJ,KAAK;CACN,CAAC;AAQG,KAAK,CAAA;AAAC,mBAAmB,CAAC,MAAM,EAAE;IACvC,SAAS,EAAG,IAAI;IAChB,OAAO,EAAG,IAAI;IACd,IAAI,EAAG,MAAM;IACb,MAAM,EAAG,MAAM;IACf,UAAU,EAAG,MAAM;IACnB,MAAM,EAAG,MAAM;IACf,QAAQ,EAAG,MAAM;IACjB,UAAU,EAAG,MAAM;CACpB,CAAC,CAAA;AAAE,OAAO,GAAC;IACV,aAAa,EAAE,MAAM;IACrB,mBAAmB,EAAE,MAAM;IAC3B,WAAW,EAAE,MAAM;IACnB,SAAS,EAAE,MAAM;IACjB,gBAAgB,EAAE,CAAA,MAAsB,CAAA;IACxC,cAAc,EAAE,CAAA,MAAsB,CAAA;IACtC,oBAAoB,EAAE,CAAA,MAAsB,CAAA;IAC5C,oBAAoB,EAAE,CAAA,MAAsB,CAAA;CAC7C,GAAE;IACD,gBAAgB;IAChB,KAAK,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC;QAC9C,GAAG,MAAM;QACT,KAAK,EAAE,IAAI;KACZ,CAAC;IAEF,oBAAoB;IACpB,KAAK,EAAC,aAAa,EAAE,GAAG,GAAG,KAAK;IAChC,KAAK,EAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,IAAI,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,MAAM;IACtG,KAAK,EAAC,aAAa,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,MAAM;IAC/E,KAAK,EAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;IAEvF,qBAAqB;IACrB,KAAK,EAAC,mBAAmB,EAAE,GAAG,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAC1F,KAAK,EAAC,WAAW,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAC3E,KAAK,EAAC,SAAS,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAEvE,kBAAkB;IAClB,KAAK,EAAC,gBAAgB;CAA0B,CAAA;AAAC,CAAC,CAAA,CAAC;AAAA,CAAC;AACpD,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;AAAI,CAAC;IACxB,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7E,CAAC;AAAC,CAAC;AAEH,gBAAgB;AAChB,MAAM,cAAc,GAA2B,EAAE,CAAC;AAClD,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;AAAI,CAAC;IACxB,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACrE,CAAC;AAAC,CAAC;AAEH,uBAAuB;AACvB,MAAM,oBAAoB,GAA2B,EAAE,CAAC;AACxD,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;AAAI,CAAC;IACxB,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7F,CAAC;AAAC,CAAC;AAEH,uBAAuB;AACvB,MAAM,oBAAoB,GAA2B,EAAE,CAAC;AACxD,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;AAAI,CAAC;IACxB,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;QACrB,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7F,CAAC;AACH,CAAC;AAAC,CAAC;AAEH,OAAO;IACL,aAAa;IACb,mBAAmB;IACnB,WAAW;IACX,SAAS;IACT,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;CACrB,CAAC;AAMG,KAAK,EAAE,CAAA;AAAE,KAAK;IAED,EAAA,CAAC,aAAa;IAEhC,eAAe;IACf,IAAI,EAAA,CAAC,WAAW,EAAE;IAElB,MAAM,EAAN,eAAM,EAAA,EAAA,CAAC,IAAI,CAAC,8BAA8B,CAAC;CAC5C,CAAA;AAGH,kBAAe,mBAAmB,CAAC"}