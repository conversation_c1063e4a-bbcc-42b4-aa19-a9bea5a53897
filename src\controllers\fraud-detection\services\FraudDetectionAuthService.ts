/**
 * Fraud Detection Authorization Service
 * 
 * Handles authorization logic for fraud detection operations.
 */

import { AppError, ErrorType, ErrorCode } from '../../../utils/errors/AppError';
import { AuthorizationContext, PermissionResult } from '../types/FraudDetectionControllerTypes';

/**
 * Authorization service for fraud detection operations
 */
export class FraudDetectionAuthService {
  private readonly adminRoles = ['ADMIN', 'SUPER_ADMIN'];
  private readonly merchantRoles = ['MERCHANT', 'ADMIN', 'SUPER_ADMIN'];
  private readonly userRoles = ['USER', 'MERCHANT', 'ADMIN', 'SUPER_ADMIN'];

  /**
   * Check if user is authorized for the given action
   */
  async checkPermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource, action } = context;

    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated'
      };
    }

    // Check role-based permissions
    const rolePermission = this.checkRolePermission(user.role, resource, action);
    if (!rolePermission.allowed) {
      return rolePermission;
    }

    // Check resource-specific permissions
    const resourcePermission = await this.checkResourcePermission(context);
    if (!resourcePermission.allowed) {
      return resourcePermission;
    }

    return { allowed: true };
  }

  /**
   * Check role-based permissions
   */
  private checkRolePermission(userRole: string, resource: string, action: string): PermissionResult {
    switch (resource) {
      case 'risk_assessment':
        return this.checkRiskAssessmentPermission(userRole, action);
      case 'fraud_config':
        return this.checkFraudConfigPermission(userRole, action);
      case 'flagged_transactions':
        return this.checkFlaggedTransactionsPermission(userRole, action);
      case 'fraud_statistics':
        return this.checkFraudStatisticsPermission(userRole, action);
      default:
        return {
          allowed: false,
          reason: `Unknown resource: ${resource}`
        };
    }
  }

  /**
   * Check risk assessment permissions
   */
  private checkRiskAssessmentPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'assess_risk':
        if (this.merchantRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Merchant role required for risk assessment',
          requiredRole: 'MERCHANT'
        };
      case 'view_assessment':
        if (this.merchantRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Merchant role required for viewing assessments',
          requiredRole: 'MERCHANT'
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`
        };
    }
  }

  /**
   * Check fraud configuration permissions
   */
  private checkFraudConfigPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'view_config':
        if (this.merchantRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Merchant role required for viewing fraud configuration',
          requiredRole: 'MERCHANT'
        };
      case 'update_config':
        if (this.adminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for updating fraud configuration',
          requiredRole: 'ADMIN'
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`
        };
    }
  }

  /**
   * Check flagged transactions permissions
   */
  private checkFlaggedTransactionsPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'view_flagged':
        if (this.merchantRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Merchant role required for viewing flagged transactions',
          requiredRole: 'MERCHANT'
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`
        };
    }
  }

  /**
   * Check fraud statistics permissions
   */
  private checkFraudStatisticsPermission(userRole: string, action: string): PermissionResult {
    switch (action) {
      case 'view_statistics':
        if (this.merchantRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Merchant role required for viewing fraud statistics',
          requiredRole: 'MERCHANT'
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`
        };
    }
  }

  /**
   * Check resource-specific permissions
   */
  private async checkResourcePermission(context: AuthorizationContext): Promise<PermissionResult> {
    const { user, resource, action, resourceId } = context;

    // Merchants can only access their own data
    if (user.role === 'MERCHANT' && user.merchantId) {
      // For merchant-specific resources, ensure they can only access their own data
      if (resource === 'fraud_config' || resource === 'flagged_transactions' || resource === 'fraud_statistics') {
        // This would typically involve checking if the resourceId belongs to the merchant
        // For now, we'll allow access and let the business logic filter by merchant
        return { allowed: true };
      }
    }

    // Admins can access all resources
    if (this.adminRoles.includes(user.role)) {
      return { allowed: true };
    }

    return { allowed: true };
  }

  /**
   * Require admin role
   */
  requireAdmin(userRole?: string): void {
    if (!userRole || !this.adminRoles.includes(userRole)) {
      throw new AppError({
        message: 'Admin role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS
      });
    }
  }

  /**
   * Require merchant role or higher
   */
  requireMerchant(userRole?: string): void {
    if (!userRole || !this.merchantRoles.includes(userRole)) {
      throw new AppError({
        message: 'Merchant role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS
      });
    }
  }

  /**
   * Require authenticated user
   */
  requireAuthenticated(userRole?: string): void {
    if (!userRole || !this.userRoles.includes(userRole)) {
      throw new AppError({
        message: 'Authentication required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS
      });
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy: Record<string, number> = {
      'USER': 1,
      'MERCHANT': 2,
      'ADMIN': 3,
      'SUPER_ADMIN': 4
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  }

  /**
   * Get user permissions for a resource
   */
  getUserPermissions(userRole: string, resource: string): string[] {
    const permissions: string[] = [];

    switch (resource) {
      case 'risk_assessment':
        if (this.merchantRoles.includes(userRole)) {
          permissions.push('assess_risk', 'view_assessment');
        }
        break;
      case 'fraud_config':
        if (this.merchantRoles.includes(userRole)) {
          permissions.push('view_config');
        }
        if (this.adminRoles.includes(userRole)) {
          permissions.push('update_config');
        }
        break;
      case 'flagged_transactions':
        if (this.merchantRoles.includes(userRole)) {
          permissions.push('view_flagged');
        }
        break;
      case 'fraud_statistics':
        if (this.merchantRoles.includes(userRole)) {
          permissions.push('view_statistics');
        }
        break;
    }

    return permissions;
  }

  /**
   * Validate authorization context
   */
  validateAuthorizationContext(context: AuthorizationContext): void {
    if (!context.user) {
      throw new AppError({
        message: 'User context is required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS
      });
    }

    if (!context.resource) {
      throw new AppError({
        message: 'Resource is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD
      });
    }

    if (!context.action) {
      throw new AppError({
        message: 'Action is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD
      });
    }
  }

  /**
   * Create authorization context from request
   */
  createAuthorizationContext(
    user: any,
    resource: string,
    action: string,
    resourceId?: string
  ): AuthorizationContext {
    return {
      user: {
        id: user?.id,
        role: user?.role,
        merchantId: user?.merchantId
      },
      resource,
      action,
      resourceId
    };
  }

  /**
   * Handle authorization error
   */
  handleAuthorizationError(result: PermissionResult): never {
    const message = result.reason || 'Access denied';
    
    throw new AppError({
      message,
      type: ErrorType.AUTHENTICATION,
      code: ErrorCode.INVALID_CREDENTIALS,
      details: {
        requiredRole: result.requiredRole,
        requiredPermissions: result.requiredPermissions
      }
    });
  }

  /**
   * Extract merchant context from request
   */
  extractMerchantContext(req: any): { merchantId?: number } {
    // If user is a merchant, use their merchant ID
    if (req.user?.role === 'MERCHANT' && req.user?.merchantId) {
      return { merchantId: parseInt(req.user.merchantId) };
    }

    // If admin, they can specify merchant ID in query/params
    if (this.adminRoles.includes(req.user?.role)) {
      const merchantId = req.query?.merchantId || req.params?.merchantId;
      return merchantId ? { merchantId: parseInt(merchantId) } : {};
    }

    return {};
  }
}
