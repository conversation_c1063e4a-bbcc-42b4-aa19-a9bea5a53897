// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import prisma from "../lib/prisma";
import { logger } from "../lib/logger";
import { getEnvironment } from "../config/environment";
// MonitoringUtils implementation
export const MonitoringUtils: any = {
  recordMetric: (metricName, value, tags = {}) => {
    // Implementation of metric recording
    console.log(`Recording metric: ${metricName} = ${value}`, tags);
  },

  startTimer: (metricName, tags = {}) => {
    const startTime = Date.now();
    return {
      stop: () => {
        const duration = Date.now() - startTime;
        console.log(`Timer ${metricName} stopped: ${duration}ms`, tags);
        return duration;
      }
    };
  }
};
import { Alert } from '../types';
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}



// Re-export the monitoring events from shared MonitoringUtils
export const monitoringEvents: any = MonitoringUtils.monitoringEvents;

// Interface for monitoring data
interface MonitoringData {
  timestamp: Date;
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  userId?: string;
  userRole?: string;
  ipAddress: string;
  userAgent: string;
  errorMessage?: string;
  environment?: string;
}

// Middleware to monitor API requests
export const monitorRequest: any = (req: Request, res: Response, next: NextFunction) => {
    const startTime: any = Date.now();

    // Store original end method
    const originalEnd: any = res.end;

    // Override end method to capture response data
    res.end = function(chunk?, encoding?, callback?) {
        const responseTime: any = Date.now() - startTime;
        const endpoint: any = req.originalUrl;
        const method: any = req.method;
        const statusCode: any = res.statusCode;
        const ipAddress: any = req.ip || req.socket.remoteAddress || "";
        const userAgent: any = req.headers["user-agent"] || "";
        const userId: any = req.user?.id; // Fixed: using id instead of userId
        const userRole: any = req.user?.role;
        const environment: any = getEnvironment();

        // Create monitoring data
        const monitoringData: MonitoringData = {
            timestamp: new Date(),
            endpoint,
            method,
            statusCode,
            responseTime,
            userId,
            userRole,
            ipAddress,
            userAgent,
            environment
        };

        // Add error message if status code is 4xx or 5xx
        if (statusCode >= 400) {
            monitoringData.errorMessage = res.statusMessage || "Unknown error";
        }

        // Log monitoring data
        logMonitoringData(monitoringData);

        // Call original end method
        return originalEnd.call(this, chunk, encoding, callback);
    };

    next();
};

// Log monitoring data to database
export const logMonitoringData: any = async (data: MonitoringData) => {
    try {
        // Use shared MonitoringUtils to log monitoring data
        await MonitoringUtils.logMonitoringData(data as any, logger, prisma);
    } catch (error) {
        logger.error("Error logging monitoring data:", error);
    }
};

// Get monitoring data
export const getMonitoringData: any = () => {
    return MonitoringUtils.getMonitoringData();
};

// Initialize monitoring
export const initializeMonitoring: any = () => {
    // Listen for monitoring events
    monitoringEvents.on("request", logMonitoringData);

    // Set up periodic health checks
    const healthCheckInterval: any = parseInt(process.env.HEALTH_CHECK_INTERVAL || "300000", 10); // 5 minutes by default
    const healthCheckTimer: any = setInterval(async () => {
        try {
            const healthReport = await checkSystemHealth();

            // Alert on critical issues
            if (healthReport.status === "unhealthy") {
                logger.error("System health check failed", healthReport);

                // In a real implementation, you would send alerts via email, SMS, etc.
                // For now, just log the error
            } else if (healthReport.status === "warning") {
                logger.warn("System health check warning", healthReport);

                // In a real implementation, you would send warnings via email, Slack, etc.
                // For now, just log the warning
            }
        } catch (error) {
            logger.error("Error during health check:", error);
        }
    }, healthCheckInterval);

    // Log startup
    // Get current environment
    const env: any = getEnvironment();

    logger.info("Monitoring system initialized", {
        environment: env,
        healthCheckInterval
    });

    // Return cleanup function
    return () => {
        monitoringEvents.removeAllListeners();
        clearInterval(healthCheckTimer);
        logger.info("Monitoring system stopped");
    };
};

// Schedule periodic database cleanup
export const scheduleMonitoringCleanup: any = (): NodeJS.Timeout => {
    // Clean up old monitoring data periodically
    const cleanupInterval: any = parseInt(process.env.MONITORING_CLEANUP_INTERVAL || "86400000", 10); // 24 hours by default

    const cleanupTimer: any = setInterval(async () => {
        try {
            // In a real implementation, you would clean up old monitoring data from the database
            // For example:
            // const thirtyDaysAgo = new Date();
            // thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            // await prisma.apiLog.deleteMany({
            //   where: {
            //     timestamp: {
            //       lt: thirtyDaysAgo
            //     }
            //   }
            // });

            logger.info("Monitoring data cleanup scheduled");
        } catch (error) {
            logger.error("Error during monitoring data cleanup:", error);
        }
    }, cleanupInterval);

    // Return the timer
    return cleanupTimer;
};

// Check system health
export const checkSystemHealth: any = async () => {
    return MonitoringUtils.checkSystemHealth(prisma, logger);
};




