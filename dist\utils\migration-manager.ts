// jscpd:ignore-file
/**
 * Migration Manager
 *
 * This utility provides a robust system for managing database migrations.
 * It ensures that migrations are applied in the correct order and only once.
 */

import fs from "fs";
import path from "path";
import { Client } from "pg";
import { logger } from "../lib/logger";
import { getDatabaseConfig } from "../config/database.config";
import { MigrationRecord, MigrationStatus } from '../types';
import { Migration, DatabaseConfig } from '../types/database';
import { logger } from "../lib/logger";
import { getDatabaseConfig } from "../config/database.config";
import { MigrationRecord, MigrationStatus } from '../types';
import { Migration, DatabaseConfig } from '../types/database';

// Migration manager class
export class MigrationManager {
    private client: Client;
    private migrationsDir: string;
    private migrationTableName: string = "_migrations";

    /**
     * Constructor
     * @param migrationsDir Path to migrations directory
     */
    constructor(migrationsDir: string) {
        this.migrationsDir = migrationsDir;
        const dbConfig: any = getDatabaseConfig();
        this.client = new Client(dbConfig);

        console.log(`Migration manager initialized for database: ${dbConfig.database}`);
        console.log(`Migrations directory: ${this.migrationsDir}`);
    }

    /**
   * Initialize migration manager
   */
    public async initialize(): Promise<void> {
        try {
            await this.client.connect();
            logger.info("Connected to database");

            // Create migrations table if it doesn't exist
            await this.createMigrationsTable();
        } catch (error) {
            logger.error("Failed to initialize migration manager:", error);
            throw error;
        }
    }

    /**
     * Create migrations table
     */
    private async createMigrationsTable(): Promise<void> {
        try {
            const query: any = `
                CREATE TABLE IF NOT EXISTS "${this.migrationTableName}" (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    applied_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    error TEXT
                )
            `;

            await this.client.query(query);
            console.log(`Migrations table ${this.migrationTableName} created or already exists`);
        } catch (error) {
            logger.error("Failed to create migrations table:", error);
            throw error;
        }
    }

    /**
   * Get applied migrations
   * @returns List of applied migrations
   */
    public async getAppliedMigrations(): Promise<MigrationRecord[]> {
        try {
            const query: any = `
        SELECT name, status, applied_at as "appliedAt", error
        FROM "${this.migrationTableName}"
        ORDER BY applied_at ASC;
      `;

            const result: any = await this.client.query(query);

            return result.rows.map(row => ({
                name: row.name,
                status: row.status as MigrationStatus,
                appliedAt: row.applied_at // Fixed: using applied_at instead of appliedAt,
                error: row.error
            }));
        } catch (error) {
            logger.error("Failed to get applied migrations:", error);
            throw error;
        }
    }

    /**
   * Get pending migrations
   * @returns List of pending migrations
   */
    public async getPendingMigrations(): Promise<string[]> {
        try {
            // Get applied migrations
            const appliedMigrations: any = await this.getAppliedMigrations();
            const appliedMigrationNames: any = appliedMigrations
                .filter(m => m.status === MigrationStatus.APPLIED)
                .map(m => m.name);

            // Get available migrations

            const availableMigrations: any = this.getAvailableMigrations();

            // Filter out applied migrations
            return availableMigrations.filter(name => !appliedMigrationNames.includes(name));
        } catch (error) {
            logger.error("Failed to get pending migrations:", error);
            throw error;
        }
    }

    /**
   * Get available migrations
   * @returns List of available migrations
   */
    private getAvailableMigrations(): string[] {
        try {
            // Check if migrations directory exists
            if (!fs.existsSync(this.migrationsDir)) {
                logger.warn(`Migrations directory does not exist: ${this.migrationsDir}`);
                return [];
            }

            // Get migration directories
            return fs.readdirSync(this.migrationsDir)
                .filter((dir)) => {
                    const dirPath: any = path.join(this.migrationsDir, dir);
                    const isMigrationDir: any = fs.statSync(dirPath).isDirectory();
                    const hasMigrationFile: any = fs.existsSync(path.join(dirPath, "migration.sql"));

                    return isMigrationDir && hasMigrationFile;
                })
                .sort(); // Sort to ensure migrations are applied in order
        } catch (error) {
            logger.error("Failed to get available migrations:", error);
            throw error;
        }
    }

    /**
   * Apply migrations
   * @returns List of applied migrations
   */
    public async applyMigrations(): Promise<MigrationRecord[]> {
        try {
            // Get pending migrations
            const pendingMigrations: any = await this.getPendingMigrations();

            if (pendingMigrations.length === 0) {
                logger.info("No pending migrations to apply");
                return [];
            }

            console.log(`Applying ${pendingMigrations.length} pending migrations...`);

            const appliedMigrations: MigrationRecord[] = [];

            // Apply migrations
            for (const migrationName of pendingMigrations) {
                try {
                    console.log(`Applying migration: ${migrationName}`);

                    const migrationPath: any = path.join(this.migrationsDir, migrationName, "migration.sql");

                    // Check if migration file exists
                    if (!fs.existsSync(migrationPath)) {
                        logger.warn(`Migration file not found: ${migrationPath}`);
                        continue;
                    }

                    // Read migration SQL
                    const migrationSql: any = fs.readFileSync(migrationPath, "utf8");

                    // Start transaction
                    await this.client.query("BEGIN");

                    try {
                        // Execute migration SQL
                        await this.client.query(migrationSql);

                        // Record migration as applied
                        await this.client.query(
                            `INSERT INTO "${this.migrationTableName}" (name, status) VALUES ($1, $2)`,
                            [migrationName, MigrationStatus.APPLIED]
                        );

                        // Commit transaction
                        await this.client.query("COMMIT");

                        console.log(`Migration ${migrationName} applied successfully`);

                        appliedMigrations.push({
                            name: migrationName,
                            status: MigrationStatus.APPLIED,
                            appliedAt: new Date()
                        });
                    } catch (error) {
                        // Rollback transaction on error
                        await this.client.query("ROLLBACK");

                        // Record migration as failed
                        await this.client.query(
                            `INSERT INTO "${this.migrationTableName}" (name, status, error) VALUES ($1, $2, $3)`,
                            [migrationName, MigrationStatus.FAILED, (error as Error).message]
                        );

                        logger.error(`Failed to apply migration ${migrationName}:`, error);

                        appliedMigrations.push({
                            name: migrationName,
                            status: MigrationStatus.FAILED,
                            appliedAt: new Date(),
                            error: (error as Error).message
                        });
                    }
                } catch (error) {
                    logger.error(`Error processing migration ${migrationName}:`, error);
                }
            }

            return appliedMigrations;
        } catch (error) {
            logger.error("Failed to apply migrations:", error);
            throw error;
        }
    }

    /**
   * Close migration manager
   */
    public async close(): Promise<void> {
        try {
            await this.client.end();
            logger.info("Migration manager closed");
        } catch (error) {
            logger.error("Failed to close migration manager:", error);
        }
    }
}

// Export default migration manager
export default MigrationManager;
