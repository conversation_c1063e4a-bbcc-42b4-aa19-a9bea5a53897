/**
 * Validate request using express-validator
 * @param validations Validation chains
 */
export declare const validate: any;
/**
 * Middleware to validate date parameters
 * @param params Parameters to validate
 */
export declare const validateDateParams: any;
/**
 * Middleware to validate date range parameters
 * @param startParam Start date parameter name
 * @param endParam End date parameter name
 */
export declare const validateDateRange: any;
/**
 * Middleware to validate numeric parameters
 * @param params Parameters to validate with options
 */
export declare const validateNumericParams: any;
/**
 * Middleware to validate enum parameters
 * @param params Parameters to validate with options
 */
export declare const validateEnumParams: any;
//# sourceMappingURL=validation.middleware.d.ts.map