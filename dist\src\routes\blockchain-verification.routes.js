"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const blockchain_verification_controller_1 = __importDefault(require("../controllers/blockchain-verification.controller"));
const validation_middleware_1 = require("../middlewares/validation.middleware");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const client_1 = require("@prisma/client");
const router = (0, express_1.Router)();
const prisma = new client_1.PrismaClient();
/**
 * @route POST /api/verification/blockchain
 * @desc Verify a blockchain transaction
 * @access Public
 */
router.post("/blockchain", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("network").notEmpty().withMessage("Network is required"),
    (0, express_validator_1.body)("txHash").notEmpty().withMessage("Transaction hash is required"),
    (0, express_validator_1.body)("amount").isNumeric().withMessage("Amount must be a number")
]), blockchain_verification_controller_1.default.verifyBlockchainTransaction);
/**
 * @route POST /api/verification/binance/trc20
 * @desc Verify a Binance TRC20 transaction
 * @access Public
 */
router.post("/binance/trc20", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("txHash").notEmpty().withMessage("Transaction hash is required"),
    (0, express_validator_1.body)("amount").isNumeric().withMessage("Amount must be a number"),
    (0, express_validator_1.body)("currency").notEmpty().withMessage("Currency is required")
]), blockchain_verification_controller_1.default.verifyBinanceTransaction);
/**
 * @route GET /api/verification/payment/:id/status
 * @desc Get verification status for a payment
 * @access Private
 */
router.get("/payment/:id/status", auth_middleware_1.authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        // Find the transaction
        const transaction = await prisma.transaction.findFirst({
            where: { paymentId: id
            },
            include: { verificationRecords: {
                    orderBy: { timestamp: "desc"
                    },
                    take: 1
                }
            }
        });
        if (!transaction) {
            return res.status(404).json({
                success: false,
                message: "Transaction not found"
            });
        }
        // Map status
        let status;
        switch (transaction.status) {
            case "COMPLETED":
                status = "VERIFIED";
                break;
            case "FAILED":
                status = "FAILED";
                break;
            case "EXPIRED":
                status = "EXPIRED";
                break;
            default:
                status = "PENDING";
        }
        return res.status(200).json({
            success: transaction.status === "COMPLETED",
            status,
            paymentId: id,
            merchantId: transaction.merchantId,
            verificationMethod: transaction.verificationMethod,
            verifiedAt: transaction.completedAt?.toISOString(),
            message: transaction.verificationRecords[0]?.data?.message,
            transactionDetails: transaction.verificationData
        });
    }
    catch (error) {
        console.error("Error getting verification status:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error instanceof Error ? error.message : "Unknown error"
        });
    }
});
exports.default = router;
//# sourceMappingURL=blockchain-verification.routes.js.map