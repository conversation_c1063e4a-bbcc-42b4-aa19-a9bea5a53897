/**
 * Payment Router
 *
 * Implements a smart routing system for optimal payment method selection.
 */
import { IPaymentMethod } from "../../../interfaces/payment/IPaymentMethod";
import { PaymentMethodType } from "../../../types/payment-method.types";
/**
 * Payment routing context
 */
export interface PaymentRoutingContext {
    merchantId: string;
    amount: number;
    currency: string;
    country?: string;
    paymentMethods: IPaymentMethod[];
    metadata?: Record<string, any>;
}
/**
 * Payment routing result
 */
export interface PaymentRoutingResult {
    recommendedMethod?: IPaymentMethod;
    alternativeMethods: IPaymentMethod[];
    scores: Record<PaymentMethodType, number>;
    reason?: string;
}
/**
 * Payment routing rule interface
 */
export interface IPaymentRoutingRule {
    /**
     * Get the rule name
     */
    getName(): string;
    /**
     * Get the rule weight (0-1)
     */
    getWeight(): number;
    /**
     * Apply the rule to score payment methods
     */
    apply(context: PaymentRoutingContext): Record<PaymentMethodType, number>;
}
/**
 * Payment router
 */
export declare class PaymentRouter {
    private rules;
    /**
   * Add a routing rule
   *
   * @param rule Payment routing rule
   * @returns This router for chaining
   */
    addRule(rule: IPaymentRoutingRule): PaymentRouter;
    /**
   * Add multiple routing rules
   *
   * @param rules Array of payment routing rules
   * @returns This router for chaining
   */
    addRules(rules: IPaymentRoutingRule[]): PaymentRouter;
    /**
   * Find the optimal payment method
   *
   * @param context Payment routing context
   * @returns Payment routing result
   */
    findOptimalMethod(context: PaymentRoutingContext): Promise<PaymentRoutingResult>;
    const sortedMethods: any;
    const recommendedMethod: any;
    const alternativeMethods: any;
}
//# sourceMappingURL=PaymentRouter.d.ts.map