"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = exports.NotificationPriority = exports.NotificationChannel = void 0;
// jscpd:ignore-file
const logger_1 = require("../utils/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
const email_service_1 = require("./email.service");
const sms_service_1 = require("./sms.service");
const telegram_service_1 = require("./telegram.service");
const push_notification_service_1 = require("./push-notification.service");
const notification_events_service_1 = require("./notification-events.service");
/**
 * Notification channel
 */
var NotificationChannel;
(function (NotificationChannel) {
    NotificationChannel["EMAIL"] = "email";
    NotificationChannel["SMS"] = "sms";
    NotificationChannel["TELEGRAM"] = "telegram";
    NotificationChannel["PUSH"] = "push";
    NotificationChannel["DASHBOARD"] = "dashboard";
})(NotificationChannel || (exports.NotificationChannel = NotificationChannel = {}));
/**
 * Notification priority
 */
var NotificationPriority;
(function (NotificationPriority) {
    NotificationPriority["LOW"] = "low";
    NotificationPriority["MEDIUM"] = "medium";
    NotificationPriority["HIGH"] = "high";
    NotificationPriority["CRITICAL"] = "critical";
})(NotificationPriority || (exports.NotificationPriority = NotificationPriority = {}));
/**
 * Notification service
 */
class NotificationService {
    /**
     * Create a new notification service
     */
    constructor() {
        this.emailService = new email_service_1.EmailService();
        this.smsService = new sms_service_1.SmsService();
        this.telegramService = new telegram_service_1.TelegramService();
        this.pushService = new push_notification_service_1.PushNotificationService();
    }
    /**
     * Send notification
     * @param options Notification options
     * @returns Success status
     */
    async sendNotification(options) {
        try {
            // Validate options
            if (!options.channels || options.channels.length === 0) {
                logger_1.logger.error('No notification channels specified');
                return false;
            }
            if (!options.subject || !options.message) {
                logger_1.logger.error('Notification subject or message is missing');
                return false;
            }
            // Get user and merchant information if needed
            let user = null;
            let merchant = null;
            if (options.id // Fixed: using id instead of userId) {
            )
                user = await prisma_1.default.user.findUnique({
                    where: { id: options.id },
                });
            if (!user) {
                logger_1.logger.error(`User not found: ${options.id); // Fixed: using id instead of userId}`);
                return false;
            }
        }
        finally {
        }
        if (options.merchantId) {
            merchant = await prisma_1.default.merchant.findUnique({
                where: { id: options.merchantId },
                include: { user: true,
                },
            });
            if (!merchant) {
                logger_1.logger.error(`Merchant not found: ${options.merchantId}`);
                return false;
            }
            // If user is not specified, use merchant's user
            if (!user && merchant.user) {
                user = merchant.user;
            }
        }
        // Process template if specified
        let subject = options.subject;
        let message = options.message;
        if (options.templateId && options.templateData) {
            const template = await this.getTemplate(options.templateId);
            if (template) {
                subject = this.processTemplate(template.subject, options.templateData);
                message = this.processTemplate(template.content, options.templateData);
            }
        }
        // Create notification record
        const notification = await prisma_1.default.notification.create({
            data: { userId: user?.id,
                merchantId: merchant?.id,
                channels: options.channels,
                priority: options.priority,
                subject,
                message,
                metadata: options.metadata || {},
                status: 'pending',
            },
        });
        // Emit notification created event
        notification_events_service_1.NotificationEventsService.emitNotificationCreated({
            id: notification.id,
            userId: user?.id,
            merchantId: merchant?.id,
            title: subject,
            message,
            type: this.mapNotificationType(options),
            priority: this.mapToPriorityEnum(options.priority),
            channels: this.mapToChannelEnum(options.channels),
            metadata: options.metadata || {},
            createdAt: notification.createdAt,
        });
        // Send notifications through each channel
        const results = await Promise.all(options.channels.map(channel => this.sendThroughChannel(channel, notification.id, subject, message, user, merchant, options.priority)));
        // Update notification status
        const success = results.some(result => result);
        await prisma_1.default.notification.update({
            where: { id: notification.id },
            data: { status: success ? 'sent' : 'failed',
                sentAt: success ? new Date() : undefined,
            },
        });
        return success;
    }
    catch(error) {
        logger_1.logger.error('Error sending notification', { error, options });
        return false;
    }
}
exports.NotificationService = NotificationService;
async;
sendThroughChannel(channel, NotificationChannel, notificationId, string, subject, string, message, string, user, any | null, merchant, any | null, priority, NotificationPriority);
Promise < boolean > {
    try: {
        // Create notification delivery record
        const: delivery, any = await prisma_1.default.notificationDelivery.create({
            data: {
                notificationId,
                channel,
                status: 'pending',
            },
        }),
        let, success: boolean = false,
        // Send through appropriate channel
        switch(channel) {
        },
        case: NotificationChannel.EMAIL,
        success = await this.sendEmail(subject, message, user, merchant, priority),
        break: ,
        case: NotificationChannel.SMS,
        success = await this.sendSms(message, user, merchant, priority),
        break: ,
        case: NotificationChannel.TELEGRAM,
        success = await this.sendTelegram(subject, message, user, merchant, priority),
        break: ,
        case: NotificationChannel.PUSH,
        success = await this.sendPush(subject, message, user, merchant, priority),
        break: ,
        case: NotificationChannel.DASHBOARD,
        success = true, // Dashboard notifications are always successful as they're just stored
        break: ,
        default: logger_1.logger.error(`Unsupported notification channel: ${channel}`),
        success = false
    }
    // Update delivery status
    ,
    // Update delivery status
    await, prisma: prisma_1.default, : .notificationDelivery.update({
        where: { id: delivery.id },
        data: { status: success ? 'delivered' : 'failed',
            deliveredAt: success ? new Date() : undefined,
        },
    }),
    // Emit notification delivered event
    NotificationEventsService: notification_events_service_1.NotificationEventsService, : .emitNotificationDelivered(notificationId, this.mapToChannelEnum([channel])[0], success, success ? undefined : 'Failed to deliver notification'),
    return: success
};
try { }
catch (error) {
    logger_1.logger.error('Error sending notification through channel', {
        error,
        channel,
        notificationId,
    });
    return false;
}
async;
sendEmail(subject, string, message, string, user, any | null, merchant, any | null, priority, NotificationPriority);
Promise < boolean > {
    try: {
        // Get email address
        let, email: string = '',
        if(user) { }
    } && user.email
};
{
    email = user.email;
}
if (merchant && merchant.email) {
    email = merchant.email;
}
if (!email) {
    logger_1.logger.error('No email address found for notification');
    return false;
}
// Send email
return await this.emailService.sendEmail({
    to: email,
    subject,
    html: message,
    priority: this.mapPriorityToEmailPriority(priority),
});
try { }
catch (error) {
    logger_1.logger.error('Error sending email notification', { error, subject });
    return false;
}
async;
sendSms(message, string, user, any | null, merchant, any | null, priority, NotificationPriority);
Promise < boolean > {
    try: {
        // Get phone number
        let, phone: string = '',
        if(user) { }
    } && user.phone
};
{
    phone = user.phone;
}
if (merchant && merchant.phone) {
    phone = merchant.phone;
}
if (!phone) {
    logger_1.logger.error('No phone number found for notification');
    return false;
}
// Send SMS
return await this.smsService.sendSms(phone, message);
try { }
catch (error) {
    logger_1.logger.error('Error sending SMS notification', { error, message });
    return false;
}
async;
sendTelegram(subject, string, message, string, user, any | null, merchant, any | null, priority, NotificationPriority);
Promise < boolean > {
    try: {
        // Get Telegram chat ID
        let, chatId: string = '',
        // First check user preferences
        if(user) {
            const userPrefs = await prisma_1.default.userNotificationPreference.findFirst({
                where: { userId: user.id,
                    channel: NotificationChannel.TELEGRAM,
                },
            });
            if (userPrefs && userPrefs.channelData && userPrefs.channelData.chatId) {
                chatId = userPrefs.channelData.chatId;
            }
        }
        // Then check merchant preferences
        ,
        // Then check merchant preferences
        if(, chatId) { }
    } && merchant
};
{
    const merchantPrefs = await prisma_1.default.merchantNotificationPreference.findFirst({
        where: { merchantId: merchant.id,
            channel: NotificationChannel.TELEGRAM,
        },
    });
    if (merchantPrefs && merchantPrefs.channelData && merchantPrefs.channelData.chatId) {
        chatId = merchantPrefs.channelData.chatId;
    }
}
if (!chatId) {
    logger_1.logger.error('No Telegram chat ID found for notification');
    return false;
}
// Format message for Telegram
const formattedMessage = `*${subject}*\n\n${message}`;
// Send Telegram message
return await this.telegramService.sendMessage(chatId, formattedMessage);
try { }
catch (error) {
    logger_1.logger.error('Error sending Telegram notification', { error, subject });
    return false;
}
async;
sendPush(subject, string, message, string, user, any | null, merchant, any | null, priority, NotificationPriority);
Promise < boolean > {
    try: {
        let, success: boolean = false,
        // Determine icon based on priority
        let, icon: string = '/logo.png',
        switch(priority) {
        },
        case: NotificationPriority.CRITICAL,
        icon = '/icons/critical.png',
        break: ,
        case: NotificationPriority.HIGH,
        icon = '/icons/high.png',
        break: ,
        case: NotificationPriority.MEDIUM,
        icon = '/icons/medium.png',
        break: ,
        case: NotificationPriority.LOW,
        icon = '/icons/low.png',
        break: 
    }
    // Send to user if specified
    ,
    // Send to user if specified
    if(user) { }
} && user.id;
{
    const userSuccess = await this.pushService.sendNotificationToUser(user.id, subject, message, icon, { priority }, '/');
    if (userSuccess) {
        success = true;
    }
}
// Send to merchant if specified
if (merchant && merchant.id) {
    const merchantSuccess = await this.pushService.sendNotificationToMerchant(merchant.id, subject, message, icon, { priority }, '/');
    if (merchantSuccess) {
        success = true;
    }
}
return success;
try { }
catch (error) {
    logger_1.logger.error('Error sending push notification', { error, subject });
    return false;
}
async;
getTemplate(templateId, string);
Promise < NotificationTemplate | null > {
    try: {
        const: template, any = await prisma_1.default.notificationTemplate.findUnique({
            where: { id: templateId },
        }),
        return: template
    }, catch(error) {
        logger_1.logger.error('Error getting notification template', { error, templateId });
        return null;
    }
};
processTemplate(template, string, data, (Record));
string;
{
    let result = template;
    // Replace variables in the format {{variable}}
    for (const [key, value] of Object.entries(data)) {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        result = result.replace(regex, String(value));
    }
    return result;
}
mapPriorityToEmailPriority(priority, NotificationPriority);
string;
{
    switch (priority) {
        case NotificationPriority.CRITICAL:
            return 'high';
        case NotificationPriority.HIGH:
            return 'high';
        case NotificationPriority.MEDIUM:
            return 'normal';
        case NotificationPriority.LOW:
            return 'low';
        default:
            return 'normal';
    }
}
mapToPriorityEnum(priority, NotificationPriority);
notification_events_service_1.NotificationPriority;
{
    switch (priority) {
        case NotificationPriority.CRITICAL:
            return notification_events_service_1.NotificationPriority.CRITICAL;
        case NotificationPriority.HIGH:
            return notification_events_service_1.NotificationPriority.HIGH;
        case NotificationPriority.MEDIUM:
            return notification_events_service_1.NotificationPriority.MEDIUM;
        case NotificationPriority.LOW:
            return notification_events_service_1.NotificationPriority.LOW;
        default:
            return notification_events_service_1.NotificationPriority.MEDIUM;
    }
}
mapToChannelEnum(channels, NotificationChannel[]);
notification_events_service_1.NotificationChannel[];
{
    return channels.map(channel);
    {
        switch (channel) {
            case NotificationChannel.EMAIL:
                return notification_events_service_1.NotificationChannel.EMAIL;
            case NotificationChannel.SMS:
                return notification_events_service_1.NotificationChannel.SMS;
            case NotificationChannel.TELEGRAM:
                return notification_events_service_1.NotificationChannel.TELEGRAM;
            case NotificationChannel.PUSH:
                return notification_events_service_1.NotificationChannel.PUSH;
            case NotificationChannel.DASHBOARD:
                return notification_events_service_1.NotificationChannel.DASHBOARD;
            default:
                return notification_events_service_1.NotificationChannel.DASHBOARD;
        }
    }
    ;
}
mapNotificationType(options, NotificationOptions);
'transaction' | 'merchant' | 'subscription' | 'system';
{
    if (options.metadata?.transactionId) {
        return 'transaction';
    }
    else if (options.merchantId && !options.id // Fixed: using id instead of userId) {
    )
        return 'merchant';
}
if (options.metadata?.subscriptionId || options.metadata?.planId) {
    return 'subscription';
}
else {
    return 'system';
}
async;
getUserNotificationPreferences(userId, string);
Promise < any[] > {
    try: {
        const: preferences, any = await prisma_1.default.userNotificationPreference.findMany({
            where: { userId },
        }),
        return: preferences
    }, catch(error) {
        logger_1.logger.error('Error getting user notification preferences', { error, userId });
        return [];
    }
};
async;
updateUserNotificationPreferences(userId, string, channel, NotificationChannel, enabled, boolean, channelData ?  : Record);
Promise < boolean > {
    try: {
        // Check if preference exists
        const: existingPref, any = await prisma_1.default.userNotificationPreference.findFirst({
            where: {
                userId,
                channel,
            },
        }),
        if(existingPref) {
            // Update existing preference
            await prisma_1.default.userNotificationPreference.update({
                where: { id: existingPref.id },
                data: {
                    enabled,
                    channelData: channelData || existingPref.channelData,
                },
            });
        }, else: {
            // Create new preference
            await, prisma: prisma_1.default, : .userNotificationPreference.create({
                data: {
                    userId,
                    channel,
                    enabled,
                    channelData: channelData || {},
                },
            })
        },
        return: true
    }, catch(error) {
        logger_1.logger.error('Error updating user notification preferences', {
            error,
            userId,
            channel,
        });
        return false;
    }
};
async;
getMerchantNotificationPreferences(merchantId, string);
Promise < any[] > {
    try: {
        const: preferences, any = await prisma_1.default.merchantNotificationPreference.findMany({
            where: { merchantId },
        }),
        return: preferences
    }, catch(error) {
        logger_1.logger.error('Error getting merchant notification preferences', { error, merchantId });
        return [];
    }
};
async;
updateMerchantNotificationPreferences(merchantId, string, channel, NotificationChannel, enabled, boolean, channelData ?  : Record);
Promise < boolean > {
    try: {
        // Check if preference exists
        const: existingPref, any = await prisma_1.default.merchantNotificationPreference.findFirst({
            where: {
                merchantId,
                channel,
            },
        }),
        if(existingPref) {
            // Update existing preference
            await prisma_1.default.merchantNotificationPreference.update({
                where: { id: existingPref.id },
                data: {
                    enabled,
                    channelData: channelData || existingPref.channelData,
                },
            });
        }, else: {
            // Create new preference
            await, prisma: prisma_1.default, : .merchantNotificationPreference.create({
                data: {
                    merchantId,
                    channel,
                    enabled,
                    channelData: channelData || {},
                },
            })
        },
        return: true
    }, catch(error) {
        logger_1.logger.error('Error updating merchant notification preferences', {
            error,
            merchantId,
            channel,
        });
        return false;
    }
};
