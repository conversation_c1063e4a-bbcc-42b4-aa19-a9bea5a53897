"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toBePositive = toBePositive;
function toBePositive(actual) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, matcherHint } = this.utils;
    const pass = isNumber(actual) && isPositive(actual);
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toBePositive', 'received', '') +
                '\n\n' +
                'Expected value to not be positive received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toBePositive', 'received', '') +
                '\n\n' +
                'Expected value to be positive received:\n' +
                `  ${printReceived(actual)}`,
    };
}
const isNumber = (value) => !isNaN(parseInt(value));
const isPositive = (value) => value > 0;
