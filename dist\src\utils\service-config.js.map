{"version": 3, "file": "service-config.js", "sourceRoot": "", "sources": ["../../../src/utils/service-config.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;GAKG;;;AAEH,0CAAuC;AACvC,uDAAuD;AAEvD;;;;;;GAMG;AACI,MAAM,gBAAgB,GAAO,CAChC,WAAmB,EACnB,SAAiB,EACjB,YAAgB,EACf,EAAE;IACH,MAAM,GAAG,GAAG,IAAA,4BAAc,GAAE,CAAC;IAE7B,gDAAgD;IAChD,MAAM,cAAc,GAAO,GAAG,GAAG,CAAC,WAAW,EAAE,IAAI,WAAW,CAAC,WAAW,EAAE,IAAI,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;IAC1G,MAAM,gBAAgB,GAAO,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAEzD,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;QACjC,OAAO,gBAAgC,CAAC;IAC5C,CAAC;IAED,4CAA4C;IAC5C,MAAM,kBAAkB,GAAO,GAAG,WAAW,CAAC,WAAW,EAAE,IAAI,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;IACzF,MAAM,oBAAoB,GAAO,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAEjE,IAAI,oBAAoB,KAAK,SAAS,EAAE,CAAC;QACrC,OAAO,oBAAoC,CAAC;IAChD,CAAC;IAED,uBAAuB;IACvB,OAAO,YAAiB,CAAC;AAC7B,CAAC,CAAC;AAzBW,QAAA,gBAAgB,oBAyB3B;AAEF;;;;;GAKG;AACI,MAAM,SAAS,GAAO,CAAC,WAAmB,EAAE,eAAuB,EAAE,EAAU,EAAE;IACpF,OAAO,IAAA,wBAAgB,EAAS,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;AAC1E,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEF;;;;;GAKG;AACI,MAAM,YAAY,GAAO,CAAC,WAAmB,EAAE,eAAuB,EAAE,EAAU,EAAE;IACvF,OAAO,IAAA,wBAAgB,EAAS,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;AAC7E,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAEF;;;;;GAKG;AACI,MAAM,SAAS,GAAO,CAAC,WAAmB,EAAE,eAAuB,EAAE,EAAU,EAAE;IACpF,OAAO,IAAA,wBAAgB,EAAS,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;AAC1E,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEF;;;;;GAKG;AACI,MAAM,aAAa,GAAO,CAAC,WAAmB,EAAE,eAAuB,EAAE,EAAU,EAAE;IACxF,OAAO,IAAA,wBAAgB,EAAS,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;AAC9E,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB;AAEF;;;;;GAKG;AACI,MAAM,cAAc,GAAO,CAAC,WAAmB,EAAE,eAAuB,EAAE,EAAU,EAAE;IACzF,OAAO,IAAA,wBAAgB,EAAS,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;AAC/E,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAEF;;;;GAIG;AACI,MAAM,qBAAqB,GAAO,CAAC,WAAmB,EAA0B,EAAE;IACrF,MAAM,GAAG,GAAO,IAAA,4BAAc,GAAE,CAAC;IACjC,MAAM,WAAW,GAA2B,EAAE,CAAC;IAE/C,gCAAgC;IAChC,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAChC,uDAAuD;QACnD,MAAM,SAAS,GAAO,GAAG,GAAG,CAAC,WAAW,EAAE,IAAI,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC;QAC3E,MAAM,aAAa,GAAO,GAAG,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC;QAE1D,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,iDAAiD;YACjD,MAAM,aAAa,GAAO,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YACxE,WAAW,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACxD,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YAC1G,uFAAuF;YACvF,MAAM,aAAa,GAAO,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5E,WAAW,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACxD,CAAC;IACL,CAAC;IAED,OAAO,WAAW,CAAC;AACvB,CAAC,CAAC;AAtBW,QAAA,qBAAqB,yBAsBhC;AAEF;;;GAGG;AACI,MAAM,gBAAgB,GAAO,CAAC,WAAmB,EAAQ,EAAE;IAC9D,MAAM,GAAG,GAAG,IAAA,4BAAc,GAAE,CAAC;IAC7B,MAAM,WAAW,GAAO,IAAA,6BAAqB,EAAC,WAAW,CAAC,CAAC;IAE3D,wBAAwB;IACxB,MAAM,iBAAiB,GAA2B,EAAE,CAAC;IACrD,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;QAC5B,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrG,iBAAiB,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;QACvE,CAAC;aAAM,CAAC;YACJ,iBAAiB,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC;QAC3D,CAAC;IACL,CAAC;IAED,eAAM,CAAC,KAAK,CAAC,6BAA6B,WAAW,OAAO,GAAG,eAAe,EAAE,iBAAiB,CAAC,CAAC;AACvG,CAAC,CAAC;AAfW,QAAA,gBAAgB,oBAe3B;AAEF,kBAAe;IACX,gBAAgB,EAAhB,wBAAgB;IAChB,SAAS,EAAT,iBAAS;IACT,YAAY,EAAZ,oBAAY;IACZ,SAAS,EAAT,iBAAS;IACT,aAAa,EAAb,qBAAa;IACb,cAAc,EAAd,sBAAc;IACd,qBAAqB,EAArB,6BAAqB;IACrB,gBAAgB,EAAhB,wBAAgB;CACnB,CAAC"}