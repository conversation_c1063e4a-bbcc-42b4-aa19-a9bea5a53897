"use strict";
// jscpd:ignore-file
/**
 * Binance Gateway
 *
 * Implements the payment gateway for Binance.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BinanceGateway = void 0;
const logger_1 = require("../../../lib/logger");
const uuid_1 = require("uuid");
/**
 * Binance gateway
 */
class BinanceGateway {
    constructor() {
        this.enabled = true;
        this.configuration = {
            apiUrl: process.env.BINANCE_API_URL || 'https://api.binance.com',
            apiKey: process.env.BINANCE_API_KEY || '',
            apiSecret: process.env.BINANCE_API_SECRET || '',
            supportedCurrencies: ['USDT', 'USDC', 'BTC', 'ETH', 'BNB'],
        };
    }
    /**
     * Get the gateway name
     */
    getName() {
        return 'binance';
    }
    /**
     * Get the supported payment method types
     */
    getSupportedPaymentMethods() {
        return ['binance_trc20', 'binance_c2c', 'binance_pay', 'crypto_transfer'];
    }
    /**
     * Process a payment through the gateway
     */
    async processPayment(request) {
        try {
            logger_1.logger.info('Processing payment through Binance gateway', {
                paymentMethodType: request.paymentMethodType,
                amount: request.amount,
                currency: request.currency,
            });
            // Validate currency
            if (!this.getSupportedCurrencies().includes(request.currency)) {
                return {
                    success: false,
                    gatewayTransactionId: (0, uuid_1.v4)(),
                    message: `Currency not supported: ${request.currency}`,
                    timestamp: new Date(),
                };
            }
            // In a real implementation, this would call the Binance API
            // For now, we'll simulate a successful payment
            // Simulate different behavior based on payment method type
            let redirectUrl;
            let details = {};
            switch (request.paymentMethodType) {
                case 'binance_pay':
                    redirectUrl = `https://pay.binance.com/checkout?id=${(0, uuid_1.v4)()}`;
                    details = {
                        paymentLink: redirectUrl,
                        expiresIn: 3600, // 1 hour
                    };
                    break;
                case 'binance_c2c':
                    details = {
                        orderId: `C2C-${Date.now()}`,
                        merchantName: 'AmazingPay Merchant',
                        instructions: 'Complete the C2C trade on Binance',
                    };
                    break;
                case 'binance_trc20':
                    details = {
                        walletAddress: '123456789',
                        network: 'TRC20',
                        confirmations: 1,
                    };
                    break;
                case 'crypto_transfer':
                    details = {
                        walletAddress: '123456789',
                        network: request.paymentData.network || 'TRC20',
                        memo: request.paymentData.memo,
                    };
                    break;
                default:
                    return {
                        success: false,
                        gatewayTransactionId: (0, uuid_1.v4)(),
                        message: `Unsupported payment method type: ${request.paymentMethodType}`,
                        timestamp: new Date(),
                    };
            }
            return {
                success: true,
                gatewayTransactionId: `BINANCE-${Date.now()}`,
                message: 'Payment initiated successfully',
                details,
                timestamp: new Date(),
                redirectUrl,
            };
        }
        catch (error) {
            logger_1.logger.error(`Binance gateway payment error: ${error.message}`, {
                paymentMethodType: request.paymentMethodType,
                error,
            });
            return {
                success: false,
                gatewayTransactionId: (0, uuid_1.v4)(),
                message: `Gateway, error: ${error.message}`,
                timestamp: new Date(),
            };
        }
    }
    /**
     * Process a refund through the gateway
     */
    async processRefund(request) {
        try {
            logger_1.logger.info('Processing refund through Binance gateway', {
                transactionId: request.transactionId,
                amount: request.amount,
                currency: request.currency,
            });
            // In a real implementation, this would call the Binance API
            // For now, we'll simulate a successful refund
            return {
                success: true,
                refundId: `REFUND-${Date.now()}`,
                message: 'Refund processed successfully',
                details: {
                    originalTransactionId: request.transactionId,
                    refundAmount: request.amount,
                    refundCurrency: request.currency,
                    reason: request.reason,
                },
                timestamp: new Date(),
            };
        }
        catch (error) {
            logger_1.logger.error(`Binance gateway refund error: ${error.message}`, {
                transactionId: request.transactionId,
                error,
            });
            return {
                success: false,
                refundId: (0, uuid_1.v4)(),
                message: `Refund, error: ${error.message}`,
                timestamp: new Date(),
            };
        }
    }
    /**
     * Check the status of a transaction
     */
    async checkTransactionStatus(transactionId) {
        try {
            logger_1.logger.info('Checking transaction status on Binance gateway', {
                transactionId,
            });
            // In a real implementation, this would call the Binance API
            // For now, we'll simulate a random status
            const statuses = ['pending', 'completed', 'failed', 'refunded', 'partially_refunded'];
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            return {
                status: randomStatus,
                details: {
                    lastChecked: new Date(),
                    confirmations: randomStatus === 'completed' ? 6 : Math.floor(Math.random() * 6),
                },
            };
        }
        catch (error) {
            logger_1.logger.error(`Binance gateway status check error: ${error.message}`, {
                transactionId,
                error,
            });
            throw error;
        }
    }
    /**
     * Get the gateway configuration
     */
    getConfiguration() {
        // Return a copy without sensitive data
        const config = { ...this.configuration };
        delete config.apiSecret;
        return config;
    }
    /**
     * Set the gateway configuration
     */
    setConfiguration(config) {
        this.configuration = {
            ...this.configuration,
            ...config,
        };
    }
    /**
     * Check if the gateway is enabled
     */
    isEnabled() {
        return this.enabled;
    }
    /**
     * Get the supported currencies
     */
    getSupportedCurrencies() {
        return this.configuration.supportedCurrencies || [];
    }
    /**
     * Validate gateway-specific payment data
     */
    validatePaymentData(paymentMethodType, data) {
        const errors = [];
        switch (paymentMethodType) {
            case 'binance_trc20':
                // Validate wallet address if provided
                if (data.walletAddress && !this.isValidTRC20Address(data.walletAddress)) {
                    errors.push('Invalid TRC20 wallet address format');
                }
                break;
            case 'binance_c2c':
                // No specific validation for C2C
                break;
            case 'binance_pay':
                // No specific validation for Binance Pay
                break;
            case 'crypto_transfer':
                // Validate network
                if (!data.network) {
                    errors.push('Network is required for crypto transfer');
                }
                break;
            default:
                errors.push(`Unsupported payment method type: ${paymentMethodType}`);
                break;
        }
        return {
            valid: errors.length === 0,
            errors: errors.length > 0 ? errors : undefined,
        };
    }
    /**
     * Check if a TRC20 address is valid
     * @param address The address to validate
     * @returns True if the address is valid
     */
    isValidTRC20Address(address) {
        // In a real implementation, this would validate the TRC20 address format
        // For now, we'll just check if it's a non-empty string
        return typeof address === 'string' && address.trim().length > 0;
    }
}
exports.BinanceGateway = BinanceGateway;
//# sourceMappingURL=BinanceGateway.js.map