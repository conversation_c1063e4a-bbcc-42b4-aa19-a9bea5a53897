"use strict";
// jscpd:ignore-file
/**
 * Alerting Routes
 *
 * These routes provide endpoints for managing alerts.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const alerting_1 = __importStar(require("../utils/alerting"));
const logger_1 = require("../lib/logger");
const router = express_1.default.Router();
/**
 * @route GET /api/alerting/recent
 * @desc Get recent alerts
 * @access Private (Admin)
 */
router.get("/recent", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        const recentAlerts = alerting_1.default.getRecentAlerts();
        res.status(200).json({
            status: "success",
            data: { alerts: recentAlerts
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting recent alerts:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get recent alerts"
        });
    }
});
/**
 * @route POST /api/alerting/clear
 * @desc Clear recent alerts
 * @access Private (Admin)
 */
router.post("/clear", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        alerting_1.default.clearRecentAlerts();
        res.status(200).json({
            status: "success",
            message: "Recent alerts cleared successfully"
        });
    }
    catch (error) {
        logger_1.logger.error("Error clearing recent alerts:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to clear recent alerts"
        });
    }
});
/**
 * @route POST /api/alerting/test
 * @desc Send a test alert
 * @access Private (Admin)
 */
router.post("/test", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, async (req, res) => {
    try {
        const { message, severity, type, channels } = req.body;
        // Validate severity
        if (severity && !Object.values(alerting_1.AlertSeverity).includes(severity)) {
            return res.status(400).json({
                status: "error",
                message: `Invalid severity. Must be one of: ${Object.values(alerting_1.AlertSeverity).join(", ")}`
            });
        }
        // Validate type
        if (type && !Object.values(alerting_1.AlertType).includes(type)) {
            return res.status(400).json({
                status: "error",
                message: `Invalid type. Must be one of: ${Object.values(alerting_1.AlertType).join(", ")}`
            });
        }
        // Validate channels
        if (channels && Array.isArray(channels)) {
            for (const channel of channels) {
                if (!Object.values(alerting_1.AlertChannel).includes(channel)) {
                    return res.status(400).json({
                        status: "error",
                        message: `Invalid, channel: ${channel}. Must be one of: ${Object.values(alerting_1.AlertChannel).join(", ")}`
                    });
                }
            }
        }
        // Send the alert
        const alert = await alerting_1.default.sendAlert({
            severity: severity || alerting_1.AlertSeverity.INFO,
            type: type || alerting_1.AlertType.SYSTEM,
            message: message || "Test alert",
            details: { test: true,
                user: req.user?.id //, Fixed: using id instead of userId
            },
            timestamp: new Date()
        }, channels || [alerting_1.AlertChannel.EMAIL]);
        res.status(200).json({
            status: "success",
            data: {
                alert
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error sending test alert:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to send test alert"
        });
    }
});
/**
 * @route GET /api/alerting/channels
 * @desc Get available alert channels
 * @access Private (Admin)
 */
router.get("/channels", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        res.status(200).json({
            status: "success",
            data: { channels: Object.values(alerting_1.AlertChannel)
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting alert channels:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get alert channels"
        });
    }
});
/**
 * @route GET /api/alerting/severities
 * @desc Get available alert severities
 * @access Private (Admin)
 */
router.get("/severities", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        res.status(200).json({
            status: "success",
            data: { severities: Object.values(alerting_1.AlertSeverity)
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting alert severities:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get alert severities"
        });
    }
});
/**
 * @route GET /api/alerting/types
 * @desc Get available alert types
 * @access Private (Admin)
 */
router.get("/types", auth_middleware_1.authenticate, auth_middleware_1.isAdmin, (req, res) => {
    try {
        res.status(200).json({
            status: "success",
            data: { types: Object.values(alerting_1.AlertType)
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error getting alert types:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to get alert types"
        });
    }
});
exports.default = router;
//# sourceMappingURL=alerting.routes.js.map