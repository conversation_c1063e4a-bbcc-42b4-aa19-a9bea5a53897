/**
 * Test Suite Builders
 *
 * Functions to create comprehensive test suites for different component types.
 */
import { BaseController } from '../../../controllers/base.controller';
import { BaseService } from '../../../services/base.service';
import { ControllerTestOptions, ServiceTestOptions, RepositoryTestOptions, TestSuiteConfig, TestScenario, TestStep } from '../core/TestTypes';
/**
 * Create a test suite for a controller
 */
export declare function createControllerTestSuite(name: string, controllerClass: new (...args: any[]) => BaseController, tests: Record<string, ControllerTestOptions>, config?: TestSuiteConfig): void;
/**
 * Create a test suite for a service
 */
export declare function createServiceTestSuite(name: string, serviceClass: new (...args: any[]) => BaseService, tests: Record<string, ServiceTestOptions>, config?: TestSuiteConfig, setupFn?: (service: BaseService) => void | Promise<void>): void;
/**
 * Create a test suite for a repository
 */
export declare function createRepositoryTestSuite(name: string, repositoryClass: new (...args: any[]) => any, tests: Record<string, RepositoryTestOptions>, config?: TestSuiteConfig): void;
/**
 * Create an integration test suite
 */
export declare function createIntegrationTestSuite(name: string, scenarios: TestScenario[], config?: TestSuiteConfig): void;
/**
 * Create a performance test suite
 */
export declare function createPerformanceTestSuite(name: string, tests: Record<string, {
    fn: Function;
    args?: any[];
    maxExecutionTime?: number;
    iterations?: number;
    warmupIterations?: number;
}>, config?: TestSuiteConfig): void;
/**
 * Create an API test suite
 */
export declare function createApiTestSuite(name: string, baseUrl: string, endpoints: Record<string, {
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    path: string;
    headers?: Record<string, string>;
    body?: any;
    expectedStatus?: number;
    expectedResponse?: any;
    auth?: any;
}>, config?: TestSuiteConfig): void;
/**
 * Create a custom test matcher
 */
export declare function createCustomMatcher(name: string, matcher: (received: any, ...args: any[]) => jest.CustomMatcherResult): void;
/**
 * Create a test data builder
 */
export declare function createTestDataBuilder<T>(defaultData: T, overrides?: Partial<T>): T;
/**
 * Create a test scenario builder
 */
export declare function createTestScenarioBuilder(name: string): {
    withDescription: (description: string) => any;
    withSetup: (setup: () => void | Promise<void>) => any;
    withTeardown: (teardown: () => void | Promise<void>) => any;
    withSteps: (steps: TestStep[]) => any;
    withExpectedOutcome: (outcome: any) => any;
    build: () => TestScenario;
};
//# sourceMappingURL=TestSuiteBuilders.d.ts.map