"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const path_1 = __importDefault(require("path"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const dashboard_controller_1 = require("../controllers/dashboard.controller");
const dashboard_widget_controller_1 = require("../controllers/dashboard-widget.controller");
const router = express_1.default.Router();
const dashboardController = new dashboard_controller_1.DashboardController();
const widgetController = new dashboard_widget_controller_1.DashboardWidgetController();
// Apply authentication middleware to all routes
router.use(auth_middleware_1.authMiddleware);
// Serve the dashboard page
router.get('/page', (req, res) => {
    res.sendFile(path_1.default.join(__dirname, '../../src/public/reports/dashboard.html'));
});
// Dashboard API routes
router.get('/', dashboardController.getDashboards);
router.get('/:id', dashboardController.getDashboardById);
router.post('/', dashboardController.createDashboard);
router.put('/:id', dashboardController.updateDashboard);
router.delete('/:id', dashboardController.deleteDashboard);
// Widget API routes
router.get('/:dashboardId/widgets', widgetController.getWidgets);
router.get('/widgets/:id', widgetController.getWidgetById);
router.post('/:dashboardId/widgets', widgetController.createWidget);
router.put('/widgets/:id', widgetController.updateWidget);
router.delete('/widgets/:id', widgetController.deleteWidget);
router.post('/:dashboardId/widgets/reorder', widgetController.reorderWidgets);
exports.default = router;
//# sourceMappingURL=dashboard.routes.js.map