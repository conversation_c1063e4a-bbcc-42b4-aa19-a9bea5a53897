"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupRoutes = void 0;
const error_middleware_1 = require("../middlewares/error.middleware");
const payment_routing_routes_1 = __importDefault(require("./payment-routing.routes"));
const fee_management_routes_1 = __importDefault(require("./fee-management.routes"));
/**
 * Set up all routes for the application
 * @param app Express application
 */
const setupRoutes = (app) => {
    // API routes
    app.use('/api/v1', (req, res) => {
        res.json({
            success: true,
            message: 'AmazingPay API v1',
            data: {
                version: '1.0.0',
                timestamp: new Date().toISOString(),
            },
        });
    });
    // Payment routing routes
    app.use('/api/payment-routing', payment_routing_routes_1.default);
    // Fee management routes
    app.use('/api/fee-management', fee_management_routes_1.default);
    // Health check route
    app.get('/health', (req, res) => {
        res.json({
            success: true,
            message: 'Server is healthy',
            data: {
                uptime: process.uptime(),
                timestamp: new Date().toISOString(),
            },
        });
    });
    // 404 handler
    app.use('*', (req, res, next) => {
        next(new error_middleware_1.AppError(`Cannot find ${req.originalUrl} on this server`, 404));
    });
};
exports.setupRoutes = setupRoutes;
