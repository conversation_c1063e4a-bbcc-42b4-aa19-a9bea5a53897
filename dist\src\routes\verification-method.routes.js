"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const verification_method_controller_1 = require("../controllers/verification-method.controller");
const router = express_1.default.Router();
// Public routes (no authentication required)
router.post("/verify", verification_method_controller_1.verifyPayment);
// Routes requiring authentication
router.use(auth_middleware_1.authMiddleware);
// Routes accessible by all authenticated users
router.get("/types", verification_method_controller_1.getVerificationMethodTypes);
// Routes accessible by ADMIN and SUPER_ADMIN
router.get("/", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN"]), verification_method_controller_1.getAllVerificationMethods);
router.post("/", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN"]), verification_method_controller_1.createVerificationMethod);
// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
router.get("/:id", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), verification_method_controller_1.getVerificationMethodById);
router.put("/:id", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN"]), verification_method_controller_1.updateVerificationMethod);
router.delete("/:id", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN"]), verification_method_controller_1.deleteVerificationMethod);
router.get("/payment-method/:paymentMethodId", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), verification_method_controller_1.getVerificationMethodsForPaymentMethod);
exports.default = router;
//# sourceMappingURL=verification-method.routes.js.map