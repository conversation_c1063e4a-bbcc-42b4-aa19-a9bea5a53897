/**
 * Request ID Middleware
 *
 * This middleware adds a unique request ID to each request.
 * The request ID is used for tracking requests across the application.
 */
declare global {
    namespace Express {
        interface Request {
            id?: string;
        }
    }
}
/**
 * Add request ID to request object and response headers
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export declare const requestIdMiddleware: any;
export default requestIdMiddleware;
//# sourceMappingURL=request-id.middleware.d.ts.map