{"version": 3, "file": "csrf.js", "sourceRoot": "", "sources": ["../../../src/utils/csrf.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AAEjC,0CAAuC;AAGvC,uCAAuC;AACvC,MAAM,WAAW,GAAQ,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAE3F,iCAAiC;AACjC,MAAM,gBAAgB,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAEhD;;;;GAIG;AACI,MAAM,iBAAiB,GAAG,CAAC,MAAc,EAAU,EAAE;IAC1D,0BAA0B;IAC1B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAE3D,uBAAuB;IACvB,MAAM,SAAS,GAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;IAElC,uBAAuB;IACvB,MAAM,SAAS,GAAQ,MAAM;SAC1B,UAAU,CAAC,QAAQ,EAAE,WAAW,CAAC;SACjC,MAAM,CAAC,GAAG,WAAW,IAAI,MAAM,IAAI,SAAS,EAAE,CAAC;SAC/C,MAAM,CAAC,KAAK,CAAC,CAAC;IAEjB,0CAA0C;IAC1C,MAAM,SAAS,GAAQ,GAAG,WAAW,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;IAElE,eAAe;IACf,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACnD,CAAC,CAAC;AAlBW,QAAA,iBAAiB,qBAkB5B;AAEF;;;;;GAKG;AACI,MAAM,iBAAiB,GAAG,CAAC,KAAa,EAAE,MAAc,EAAW,EAAE;IAC1E,IAAI,CAAC;QACH,eAAe;QACf,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QAE7D,yBAAyB;QACzB,MAAM,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEvE,2BAA2B;QAC3B,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY,IAAI,CAAC,SAAS,EAAE,CAAC;YAChD,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,kBAAkB;QAClB,MAAM,SAAS,GAAQ,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAElD,8BAA8B;QAC9B,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YACrB,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,gBAAgB,EAAE,CAAC;YAC9C,eAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,8BAA8B;QAC9B,MAAM,iBAAiB,GAAQ,MAAM;aAClC,UAAU,CAAC,QAAQ,EAAE,WAAW,CAAC;aACjC,MAAM,CAAC,GAAG,WAAW,IAAI,MAAM,IAAI,SAAS,EAAE,CAAC;aAC/C,MAAM,CAAC,KAAK,CAAC,CAAC;QAEjB,qBAAqB;QACrB,IAAI,SAAS,KAAK,iBAAiB,EAAE,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACpD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AA9CW,QAAA,iBAAiB,qBA8C5B;AAEF;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,CAAC,GAAY,EAAiB,EAAE;IAC9D,gBAAgB;IAChB,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IAE/E,IAAI,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;QACnD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,aAAa;IACb,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAC/B,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;IACxB,CAAC;IAED,cAAc;IACd,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACjC,OAAO,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC;IACnC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAnBW,QAAA,gBAAgB,oBAmB3B;AAEF,kBAAe;IACb,iBAAiB,EAAjB,yBAAiB;IACjB,iBAAiB,EAAjB,yBAAiB;IACjB,gBAAgB,EAAhB,wBAAgB;CACjB,CAAC"}