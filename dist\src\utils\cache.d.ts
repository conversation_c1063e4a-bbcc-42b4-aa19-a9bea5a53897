/**
 * Cache Utility
 *
 * This utility provides functions for caching API responses and other data.
 * It uses Redis if available, otherwise falls back to an in-memory store.
 */
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Get a value from the cache
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
export declare const get: any;
/**
 * Get a value from the cache synchronously (local cache only)
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
export declare const getSync: any;
/**
 * Set a value in the cache
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
export declare const set: any;
/**
 * Set a value in the cache synchronously (local cache only)
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
export declare const setSync: any;
/**
 * Delete a value from the cache
 * @param key Cache key
 * @returns Whether the value was deleted successfully
 */
export declare const del: any;
/**
 * Clear the entire cache
 */
export declare const clear: any;
/**
 * Get cache statistics
 * @returns Cache statistics
 */
export declare const getStats: any;
/**
 * Generate a cache key from a request
 * @param req Express request
 * @returns Cache key
 */
export declare const generateCacheKey: any;
/**
 * Middleware to cache API responses
 * @param ttl Time to live in seconds (optional)
 * @returns Express middleware
 */
export declare const cacheMiddleware: any;
/**
 * Middleware to invalidate cache for specific routes
 * @param patterns Array of route patterns to match (e.g., '/api/users')
 * @returns Express middleware
 */
export declare const invalidateCacheMiddleware: any;
/**
 * Initialize cache
 */
export declare const initializeCache: any;
declare const _default: {
    get: any;
    getSync: any;
    set: any;
    setSync: any;
    del: any;
    clear: any;
    getStats: any;
    generateCacheKey: any;
    cacheMiddleware: any;
    invalidateCacheMiddleware: any;
    initializeCache: any;
};
export default _default;
//# sourceMappingURL=cache.d.ts.map