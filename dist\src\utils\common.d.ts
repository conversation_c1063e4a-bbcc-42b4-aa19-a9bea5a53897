/**
 * Common Utility Functions
 *
 * This file contains common utility functions used throughout the application.
 * It helps eliminate duplication by providing a single source of truth for utility functions.
 */
/**
 * Check if a value is defined
 * @param value Value to check
 * @returns True if value is defined
 */
export declare function isDefined<T>(value: T | undefined | null): value is T;
/**
 * Check if a value is undefined or null
 * @param value Value to check
 * @returns True if value is undefined or null
 */
export declare function isUndefined<T>(value: T | undefined | null): value is undefined | null;
/**
 * Check if a string is empty
 * @param value String to check
 * @returns True if string is empty
 */
export declare function isEmpty(value: string | undefined | null): boolean;
/**
 * Check if a string is not empty
 * @param value String to check
 * @returns True if string is not empty
 */
export declare function isNotEmpty(value: string | undefined | null): boolean;
/**
 * Format currency amount
 * @param amount Amount to format
 * @param currency Currency code
 * @returns Formatted currency amount
 */
export declare function formatCurrency(amount: number, currency: string): string;
/**
 * Format date
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date
 */
export declare function formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string;
/**
 * Generate a random string
 * @param length Length of the string
 * @returns Random string
 */
export declare function generateRandomString(length: number): string;
/**
 * Truncate a string
 * @param str String to truncate
 * @param maxLength Maximum length
 * @param suffix Suffix to add if truncated
 * @returns Truncated string
 */
export declare function truncate(str: string, maxLength: number, suffix?: string): string;
//# sourceMappingURL=common.d.ts.map