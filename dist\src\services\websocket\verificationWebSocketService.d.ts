import { Server as HttpServer } from "http";
import { Server as WebSocketServer } from "socket.io";
import { VerificationStatus } from "../../types/verification";
export declare const verificationEvents: any;
export interface PaymentVerificationMessage {
    paymentId: string;
    merchantId: string;
    status: VerificationStatus;
    timestamp: string;
    verificationMethod?: string;
    message?: string;
    transactionDetails?: Record<string, any>;
}
/**
 * WebSocket service for payment verification
 */
export declare class VerificationWebSocketService {
    private static instance;
    private io;
    private connectedClients;
    /**
   * Get singleton instance
   */
    static getInstance(): VerificationWebSocketService;
    /**
   * Initialize WebSocket server
   * @param httpServer HTTP server
   */
    initialize(httpServer: HttpServer): WebSocketServer;
    /**
   * Handle WebSocket connection
   * @param socket Socket
   */
    private handleConnection;
    /**
   * Add client to connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
    private addClient;
    /**
   * Remove client from connected clients
   * @param paymentId Payment ID
   * @param socketId Socket ID
   */
    private removeClient;
    /**
   * Set up verification event listeners
   */
    private setupEventListeners;
    /**
   * Emit event to payment room
   * @param paymentId Payment ID
   * @param event Event name
   * @param data Event data
   */
    emitToPayment(paymentId: string, event: string, data: any): void;
    /**
   * Emit event to merchant room
   * @param merchantId Merchant ID
   * @param event Event name
   * @param data Event data
   */
    emitToMerchant(merchantId: string, event: string, data: any): void;
    /**
   * Get connected clients count
   */
    getConnectedClientsCount(): number;
    /**
   * Get connected payments count
   */
    getConnectedPaymentsCount(): number;
}
export declare const verificationWebSocketService: any;
export default verificationWebSocketService;
//# sourceMappingURL=verificationWebSocketService.d.ts.map