import { Server as WebSocketServer } from 'socket.io';
import { logger } from './logger';

interface WebSocketStats {
  connections: {
    total: number;
    active: number;
    disconnected: number;
  };
  messages: {
    sent: number;
    received: number;
    errors: number;
  };
  rooms: {
    count: number;
    active: Map<string, number>;
  };
  events: {
    count: number;
    types: Map<string, number>;
  };
  errors: {
    count: number;
    lastError: string | null;
  };
  performance: {
    avgResponseTime: number;
    peakConnections: number;
    lastHealthCheck: Date;
  };
  messageHistory: Array<{
    type: 'sent' | 'received' | 'error';
    event?: string;
    timestamp: Date;
    size?: number;
    room?: string;
  }>;
}

export class WebSocketMonitor {
  private static instance: WebSocketMonitor;
  private io: WebSocketServer | null = null;
  private stats: WebSocketStats = {
    connections: { total: 0, active: 0, disconnected: 0 },
    messages: { sent: 0, received: 0, errors: 0 },
    rooms: { count: 0, active: new Map() },
    events: { count: 0, types: new Map() },
    errors: { count: 0, lastError: null },
    performance: {
      avgResponseTime: 0,
      peakConnections: 0,
      lastHealthCheck: new Date(),
    },
    messageHistory: [],
  };
  private monitoringInterval: NodeJS.Timeout | null = null;
  private historyLimit: number = 100; // Limit history entries to prevent memory leaks

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Get the singleton instance
   */
  public static getInstance(): WebSocketMonitor {
    if (!WebSocketMonitor.instance) {
      WebSocketMonitor.instance = new WebSocketMonitor();
    }
    return WebSocketMonitor.instance;
  }

  /**
   * Initialize the WebSocket monitor with a socket.io server instance
   */
  public initialize(io: WebSocketServer): void {
    this.io = io;
    this.setupEventListeners();
    this.startMonitoring();
    logger.info('WebSocket monitor initialized');
  }

  /**
   * Set up event listeners for socket.io events
   */
  private setupEventListeners(): void {
    if (!this.io) {
      logger.warn('Cannot setup event listeners: WebSocket server not initialized');
      return;
    }

    this.io.on('connection', (socket) => {
      // Update connection stats
      this.stats.connections.total++;
      this.stats.connections.active++;

      if (this.stats.connections.active > this.stats.performance.peakConnections) {
        this.stats.performance.peakConnections = this.stats.connections.active;
      }

      // Log connection
      this.stats.messageHistory.push({
        type: 'received',
        event: 'connection',
        timestamp: new Date(),
      });

      // Limit history size
      if (this.stats.messageHistory.length > this.historyLimit) {
        this.stats.messageHistory.shift();
      }

      // Track rooms
      socket.on('join', (room) => {
        const currentCount = this.stats.rooms.active.get(room) || 0;
        this.stats.rooms.active.set(room, currentCount + 1);
        this.stats.rooms.count = this.stats.rooms.active.size;

        // Log join event
        this.stats.messageHistory.push({
          type: 'received',
          event: 'join',
          room,
          timestamp: new Date(),
        });

        // Limit history size
        if (this.stats.messageHistory.length > this.historyLimit) {
          this.stats.messageHistory.shift();
        }
      });

      socket.on('leave', (room) => {
        const currentCount = this.stats.rooms.active.get(room) || 0;
        if (currentCount > 1) {
          this.stats.rooms.active.set(room, currentCount - 1);
        } else {
          this.stats.rooms.active.delete(room);
        }
        this.stats.rooms.count = this.stats.rooms.active.size;

        // Log leave event
        this.stats.messageHistory.push({
          type: 'received',
          event: 'leave',
          room,
          timestamp: new Date(),
        });

        // Limit history size
        if (this.stats.messageHistory.length > this.historyLimit) {
          this.stats.messageHistory.shift();
        }
      });

      // Track messages
      socket.onAny((event) => {
        this.stats.messages.received++;
        this.stats.events.count++;

        const currentCount = this.stats.events.types.get(event) || 0;
        this.stats.events.types.set(event, currentCount + 1);
      });

      // Track disconnections
      socket.on('disconnect', () => {
        this.stats.connections.active--;
        this.stats.connections.disconnected++;

        // Log disconnection
        this.stats.messageHistory.push({
          type: 'received',
          event: 'disconnect',
          timestamp: new Date(),
        });

        // Limit history size
        if (this.stats.messageHistory.length > this.historyLimit) {
          this.stats.messageHistory.shift();
        }
      });

      // Track errors
      socket.on('error', (err) => {
        this.stats.errors.count++;
        this.stats.errors.lastError = err.message || 'Unknown error';
        this.stats.messages.errors++;

        // Log error
        this.stats.messageHistory.push({
          type: 'error',
          timestamp: new Date(),
        });

        // Limit history size
        if (this.stats.messageHistory.length > this.historyLimit) {
          this.stats.messageHistory.shift();
        }

        logger.error('WebSocket error:', err);
      });
    });
  }

  /**
   * Start periodic monitoring
   */
  private startMonitoring(): void {
    // Check health every minute
    this.monitoringInterval = setInterval(() => {
      this.checkHealth();
    }, 60000); // 1 minute
  }

  /**
   * Check the health of the WebSocket server
   */
  private checkHealth(): void {
    if (!this.io) {
      logger.warn('Cannot check health: WebSocket server not initialized');
      return;
    }

    // Update last health check timestamp
    this.stats.performance.lastHealthCheck = new Date();

    // Log health check
    logger.debug('WebSocket health check', {
      connections: this.stats.connections,
      messages: this.stats.messages,
      rooms: {
        count: this.stats.rooms.count,
        active: Array.from(this.stats.rooms.active.entries()),
      },
      events: {
        count: this.stats.events.count,
        types: Array.from(this.stats.events.types.entries()),
      },
      errors: this.stats.errors,
      performance: this.stats.performance,
    });

    // Check for potential issues
    if (this.stats.errors.count > 0) {
      logger.warn('WebSocket errors detected', {
        count: this.stats.errors.count,
        lastError: this.stats.errors.lastError,
      });
    }
  }

  /**
   * Get the current WebSocket stats
   */
  public getStats(): WebSocketStats {
    return {
      ...this.stats,
      performance: {
        ...this.stats.performance,
        lastHealthCheck: new Date(),
      },
    };
  }

  /**
   * Stop monitoring
   */
  public stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('WebSocket monitoring stopped');
    }
  }
}
