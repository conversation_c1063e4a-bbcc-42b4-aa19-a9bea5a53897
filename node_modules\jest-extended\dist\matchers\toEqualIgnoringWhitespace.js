"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toEqualIgnoringWhitespace = toEqualIgnoringWhitespace;
const jest_diff_1 = require("jest-diff");
const print_1 = require("../utils/print");
const removeWhitespace = (str) => str.trim().replace(/\s+/g, '');
const getDiff = (received, expected) => {
    /* calculate diff of received w.r.t expected string */
    const diff = (0, jest_diff_1.diffStringsRaw)(expected, received, false);
    /* mark every diff result object with value of white-space as DIFF_EQUAL */
    diff.forEach(diffObject => {
        if (diffObject[1].trim())
            return;
        diffObject[0] = jest_diff_1.DIFF_EQUAL;
    });
    return diff;
};
function toEqualIgnoringWhitespace(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { matcherHint, EXPECTED_COLOR } = this.utils;
    /* determine whether strings are equal after removing white-space */
    const pass = typeof actual === 'string' && removeWhitespace(actual) === removeWhitespace(expected);
    /* eslint-disable indent */ // prettier conflicts with indent rule
    return {
        pass,
        message: pass
            ? () => matcherHint('.not.toEqualIgnoringWhitespace') +
                '\n\n' +
                'Expected values to not be equal while ignoring white-space (using ===):\n' +
                `Expected: not  ${EXPECTED_COLOR(expected)}\n\n`
            : () => {
                const diff = getDiff(String(actual), expected);
                return (matcherHint('.toEqualIgnoringWhitespace') +
                    '\n\n' +
                    'Expected values to be equal while ignoring white-space (using ===):\n' +
                    // @ts-expect-error OK to have implicit any for this.utils
                    `Expected:\n  ${(0, print_1.printExpected)(this.utils, diff)}\n\n` +
                    // @ts-expect-error OK to have implicit any for this.utils
                    `Received:\n  ${(0, print_1.printReceived)(this.utils, diff)}`);
            },
    };
    /* eslint-enable indent */
}
