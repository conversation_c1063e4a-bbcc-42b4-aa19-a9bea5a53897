/**
 * Test Runners
 *
 * Functions to execute tests for different types of components.
 */

import { BaseController } from '../../../controllers/base.controller';
import { BaseService } from '../../../services/base.service';
// BaseRepository not available - using any type
import {
  ControllerTestOptions,
  ServiceTestOptions,
  RepositoryTestOptions,
  MiddlewareTestOptions,
  MockRequest,
  MockResponse,
  MockNext,
  TestResult,
  TestError,
  TestErrorType,
} from '../core/TestTypes';
import {
  createMockRequest,
  createMockResponse,
  createMockNext,
  createMockPrismaClient,
} from '../factories/MockFactories';

/**
 * Test a controller method
 */
export async function testController(
  controller: any,
  method: string,
  options: ControllerTestOptions = {}
): Promise<{ req: MockRequest; res: MockResponse; next: MockNext; result?: any }> {
  const startTime = Date.now();
  let req: MockRequest;
  let res: MockResponse;
  let next: MockNext;

  try {
    // Setup
    req = options.req || createMockRequest();
    res = options.res || createMockResponse();
    next = options.next || createMockNext();

    // Run setup hooks
    if (options.beforeEach) {
      await options.beforeEach();
    }

    if (options.setup) {
      await options.setup();
    }

    if (options.controllerSetup) {
      await options.controllerSetup(controller);
    }

    // Validate request if validator provided
    if (options.validateRequest) {
      await options.validateRequest(req);
    }

    // Execute the controller method
    const result = await (controller[method] as Function)(req, res, next);

    // Validate response if validator provided
    if (options.validateResponse) {
      await options.validateResponse(res);
    }

    // Assert expected status
    if (options.expectedStatus !== undefined) {
      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
    }

    // Assert expected response
    if (options.expectedResponse !== undefined) {
      if (typeof options.expectedResponse === 'function') {
        expect(res.json).toHaveBeenCalledWith(expect.objectContaining(options.expectedResponse()));
      } else {
        expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
      }
    }

    // Run cleanup hooks
    if (options.controllerCleanup) {
      await options.controllerCleanup(controller);
    }

    if (options.cleanup) {
      await options.cleanup();
    }

    if (options.afterEach) {
      await options.afterEach();
    }

    return { req, res, next, result };
  } catch (error) {
    // Handle expected errors
    if (options.expectedError) {
      if (typeof options.expectedError === 'function') {
        expect(error).toEqual(expect.objectContaining(options.expectedError()));
      } else {
        expect(error).toEqual(options.expectedError);
      }
      return { req: req!, res: res!, next: next! };
    }

    // Wrap unexpected errors
    const testError = new TestError(
      `Controller test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      TestErrorType.EXECUTION_ERROR,
      undefined,
      error instanceof Error ? error : new Error(String(error))
    );

    throw testError;
  }
}

/**
 * Test a service method
 */
export async function testService(
  service: any,
  method: string,
  options: ServiceTestOptions = {}
): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Setup
    const args = options.args || [];

    // Run setup hooks
    if (options.beforeEach) {
      await options.beforeEach();
    }

    if (options.setup) {
      await options.setup();
    }

    if (options.serviceSetup) {
      await options.serviceSetup(service);
    }

    // Mock dependencies if provided
    if (options.mockDependencies) {
      Object.entries(options.mockDependencies).forEach(([key, value]) => {
        (service as any)[key] = value;
      });
    }

    // Mock methods if provided
    if (options.mockMethods) {
      Object.entries(options.mockMethods).forEach(([key, mockFn]) => {
        (service as any)[key] = mockFn;
      });
    }

    // Execute the service method
    const result = await (service[method] as Function)(...args);

    // Validate result if validator provided
    if (options.validateResult) {
      await options.validateResult(result);
    }

    // Assert expected result
    if (options.expectedResult !== undefined) {
      if (typeof options.expectedResult === 'function') {
        expect(result).toEqual(expect.objectContaining(options.expectedResult()));
      } else {
        expect(result).toEqual(options.expectedResult);
      }
    }

    // Run cleanup hooks
    if (options.serviceCleanup) {
      await options.serviceCleanup(service);
    }

    if (options.cleanup) {
      await options.cleanup();
    }

    if (options.afterEach) {
      await options.afterEach();
    }

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      result,
      executionTime,
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;

    // Handle expected errors
    if (options.expectedError) {
      if (typeof options.expectedError === 'function') {
        expect(error).toEqual(expect.objectContaining(options.expectedError()));
      } else {
        expect(error).toEqual(options.expectedError);
      }

      return {
        success: true,
        error: error instanceof Error ? error : new Error(String(error)),
        executionTime,
      };
    }

    // Return test failure result
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
      executionTime,
    };
  }
}

/**
 * Test a repository method
 */
export async function testRepository(
  repository: any,
  method: string,
  options: RepositoryTestOptions = {}
): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Setup
    const args = options.args || [];
    const mockPrisma = options.mockPrisma || createMockPrismaClient();

    // Replace the repository's prisma client with the mock
    (repository as any).prisma = mockPrisma;

    // Run setup hooks
    if (options.beforeEach) {
      await options.beforeEach();
    }

    if (options.setup) {
      await options.setup();
    }

    if (options.repositorySetup) {
      await options.repositorySetup(repository);
    }

    // Mock transaction if needed
    if (options.mockTransaction) {
      (mockPrisma as any).$transaction.mockImplementation((callback: Function) => {
        return Promise.resolve(options.mockTransactionResult || callback(mockPrisma));
      });
    }

    // Mock specific queries if provided
    if (options.mockQueries) {
      Object.entries(options.mockQueries).forEach(([queryName, mockResult]) => {
        if ((mockPrisma as any)[queryName]) {
          Object.keys((mockPrisma as any)[queryName]).forEach((method) => {
            if (typeof (mockPrisma as any)[queryName][method] === 'function') {
              (mockPrisma as any)[queryName][method].mockResolvedValue(mockResult);
            }
          });
        }
      });
    }

    // Execute the repository method
    const result = await (repository[method] as Function)(...args);

    // Validate query if validator provided
    if (options.validateQuery) {
      await options.validateQuery(mockPrisma);
    }

    // Validate result if validator provided
    if (options.validateResult) {
      await options.validateResult(result);
    }

    // Assert expected result
    if (options.expectedResult !== undefined) {
      if (typeof options.expectedResult === 'function') {
        expect(result).toEqual(expect.objectContaining(options.expectedResult()));
      } else {
        expect(result).toEqual(options.expectedResult);
      }
    }

    // Run cleanup hooks
    if (options.repositoryCleanup) {
      await options.repositoryCleanup(repository);
    }

    if (options.cleanup) {
      await options.cleanup();
    }

    if (options.afterEach) {
      await options.afterEach();
    }

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      result,
      executionTime,
      metadata: {
        mockPrismaUsed: true,
        transactionMocked: options.mockTransaction,
      },
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;

    // Handle expected errors
    if (options.expectedError) {
      if (typeof options.expectedError === 'function') {
        expect(error).toEqual(expect.objectContaining(options.expectedError()));
      } else {
        expect(error).toEqual(options.expectedError);
      }

      return {
        success: true,
        error: error instanceof Error ? error : new Error(String(error)),
        executionTime,
      };
    }

    // Return test failure result
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
      executionTime,
    };
  }
}

/**
 * Test middleware
 */
export async function testMiddleware(
  middleware: Function,
  options: MiddlewareTestOptions = {}
): Promise<{ req: MockRequest; res: MockResponse; next: MockNext }> {
  const startTime = Date.now();

  // Setup - declare outside try block so they're available in catch
  const req = options.req || createMockRequest();
  const res = options.res || createMockResponse();
  const next = options.next || createMockNext();

  try {
    // Run setup hooks
    if (options.beforeEach) {
      await options.beforeEach();
    }

    if (options.setup) {
      await options.setup();
    }

    if (options.middlewareSetup) {
      await options.middlewareSetup(req, res, next);
    }

    // Execute the middleware
    await middleware(req, res, next);

    // Assert next was called if expected
    if (options.expectNextCalled !== undefined) {
      if (options.expectNextCalled) {
        expect(next).toHaveBeenCalled();
      } else {
        expect(next).not.toHaveBeenCalled();
      }
    }

    // Assert next was called with specific arguments
    if (options.expectNextCalledWith !== undefined) {
      expect(next).toHaveBeenCalledWith(options.expectNextCalledWith);
    }

    // Assert expected status
    if (options.expectedStatus !== undefined) {
      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
    }

    // Assert expected response
    if (options.expectedResponse !== undefined) {
      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
    }

    // Run cleanup hooks
    if (options.middlewareCleanup) {
      await options.middlewareCleanup(req, res, next);
    }

    if (options.cleanup) {
      await options.cleanup();
    }

    if (options.afterEach) {
      await options.afterEach();
    }

    return { req, res, next };
  } catch (error) {
    // Handle expected errors
    if (options.expectedError) {
      if (typeof options.expectedError === 'function') {
        expect(error).toEqual(expect.objectContaining(options.expectedError()));
      } else {
        expect(error).toEqual(options.expectedError);
      }
      return { req: req as any, res: res as any, next: next as any };
    }

    // Wrap unexpected errors
    const testError = new TestError(
      `Middleware test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      TestErrorType.EXECUTION_ERROR,
      undefined,
      error instanceof Error ? error : new Error(String(error))
    );

    throw testError;
  }
}

/**
 * Test utility function
 */
export async function testUtility(
  utilityFunction: Function,
  options: {
    args?: any[];
    expectedResult?: any;
    expectedError?: any;
    setup?: () => void | Promise<void>;
    cleanup?: () => void | Promise<void>;
  } = {}
): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Setup
    const args = options.args || [];

    if (options.setup) {
      await options.setup();
    }

    // Execute the utility function
    const result = await utilityFunction(...args);

    // Assert expected result
    if (options.expectedResult !== undefined) {
      expect(result).toEqual(options.expectedResult);
    }

    if (options.cleanup) {
      await options.cleanup();
    }

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      result,
      executionTime,
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;

    // Handle expected errors
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
      return {
        success: true,
        error: error instanceof Error ? error : new Error(String(error)),
        executionTime,
      };
    }

    // Return test failure result
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
      executionTime,
    };
  }
}
