/**
 * Response Mapper
 * 
 * Handles response formatting for alert aggregation operations.
 */

import { Response } from 'express';
import {
  ApiResponse,
  SuccessResponse,
  ErrorResponse,
  AggregationRuleResponse,
  CorrelationRuleResponse
} from '../types/AlertAggregationTypes';
import { AppError } from '../../../utils/errors/AppError';

/**
 * Response mapper for alert aggregation
 */
export class ResponseMapper {
  
  /**
   * Send success response
   */
  static sendSuccess<T>(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId || 'unknown'
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(
    res: Response,
    error: AppError | Error,
    statusCode?: number
  ): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details
        },
        timestamp: new Date(),
        requestId: res.locals.requestId || 'unknown'
      };
      
      statusCode = statusCode || error.statusCode || 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message || 'Internal server error',
          code: 'INTERNAL_SERVER_ERROR',
          type: 'INTERNAL'
        },
        timestamp: new Date(),
        requestId: res.locals.requestId || 'unknown'
      };
      
      statusCode = statusCode || 500;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send aggregation rules list response
   */
  static sendAggregationRulesList(
    res: Response,
    rules: AggregationRuleResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);
    
    this.sendSuccess(
      res,
      rules,
      `Retrieved ${rules.length} aggregation rules`,
      200,
      {
        page,
        limit,
        total,
        totalPages
      }
    );
  }

  /**
   * Send single aggregation rule response
   */
  static sendAggregationRule(
    res: Response,
    rule: AggregationRuleResponse,
    message?: string
  ): void {
    this.sendSuccess(
      res,
      rule,
      message || 'Aggregation rule retrieved successfully'
    );
  }

  /**
   * Send aggregation rule created response
   */
  static sendAggregationRuleCreated(
    res: Response,
    rule: AggregationRuleResponse
  ): void {
    this.sendSuccess(
      res,
      rule,
      'Aggregation rule created successfully',
      201
    );
  }

  /**
   * Send aggregation rule updated response
   */
  static sendAggregationRuleUpdated(
    res: Response,
    rule: AggregationRuleResponse
  ): void {
    this.sendSuccess(
      res,
      rule,
      'Aggregation rule updated successfully'
    );
  }

  /**
   * Send aggregation rule deleted response
   */
  static sendAggregationRuleDeleted(res: Response): void {
    this.sendSuccess(
      res,
      null,
      'Aggregation rule deleted successfully'
    );
  }

  /**
   * Send correlation rules list response
   */
  static sendCorrelationRulesList(
    res: Response,
    rules: CorrelationRuleResponse[],
    total: number,
    page: number = 1,
    limit: number = 10
  ): void {
    const totalPages = Math.ceil(total / limit);
    
    this.sendSuccess(
      res,
      rules,
      `Retrieved ${rules.length} correlation rules`,
      200,
      {
        page,
        limit,
        total,
        totalPages
      }
    );
  }

  /**
   * Send single correlation rule response
   */
  static sendCorrelationRule(
    res: Response,
    rule: CorrelationRuleResponse,
    message?: string
  ): void {
    this.sendSuccess(
      res,
      rule,
      message || 'Correlation rule retrieved successfully'
    );
  }

  /**
   * Send correlation rule created response
   */
  static sendCorrelationRuleCreated(
    res: Response,
    rule: CorrelationRuleResponse
  ): void {
    this.sendSuccess(
      res,
      rule,
      'Correlation rule created successfully',
      201
    );
  }

  /**
   * Send correlation rule updated response
   */
  static sendCorrelationRuleUpdated(
    res: Response,
    rule: CorrelationRuleResponse
  ): void {
    this.sendSuccess(
      res,
      rule,
      'Correlation rule updated successfully'
    );
  }

  /**
   * Send correlation rule deleted response
   */
  static sendCorrelationRuleDeleted(res: Response): void {
    this.sendSuccess(
      res,
      null,
      'Correlation rule deleted successfully'
    );
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: any[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: 'VALIDATION',
      code: 'INVALID_INPUT',
      details: { errors }
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: 'AUTHENTICATION',
      code: 'INVALID_CREDENTIALS',
      details: { requiredRole }
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(
    res: Response,
    resource: string = 'Resource'
  ): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: 'NOT_FOUND',
      code: 'RESOURCE_NOT_FOUND'
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(
    res: Response,
    message: string = 'Internal server error'
  ): void {
    const error = new AppError({
      message,
      type: 'INTERNAL',
      code: 'INTERNAL_SERVER_ERROR'
    });

    this.sendError(res, error, 500);
  }

  /**
   * Format pagination metadata
   */
  static formatPagination(
    page: number,
    limit: number,
    total: number
  ): {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } {
    const totalPages = Math.ceil(total / limit);
    
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };
  }

  /**
   * Create API response wrapper
   */
  static createApiResponse<T>(
    success: boolean,
    data?: T,
    message?: string,
    error?: any
  ): ApiResponse<T> {
    return {
      success,
      data,
      message,
      error
    };
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: any, res: Response, next: Function) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res: Response): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Response-Time', Date.now());
  }

  /**
   * Log response for debugging
   */
  static logResponse(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number
  ): void {
    console.log(`${method} ${url} - ${statusCode} - ${responseTime}ms`);
  }
}
