import { Application } from "express";
import { RouteTestSuite } from "../suites/RouteTestSuite";
import { Container } from "../../core/Container";
/**
 * Route test runner
 * This class runs route tests
 */
export declare class RouteTestRunner {
    private app;
    private container;
    private routeTestSuite;
    /**
     * Create a new route test runner
     */
    constructor();
    /**
     * Run all tests
     */
    runAllTests(): Promise<void>;
    /**
     * Get the Express application
     * @returns Express application
     */
    getApp(): Application;
    /**
     * Get the container
     * @returns Container
     */
    getContainer(): Container;
    /**
     * Get the route test suite
     * @returns Route test suite
     */
    getRouteTestSuite(): RouteTestSuite;
}
export default RouteTestRunner;
//# sourceMappingURL=RouteTestRunner.d.ts.map