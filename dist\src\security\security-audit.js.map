{"version": 3, "file": "security-audit.js", "sourceRoot": "", "sources": ["../../../src/security/security-audit.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAEH,oDAA4B;AAW5B,MAAa,eAAe;IAA5B;QACU,YAAO,GAA0B,EAAE,CAAC;IAuZ9C,CAAC;IArZC;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAElB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAChC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAChC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC1B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,uCAAuC;QACvC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,cAAc;YACxB,IAAI,EAAE,iCAAiC;YACvC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,qEAAqE;YAC9E,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;QAEH,gCAAgC;QAChC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC3C,IAAI,WAAW,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;gBAC9B,IAAI,CAAC,SAAS,CAAC;oBACb,QAAQ,EAAE,cAAc;oBACxB,IAAI,EAAE,0BAA0B;oBAChC,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,yDAAyD;oBAClE,QAAQ,EAAE,MAAM;iBACjB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC,SAAS,CAAC;gBACb,QAAQ,EAAE,cAAc;gBACxB,IAAI,EAAE,0BAA0B;gBAChC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,6CACP,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAC3C,EAAE;gBACF,QAAQ,EAAE,UAAU;gBACpB,cAAc,EAAE,6CAA6C;aAC9D,CAAC,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,MAAM,QAAQ,GAAG,qBAAqB,CAAC;QACvC,MAAM,IAAI,GAAG,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACxE,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC;gBACb,QAAQ,EAAE,cAAc;gBACxB,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,mCAAmC;gBAC5C,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,mCAAmC;QACnC,MAAM,YAAY,GAAG,4CAA4C,CAAC;QAClE,MAAM,cAAc,GAAG,WAAW,CAAC;QAEnC,IAAI,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,EAAE,CAAC;YAC9F,IAAI,CAAC,SAAS,CAAC;gBACb,QAAQ,EAAE,kBAAkB;gBAC5B,IAAI,EAAE,6BAA6B;gBACnC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,+CAA+C;gBACxD,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC;gBACb,QAAQ,EAAE,kBAAkB;gBAC5B,IAAI,EAAE,6BAA6B;gBACnC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,kDAAkD;gBAC3D,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,qDAAqD;aACtE,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,kBAAkB;YAC5B,IAAI,EAAE,0BAA0B;YAChC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,6CAA6C;YACtD,QAAQ,EAAE,UAAU;SACrB,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,kBAAkB;YAC5B,IAAI,EAAE,gBAAgB;YACtB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,2DAA2D;YACpE,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,0DAA0D;SAC3E,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,uDAAuD;YAChE,QAAQ,EAAE,UAAU;YACpB,cAAc,EAAE,yCAAyC;SAC1D,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,oBAAoB;YAC1B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,4DAA4D;YACrE,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,qDAAqD;SACtE,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,6BAA6B;YACnC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,8CAA8C;YACvD,QAAQ,EAAE,QAAQ;YAClB,cAAc,EAAE,gDAAgD;SACjE,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,iCAAiC;QACjC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,2BAA2B;YACjC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,sCAAsC;YAC/C,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,uDAAuD;YAChE,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,gDAAgD;SACjE,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,eAAe;YACzB,IAAI,EAAE,iCAAiC;YACvC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,8CAA8C;YACvD,QAAQ,EAAE,UAAU;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,qDAAqD;YAC9D,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,kDAAkD;SACnE,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,4BAA4B;YAClC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,wCAAwC;YACjD,QAAQ,EAAE,UAAU;YACpB,cAAc,EAAE,+CAA+C;SAChE,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,8CAA8C;YACvD,QAAQ,EAAE,UAAU;SACrB,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,gDAAgD;YACzD,QAAQ,EAAE,QAAQ;YAClB,cAAc,EAAE,oDAAoD;SACrE,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,oCAAoC;QACpC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,8BAA8B;YACpC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,oCAAoC;YAC7C,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,yCAAyC;YAClD,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,gBAAgB;YAC1B,IAAI,EAAE,sBAAsB;YAC5B,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,qCAAqC;YAC9C,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,8BAA8B;QAC9B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,wBAAwB;YAC9B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,gDAAgD;YACzD,QAAQ,EAAE,MAAM;YAChB,cAAc,EACZ,oFAAoF;SACvF,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,gBAAgB;YACtB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,0CAA0C;YACnD,QAAQ,EAAE,QAAQ;YAClB,cAAc,EAAE,uDAAuD;SACxE,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,SAAS;YACnB,IAAI,EAAE,aAAa;YACnB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,8DAA8D;YACvE,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,wDAAwD;SACzE,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,kCAAkC;QAClC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,cAAc;YACxB,IAAI,EAAE,4BAA4B;YAClC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,kDAAkD;YAC3D,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,sDAAsD;SACvE,CAAC,CAAC;QAEH,gCAAgC;QAChC,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,cAAc;YACxB,IAAI,EAAE,0BAA0B;YAChC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,sDAAsD;YAC/D,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,SAAS,CAAC;YACb,QAAQ,EAAE,cAAc;YACxB,IAAI,EAAE,uBAAuB;YAC7B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,0CAA0C;YACnD,QAAQ,EAAE,QAAQ;YAClB,cAAc,EAAE,qDAAqD;SACtE,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,MAA2B;QAC3C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAe;QAC5C,OAAO,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CACxC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CACxD,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAC5F,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;QACpE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAE/D,IAAI,MAAM,GAAG,6BAA6B,CAAC;QAC3C,MAAM,IAAI,cAAc,CAAC;QACzB,MAAM,IAAI,kBAAkB,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC;QACpD,MAAM,IAAI,aAAa,MAAM,CAAC,MAAM,IAAI,CAAC;QACzC,MAAM,IAAI,eAAe,QAAQ,CAAC,MAAM,IAAI,CAAC;QAC7C,MAAM,IAAI,sBAAsB,cAAc,CAAC,MAAM,IAAI,CAAC;QAC1D,MAAM,IAAI,kBAAkB,UAAU,CAAC,MAAM,MAAM,CAAC;QAEpD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,sBAAsB,CAAC;YACjC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC/B,MAAM,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,OAAO,IAAI,CAAC;gBACpD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;oBACzB,MAAM,IAAI,uBAAuB,KAAK,CAAC,cAAc,IAAI,CAAC;gBAC5D,CAAC;YACH,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,2BAA2B,CAAC;YACtC,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC3B,MAAM,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,OAAO,IAAI,CAAC;gBACpD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;oBACzB,MAAM,IAAI,uBAAuB,KAAK,CAAC,cAAc,IAAI,CAAC;gBAC5D,CAAC;YACH,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,eAAe,CAAC;YAC1B,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC3B,MAAM,IAAI,OAAO,OAAO,CAAC,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI,CAAC;gBACxD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;oBAC3B,MAAM,IAAI,uBAAuB,OAAO,CAAC,cAAc,IAAI,CAAC;gBAC9D,CAAC;YACH,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,MAAM,IAAI,mBAAmB,CAAC;QAC9B,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACtB,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAxZD,0CAwZC"}