// jscpd:ignore-file
import { logger } from "../utils/logger";
import { AlertService } from "./alert.service";
import { MonitoringService } from "./monitoring.service";
import prisma from "../lib/prisma";
import { Merchant } from '../types';
import {
  Alert,
  AlertType,
  AlertSeverity,
  AlertNotificationMethod,
  AlertThresholds
} from '../types/alert.types';


/**
 * Alert threshold configuration
 */
interface AlertThresholds {
  // System health thresholds
  systemHealth: {, memoryUsageWarning: number; // MB
    memoryUsageCritical: number; // MB
    cpuUsageWarning: number; // %
    cpuUsageCritical: number; // %
    apiLatencyWarning: number; // ms
    apiLatencyCritical: number; // ms
  };

  // Payment thresholds
  payments: {, successRateWarning: number; // %
    successRateCritical: number; // %
    failureRateWarning: number; // %
    failureRateCritical: number; // %
  };

  // Verification thresholds
  verification: {, successRateWarning: number; // %
    successRateCritical: number; // %
    failureRateWarning: number; // %
    failureRateCritical: number; // %
    averageTimeWarning: number; // ms
    averageTimeCritical: number; // ms
  };

  // Webhook thresholds
  webhooks: {, deliveryRateWarning: number; // %
    deliveryRateCritical: number; // %
    failureRateWarning: number; // %
    failureRateCritical: number; // %
    averageLatencyWarning: number; // ms
    averageLatencyCritical: number; // ms
  };
}

/**
 * Alert monitor service
 */
export class AlertMonitorService {
    private alertService: AlertService;
    private monitoringService: MonitoringService;
    private thresholds: AlertThresholds;
    private checkInterval: number; // ms
    private intervalId: NodeJS.Timeout | null = null;
    private isRunning: boolean = false;
    private lastAlertTimes: Map<string, number> = new Map();
    private alertCooldown: number = 15 * 60 * 1000; // 15 minutes

    /**
   * Create a new alert monitor service
   * @param checkInterval Check interval in milliseconds (default: 5 minutes)
   */
    constructor(checkInterval: number = 5 * 60 * 1000) {
        this.alertService = new AlertService();
        this.monitoringService = new MonitoringService();
        this.checkInterval = checkInterval;

        // Set default thresholds
        this.thresholds = {
            systemHealth: {, memoryUsageWarning: 1024, // 1 GB
                memoryUsageCritical: 2048, // 2 GB
                cpuUsageWarning: 70, // 70%
                cpuUsageCritical: 90, // 90%
                apiLatencyWarning: 1000, // 1 second
                apiLatencyCritical: 5000 // 5 seconds
            },
            payments: {, successRateWarning: 90, // 90%
                successRateCritical: 80, // 80%
                failureRateWarning: 10, // 10%
                failureRateCritical: 20 // 20%
            },
            verification: {, successRateWarning: 90, // 90%
                successRateCritical: 80, // 80%
                failureRateWarning: 10, // 10%
                failureRateCritical: 20, // 20%
                averageTimeWarning: 5000, // 5 seconds
                averageTimeCritical: 10000 // 10 seconds
            },
            webhooks: {, deliveryRateWarning: 90, // 90%
                deliveryRateCritical: 80, // 80%
                failureRateWarning: 10, // 10%
                failureRateCritical: 20, // 20%
                averageLatencyWarning: 1000, // 1 second
                averageLatencyCritical: 5000 // 5 seconds
            }
        };
    }

    /**
   * Start monitoring
   */
    public start(): void {
        if (this.isRunning) {
            logger.warn("Alert monitor is already running");
            return;
        }

        logger.info("Starting alert monitor");
        this.isRunning = true;

        // Run initial check
        this.checkMetrics().catch(error) => {
            logger.error("Error in initial alert monitor check", { error });
        });

        // Set up interval
        this.intervalId = setInterval(() => {
            this.checkMetrics().catch(error) => {
                logger.error("Error in alert monitor check", { error });
            });
        }, this.checkInterval);
    }

    /**
   * Stop monitoring
   */
    public stop(): void {
        if (!this.isRunning || !this.intervalId) {
            logger.warn("Alert monitor is not running");
            return;
        }

        logger.info("Stopping alert monitor");
        clearInterval(this.intervalId);
        this.isRunning = false;
        this.intervalId = null;
    }

    /**
   * Check metrics and generate alerts
   */
    private async checkMetrics(): Promise<void> {
        try {
            // Get metrics
            const metrics: any =await this.monitoringService.getMetrics();

            // Check system health
            await this.checkSystemHealth(metrics.systemHealth);

            // Check payment metrics
            await this.checkPaymentMetrics(metrics.payments);

            // Check verification metrics
            await this.checkVerificationMetrics(metrics.verification);

            // Check webhook metrics
            await this.checkWebhookMetrics(metrics.webhooks);

            // Check merchant-specific metrics
            await this.checkMerchantMetrics();

            logger.debug("Alert monitor check completed");
        } catch (error) {
            logger.error("Error checking metrics", { error });
        }
    }

    /**
   * Check system health metrics
   * @param systemHealth System health metrics
   */
    private async checkSystemHealth(systemHealth): Promise<void> {
    // Check memory usage
        if (systemHealth.memoryUsage >= this.thresholds.systemHealth.memoryUsageCritical) {
            await this.createAlertIfNotCooling(
                "system_memory_critical",
                AlertType.SYSTEM_HEALTH,
                AlertSeverity.CRITICAL,
                "Critical Memory Usage",
                `Memory usage is critically high: ${systemHealth.memoryUsage.toFixed(2)} MB`,
                {
                    memoryUsage: systemHealth.memoryUsage,
                    threshold: this.thresholds.systemHealth.memoryUsageCritical
                }
            );
        } else if (systemHealth.memoryUsage >= this.thresholds.systemHealth.memoryUsageWarning) {
            await this.createAlertIfNotCooling(
                "system_memory_warning",
                AlertType.SYSTEM_HEALTH,
                AlertSeverity.WARNING,
                "High Memory Usage",
                `Memory usage is high: ${systemHealth.memoryUsage.toFixed(2)} MB`,
                {
                    memoryUsage: systemHealth.memoryUsage,
                    threshold: this.thresholds.systemHealth.memoryUsageWarning
                }
            );
        }

        // Check CPU usage
        if (systemHealth.cpuUsage >= this.thresholds.systemHealth.cpuUsageCritical) {
            await this.createAlertIfNotCooling(
                "system_cpu_critical",
                AlertType.SYSTEM_HEALTH,
                AlertSeverity.CRITICAL,
                "Critical CPU Usage",
                `CPU usage is critically high: ${systemHealth.cpuUsage.toFixed(2)}%`,
                {
                    cpuUsage: systemHealth.cpuUsage,
                    threshold: this.thresholds.systemHealth.cpuUsageCritical
                }
            );
        } else if (systemHealth.cpuUsage >= this.thresholds.systemHealth.cpuUsageWarning) {
            await this.createAlertIfNotCooling(
                "system_cpu_warning",
                AlertType.SYSTEM_HEALTH,
                AlertSeverity.WARNING,
                "High CPU Usage",
                `CPU usage is high: ${systemHealth.cpuUsage.toFixed(2)}%`,
                {
                    cpuUsage: systemHealth.cpuUsage,
                    threshold: this.thresholds.systemHealth.cpuUsageWarning
                }
            );
        }

        // Check API latency
        if (systemHealth.apiLatency >= this.thresholds.systemHealth.apiLatencyCritical) {
            await this.createAlertIfNotCooling(
                "system_latency_critical",
                AlertType.SYSTEM_HEALTH,
                AlertSeverity.CRITICAL,
                "Critical API Latency",
                `API latency is critically high: ${systemHealth.apiLatency.toFixed(2)} ms`,
                {
                    apiLatency: systemHealth.apiLatency,
                    threshold: this.thresholds.systemHealth.apiLatencyCritical
                }
            );
        } else if (systemHealth.apiLatency >= this.thresholds.systemHealth.apiLatencyWarning) {
            await this.createAlertIfNotCooling(
                "system_latency_warning",
                AlertType.SYSTEM_HEALTH,
                AlertSeverity.WARNING,
                "High API Latency",
                `API latency is high: ${systemHealth.apiLatency.toFixed(2)} ms`,
                {
                    apiLatency: systemHealth.apiLatency,
                    threshold: this.thresholds.systemHealth.apiLatencyWarning
                }
            );
        }
    }

    /**
   * Check payment metrics
   * @param payments Payment metrics
   */
    private async checkPaymentMetrics(payments): Promise<void> {
    // Check payment success rate
        if (payments.successRate <= this.thresholds.payments.successRateCritical) {
            await this.createAlertIfNotCooling(
                "payment_success_rate_critical",
                AlertType.PAYMENT_FAILURE,
                AlertSeverity.CRITICAL,
                "Critical Payment Success Rate",
                `Payment success rate is critically low: ${payments.successRate.toFixed(2)}%`,
                {
                    successRate: payments.successRate,
                    threshold: this.thresholds.payments.successRateCritical
                }
            );
        } else if (payments.successRate <= this.thresholds.payments.successRateWarning) {
            await this.createAlertIfNotCooling(
                "payment_success_rate_warning",
                AlertType.PAYMENT_FAILURE,
                AlertSeverity.WARNING,
                "Low Payment Success Rate",
                `Payment success rate is low: ${payments.successRate.toFixed(2)}%`,
                {
                    successRate: payments.successRate,
                    threshold: this.thresholds.payments.successRateWarning
                }
            );
        }

        // Check payment failure rate
        const failureRate: number =100 - payments.successRate;
        if (failureRate >= this.thresholds.payments.failureRateCritical) {
            await this.createAlertIfNotCooling(
                "payment_failure_rate_critical",
                AlertType.PAYMENT_FAILURE,
                AlertSeverity.CRITICAL,
                "Critical Payment Failure Rate",
                `Payment failure rate is critically high: ${failureRate.toFixed(2)}%`,
                {
                    failureRate,
                    threshold: this.thresholds.payments.failureRateCritical
                }
            );
        } else if (failureRate >= this.thresholds.payments.failureRateWarning) {
            await this.createAlertIfNotCooling(
                "payment_failure_rate_warning",
                AlertType.PAYMENT_FAILURE,
                AlertSeverity.WARNING,
                "High Payment Failure Rate",
                `Payment failure rate is high: ${failureRate.toFixed(2)}%`,
                {
                    failureRate,
                    threshold: this.thresholds.payments.failureRateWarning
                }
            );
        }
    }

    /**
   * Check verification metrics
   * @param verification Verification metrics
   */
    private async checkVerificationMetrics(verification): Promise<void> {
    // Check verification success rate
        if (verification.successRate <= this.thresholds.verification.successRateCritical) {
            await this.createAlertIfNotCooling(
                "verification_success_rate_critical",
                AlertType.VERIFICATION_FAILURE,
                AlertSeverity.CRITICAL,
                "Critical Verification Success Rate",
                `Verification success rate is critically low: ${verification.successRate.toFixed(2)}%`,
                {
                    successRate: verification.successRate,
                    threshold: this.thresholds.verification.successRateCritical
                }
            );
        } else if (verification.successRate <= this.thresholds.verification.successRateWarning) {
            await this.createAlertIfNotCooling(
                "verification_success_rate_warning",
                AlertType.VERIFICATION_FAILURE,
                AlertSeverity.WARNING,
                "Low Verification Success Rate",
                `Verification success rate is low: ${verification.successRate.toFixed(2)}%`,
                {
                    successRate: verification.successRate,
                    threshold: this.thresholds.verification.successRateWarning
                }
            );
        }

        // Check verification average time
        if (verification.averageTime >= this.thresholds.verification.averageTimeCritical) {
            await this.createAlertIfNotCooling(
                "verification_time_critical",
                AlertType.VERIFICATION_FAILURE,
                AlertSeverity.CRITICAL,
                "Critical Verification Time",
                `Verification average time is critically high: ${verification.averageTime.toFixed(2)} ms`,
                {
                    averageTime: verification.averageTime,
                    threshold: this.thresholds.verification.averageTimeCritical
                }
            );
        } else if (verification.averageTime >= this.thresholds.verification.averageTimeWarning) {
            await this.createAlertIfNotCooling(
                "verification_time_warning",
                AlertType.VERIFICATION_FAILURE,
                AlertSeverity.WARNING,
                "High Verification Time",
                `Verification average time is high: ${verification.averageTime.toFixed(2)} ms`,
                {
                    averageTime: verification.averageTime,
                    threshold: this.thresholds.verification.averageTimeWarning
                }
            );
        }
    }

    /**
   * Check webhook metrics
   * @param webhooks Webhook metrics
   */
    private async checkWebhookMetrics(webhooks): Promise<void> {
    // Check webhook delivery rate
        if (webhooks.deliveryRate <= this.thresholds.webhooks.deliveryRateCritical) {
            await this.createAlertIfNotCooling(
                "webhook_delivery_rate_critical",
                AlertType.WEBHOOK_FAILURE,
                AlertSeverity.CRITICAL,
                "Critical Webhook Delivery Rate",
                `Webhook delivery rate is critically low: ${webhooks.deliveryRate.toFixed(2)}%`,
                {
                    deliveryRate: webhooks.deliveryRate,
                    threshold: this.thresholds.webhooks.deliveryRateCritical
                }
            );
        } else if (webhooks.deliveryRate <= this.thresholds.webhooks.deliveryRateWarning) {
            await this.createAlertIfNotCooling(
                "webhook_delivery_rate_warning",
                AlertType.WEBHOOK_FAILURE,
                AlertSeverity.WARNING,
                "Low Webhook Delivery Rate",
                `Webhook delivery rate is low: ${webhooks.deliveryRate.toFixed(2)}%`,
                {
                    deliveryRate: webhooks.deliveryRate,
                    threshold: this.thresholds.webhooks.deliveryRateWarning
                }
            );
        }

        // Check webhook average latency
        if (webhooks.averageLatency >= this.thresholds.webhooks.averageLatencyCritical) {
            await this.createAlertIfNotCooling(
                "webhook_latency_critical",
                AlertType.WEBHOOK_FAILURE,
                AlertSeverity.CRITICAL,
                "Critical Webhook Latency",
                `Webhook average latency is critically high: ${webhooks.averageLatency.toFixed(2)} ms`,
                {
                    averageLatency: webhooks.averageLatency,
                    threshold: this.thresholds.webhooks.averageLatencyCritical
                }
            );
        } else if (webhooks.averageLatency >= this.thresholds.webhooks.averageLatencyWarning) {
            await this.createAlertIfNotCooling(
                "webhook_latency_warning",
                AlertType.WEBHOOK_FAILURE,
                AlertSeverity.WARNING,
                "High Webhook Latency",
                `Webhook average latency is high: ${webhooks.averageLatency.toFixed(2)} ms`,
                {
                    averageLatency: webhooks.averageLatency,
                    threshold: this.thresholds.webhooks.averageLatencyWarning
                }
            );
        }
    }

    /**
   * Check merchant-specific metrics
   */
    private async checkMerchantMetrics(): Promise<void> {
        try {
            // Get all active merchants
            const merchants: any =await prisma.merchant.findMany({
                where: {, isActive: true },
                select: {, id: true, name: true, email: true }
            });

            // Check each merchant's metrics
            for (const merchant of merchants) {
                // Get merchant's recent transactions
                const recentTransactions: any =await prisma.transaction.findMany({
                    where: {, merchantId: merchant.id,
                        createdAt: {, gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
                        }
                    }
                });

                // Skip if no recent transactions
                if (recentTransactions.length === 0) {
                    continue;
                }

                // Calculate success rate
                const successfulTransactions: any =recentTransactions.filter(
                    txn => txn.status === "completed"
                ).length;
                const successRate: any =(successfulTransactions / recentTransactions.length) * 100;

                // Check success rate
                if (successRate <= this.thresholds.payments.successRateCritical) {
                    await this.createAlertIfNotCooling(
                        `merchant_${merchant.id}_success_rate_critical`,
                        AlertType.PAYMENT_FAILURE,
                        AlertSeverity.CRITICAL,
                        `Critical Payment Success Rate for ${merchant.name}`,
                        `Payment success rate for merchant ${merchant.name} is critically low: ${successRate.toFixed(2)}%`,
                        {
                            merchantId: merchant.id,
                            merchantName: merchant.name,
                            successRate,
                            threshold: this.thresholds.payments.successRateCritical,
                            transactionCount: recentTransactions.length,
                            successfulTransactions
                        },
                        merchant.id
                    );
                } else if (successRate <= this.thresholds.payments.successRateWarning) {
                    await this.createAlertIfNotCooling(
                        `merchant_${merchant.id}_success_rate_warning`,
                        AlertType.PAYMENT_FAILURE,
                        AlertSeverity.WARNING,
                        `Low Payment Success Rate for ${merchant.name}`,
                        `Payment success rate for merchant ${merchant.name} is low: ${successRate.toFixed(2)}%`,
                        {
                            merchantId: merchant.id,
                            merchantName: merchant.name,
                            successRate,
                            threshold: this.thresholds.payments.successRateWarning,
                            transactionCount: recentTransactions.length,
                            successfulTransactions
                        },
                        merchant.id
                    );
                }
            }
        } catch (error) {
            logger.error("Error checking merchant metrics", { error });
        }
    }

    /**
   * Create an alert if not in cooldown period
   * @param alertKey Alert key for cooldown tracking
   * @param type Alert type
   * @param severity Alert severity
   * @param title Alert title
   * @param message Alert message
   * @param details Alert details
   * @param merchantId Merchant ID (optional)
   */
    private async createAlertIfNotCooling(
        alertKey: string,
        type: AlertType,
        severity: AlertSeverity,
        title: string,
        message: string,
        details: Record<string, any>,
        merchantId?: string
    ): Promise<void> {
        const now: any =Date.now();
        const lastAlertTime: any =this.lastAlertTimes.get(alertKey) || 0;

        // Check if alert is in cooldown period
        if (now - lastAlertTime < this.alertCooldown) {
            logger.debug(`Alert ${alertKey} is in cooldown period, skipping`);
            return;
        }

        // Create alert
        try {
            await this.alertService.createAlert({
                type,
                severity,
                title,
                message,
                source: "monitor",
                details,
                merchantId,
                notificationMethods: [
                    AlertNotificationMethod.DASHBOARD,
                    AlertNotificationMethod.EMAIL,
                    AlertNotificationMethod.WEBHOOK
                ]
            });

            // Update last alert time
            this.lastAlertTimes.set(alertKey, now);
            logger.info(`Created alert: ${title}`);
        } catch (error) {
            logger.error("Error creating alert", { error, alertKey, title });
        }
    }
}
