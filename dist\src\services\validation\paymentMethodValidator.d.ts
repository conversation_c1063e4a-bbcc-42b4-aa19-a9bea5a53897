import { PaymentMethodType } from "../../types/paymentMethodTypes";
/**
 * Validation result
 */
interface ValidationResult {
    isValid: boolean;
    error?: string;
}
/**
 * Validate payment method configuration based on type
 * @param type Payment method type
 * @param config Configuration object
 */
export declare function validatePaymentMethodConfig(type: PaymentMethodType, config: any): ValidationResult;
/**
 * Validate wallet address format
 * @param address Wallet address
 * @param network Network type
 */
export declare function validateWalletAddress(address: string, network: string): ValidationResult;
/**
 * Validate transaction hash format
 * @param txHash Transaction hash
 * @param network Network type
 */
export declare function validateTransactionHash(txHash: string, network: string): ValidationResult;
export {};
//# sourceMappingURL=paymentMethodValidator.d.ts.map