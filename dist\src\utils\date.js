"use strict";
// jscpd:ignore-file
/**
 * Date utility functions
 * Direct re-exports from shared DateUtils to eliminate duplication
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatRelative = exports.parseDate = exports.isValidDate = exports.endOfMonth = exports.startOfMonth = exports.endOfDay = exports.startOfDay = exports.diffInDays = exports.addYears = exports.addMonths = exports.addDays = exports.formatDateTime = exports.formatDate = void 0;
const utils_1 = require("../utils");
// Format functions with specific formats
const formatDate = (date) => utils_1.DateUtils.format(date, 'yyyy-MM-dd');
exports.formatDate = formatDate;
const formatDateTime = (date) => utils_1.DateUtils.format(date, 'yyyy-MM-dd HH:mm:ss');
exports.formatDateTime = formatDateTime;
// Direct re-exports with specific unit types
const addDays = (date, days) => utils_1.DateUtils.add(date, days, 'day');
exports.addDays = addDays;
const addMonths = (date, months) => utils_1.DateUtils.add(date, months, 'month');
exports.addMonths = addMonths;
const addYears = (date, years) => utils_1.DateUtils.add(date, years, 'year');
exports.addYears = addYears;
const diffInDays = (date1, date2) => Math.abs(utils_1.DateUtils.diff(date1, date2, 'day'));
exports.diffInDays = diffInDays;
const startOfDay = (date) => utils_1.DateUtils.startOf(date, 'day');
exports.startOfDay = startOfDay;
const endOfDay = (date) => utils_1.DateUtils.endOf(date, 'day');
exports.endOfDay = endOfDay;
const startOfMonth = (date) => utils_1.DateUtils.startOf(date, 'month');
exports.startOfMonth = startOfMonth;
const endOfMonth = (date) => utils_1.DateUtils.endOf(date, 'month');
exports.endOfMonth = endOfMonth;
// Utility functions
const isValidDate = (date) => !isNaN(date.getTime());
exports.isValidDate = isValidDate;
const parseDate = (dateStr) => {
    const date = utils_1.DateUtils.toDate(dateStr);
    return (0, exports.isValidDate)(date) ? date : null;
};
exports.parseDate = parseDate;
// Use DateFormatterUtils for relative formatting
const formatRelative = (date) => utils_1.DateFormatterUtils.formatRelativeTime(date);
exports.formatRelative = formatRelative;
