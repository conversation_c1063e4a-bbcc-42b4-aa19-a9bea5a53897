"use strict";
// jscpd:ignore-file
/**
 * Migration Manager
 *
 * This utility provides a robust system for managing database migrations.
 * It ensures that migrations are applied in the correct order and only once.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationManager = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const pg_1 = require("pg");
const logger_1 = require("../lib/logger");
const database_config_1 = require("../config/database.config");
const types_1 = require("../types");
// Migration manager class
class MigrationManager {
    /**
     * Constructor
     * @param migrationsDir Path to migrations directory
     */
    constructor(migrationsDir) {
        this.migrationTableName = "_migrations";
        this.migrationsDir = migrationsDir;
        const dbConfig = (0, database_config_1.getDatabaseConfig)();
        this.client = new pg_1.Client(dbConfig);
        console.log(`Migration manager initialized for database: ${dbConfig.database}`);
        console.log(`Migrations directory: ${this.migrationsDir}`);
    }
    /**
   * Initialize migration manager
   */
    async initialize() {
        try {
            await this.client.connect();
            logger_1.logger.info("Connected to database");
            // Create migrations table if it doesn't exist
            await this.createMigrationsTable();
        }
        catch (error) {
            logger_1.logger.error("Failed to initialize migration manager:", error);
            throw error;
        }
    }
    /**
     * Create migrations table
     */
    async createMigrationsTable() {
        try {
            const query = `
                CREATE TABLE IF NOT EXISTS "${this.migrationTableName}" (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    applied_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    error TEXT
                )
            `;
            await this.client.query(query);
            console.log(`Migrations table ${this.migrationTableName} created or already exists`);
        }
        catch (error) {
            logger_1.logger.error("Failed to create migrations table:", error);
            throw error;
        }
    }
    /**
   * Get applied migrations
   * @returns List of applied migrations
   */
    async getAppliedMigrations() {
        try {
            const query = `
        SELECT name, status, applied_at as "appliedAt", error
        FROM "${this.migrationTableName}"
        ORDER BY applied_at ASC;
      `;
            const result = await this.client.query(query);
            return result.rows.map(row => ({
                name: row.name,
                status: row.status,
                appliedAt: row.applied_at // Fixed: using applied_at instead of appliedAt,
                , // Fixed: using applied_at instead of appliedAt,
                error: row.error
            }));
        }
        catch (error) {
            logger_1.logger.error("Failed to get applied migrations:", error);
            throw error;
        }
    }
    /**
   * Get pending migrations
   * @returns List of pending migrations
   */
    async getPendingMigrations() {
        try {
            // Get applied migrations
            const appliedMigrations = await this.getAppliedMigrations();
            const appliedMigrationNames = appliedMigrations
                .filter(m => m.status === types_1.MigrationStatus.APPLIED)
                .map(m => m.name);
            // Get available migrations
            const availableMigrations = this.getAvailableMigrations();
            // Filter out applied migrations
            return availableMigrations.filter(name => !appliedMigrationNames.includes(name));
        }
        catch (error) {
            logger_1.logger.error("Failed to get pending migrations:", error);
            throw error;
        }
    }
    /**
   * Get available migrations
   * @returns List of available migrations
   */
    getAvailableMigrations() {
        try {
            // Check if migrations directory exists
            if (!fs_1.default.existsSync(this.migrationsDir)) {
                logger_1.logger.warn(`Migrations directory does not exist: ${this.migrationsDir}`);
                return [];
            }
            // Get migration directories
            return fs_1.default.readdirSync(this.migrationsDir)
                .filter((dir));
            {
                const dirPath = path_1.default.join(this.migrationsDir, dir);
                const isMigrationDir = fs_1.default.statSync(dirPath).isDirectory();
                const hasMigrationFile = fs_1.default.existsSync(path_1.default.join(dirPath, "migration.sql"));
                return isMigrationDir && hasMigrationFile;
            }
            sort(); // Sort to ensure migrations are applied in order
        }
        catch (error) {
            logger_1.logger.error("Failed to get available migrations:", error);
            throw error;
        }
    }
    /**
   * Apply migrations
   * @returns List of applied migrations
   */
    async applyMigrations() {
        try {
            // Get pending migrations
            const pendingMigrations = await this.getPendingMigrations();
            if (pendingMigrations.length === 0) {
                logger_1.logger.info("No pending migrations to apply");
                return [];
            }
            console.log(`Applying ${pendingMigrations.length} pending migrations...`);
            const appliedMigrations = [];
            // Apply migrations
            for (const migrationName of pendingMigrations) {
                try {
                    console.log(`Applying migration: ${migrationName}`);
                    const migrationPath = path_1.default.join(this.migrationsDir, migrationName, "migration.sql");
                    // Check if migration file exists
                    if (!fs_1.default.existsSync(migrationPath)) {
                        logger_1.logger.warn(`Migration file not found: ${migrationPath}`);
                        continue;
                    }
                    // Read migration SQL
                    const migrationSql = fs_1.default.readFileSync(migrationPath, "utf8");
                    // Start transaction
                    await this.client.query("BEGIN");
                    try {
                        // Execute migration SQL
                        await this.client.query(migrationSql);
                        // Record migration as applied
                        await this.client.query(`INSERT INTO "${this.migrationTableName}" (name, status) VALUES ($1, $2)`, [migrationName, types_1.MigrationStatus.APPLIED]);
                        // Commit transaction
                        await this.client.query("COMMIT");
                        console.log(`Migration ${migrationName} applied successfully`);
                        appliedMigrations.push({
                            name: migrationName,
                            status: types_1.MigrationStatus.APPLIED,
                            appliedAt: new Date()
                        });
                    }
                    catch (error) {
                        // Rollback transaction on error
                        await this.client.query("ROLLBACK");
                        // Record migration as failed
                        await this.client.query(`INSERT INTO "${this.migrationTableName}" (name, status, error) VALUES ($1, $2, $3)`, [migrationName, types_1.MigrationStatus.FAILED, error.message]);
                        logger_1.logger.error(`Failed to apply migration ${migrationName}:`, error);
                        appliedMigrations.push({
                            name: migrationName,
                            status: types_1.MigrationStatus.FAILED,
                            appliedAt: new Date(),
                            error: error.message
                        });
                    }
                }
                catch (error) {
                    logger_1.logger.error(`Error processing migration ${migrationName}:`, error);
                }
            }
            return appliedMigrations;
        }
        catch (error) {
            logger_1.logger.error("Failed to apply migrations:", error);
            throw error;
        }
    }
    /**
   * Close migration manager
   */
    async close() {
        try {
            await this.client.end();
            logger_1.logger.info("Migration manager closed");
        }
        catch (error) {
            logger_1.logger.error("Failed to close migration manager:", error);
        }
    }
}
exports.MigrationManager = MigrationManager;
// Export default migration manager
exports.default = MigrationManager;
//# sourceMappingURL=migration-manager.js.map