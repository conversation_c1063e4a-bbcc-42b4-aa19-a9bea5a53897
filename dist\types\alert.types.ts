// jscpd:ignore-file
/**
 * Alert Types
 *
 * This file contains all alert-related type definitions to ensure consistency
 * across the application.
 */

/**
 * Alert severity levels
 */
export enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',

  // Legacy mappings for backward compatibility
  INFO = 'LOW',
  WARNING = 'MEDIUM',
  ERROR = 'HIGH',
}

/**
 * Alert types
 */
export enum AlertType {
  SECURITY = 'SECURITY',
  PAYMENT = 'PAYMENT',
  SYSTEM = 'SYSTEM',
  FRAUD = 'FRAUD',
  VERIFICATION = 'VERIFICATION',
  OTHER = 'OTHER',

  // Legacy mappings for backward compatibility
  SYSTEM_HEALTH = 'SYSTEM',
  PAYMENT_FAILURE = 'PAYMENT',
  VERIFICATION_FAILURE = 'VERIFICATION',
  WEBHOOK_FAILURE = 'OTHER',
  PERFORMANCE = 'SYSTEM',
  CUSTOM = 'OTHER',
  DATABASE = 'SYSTEM',
  APPLICATION = 'SYSTEM',
}

/**
 * Alert status
 */
export enum AlertStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED',
  IGNORED = 'IGNORED',

  // Legacy mappings for backward compatibility
  ACTIVE = 'OPEN',
  ACKNOWLEDGED = 'IN_PROGRESS',
}

/**
 * Alert notification method
 */
export enum AlertNotificationMethod {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  WEBHOOK = 'WEBHOOK',
  DASHBOARD = 'DASHBOARD',
  TELEGRAM = 'TELEGRAM',
  PUSH = 'PUSH',
  SLACK = 'SLACK',
}

/**
 * Alert Level (legacy - use AlertSeverity instead)
 */
export enum AlertLevel {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
  CRITICAL = "critical",
}

/**
 * Alert notification channel (alias for AlertNotificationMethod)
 */
export const AlertChannel = AlertNotificationMethod;

/**
 * Alert data
 */
export interface AlertData {
  type: AlertType;
  severity: AlertSeverity;
  title: string;
  message: string;
  source?: string;
  details?: Record<string, any>;
  merchantId?: string;
  notificationMethods?: AlertNotificationMethod[];
  timestamp?: Date;
}

/**
 * Alert
 */
export interface Alert {
  id: string;
  type: AlertType;
  severity: AlertSeverity;
  status: AlertStatus;
  title: string;
  message: string;
  details?: Record<string, any>;
  source?: string;
  merchantId?: string;
  userId?: string;
  transactionId?: string;
  resolvedById?: string;
  resolvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Alert notification
 */
export interface AlertNotification {
  id: string;
  alert: AlertData;
  channels: AlertNotificationMethod[];
  status: "pending" | "sent" | "failed";
  createdAt: Date;
  sentAt?: Date;
  error?: string;
}

/**
 * Alert notification configuration
 */
export interface AlertNotificationConfig {
  email: {
    enabled: boolean;
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
    from: string;
    recipients: string[];
  };
  sms: {
    enabled: boolean;
    accountSid: string;
    authToken: string;
    from: string;
    recipients: string[];
  };
  slack: {
    enabled: boolean;
    webhookUrl: string;
    channel: string;
  };
  minAlertLevel: AlertSeverity;
}

/**
 * Alert notification options
 */
export interface AlertNotificationOptions {
  level: AlertSeverity;
  subject: string;
  message: string;
  data?: Record<string, any>;
}

/**
 * Legacy Alert interface (for backward compatibility)
 */
export interface LegacyAlert {
  level: AlertLevel;
  message: string;
  timestamp: string;
  data?: Record<string, any>;
  resolved?: boolean;
  resolvedAt?: string;
}

/**
 * Alert threshold configuration
 */
export interface AlertThresholds {
  // System health thresholds
  systemHealth: {
    memoryUsageWarning: number; // MB
    memoryUsageCritical: number; // MB
    cpuUsageWarning: number; // %
    cpuUsageCritical: number; // %
    apiLatencyWarning: number; // ms
    apiLatencyCritical: number; // ms
  };

  // Payment thresholds
  payments: {
    successRateWarning: number; // %
    successRateCritical: number; // %
    failureRateWarning: number; // %
    failureRateCritical: number; // %
  };

  // Verification thresholds
  verification: {
    successRateWarning: number; // %
    successRateCritical: number; // %
    failureRateWarning: number; // %
    failureRateCritical: number; // %
    averageTimeWarning: number; // ms
    averageTimeCritical: number; // ms
  };
}

// Export all types
export default {
  AlertSeverity,
  AlertType,
  AlertStatus,
  AlertNotificationMethod,
  AlertChannel
};
