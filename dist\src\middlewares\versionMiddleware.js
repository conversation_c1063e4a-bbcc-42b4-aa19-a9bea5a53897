"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.versionMiddleware = versionMiddleware;
exports.versionHeaderMiddleware = versionHeaderMiddleware;
const VersionRegistry_1 = require("../core/VersionRegistry");
/**
 * Version middleware
 * This middleware handles API versioning
 */
function versionMiddleware(req, res, next) {
    const versionRegistry = VersionRegistry_1.VersionRegistry.getInstance();
    // Get version from header or query parameter
    const headerVersion = req.headers["x-api-version"];
    const queryVersion = req.query.version;
    // Get version from URL path
    const urlVersion = req.path.match(/^\/api\/(v\d+)/)?.[1];
    // Determine version to use
    let version = urlVersion || headerVersion || queryVersion || versionRegistry.getCurrentVersion();
    // Check if version exists
    if (!versionRegistry.hasVersion(version)) {
        // Use current version if version doesn't exist
        version = versionRegistry.getCurrentVersion();
    }
    // Check if version is sunset
    if (versionRegistry.isVersionSunset(version)) {
        // Return 410 Gone for sunset versions
        return res.status(410).json({
            success: false,
            error: { message: `API version ${version} has been sunset. Please use a supported version.`,
                code: "VERSION_SUNSET",
                supportedVersions: versionRegistry.getActiveVersions().map(v => v.version)
            }
        });
    }
    // Check if version is deprecated
    if (versionRegistry.isVersionDeprecated(version)) {
        // Add deprecation header
        const versionInfo = versionRegistry.getVersion(version);
        const sunsetDate = versionInfo.sunsetDate?.toISOString() || "unknown";
        res.setHeader("Deprecation", "true");
        res.setHeader("Sunset", sunsetDate);
        res.setHeader("Link", `</api/versions>; rel="deprecation"; type="application/json"`);
    }
    // Set version on request
    req.apiVersion = version;
    // Add version header to response
    res.setHeader("X-API-Version", version);
    // Continue
    next();
}
/**
 * Version header middleware
 * This middleware adds version headers to responses
 */
function versionHeaderMiddleware(req, res, next) {
    const versionRegistry = VersionRegistry_1.VersionRegistry.getInstance();
    // Get current version
    const currentVersion = versionRegistry.getCurrentVersion();
    // Add version header to response
    res.setHeader("X-API-Version", currentVersion);
    // Continue
    next();
}
exports.default = versionMiddleware;
//# sourceMappingURL=versionMiddleware.js.map