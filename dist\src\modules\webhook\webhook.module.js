"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookModule = void 0;
const module_1 = require("../../core/module");
const utils_1 = require("../../utils");
const auth_middleware_1 = require("../../middlewares/auth.middleware");
/**
 * Webhook Module
 * This module provides webhook functionality with zero duplication
 */
class WebhookModule {
    /**
     * Create a new webhook module
     */
    constructor() {
        this.moduleRegistry = new module_1.ModuleRegistry();
        this.container = new module_1.Container();
        // Create module factory
        this.moduleFactory = new module_1.ModuleFactory('webhook', 'Webhook');
        // Get router, repository, service, and controller from factory
        const { router, repository, service, controller } = this.moduleFactory.build();
        // Configure router
        router
            .addRoute('get', '/:id', controller.getById)
            .addRoute('post', '/', controller.create)
            .addRoute('put', '/:id', controller.update)
            .addRoute('delete', '/:id', controller.delete)
            .addRoute('get', '/merchant/:merchantId', controller.getWebhooksByMerchantId)
            .addRoute('post', '/:id/trigger', controller.triggerWebhook)
            .addMiddleware(auth_middleware_1.authMiddleware);
        // Add custom repository methods
        this.moduleFactory.addRepositoryMethod('findByMerchantId', async (merchantId, options = {}) => {
            try {
                return await repository.findByFieldWithPagination('merchantId', merchantId, options);
            }
            catch (error) {
                utils_1.logger.error(`Error finding webhooks by merchant ID ${merchantId}:`, error);
                throw utils_1.ErrorFactory.handle(error);
            }
        });
        this.moduleFactory.addServiceMethod('triggerWebhook', async (webhookId, payload) => {
            try {
                // Get webhook
                const webhook = await service.getById(webhookId);
                // Check if webhook exists
                // jscpd:ignore-end
                if (!webhook) {
                    throw utils_1.ErrorFactory.notFound('Webhook', webhookId);
                }
                // Deliver webhook
                // Implementation would depend on the specific delivery process
                // This is a placeholder implementation
                const deliveryId = `del_${Date.now()}`;
                utils_1.logger.info(`Webhook triggered: ${webhookId}`, {
                    webhookId,
                    deliveryId,
                    url: webhook.url
                });
                return {
                    success: true,
                    message: 'Webhook delivered successfully',
                    deliveryId
                };
            }
            catch (error) {
                utils_1.logger.error(`Error triggering webhook ${webhookId}:`, error);
                throw utils_1.ErrorFactory.handle(error);
            }
        });
        // Add custom controller methods
        this.moduleFactory.addControllerMethod('getWebhooksByMerchantId', async (req, res) => {
            try {
                // Check authorization
                const { userRole, merchantId } = req.user;
                // Get merchant ID from params
                const { merchantId: requestedMerchantId } = req.params;
                // Check if user has permission to view these webhooks
                if (userRole !== 'ADMIN' && merchantId !== requestedMerchantId) {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to view these webhooks');
                }
                // Parse pagination parameters
                const limit = parseInt(req.query.limit) || 10;
                const page = parseInt(req.query.page) || 1;
                const offset = (page - 1) * limit;
                // Get webhooks
                const webhooks = await service.findByMerchantId(requestedMerchantId, { limit, offset });
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: webhooks
                });
            }
            catch (error) {
                utils_1.logger.error(`Error getting webhooks by merchant ID:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while getting webhooks'
                });
            }
        });
        this.moduleFactory.addControllerMethod('triggerWebhook', async (req, res) => {
            try {
                // Check authorization
                const { userRole } = req.user;
                // Only admins and merchants can trigger webhooks
                if (userRole !== 'ADMIN' && userRole !== 'MERCHANT') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to trigger webhooks');
                }
                // Get webhook ID from params
                const { id } = req.params;
                // Get payload from request body
                const payload = req.body;
                // Trigger webhook
                const result = await service.triggerWebhook(id, payload);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                utils_1.logger.error(`Error triggering webhook:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while triggering webhook'
                });
            }
        });
        // Create module
        this.module = {
            name: 'webhook',
            router,
            repository,
            service,
            controller,
            dependencies: [],
            initialize: async () => {
                utils_1.logger.info('Initializing webhook module');
                // Register dependencies
                this.container.registerSingleton('webhookRepository', () => repository);
                this.container.registerSingleton('webhookService', () => service);
                this.container.registerSingleton('webhookController', () => controller);
                utils_1.logger.info('Webhook module initialized');
            }
        };
        // Register the module
        this.moduleRegistry.registerModule(this.module);
    }
    /**
     * Get the module
     * @returns Webhook module
     */
    getModule() {
        return this.module;
    }
}
exports.WebhookModule = WebhookModule;
//# sourceMappingURL=webhook.module.js.map