"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toBeNaN = toBeNaN;
function toBeNaN(actual) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, matcherHint } = this.utils;
    const pass = isNaN(actual);
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toBeNaN', 'received', '') +
                '\n\n' +
                'Expected value to be a number received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toBeNaN', 'received', '') +
                '\n\n' +
                'Expected value to not be a number received:\n' +
                `  ${printReceived(actual)}`,
    };
}
