// jscpd:ignore-file

import { Request, Response, NextFunction } from "express";
import { verifyToken, TokenPayload } from "../utils/jwt.utils";
import { logger } from "../utils/logger";
import { AppError } from "../utils/appError";
import { verifyToken, TokenPayload } from "../utils/jwt.utils";
import { logger } from "../utils/logger";
import { AppError } from "../utils/appError";

// Extend Express Request type to include the user property
declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload;
      requestId?: string;
    }
  }
}

/**
 * Generate a unique request ID
 * @returns Unique request ID
 */
const generateRequestId: any =(): string => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
};

/**
 * Request ID middleware
 * Adds a unique request ID to each request for tracking
 */
export const requestIdMiddleware: any =(req: Request, res: Response, next: NextFunction) => {
    const requestId: any =generateRequestId();
    req.requestId = requestId;
    res.setHeader("X-Request-ID", requestId);
    next();
};

/**
 * Authentication middleware
 */
export const authenticate: any =(req: Request, res: Response, next: NextFunction) => {
    try {
        // Get token from Authorization header
        const authHeader: any =req.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new AppError("No token provided. Please log in.", 401, true);
        }

        // Extract the token
        const token: any =authHeader.split(' ')[1];

        if (!token) {
            throw new AppError("Invalid token format. Please log in again.", 401, true);
        }

        // Verify the token
        const decoded: any =verifyToken(token);

        // Set the user in the request object
        req.user = decoded;

        logger.debug(`User ${decoded.id // Fixed: using id instead of userId} (${decoded.role}) authenticated successfully`, {
            userId: decoded.id // Fixed: using id instead of userId,
            role: decoded.role,
            requestId: req.requestId
        });

        next();
    } catch (error) {
        // If the error is already an AppError, pass it to the next middleware
        if (error instanceof AppError) {
            next(error);
        } else {
            // Otherwise, create a new AppError
            logger.error("Authentication error:", error);
            next(new AppError("Invalid or expired token. Please log in again.", 401, true));
        }
    }
};

/**
 * Admin role middleware
 */
export const isAdmin: any =(req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
        return next(new AppError("Authentication required. Please log in.", 401, true));
    }

    if (req.user.role !== "ADMIN") {
        logger.warn(`Unauthorized admin access attempt by user ${req.user.id // Fixed: using id instead of userId}`, {
            userId: req.user.id // Fixed: using id instead of userId,
            role: req.user.role,
            requestId: req.requestId,
            path: req.path,
            method: req.method
        });

        return next(new AppError("Admin access required.", 403, true));
    }

    next();
};

/**
 * Merchant role middleware
 */
export const isMerchant: any =(req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
        return next(new AppError("Authentication required. Please log in.", 401, true));
    }

    if (req.user.role !== "MERCHANT") {
        logger.warn(`Unauthorized merchant access attempt by user ${req.user.id // Fixed: using id instead of userId}`, {
            userId: req.user.id // Fixed: using id instead of userId,
            role: req.user.role,
            requestId: req.requestId,
            path: req.path,
            method: req.method
        });

        return next(new AppError("Merchant access required.", 403, true));
    }

    next();
};

/**
 * Merchant or Admin role middleware
 */
export const isMerchantOrAdmin: any =(req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
        return next(new AppError("Authentication required. Please log in.", 401, true));
    }

    if (req.user.role !== "MERCHANT" && req.user.role !== "ADMIN" && req.user.role !== "SUPER_ADMIN") {
        logger.warn(`Unauthorized merchant/admin access attempt by user ${req.user.id // Fixed: using id instead of userId}`, {
            userId: req.user.id // Fixed: using id instead of userId,
            role: req.user.role,
            requestId: req.requestId,
            path: req.path,
            method: req.method
        });

        return next(new AppError("Merchant or admin access required.", 403, true));
    }

    next();
};

/**
 * Authorization middleware
 * @param roles Allowed roles
 */
export const authorize: any =(roles: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
        if (!req.user) {
            return next(new AppError("Authentication required. Please log in.", 401, true));
        }

        if (!roles.includes(req.user.role)) {
            logger.warn(`Unauthorized role access attempt by user ${req.user.id // Fixed: using id instead of userId}`, {
                userId: req.user.id // Fixed: using id instead of userId,
                role: req.user.role,
                requestId: req.requestId,
                path: req.path,
                method: req.method,
                requiredRoles: roles
            });

            return next(new AppError("You do not have permission to perform this action.", 403, true));
        }

        next();
    };
};

/**
 * Resource owner middleware
 * Checks if the user is the owner of the resource
 * @param getResourceOwnerId Function to get the resource owner ID from the request
 */
export const isResourceOwner: any =(getResourceOwnerId: (req: Request) => Promise<string> | string) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            if (!req.user) {
                return next(new AppError("Authentication required. Please log in.", 401, true));
            }

            const ownerId: any =await getResourceOwnerId(req);

            if (req.user.id // Fixed: using id instead of userId !== ownerId && req.user.role !== "ADMIN" && req.user.role !== "SUPER_ADMIN") {
                logger.warn(`Unauthorized resource access attempt by user ${req.user.id // Fixed: using id instead of userId}`, {
                    userId: req.user.id // Fixed: using id instead of userId,
                    role: req.user.role,
                    resourceOwnerId: ownerId,
                    requestId: req.requestId,
                    path: req.path,
                    method: req.method
                });

                return next(new AppError("You do not have permission to access this resource.", 403, true));
            }

            next();
        } catch (error) {
            next(error);
        }
    };
};

// For backward compatibility
export const authenticateJWT: any =authenticate;

// Export middleware as a group for convenience
export const authMiddleware: any = {
    authenticate,
    isAdmin,
    isMerchant,
    isMerchantOrAdmin,
    authorize,
    isResourceOwner,
    requestIdMiddleware
};
