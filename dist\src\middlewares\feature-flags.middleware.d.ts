/**
 * Feature Flags Middleware
 *
 * This middleware provides functions for checking feature flags in requests
 * to ensure complete isolation between production and demo environments.
 */
/**
 * Check if a feature is enabled
 * @param featureName Feature name
 * @returns Middleware function
 */
export declare const checkFeatureEnabled: any;
/**
 * Add feature flags to response
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export declare const addFeatureFlagsToResponse: any;
/**
 * Check if real payments are enabled
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export declare const checkRealPaymentsEnabled: any;
declare const _default: {
    checkFeatureEnabled: any;
    addFeatureFlagsToResponse: any;
    checkRealPaymentsEnabled: any;
};
export default _default;
//# sourceMappingURL=feature-flags.middleware.d.ts.map