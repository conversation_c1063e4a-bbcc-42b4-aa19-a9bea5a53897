/**
 * Verification Alert Service
 *
 * This service monitors verification metrics and generates alerts when issues are detected.
 */
import { BaseService } from '../../shared/modules/services/BaseService';
/**
 * Verification alert service
 */
export declare class VerificationAlertService extends BaseService {
    private prisma;
    private notificationService;
    private thresholds;
    /**
     * Constructor
     */
    constructor();
    /**
     * Check for alerts based on verification metrics
     */
    checkForAlerts(): Promise<void>;
    /**
     * Check for high failure rate
     * @param metrics Verification metrics
     */
    private checkFailureRate;
    /**
     * Check for high latency
     * @param metrics Verification metrics
     */
    private checkLatency;
    /**
     * Check for repeated errors
     * @param metrics Verification metrics
     */
    private checkRepeatedErrors;
    /**
     * Check for payment method issues
     * @param metrics Verification metrics
     */
    private checkPaymentMethodIssues;
}
//# sourceMappingURL=verification-alert.service.d.ts.map