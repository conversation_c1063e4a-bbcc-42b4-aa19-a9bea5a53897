"use strict";
// jscpd:ignore-file
/**
 * Users Module
 *
 * This module handles user management.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const ModuleFactory_1 = require("../../factories/ModuleFactory");
const logger_1 = require("../../utils/logger");
/**
 * Users Module
 */
class UsersModule extends ModuleFactory_1.BaseModule {
    /**
     * Constructor
     */
    constructor() {
        super('UsersModule');
    }
    /**
     * Initialize the module
     */
    initialize() {
        logger_1.logger.info('Initializing UsersModule');
        // Get controllers
        const userController = this.controllerFactory.getController('user');
        // Set up routes
        this.router.get('/', userController.getAll);
        this.router.get('/:id', userController.getById);
        this.router.post('/', userController.create);
        this.router.put('/:id', userController.update);
        this.router.delete('/:id', userController.delete);
        this.router.get('/me', userController.getProfile);
        this.router.put('/me', userController.updateProfile);
        this.router.put('/me/password', userController.changePassword);
        logger_1.logger.info('UsersModule initialized');
    }
}
// Export the module
exports.default = new UsersModule();
//# sourceMappingURL=users.module.js.map