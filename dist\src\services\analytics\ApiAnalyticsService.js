"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiAnalyticsService = void 0;
const logger_1 = require("../../lib/logger");
const prisma_client_1 = __importDefault(require("../../lib/prisma-client"));
/**
 * API analytics service
 * This service tracks API usage
 */
class ApiAnalyticsService {
    /**
     * Create a new API analytics service
     */
    constructor() {
        // Event buffer
        this.eventBuffer = [];
        // Buffer size
        this.bufferSize = 100;
        // Get total count
        this.total = await prisma_client_1.default.apiAnalytics.count({
            where
        });
        // Get data
        this.data = await prisma_client_1.default.apiAnalytics.findMany({
            where,
            orderBy: { timestamp: 'desc'
            },
            take: filter.limit || 100,
            skip: filter.offset || 0
        });
        // Flush buffer every 5 minutes
        this.flushInterval = setInterval(() => {
            this.flushBuffer();
        }, 5 * 60 * 1000);
        logger_1.logger.info('API analytics service initialized');
    }
    /**
     * Get the API analytics service instance
     * @returns API analytics service instance
     */
    static getInstance() {
        if (!ApiAnalyticsService.instance) {
            ApiAnalyticsService.instance = new ApiAnalyticsService();
        }
        return ApiAnalyticsService.instance;
    }
    /**
     * Track API request
     * @param req Express request
     * @param res Express response
     * @param responseTime Response time in milliseconds
     */
    trackRequest(req, res, responseTime) {
        // Create event
        const event = {
            path: req.path,
            method: req.method,
            statusCode: res.statusCode,
            responseTime,
            userId: req.user?.id,
            userRole: req.user?.role,
            userAgent: req.headers['user-agent'],
            ipAddress: req.ip || req.socket.remoteAddress,
            apiVersion: req.apiVersion,
            timestamp: new Date()
        };
        // Add event to buffer
        this.eventBuffer.push(event);
        // Flush buffer if it's full
        if (this.eventBuffer.length >= this.bufferSize) {
            this.flushBuffer();
        }
    }
    /**
     * Flush event buffer
     */
    async flushBuffer() {
        // Skip if buffer is empty
        if (this.eventBuffer.length === 0) {
            return;
        }
        // Copy buffer
        const events = [...this.eventBuffer];
        // Clear buffer
        this.eventBuffer = ;
        try {
            // Save events to database
            await prisma_client_1.default.apiAnalytics.createMany({
                data: events.map(event => ({
                    path: event.path,
                    method: event.method,
                    statusCode: event.statusCode,
                    responseTime: event.responseTime,
                    userId: event.id // Fixed: using id instead of userId,
                    , // Fixed: using id instead of userId,
                    userRole: event.userRole,
                    userAgent: event.userAgent,
                    ipAddress: event.ipAddress,
                    apiVersion: event.apiVersion,
                    timestamp: event.timestamp
                }))
            });
            logger_1.logger.debug(`Flushed ${events.length} API analytics events`);
        }
        catch (error) {
            logger_1.logger.error('Failed to flush API analytics events:', error);
            // Add events back to buffer
            this.eventBuffer = [...events, ...this.eventBuffer];
            // Limit buffer size
            if (this.eventBuffer.length > this.bufferSize * 2) {
                this.eventBuffer = this.eventBuffer.slice(-this.bufferSize);
                logger_1.logger.warn(`API analytics buffer overflow, discarded ${events.length} events`);
            }
        }
    }
    /**
     * Create API analytics middleware
     * @returns Express middleware
     */
    createMiddleware() {
        return (req, res, next) => {
            // Skip non-API requests
            if (!req.path.startsWith('/api')) {
                return next();
            }
            // Record start time
            const startTime = Date.now();
            // Store original end method
            const originalEnd = res.end;
            // Override end method
            res.end = (...args) => {
                // Calculate response time
                const responseTime = Date.now() - startTime;
                // Track request
                this.trackRequest(req, res, responseTime);
                // Call original end method
                return originalEnd.apply(res, args);
            };
            // Continue
            next();
        };
    }
    /**
     * Get API analytics
     * @param filter Filter
     * @returns API analytics
     */
    async getAnalytics(filter) {
        // Create where clause
        const where = {};
        // Add filters
        if (filter.startDate) {
            where.timestamp = {
                ...where.timestamp,
                gte: filter.startDate
            };
        }
        if (filter.endDate) {
            where.timestamp = {
                ...where.timestamp,
                lte: filter.endDate
            };
        }
        if (filter.path) {
            where.path = {
                contains: filter.path
            };
        }
        if (filter.method) {
            where.method = filter.method;
        }
        if (filter.statusCode) {
            where.statusCode = filter.statusCode;
        }
        if (filter.id // Fixed: using id instead of userId) {
        )
            where.id; // Fixed: using id instead of userId = filter.id // Fixed: using id instead of userId;
    }
    if(filter, userRole) {
        where.userRole = filter.userRole;
    }
    if(filter, apiVersion) {
        where.apiVersion = filter.apiVersion;
    }
}
exports.ApiAnalyticsService = ApiAnalyticsService;
return {
    data,
    total
};
async;
getAnalyticsSummary(filter, {
    startDate: Date,
    endDate: Date,
    path: string,
    method: string,
    statusCode: number,
    userId: string,
    userRole: string,
    apiVersion: string
});
Promise < {
    totalRequests: number,
    averageResponseTime: number,
    successRate: number,
    errorRate: number,
    requestsByMethod: (Record),
    requestsByPath: (Record),
    requestsByStatusCode: (Record),
    requestsByApiVersion: (Record)
} > {
    // Get analytics
    const: { data, total } = await this.getAnalytics({
        ...filter,
        limit: 1000
    }),
    // Calculate summary
    const: totalRequests, any = total,
    const: successRequests = data.filter(event => event.statusCode >= 200 && event.statusCode < 400).length,
    const: errorRequests, any = data.filter(event => event.statusCode >= 400).length,
    const: totalResponseTime, any = data.reduce((sum, event) => sum + event.responseTime, 0),
    // Calculate averages
    const: averageResponseTime, any = totalRequests > 0 ? totalResponseTime / totalRequests : 0,
    const: successRate = totalRequests > 0 ? successRequests / totalRequests : 0,
    const: errorRate = totalRequests > 0 ? errorRequests / totalRequests : 0,
    // Group by method
    const: requestsByMethod
};
{ }
;
data.forEach((event));
{
    requestsByMethod[event.method] = (requestsByMethod[event.method] || 0) + 1;
}
;
// Group by path
const requestsByPath = {};
data.forEach((event));
{
    requestsByPath[event.path] = (requestsByPath[event.path] || 0) + 1;
}
;
// Group by status code
const requestsByStatusCode = {};
data.forEach((event));
{
    requestsByStatusCode[event.statusCode] = (requestsByStatusCode[event.statusCode] || 0) + 1;
}
;
// Group by API version
const requestsByApiVersion = {};
data.forEach((event));
{
    if (event.apiVersion) {
        requestsByApiVersion[event.apiVersion] = (requestsByApiVersion[event.apiVersion] || 0) + 1;
    }
}
;
return {
    totalRequests,
    averageResponseTime,
    successRate,
    errorRate,
    requestsByMethod,
    requestsByPath,
    requestsByStatusCode,
    requestsByApiVersion
};
close();
void {
    : .flushInterval,
    // Flush buffer
    this: .flushBuffer(),
    logger: logger_1.logger, : .info('API analytics service closed')
};
exports.default = ApiAnalyticsService;
//# sourceMappingURL=ApiAnalyticsService.js.map