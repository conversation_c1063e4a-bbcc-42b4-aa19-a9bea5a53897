"use strict";
// jscpd:ignore-file
/**
 * Payment Routing Service
 *
 * This service provides intelligent payment method routing based on success rates,
 * costs, and other factors to optimize payment processing.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRoutingService = exports.RoutingRuleOperator = exports.RoutingRuleType = void 0;
const base_service_1 = require("./base.service");
const logger_1 = require("../lib/logger");
const apiResponseMiddleware_1 = require("../middlewares/apiResponseMiddleware");
const transaction_service_1 = require("./transaction.service");
const payment_method_service_1 = require("./payment-method.service");
const merchant_service_1 = require("./merchant.service");
const prisma_1 = __importDefault(require("../lib/prisma"));
/**
 * Routing rule type
 */
var RoutingRuleType;
(function (RoutingRuleType) {
    RoutingRuleType["SUCCESS_RATE"] = "SUCCESS_RATE";
    RoutingRuleType["COST"] = "COST";
    RoutingRuleType["REGION"] = "REGION";
    RoutingRuleType["AMOUNT"] = "AMOUNT";
    RoutingRuleType["MERCHANT_PREFERENCE"] = "MERCHANT_PREFERENCE";
    RoutingRuleType["CUSTOM"] = "CUSTOM";
})(RoutingRuleType || (exports.RoutingRuleType = RoutingRuleType = {}));
/**
 * Routing rule operator
 */
var RoutingRuleOperator;
(function (RoutingRuleOperator) {
    RoutingRuleOperator["EQUALS"] = "EQUALS";
    RoutingRuleOperator["NOT_EQUALS"] = "NOT_EQUALS";
    RoutingRuleOperator["GREATER_THAN"] = "GREATER_THAN";
    RoutingRuleOperator["LESS_THAN"] = "LESS_THAN";
    RoutingRuleOperator["CONTAINS"] = "CONTAINS";
    RoutingRuleOperator["NOT_CONTAINS"] = "NOT_CONTAINS";
})(RoutingRuleOperator || (exports.RoutingRuleOperator = RoutingRuleOperator = {}));
/**
 * Payment routing service
 */
class PaymentRoutingService extends base_service_1.BaseService {
    constructor() {
        super();
        this.prisma = prisma_1.default;
        this.transactionService = new transaction_service_1.TransactionService();
        this.paymentMethodService = new payment_method_service_1.PaymentMethodService();
        this.merchantService = new merchant_service_1.MerchantService();
    }
    /**
     * Create a generic service error
     * @param message Error message
     * @param statusCode HTTP status code
     * @param errorCode API error code
     * @returns Service error
     */
    genericError(message, statusCode, errorCode) {
        return new base_service_1.ServiceError(message, statusCode, errorCode);
    }
    /**
     * Get optimal payment method for a transaction
     * @param merchantId Merchant ID
     * @param amount Transaction amount
     * @param currency Currency
     * @param country Country code
     * @param ipAddress IP address
     * @param deviceType Device type
     * @param customParams Custom parameters
     * @returns Routing result
     */
    async getOptimalPaymentMethod(merchantId, amount, currency, country, ipAddress, deviceType, customParams) {
        try {
            // Get merchant
            const merchant = await this.merchantService.getMerchantById(merchantId);
            if (!merchant) {
                throw this.genericError('Merchant not found', 404, apiResponseMiddleware_1.ApiErrorCode.NOT_FOUND);
            }
            const paymentMethods = await this.paymentMethodService.getMerchantPaymentMethods(merchantId);
            if (!paymentMethods || paymentMethods.length === 0) {
                throw this.genericError('No payment methods available for this merchant', 400, apiResponseMiddleware_1.ApiErrorCode.BAD_REQUEST);
            }
            // Get payment method metrics
            const paymentMethodsWithMetrics = await this.getPaymentMethodMetrics(paymentMethods);
            // Create routing context
            const context = {
                merchant,
                amount,
                currency,
                country,
                ipAddress,
                deviceType,
                customParams,
            };
            // Get routing rules
            const rules = await this.getRoutingRules(merchantId);
            // Apply routing rules
            const matchedRule = this.findMatchingRule(rules, context);
            if (matchedRule) {
                // Get payment methods from matched rule
                const rulePaymentMethods = paymentMethodsWithMetrics.filter((pm) => matchedRule.paymentMethodIds.includes(pm.id));
                if (rulePaymentMethods.length > 0) {
                    // Sort by success rate
                    rulePaymentMethods.sort((a, b) => b.successRate - a.successRate);
                    return {
                        recommendedPaymentMethod: rulePaymentMethods[0],
                        alternativePaymentMethods: rulePaymentMethods.slice(1),
                        matchedRule,
                        scores: this.calculateScores(rulePaymentMethods, context),
                    };
                }
            }
            // No matching rule or no payment methods in rule, use scoring
            const scores = this.calculateScores(paymentMethodsWithMetrics, context);
            // Sort payment methods by score
            const sortedPaymentMethods = [...paymentMethodsWithMetrics].sort((a, b) => scores[b.id] - scores[a.id]);
            return {
                recommendedPaymentMethod: sortedPaymentMethods[0],
                alternativePaymentMethods: sortedPaymentMethods.slice(1),
                scores,
            };
        }
        catch (error) {
            if (error instanceof base_service_1.ServiceError) {
                throw error;
            }
            logger_1.logger.error('Error getting optimal payment method:', error);
            throw this.genericError('Failed to get optimal payment method', 500, apiResponseMiddleware_1.ApiErrorCode.SERVER_ERROR);
        }
    }
    /**
     * Record routing decision
     * @param transactionId Transaction ID
     * @param recommendedPaymentMethodId Recommended payment method ID
     * @param selectedPaymentMethodId Selected payment method ID
     * @param matchedRuleId Matched rule ID
     * @param scores Routing scores
     * @returns Created routing decision
     */
    async recordRoutingDecision(transactionId, recommendedPaymentMethodId, selectedPaymentMethodId, matchedRuleId, scores) {
        try {
            return await this.prisma.paymentRoutingDecision.create({
                data: {
                    transactionId,
                    recommendedPaymentMethodId,
                    selectedPaymentMethodId,
                    matchedRuleId,
                    scores: scores ? JSON.stringify(scores) : null,
                },
            });
        }
        catch (error) {
            logger_1.logger.error('Error recording routing decision:', error);
            throw this.genericError('Failed to record routing decision', 500, apiResponseMiddleware_1.ApiErrorCode.SERVER_ERROR);
        }
    }
    /**
     * Create routing rule
     * @param merchantId Merchant ID
     * @param rule Routing rule
     * @returns Created rule
     */
    async createRoutingRule(merchantId, rule) {
        try {
            // Validate payment method IDs
            const paymentMethods = await this.paymentMethodService.getMerchantPaymentMethods(merchantId);
            const validPaymentMethodIds = paymentMethods.map((pm) => pm.id);
            const invalidPaymentMethodIds = rule.paymentMethodIds.filter((id) => !validPaymentMethodIds.includes(id));
            if (invalidPaymentMethodIds.length > 0) {
                throw this.genericError(`Invalid payment method IDs: ${invalidPaymentMethodIds.join(', ')}`, 400, apiResponseMiddleware_1.ApiErrorCode.BAD_REQUEST);
            }
            // Create rule
            return await this.prisma.paymentRoutingRule.create({
                data: {
                    merchantId,
                    type: rule.type,
                    operator: rule.operator,
                    value: JSON.stringify(rule.value),
                    paymentMethodIds: rule.paymentMethodIds,
                    priority: rule.priority,
                },
            });
        }
        catch (error) {
            if (error instanceof base_service_1.ServiceError) {
                throw error;
            }
            logger_1.logger.error('Error creating routing rule:', error);
            throw this.genericError('Failed to create routing rule', 500, apiResponseMiddleware_1.ApiErrorCode.SERVER_ERROR);
        }
    }
    /**
     * Get routing rules for merchant
     * @param merchantId Merchant ID
     * @returns List of routing rules
     */
    async getRoutingRules(merchantId) {
        try {
            const rules = await this.prisma.paymentRoutingRule.findMany({
                where: { merchantId },
                orderBy: { priority: 'asc' },
            });
            return rules.map((rule) => ({
                type: rule.type,
                operator: rule.operator,
                value: JSON.parse(rule.value),
                paymentMethodIds: rule.paymentMethodIds,
                priority: rule.priority,
            }));
        }
        catch (error) {
            logger_1.logger.error('Error getting routing rules:', error);
            throw this.genericError('Failed to get routing rules', 500, apiResponseMiddleware_1.ApiErrorCode.SERVER_ERROR);
        }
    }
    /**
     * Get payment method metrics
     * @param paymentMethods Payment methods
     * @returns Payment methods with metrics
     */
    async getPaymentMethodMetrics(paymentMethods) {
        try {
            const result = [];
            for (const paymentMethod of paymentMethods) {
                // Get transactions for this payment method
                const transactions = await this.prisma.transaction.findMany({
                    where: { paymentMethodId: paymentMethod.id },
                    orderBy: { createdAt: 'desc' },
                    take: 100, // Consider last 100 transactions
                });
                // Calculate metrics
                const transactionCount = transactions.length;
                if (transactionCount === 0) {
                    // No transactions, use default values
                    result.push({
                        ...paymentMethod,
                        successRate: 100, // Assume 100% success rate for new payment methods
                        avgProcessingTime: 0,
                        costPerTransaction: this.getPaymentMethodCost(paymentMethod.type),
                        transactionCount: 0,
                    });
                }
                else {
                    // Calculate success rate
                    const successfulTransactions = transactions.filter((t) => t.status === 'COMPLETED');
                    const successRate = (successfulTransactions.length / transactionCount) * 100;
                    // Calculate average processing time
                    const completedTransactionsWithTimes = successfulTransactions.filter((t) => t.completedAt && t.createdAt);
                    let avgProcessingTime = 0;
                    if (completedTransactionsWithTimes.length > 0) {
                        const totalProcessingTime = completedTransactionsWithTimes.reduce((total, t) => {
                            const processingTime = new Date(t.completedAt).getTime() - new Date(t.createdAt).getTime();
                            return total + processingTime / 1000; // Convert to seconds
                        }, 0);
                        avgProcessingTime = totalProcessingTime / completedTransactionsWithTimes.length;
                    }
                    // Get cost per transaction
                    const costPerTransaction = this.getPaymentMethodCost(paymentMethod.type);
                    result.push({
                        ...paymentMethod,
                        successRate,
                        avgProcessingTime,
                        costPerTransaction,
                        transactionCount,
                    });
                }
            }
            return result;
        }
        catch (error) {
            logger_1.logger.error('Error getting payment method metrics:', error);
            throw this.genericError('Failed to get payment method metrics', 500, apiResponseMiddleware_1.ApiErrorCode.SERVER_ERROR);
        }
    }
    /**
     * Get payment method cost
     * @param paymentMethodType Payment method type
     * @returns Cost percentage
     */
    getPaymentMethodCost(paymentMethodType) {
        // These would typically come from a configuration or database
        switch (paymentMethodType) {
            case 'BINANCE_PAY':
                return 1.0; // 1.0%
            case 'BINANCE_C2C':
                return 0.8; // 0.8%
            case 'BINANCE_TRC20':
                return 0.5; // 0.5%
            case 'CRYPTO_TRANSFER':
                return 0.3; // 0.3%
            default:
                return 1.0; // Default 1.0%
        }
    }
    /**
     * Find matching routing rule
     * @param rules Routing rules
     * @param context Routing context
     * @returns Matching rule or undefined
     */
    findMatchingRule(rules, context) {
        for (const rule of rules) {
            if (this.ruleMatches(rule, context)) {
                return rule;
            }
        }
        return undefined;
    }
    /**
     * Check if rule matches context
     * @param rule Routing rule
     * @param context Routing context
     * @returns Whether rule matches
     */
    ruleMatches(rule, context) {
        switch (rule.type) {
            case RoutingRuleType.AMOUNT:
                return this.compareValues(context.amount, rule.operator, rule.value);
            case RoutingRuleType.REGION:
                return this.compareValues(context.country, rule.operator, rule.value);
            case RoutingRuleType.MERCHANT_PREFERENCE:
                // Check if merchant has preference set
                return (context.merchant.preferredPaymentMethodId !== null &&
                    rule.paymentMethodIds.includes(context.merchant.preferredPaymentMethodId));
            case RoutingRuleType.CUSTOM:
                // Check custom parameters
                if (!context.customParams)
                    return false;
                const { param, value } = rule.value;
                return this.compareValues(context.customParams[param], rule.operator, value);
            default:
                return false;
        }
    }
    /**
     * Compare values using operator
     * @param a First value
     * @param operator Operator
     * @param b Second value
     * @returns Comparison result
     */
    compareValues(a, operator, b) {
        switch (operator) {
            case RoutingRuleOperator.EQUALS:
                return a === b;
            case RoutingRuleOperator.NOT_EQUALS:
                return a !== b;
            case RoutingRuleOperator.GREATER_THAN:
                return a > b;
            case RoutingRuleOperator.LESS_THAN:
                return a < b;
            case RoutingRuleOperator.CONTAINS:
                return Array.isArray(a) ? a.includes(b) : String(a).includes(String(b));
            case RoutingRuleOperator.NOT_CONTAINS:
                return Array.isArray(a) ? !a.includes(b) : !String(a).includes(String(b));
            default:
                return false;
        }
    }
    /**
     * Calculate scores for payment methods
     * @param paymentMethods Payment methods with metrics
     * @param context Routing context
     * @returns Scores by payment method ID
     */
    calculateScores(paymentMethodsWithMetrics, context) {
        const scores = {};
        // Weights for different factors
        const weights = {
            successRate: 0.5,
            processingTime: 0.2,
            cost: 0.3,
        };
        // Calculate scores
        for (const pm of paymentMethodsWithMetrics) {
            // Success rate score (higher is better)
            const successRateScore = pm.successRate;
            // Processing time score (lower is better)
            const processingTimeScore = pm.avgProcessingTime === 0 ? 100 : 100 / (1 + pm.avgProcessingTime / 10);
            // Cost score (lower is better)
            const costScore = 100 - pm.costPerTransaction * 10;
            // Calculate weighted score
            scores[pm.id] =
                successRateScore * weights.successRate +
                    processingTimeScore * weights.processingTime +
                    costScore * weights.cost;
            // Adjust score based on merchant preference
            if (context.merchant.preferredPaymentMethodId === pm.id) {
                scores[pm.id] *= 1.2; // 20% bonus for preferred payment method
            }
        }
        return scores;
    }
}
exports.PaymentRoutingService = PaymentRoutingService;
