"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const prisma_1 = __importDefault(require("../lib/prisma"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
async function testConnection() {
    console.log('🔍 Testing database connection...');
    try {
        // Test database connection
        console.log('🔌 Connecting to database...');
        await prisma_1.default.$connect();
        console.log('✅ Database connection successful!');
        // Test query execution
        console.log('🔍 Testing query execution...');
        try {
            const userCount = await prisma_1.default.user.count();
            console.log(`✅ Query executed successfully! Found ${userCount} users.`);
        }
        catch (queryError) {
            console.error('❌ Query execution failed:', queryError);
            console.log('⚠️ This might be because the User table does not exist yet.');
            console.log('⚠️ You may need to run database migrations: npx prisma migrate dev');
        }
        // Test environment variables
        console.log('\n🔍 Checking environment variables...');
        const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET', 'PORT'];
        let allEnvVarsPresent = true;
        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                console.error(`❌ Missing required environment variable: ${envVar}`);
                allEnvVarsPresent = false;
            }
            else {
                console.log(`✅ Environment variable found: ${envVar}`);
            }
        }
        if (allEnvVarsPresent) {
            console.log('✅ All required environment variables are present!');
        }
        else {
            console.log('⚠️ Some required environment variables are missing. Please check your .env file.');
        }
        // Print database connection info
        console.log('\n📊 Database connection info:');
        console.log(`🔗 URL: ${process.env.DATABASE_URL?.replace(/:[^:]*@/, ':****@')}`);
        console.log(`🏠 Host: ${process.env.DB_HOST || 'localhost'}`);
        console.log(`🔢 Port: ${process.env.DB_PORT || '5432'}`);
        console.log(`👤 Username: ${process.env.DB_USERNAME || 'postgres'}`);
        console.log(`📚 Database: ${process.env.DB_NAME || 'amazingpay'}`);
    }
    catch (error) {
        console.error('❌ Database connection failed:', error);
        console.log('\n⚠️ Please check your database configuration in the .env file.');
        console.log('⚠️ Make sure PostgreSQL is running and accessible.');
        console.log('⚠️ You may need to create the database: CREATE DATABASE amazingpay;');
    }
    finally {
        try {
            await prisma_1.default.$disconnect();
            console.log('🔌 Database connection closed.');
        }
        catch (disconnectError) {
            console.error('❌ Error disconnecting from database:', disconnectError);
        }
    }
}
// Run the test
testConnection()
    .catch(console.error);
//# sourceMappingURL=test-connection.js.map