// jscpd:ignore-file
import { Request, Response } from "express";
import { IdentityVerificationService } from "../services/identity-verification.service";
import { asyncHandler } from "../utils/asyncHandler";
import { AppError } from "../utils/errors/AppError";
import { User, Merchant } from '../types';
import { IdentityVerificationService } from "../services/identity-verification.service";
import { asyncHandler } from "../utils/asyncHandler";
import { AppError } from "../utils/errors/AppError";
import { User, Merchant } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


const identityVerificationService: any = new IdentityVerificationService();

/**
 * Verify identity using Ethereum signature
 */
export const verifyEthereumSignature: any = asyncHandler(async (req: Request, res: Response) => {
    const { address, message, signature } = req.body;

    // Validate required fields
    if (!address || !message || !signature) {
        throw new AppError({
            message: "Address, message, and signature are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID or merchant ID from authenticated user
    const userId: any = req.user?.role === "USER" ? req.user.id // Fixed: using id instead of userId : undefined;
    const merchantId = req.user?.role === "MERCHANT" ? req.user.id // Fixed: using id instead of userId : undefined;

    // Verify signature
    const result: any = await identityVerificationService.verifyEthereumSignature(
        address,
        message,
        signature,
        userId,
        merchantId
    );

    // Return result
    res.status(result.success ? 200 : 400).json(result);
});

/**
 * Verify identity using ERC-1484
 */
export const verifyERC1484Identity: any = asyncHandler(async (req: Request, res: Response) => {
    const { address, ein, registryAddress } = req.body;

    // Validate required fields
    if (!address || !ein || !registryAddress) {
        throw new AppError({
            message: "Address, EIN, and registry address are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID or merchant ID from authenticated user
    const userId: any = req.user?.role === "USER" ? req.user.id // Fixed: using id instead of userId : undefined;
    const merchantId = req.user?.role === "MERCHANT" ? req.user.id // Fixed: using id instead of userId : undefined;

    // Verify ERC-1484 identity
    const result: any = await identityVerificationService.verifyERC1484Identity(
        address,
        ein,
        registryAddress,
        userId,
        merchantId
    );

    // Return result
    res.status(result.success ? 200 : 400).json(result);
});

/**
 * Verify identity using ERC-725
 */
export const verifyERC725Identity: any = asyncHandler(async (req: Request, res: Response) => {
    const { address, key, value } = req.body;

    // Validate required fields
    if (!address || !key || !value) {
        throw new AppError({
            message: "Address, key, and value are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID or merchant ID from authenticated user
    const userId: any = req.user?.role === "USER" ? req.user.id // Fixed: using id instead of userId : undefined;
    const merchantId = req.user?.role === "MERCHANT" ? req.user.id // Fixed: using id instead of userId : undefined;

    // Verify ERC-725 identity
    const result: any = await identityVerificationService.verifyERC725Identity(
        address,
        key,
        value,
        userId,
        merchantId
    );

    // Return result
    res.status(result.success ? 200 : 400).json(result);
});

/**
 * Verify identity using ENS
 */
export const verifyENS: any = asyncHandler(async (req: Request, res: Response) => {
    const { ensName, address } = req.body;

    // Validate required fields
    if (!ensName || !address) {
        throw new AppError({
            message: "ENS name and address are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID or merchant ID from authenticated user
    const userId: any = req.user?.role === "USER" ? req.user.id // Fixed: using id instead of userId : undefined;
    const merchantId = req.user?.role === "MERCHANT" ? req.user.id // Fixed: using id instead of userId : undefined;

    // Verify ENS identity
    const result: any = await identityVerificationService.verifyENS(
        ensName,
        address,
        userId,
        merchantId
    );

    // Return result
    res.status(result.success ? 200 : 400).json(result);
});

/**
 * Get identity verification by ID
 */
export const getVerificationById: any = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    // Get verification
    const verification: any = await identityVerificationService.getVerificationById(id);

    // Return verification
    res.status(200).json(verification);
});

/**
 * Get identity verifications for user
 */
export const getVerificationsForUser: any = asyncHandler(async (req: Request, res: Response) => {
    const userId: any = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get verifications
    const verifications: any = await identityVerificationService.getVerificationsForUser(userId);

    // Return verifications
    res.status(200).json(verifications);
});

/**
 * Get identity verifications for merchant
 */
export const getVerificationsForMerchant: any = asyncHandler(async (req: Request, res: Response) => {
    const merchantId: any = req.user?.id // Fixed: using id instead of userId;

    if (!merchantId) {
        throw new AppError({
            message: "Merchant ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get verifications
    const verifications: any = await identityVerificationService.getVerificationsForMerchant(merchantId);

    // Return verifications
    res.status(200).json(verifications);
});

/**
 * Verify identity using Polygon ID
 */
export const verifyPolygonID: any = asyncHandler(async (req: Request, res: Response) => {
    const { address, proof } = req.body;

    // Validate required fields
    if (!address || !proof) {
        throw new AppError({
            message: "Address and proof are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID or merchant ID from authenticated user
    const userId: any = req.user?.role === "USER" ? req.user.id // Fixed: using id instead of userId : undefined;
    const merchantId = req.user?.role === "MERCHANT" ? req.user.id // Fixed: using id instead of userId : undefined;

    // Verify Polygon ID
    const result: any = await identityVerificationService.verifyPolygonID(
        address,
        proof,
        userId,
        merchantId
    );

    // Return result
    res.status(result.success ? 200 : 400).json(result);
});

/**
 * Verify identity using Worldcoin
 */
export const verifyWorldcoin: any = asyncHandler(async (req: Request, res: Response) => {
    const { address, nullifier, proof } = req.body;

    // Validate required fields
    if (!address || !nullifier || !proof) {
        throw new AppError({
            message: "Address, nullifier, and proof are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID or merchant ID from authenticated user
    const userId: any = req.user?.role === "USER" ? req.user.id // Fixed: using id instead of userId : undefined;
    const merchantId = req.user?.role === "MERCHANT" ? req.user.id // Fixed: using id instead of userId : undefined;

    // Verify Worldcoin
    const result: any = await identityVerificationService.verifyWorldcoin(
        address,
        nullifier,
        proof,
        userId,
        merchantId
    );

    // Return result
    res.status(result.success ? 200 : 400).json(result);
});

/**
 * Verify identity using Unstoppable Domains
 */
export const verifyUnstoppableDomains: any = asyncHandler(async (req: Request, res: Response) => {
    const { domain, address } = req.body;

    // Validate required fields
    if (!domain || !address) {
        throw new AppError({
            message: "Domain and address are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID or merchant ID from authenticated user
    const userId: any = req.user?.role === "USER" ? req.user.id // Fixed: using id instead of userId : undefined;
    const merchantId = req.user?.role === "MERCHANT" ? req.user.id // Fixed: using id instead of userId : undefined;

    // Verify Unstoppable Domains
    const result: any = await identityVerificationService.verifyUnstoppableDomains(
        domain,
        address,
        userId,
        merchantId
    );

    // Return result
    res.status(result.success ? 200 : 400).json(result);
});

/**
 * Add a claim to an identity verification
 */
export const addClaim: any = asyncHandler(async (req: Request, res: Response) => {
    const { verificationId, type, value, issuer } = req.body;

    // Validate required fields
    if (!verificationId || !type || !value || !issuer) {
        throw new AppError({
            message: "Verification ID, type, value, and issuer are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Add claim
    const claim: any = await identityVerificationService.addClaim(
        verificationId,
        type,
        value,
        issuer
    );

    // Return claim
    res.status(201).json(claim);
});

/**
 * Revoke a claim
 */
export const revokeClaim: any = asyncHandler(async (req: Request, res: Response) => {
    const { claimId } = req.params;

    // Revoke claim
    const claim: any = await identityVerificationService.revokeClaim(claimId);

    // Return claim
    res.status(200).json(claim);
});

/**
 * Set expiration for a verification
 */
export const setVerificationExpiration: any = asyncHandler(async (req: Request, res: Response) => {
    const { verificationId, expiresAt } = req.body;

    // Validate required fields
    if (!verificationId || !expiresAt) {
        throw new AppError({
            message: "Verification ID and expiration date are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Set expiration
    const verification: any = await identityVerificationService.setVerificationExpiration(
        verificationId,
        new Date(expiresAt)
    );

    // Return verification
    res.status(200).json(verification);
});

/**
 * Check verification expiration
 */
export const checkVerificationExpiration: any = asyncHandler(async (req: Request, res: Response) => {
    // Check expiration
    const count: any = await identityVerificationService.checkVerificationExpiration();

    // Return count
    res.status(200).json({ count });
});

/**
 * Get verification statistics
 */
export const getVerificationStats: any = asyncHandler(async (req: Request, res: Response) => {
    // Get stats
    const stats: any = await identityVerificationService.getVerificationStats();

    // Return stats
    res.status(200).json(stats);
});

/**
 * Create a blockchain-based verification request
 */
export const createBlockchainVerificationRequest: any = asyncHandler(async (req: Request, res: Response) => {
    const { walletAddress, network } = req.body;

    // Validate required fields
    if (!walletAddress || !network) {
        throw new AppError({
            message: "Wallet address and network are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID from authenticated user
    const userId: any = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Create verification request
    const request: any = await identityVerificationService.createBlockchainVerificationRequest(
        userId,
        walletAddress,
        network
    );

    // Return request
    res.status(201).json({
        success: true,
        data: request,
        message: "Blockchain verification request created successfully"
    });
});

/**
 * Complete a blockchain-based verification request
 */
export const completeBlockchainVerification: any = asyncHandler(async (req: Request, res: Response) => {
    const { requestId, signature } = req.body;

    // Validate required fields
    if (!requestId || !signature) {
        throw new AppError({
            message: "Request ID and signature are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Complete verification
    const isVerified: any = await identityVerificationService.completeBlockchainVerification(
        requestId,
        signature
    );

    // Return result
    if (isVerified) {
        res.status(200).json({
            success: true,
            message: "Blockchain identity verified successfully"
        });
    } else {
        res.status(400).json({
            success: false,
            message: "Blockchain identity verification failed"
        });
    }
});

/**
 * Get supported blockchain networks
 */
export const getSupportedNetworks: any = asyncHandler(async (req: Request, res: Response) => {
    // Get supported networks
    const networks: any = await identityVerificationService.getSupportedNetworks();

    // Return networks
    res.status(200).json({
        success: true,
        data: networks,
        message: "Supported blockchain networks retrieved successfully"
    });
});

/**
 * Verify ENS domain ownership
 */
export const verifyENSDomain: any = asyncHandler(async (req: Request, res: Response) => {
    const { ensName } = req.body;

    // Validate required fields
    if (!ensName) {
        throw new AppError({
            message: "ENS name is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID from authenticated user
    const userId: any = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Verify ENS domain
    const result: any = await identityVerificationService.verifyENSDomain(
        userId,
        ensName
    );

    // Return result
    res.status(201).json({
        success: true,
        data: result,
        message: "ENS domain verification initiated successfully"
    });
});

/**
 * Complete ENS verification with signature
 */
export const completeENSVerification: any = asyncHandler(async (req: Request, res: Response) => {
    const { verificationId, signature } = req.body;

    // Validate required fields
    if (!verificationId || !signature) {
        throw new AppError({
            message: "Verification ID and signature are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Complete verification
    const isVerified: any = await identityVerificationService.completeENSVerification(
        verificationId,
        signature
    );

    // Return result
    if (isVerified) {
        res.status(200).json({
            success: true,
            message: "ENS domain verification completed successfully"
        });
    } else {
        res.status(400).json({
            success: false,
            message: "ENS domain verification failed"
        });
    }
});

/**
 * Verify Unstoppable Domain ownership
 */
export const verifyUnstoppableDomain: any = asyncHandler(async (req: Request, res: Response) => {
    const { domain } = req.body;

    // Validate required fields
    if (!domain) {
        throw new AppError({
            message: "Unstoppable domain is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID from authenticated user
    const userId: any = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Verify Unstoppable domain
    const result: any = await identityVerificationService.verifyUnstoppableDomain(
        userId,
        domain
    );

    // Return result
    res.status(201).json({
        success: true,
        data: result,
        message: "Unstoppable domain verification initiated successfully"
    });
});

/**
 * Complete Unstoppable domain verification with signature
 */
export const completeUnstoppableDomainVerification: any = asyncHandler(async (req: Request, res: Response) => {
    const { verificationId, signature } = req.body;

    // Validate required fields
    if (!verificationId || !signature) {
        throw new AppError({
            message: "Verification ID and signature are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Complete verification
    const isVerified: any = await identityVerificationService.completeUnstoppableDomainVerification(
        verificationId,
        signature
    );

    // Return result
    if (isVerified) {
        res.status(200).json({
            success: true,
            message: "Unstoppable domain verification completed successfully"
        });
    } else {
        res.status(400).json({
            success: false,
            message: "Unstoppable domain verification failed"
        });
    }
});

/**
 * Create a Polygon ID verification request
 */
export const createPolygonIDVerificationRequest: any = asyncHandler(async (req: Request, res: Response) => {
    // Get user ID from authenticated user
    const userId: any = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Create verification request
    const result: any = await identityVerificationService.createPolygonIDVerificationRequest(userId);

    // Return result
    res.status(201).json({
        success: true,
        data: result,
        message: "Polygon ID verification request created successfully"
    });
});

/**
 * Handle Polygon ID verification callback
 */
export const handlePolygonIDCallback: any = asyncHandler(async (req: Request, res: Response) => {
    const { sessionId, proofData } = req.body;

    // Validate required fields
    if (!sessionId || !proofData) {
        throw new AppError({
            message: "Session ID and proof data are required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Handle callback
    const isVerified: any = await identityVerificationService.handlePolygonIDCallback(
        sessionId,
        proofData
    );

    // Return result
    if (isVerified) {
        res.status(200).json({
            success: true,
            message: "Polygon ID verification completed successfully"
        });
    } else {
        res.status(400).json({
            success: false,
            message: "Polygon ID verification failed"
        });
    }
});

/**
 * Check Polygon ID verification status
 */
export const checkPolygonIDVerificationStatus: any = asyncHandler(async (req: Request, res: Response) => {
    const { verificationId } = req.params;

    // Validate required fields
    if (!verificationId) {
        throw new AppError({
            message: "Verification ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Check status
    const status: any = await identityVerificationService.checkPolygonIDVerificationStatus(verificationId);

    // Return status
    res.status(200).json({
        success: true,
        data: status,
        message: "Polygon ID verification status retrieved successfully"
    });
});

/**
 * Verify Worldcoin identity
 */
export const verifyWorldcoinIdentity: any = asyncHandler(async (req: Request, res: Response) => {
    const { proof } = req.body;

    // Validate required fields
    if (!proof) {
        throw new AppError({
            message: "Worldcoin proof is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Get user ID from authenticated user
    const userId: any = req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: "User ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Verify Worldcoin identity
    const result: any = await identityVerificationService.verifyWorldcoinIdentity(
        userId,
        proof
    );

    // Return result
    res.status(200).json({
        success: true,
        data: result,
        message: "Worldcoin identity verified successfully"
    });
});

// Export all controller methods
export default {
    verifyEthereumSignature,
    verifyERC1484Identity,
    verifyERC725Identity,
    verifyENS,
    getVerificationById,
    getVerificationsForUser,
    getVerificationsForMerchant,
    verifyPolygonID,
    verifyWorldcoin,
    verifyUnstoppableDomains,
    addClaim,
    revokeClaim,
    setVerificationExpiration,
    checkVerificationExpiration,
    getVerificationStats,
    createBlockchainVerificationRequest,
    completeBlockchainVerification,
    getSupportedNetworks,
    verifyENSDomain,
    completeENSVerification,
    verifyUnstoppableDomain,
    completeUnstoppableDomainVerification,
    createPolygonIDVerificationRequest,
    handlePolygonIDCallback,
    checkPolygonIDVerificationStatus,
    verifyWorldcoinIdentity
};
