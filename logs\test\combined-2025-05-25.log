{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T02:08:02.979Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T02:08:02.981Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T02:08:02.982Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T02:08:02.983Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:24:35.298Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:24:35.300Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:24:35.301Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:24:35.302Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:246:7)","statusCode":404,"timestamp":"2025-05-25T03:24:35.379Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:421:17)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:24:35.416Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:455:17)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:24:35.419Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:27:41.416Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:27:41.418Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:27:41.419Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:27:41.420Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:75:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.483Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:104:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.491Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:220:7)","statusCode":404,"timestamp":"2025-05-25T03:27:41.510Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:346:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.541Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:366:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.546Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:374:37)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.550Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Message is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Message is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:118:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:385:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:27:41.551Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Signature is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Signature is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:122:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:396:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:27:41.553Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:408:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.555Z"}
