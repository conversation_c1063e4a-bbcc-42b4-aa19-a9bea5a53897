{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T02:08:02.979Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T02:08:02.981Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T02:08:02.982Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T02:08:02.983Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:24:35.298Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:24:35.300Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:24:35.301Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:24:35.302Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:246:7)","statusCode":404,"timestamp":"2025-05-25T03:24:35.379Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:421:17)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:24:35.416Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:455:17)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:24:35.419Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:27:41.416Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:27:41.418Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:27:41.419Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:27:41.420Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:75:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.483Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:104:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.491Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:220:7)","statusCode":404,"timestamp":"2025-05-25T03:27:41.510Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:346:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.541Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:366:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.546Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:374:37)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.550Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Message is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Message is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:118:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:385:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:27:41.551Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Signature is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Signature is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:122:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:396:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:27:41.553Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:408:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:27:41.555Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:36:52.082Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:36:52.083Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:36:52.084Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:36:52.086Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:75:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.139Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:104:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.147Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:122:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.151Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:136:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.158Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:178:7)","statusCode":404,"timestamp":"2025-05-25T03:36:52.165Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:304:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.194Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:324:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.197Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:332:37)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.200Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Message is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Message is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:118:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:343:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:36:52.203Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Signature is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Signature is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:122:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:354:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:36:52.207Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:366:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:36:52.210Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:39:06.621Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:39:06.622Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:39:06.623Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:39:06.624Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:76:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.799Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:105:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.840Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:123:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.851Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:137:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.856Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:180:7)","statusCode":404,"timestamp":"2025-05-25T03:39:06.866Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:307:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.900Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:328:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.904Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:336:37)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.907Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Message is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Message is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:118:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:347:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:39:06.910Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Signature is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Signature is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:122:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:358:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:39:06.915Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:370:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:39:06.922Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:41:35.573Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:41:35.574Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:41:35.575Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:41:35.576Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:76:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.631Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:105:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.641Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:123:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.645Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:137:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.648Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:180:7)","statusCode":404,"timestamp":"2025-05-25T03:41:35.656Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:307:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.686Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:328:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.691Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:336:37)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.695Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Message is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Message is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:118:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:347:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:41:35.698Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Signature is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Signature is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:122:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:358:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:41:35.704Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:370:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:41:35.707Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:43:03.004Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:43:03.006Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:43:03.007Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:43:03.007Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts:86:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:43:03.058Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:43:58.419Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:43:58.421Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:43:58.422Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:43:58.423Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.462Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:90:37)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:43:58.490Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Message is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Message is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:118:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:102:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:43:58.492Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Signature is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Signature is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:122:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:114:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:43:58.494Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:127:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:43:58.498Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.503Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:183:7)","statusCode":404,"timestamp":"2025-05-25T03:43:58.511Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.524Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.526Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.528Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.529Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.531Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.535Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.537Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.538Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.540Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.542Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:43:58.544Z"}
{"level":"info","message":"Logger initialized with level: info","timestamp":"2025-05-25T03:57:00.952Z"}
{"level":"info","message":"Environment: test","timestamp":"2025-05-25T03:57:00.953Z"}
{"level":"info","message":"Operational mode: development","timestamp":"2025-05-25T03:57:00.954Z"}
{"level":"info","message":"Log files directory: F:\\Amazing pay flow\\logs\\test","timestamp":"2025-05-25T03:57:00.955Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:00.999Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Address is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Address is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:114:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:90:37)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:57:01.029Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Message is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Message is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:118:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:102:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:57:01.031Z"}
{"code":"INVALID_PARAMETERS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Signature is required","name":"IdentityVerificationError","stack":"IdentityVerificationError: Signature is required\n    at Function.invalidParameters (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:114:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:122:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:114:37)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-05-25T03:57:01.033Z"}
{"code":"INVALID_ADDRESS","isOperational":true,"level":"error","message":"Error verifying Ethereum signature: Invalid Ethereum address format","name":"IdentityVerificationError","stack":"IdentityVerificationError: Invalid Ethereum address format\n    at Function.invalidAddress (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:37:16)\n    at EthereumSignatureVerification.validateParams (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:126:45)\n    at EthereumSignatureVerification.verify (F:\\Amazing pay flow\\src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts:36:18)\n    at IdentityVerificationService.verifyEthereumSignature (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:64:53)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:127:36)\n    at Promise.then.completed (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (F:\\Amazing pay flow\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (F:\\Amazing pay flow\\node_modules\\jest-runner\\build\\runTest.js:444:34)","statusCode":400,"timestamp":"2025-05-25T03:57:01.037Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.039Z"}
{"code":"VERIFICATION_NOT_FOUND","isOperational":true,"level":"error","message":"Error getting verification by ID: Verification not found","name":"IdentityVerificationError","stack":"IdentityVerificationError: Verification not found\n    at Function.verificationNotFound (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationError.ts:70:16)\n    at IdentityVerificationService.getVerificationById (F:\\Amazing pay flow\\src\\services\\identity-verification\\core\\IdentityVerificationService.ts:78:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at Object.<anonymous> (F:\\Amazing pay flow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts:183:7)","statusCode":404,"timestamp":"2025-05-25T03:57:01.047Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.058Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.062Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.063Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.065Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.066Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.068Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.069Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.070Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.072Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.077Z"}
{"level":"warn","message":"Ethereum signature verification failed for address: ******************************************","timestamp":"2025-05-25T03:57:01.079Z"}
