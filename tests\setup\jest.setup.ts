/**
 * Jest Setup Configuration
 * 
 * Global setup for all Jest tests including mocks, utilities, and environment configuration.
 */

import { jest } from '@jest/globals';

// Extend Jest matchers
import 'jest-extended';

// Mock console methods in test environment
const originalConsole = global.console;

beforeAll(() => {
  // Mock console methods to reduce noise in tests
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
});

afterAll(() => {
  // Restore original console
  global.console = originalConsole;
});

// Global test utilities
global.testUtils = {
  /**
   * Create a mock function with TypeScript support
   */
  createMockFn: <T extends (...args: any[]) => any>(implementation?: T) => {
    return jest.fn(implementation) as jest.MockedFunction<T>;
  },

  /**
   * Create a partial mock object
   */
  createPartialMock: <T>(obj: Partial<T>): T => {
    return obj as T;
  },

  /**
   * Wait for a specified amount of time
   */
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Generate a random string
   */
  randomString: (length: number = 10): string => {
    return Math.random().toString(36).substring(2, length + 2);
  },

  /**
   * Generate a random number within range
   */
  randomNumber: (min: number = 0, max: number = 100): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * Generate a mock Ethereum address
   */
  mockEthereumAddress: (): string => {
    return '0x' + Array.from({ length: 40 }, () => 
      Math.floor(Math.random() * 16).toString(16)
    ).join('');
  },

  /**
   * Generate a mock UUID
   */
  mockUUID: (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  /**
   * Create a mock date
   */
  mockDate: (offset: number = 0): Date => {
    return new Date(Date.now() + offset);
  }
};

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
process.env.REDIS_URL = 'redis://localhost:6379';

// Mock Prisma Client
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    merchant: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    transaction: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    identityVerification: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    riskAssessment: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      aggregate: jest.fn(),
    },
    fraudDetectionConfig: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      upsert: jest.fn(),
    },
    admin: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    alert: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    $transaction: jest.fn(),
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  })),
}));

// Mock Redis
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    flushall: jest.fn(),
  })),
}));

// Mock crypto module for consistent testing
jest.mock('crypto', () => ({
  ...jest.requireActual('crypto'),
  randomBytes: jest.fn(() => Buffer.from('mock-random-bytes')),
  createHash: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    digest: jest.fn(() => 'mock-hash'),
  })),
}));

// Mock ethers for Ethereum signature verification
jest.mock('ethers', () => ({
  ethers: {
    utils: {
      verifyMessage: jest.fn(),
      isAddress: jest.fn(),
      getAddress: jest.fn(),
    },
  },
}));

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Custom matchers
expect.extend({
  /**
   * Check if a value is a valid Ethereum address
   */
  toBeValidEthereumAddress(received: string) {
    const isValid = /^0x[a-fA-F0-9]{40}$/.test(received);
    return {
      message: () => `expected ${received} to be a valid Ethereum address`,
      pass: isValid,
    };
  },

  /**
   * Check if a value is a valid UUID
   */
  toBeValidUUID(received: string) {
    const isValid = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(received);
    return {
      message: () => `expected ${received} to be a valid UUID`,
      pass: isValid,
    };
  },

  /**
   * Check if a response has the expected API structure
   */
  toMatchApiResponse(received: any, expected?: any) {
    const hasSuccess = typeof received.success === 'boolean';
    const hasData = received.success ? received.data !== undefined : true;
    const hasError = !received.success ? received.error !== undefined : true;
    const hasTimestamp = received.timestamp !== undefined;

    const isValid = hasSuccess && hasData && hasError && hasTimestamp;

    return {
      message: () => `expected ${JSON.stringify(received)} to match API response structure`,
      pass: isValid,
    };
  },

  /**
   * Check if an error has the expected structure
   */
  toMatchErrorStructure(received: any) {
    const hasType = typeof received.type === 'string';
    const hasCode = typeof received.code === 'string';
    const hasMessage = typeof received.message === 'string';

    const isValid = hasType && hasCode && hasMessage;

    return {
      message: () => `expected ${JSON.stringify(received)} to match error structure`,
      pass: isValid,
    };
  }
});

// Declare global types for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidEthereumAddress(): R;
      toBeValidUUID(): R;
      toMatchApiResponse(expected?: any): R;
      toMatchErrorStructure(): R;
    }
  }

  const testUtils: {
    createMockFn: <T extends (...args: any[]) => any>(implementation?: T) => jest.MockedFunction<T>;
    createPartialMock: <T>(obj: Partial<T>) => T;
    wait: (ms: number) => Promise<void>;
    randomString: (length?: number) => string;
    randomNumber: (min?: number, max?: number) => number;
    mockEthereumAddress: () => string;
    mockUUID: () => string;
    mockDate: (offset?: number) => Date;
  };
}

export {};
