"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const cache_controller_1 = __importDefault(require("../controllers/cache.controller"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
/**
 * @route   GET /api/cache/status
 * @desc    Get cache status
 * @access  Admin
 */
router.get("/status", auth_1.authenticateJWT, auth_1.isAdmin, cache_controller_1.default.getStatus);
/**
 * @route   POST /api/cache/enable
 * @desc    Enable cache
 * @access  Admin
 */
router.post("/enable", auth_1.authenticateJWT, auth_1.isAdmin, cache_controller_1.default.enable);
/**
 * @route   POST /api/cache/disable
 * @desc    Disable cache
 * @access  Admin
 */
router.post("/disable", auth_1.authenticateJWT, auth_1.isAdmin, cache_controller_1.default.disable);
/**
 * @route   POST /api/cache/clear
 * @desc    Clear cache
 * @access  Admin
 */
router.post("/clear", auth_1.authenticateJWT, auth_1.isAdmin, cache_controller_1.default.clear);
exports.default = router;
//# sourceMappingURL=cache.routes.js.map