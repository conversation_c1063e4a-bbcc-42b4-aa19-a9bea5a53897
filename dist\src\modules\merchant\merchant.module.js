"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantModule = void 0;
const module_1 = require("../../core/module");
const utils_1 = require("../../utils");
const auth_middleware_1 = require("../../middlewares/auth.middleware");
class MerchantModule {
    /**
     * Create a new merchant module
     */
    constructor() {
        this.moduleRegistry = new module_1.ModuleRegistry();
        this.container = new module_1.Container();
        // Create module factory
        this.moduleFactory = new module_1.ModuleFactory('merchant', 'Merchant');
        // Get router, repository, service, and controller from factory
        const { router, repository, service, controller } = this.moduleFactory.build();
        // Configure router
        router
            .addRoute('get', '/stats', controller.getStats)
            .addRoute('get', '/payments', controller.getPayments)
            .addRoute('post', '/verify', controller.verifyMerchant)
            .addRoute('post', '/api-key', controller.generateApiKey)
            .addRoute('delete', '/api-key/:id', controller.revokeApiKey)
            .addMiddleware(auth_middleware_1.authMiddleware);
        // Add custom repository methods
        this.moduleFactory.addRepositoryMethod('findByUserId', async (userId) => {
            try {
                return await repository.findByField('userId', userId);
            }
            catch (error) {
                utils_1.logger.error(`Error finding merchant by user ID ${userId}:`, error);
                throw error;
            }
        });
        this.moduleFactory.addRepositoryMethod('findByApiKey', async (apiKey) => {
            try {
                return await repository.findByField('apiKey', apiKey);
            }
            catch (error) {
                utils_1.logger.error(`Error finding merchant by API key:`, error);
                throw error;
            }
        });
        // Add custom service methods
        this.moduleFactory.addServiceMethod('verifyMerchant', async (id, verificationData) => {
            try {
                // Get merchant
                const merchant = await service.getById(id);
                // Check if merchant exists
                if (!merchant) {
                    throw utils_1.ErrorFactory.notFound('Merchant', id);
                }
                // Verify merchant
                // Implementation would depend on the specific verification process
                const isVerified = true; // Placeholder
                // Update merchant status
                const updatedMerchant = await service.update(id, {
                    isVerified,
                    verifiedAt: new Date(),
                    updatedAt: new Date()
                });
                utils_1.logger.info(`Merchant verified: ${id}`, {
                    merchantId: id,
                    isVerified
                });
                return {
                    success: true,
                    merchant: updatedMerchant,
                    isVerified
                };
            }
            catch (error) {
                utils_1.logger.error(`Error verifying merchant ${id}:`, error);
                throw utils_1.ErrorFactory.handle(error);
            }
        });
        this.moduleFactory.addServiceMethod('generateApiKey', async (id) => {
            try {
                // Get merchant
                const merchant = await service.getById(id);
                // Check if merchant exists
                if (!merchant) {
                    throw utils_1.ErrorFactory.notFound('Merchant', id);
                }
                // Check if merchant is verified
                if (!merchant.isVerified) {
                    throw utils_1.ErrorFactory.validation('Merchant must be verified to generate an API key');
                }
                // Generate API key
                const apiKey = await utils_1.CryptoUtils.generateApiKey('mch', 32);
                // Update merchant with API key
                const updatedMerchant = await service.update(id, {
                    apiKey,
                    updatedAt: new Date()
                });
                utils_1.logger.info(`API key generated for merchant: ${id}`, {
                    merchantId: id
                });
                return {
                    success: true,
                    merchant: updatedMerchant,
                    apiKey
                };
            }
            catch (error) {
                utils_1.logger.error(`Error generating API key for merchant ${id}:`, error);
                throw utils_1.ErrorFactory.handle(error);
            }
        });
        this.moduleFactory.addServiceMethod('revokeApiKey', async (id) => {
            try {
                // Get merchant
                const merchant = await service.getById(id);
                // Check if merchant exists
                if (!merchant) {
                    throw utils_1.ErrorFactory.notFound('Merchant', id);
                }
                // Update merchant to remove API key
                const updatedMerchant = await service.update(id, {
                    apiKey: null,
                    updatedAt: new Date()
                });
                utils_1.logger.info(`API key revoked for merchant: ${id}`, {
                    merchantId: id
                });
                return {
                    success: true,
                    merchant: updatedMerchant
                };
            }
            catch (error) {
                utils_1.logger.error(`Error revoking API key for merchant ${id}:`, error);
                throw utils_1.ErrorFactory.handle(error);
            }
        });
        this.moduleFactory.addServiceMethod('getDashboardData', async (id) => {
            try {
                // Get merchant
                const merchant = await service.getById(id);
                // Check if merchant exists
                if (!merchant) {
                    throw utils_1.ErrorFactory.notFound('Merchant', id);
                }
                // Get payment service
                const paymentService = this.container.resolve('paymentService');
                // Get merchant payments
                const payments = await paymentService.getByMerchantId(id, { limit: 5 });
                // Get payment stats
                const stats = await paymentService.getPaymentStats(id);
                // Return dashboard data
                return {
                    merchant,
                    recentPayments: payments.data,
                    stats
                };
            }
            catch (error) {
                utils_1.logger.error(`Error getting dashboard data for merchant ${id}:`, error);
                throw utils_1.ErrorFactory.handle(error);
            }
        });
        // Add custom controller methods
        this.moduleFactory.addControllerMethod('getDashboard', async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = req.user;
                // Only merchants can view their dashboard
                if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to view this dashboard');
                }
                // Get dashboard data
                const dashboardData = await service.getDashboardData(merchantId);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: dashboardData
                });
            }
            catch (error) {
                utils_1.logger.error(`Error getting merchant dashboard:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while getting merchant dashboard'
                });
            }
        });
        this.moduleFactory.addControllerMethod('getStats', async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = req.user;
                // Only merchants can view their stats
                if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to view these stats');
                }
                // Get payment service
                const paymentService = this.container.resolve('paymentService');
                // Get payment stats
                const stats = await paymentService.getPaymentStats(merchantId);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: stats
                });
            }
            catch (error) {
                utils_1.logger.error(`Error getting merchant stats:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while getting merchant stats'
                });
            }
        });
        this.moduleFactory.addControllerMethod('getPayments', async (req, res) => {
            try {
                // Check authorization
                const { userRole, userId, merchantId } = req.user;
                // Only merchants can view their payments
                if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to view these payments');
                }
                // Get payment service
                const paymentService = this.container.resolve('paymentService');
                // Parse pagination parameters
                const limit = parseInt(req.query.limit) || 10;
                const page = parseInt(req.query.page) || 1;
                const offset = (page - 1) * limit;
                // Get merchant payments
                const payments = await paymentService.getByMerchantId(merchantId, { limit, offset });
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: payments
                });
            }
            catch (error) {
                utils_1.logger.error(`Error getting merchant payments:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while getting merchant payments'
                });
            }
        });
        this.moduleFactory.addControllerMethod('verifyMerchant', async (req, res) => {
            try {
                // Check authorization
                const { userRole } = req.user;
                // Only admins can verify merchants
                if (userRole !== 'ADMIN') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to verify merchants');
                }
                // Get merchant ID from request body
                const { merchantId, verificationData } = req.body;
                if (!merchantId) {
                    return res.status(400).json({
                        success: false,
                        error: 'Merchant ID is required'
                    });
                }
                // Verify merchant
                const result = await service.verifyMerchant(merchantId, verificationData);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                utils_1.logger.error(`Error verifying merchant:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while verifying merchant'
                });
            }
        });
        this.moduleFactory.addControllerMethod('generateApiKey', async (req, res) => {
            try {
                // Check authorization
                const { userRole, merchantId } = req.user;
                // Only merchants can generate API keys for their own account
                if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to generate API keys');
                }
                // Generate API key
                const result = await service.generateApiKey(merchantId);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                utils_1.logger.error(`Error generating API key:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while generating API key'
                });
            }
        });
        this.moduleFactory.addControllerMethod('revokeApiKey', async (req, res) => {
            try {
                // Check authorization
                const { userRole, merchantId } = req.user;
                // Only merchants can revoke API keys for their own account
                if (userRole !== 'MERCHANT' && userRole !== 'ADMIN') {
                    throw utils_1.ErrorFactory.authorization('You do not have permission to revoke API keys');
                }
                // Revoke API key
                const result = await service.revokeApiKey(merchantId);
                // Send success response
                return res.status(200).json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                utils_1.logger.error(`Error revoking API key:`, error);
                return res.status(500).json({
                    success: false,
                    error: error.message || 'An error occurred while revoking API key'
                });
            }
        });
        // Create module
        this.module = {
            name: 'merchant',
            router,
            repository,
            service,
            controller,
            dependencies: ['payment', 'user'],
            initialize: async () => {
                utils_1.logger.info('Initializing merchant module');
                // Register dependencies
                this.container.registerSingleton('merchantRepository', () => repository);
                this.container.registerSingleton('merchantService', () => service);
                this.container.registerSingleton('merchantController', () => controller);
                utils_1.logger.info('Merchant module initialized');
            }
        };
        // Register the module
        this.moduleRegistry.registerModule(this.module);
    }
    /**
     * Get the module
     * @returns Merchant module
     */
    getModule() {
        return this.module;
    }
}
exports.MerchantModule = MerchantModule;
//# sourceMappingURL=merchant.module.js.map