/**
 * CRUD Controller
 *
 * This is a base controller class that provides CRUD functionality
 * for all controllers in the application.
 */
import { Request, Response } from 'express';
import { BaseController } from './BaseController';
export declare class CrudController extends BaseController {
    /**
     * Get all items
     */
    protected getAll(req: Request, res: Response, service: any, message?: string): Promise<Response<any, Record<string, any>>>;
    /**
     * Get item by ID
     */
    protected getById(req: Request, res: Response, service: any, message?: string): Promise<Response<any, Record<string, any>>>;
    /**
     * Create item
     */
    protected create(req: Request, res: Response, service: any, validationSchema?: any, message?: string): Promise<Response<any, Record<string, any>>>;
    /**
     * Update item
     */
    protected update(req: Request, res: Response, service: any, validationSchema?: any, message?: string): Promise<Response<any, Record<string, any>>>;
    /**
     * Delete item
     */
    protected delete(req: Request, res: Response, service: any, message?: string): Promise<Response<any, Record<string, any>>>;
}
//# sourceMappingURL=CrudController.d.ts.map