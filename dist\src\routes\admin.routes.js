"use strict";
// jscpd:ignore-file
/**
 * Admin Routes
 *
 * Routes for admin operations with RBAC.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const enhanced_auth_middleware_1 = require("../middlewares/enhanced-auth.middleware");
const audit_middleware_1 = require("../middlewares/audit.middleware");
const admin_1 = require("../controllers/admin");
// Create controller instance
const adminController = new admin_1.AdminController();
const router = (0, express_1.Router)();
// All admin routes require authentication
router.use(enhanced_auth_middleware_1.enhancedAuthenticate);
// Admin dashboard
router.get('/dashboard', (0, enhanced_auth_middleware_1.requirePermission)('admin', 'access'), adminController.getDashboardData);
// Admin users management
router.get('/users', (0, enhanced_auth_middleware_1.requirePermission)('admin_users', 'view'), adminController.getAdminUsers);
router.get('/users/:id', (0, enhanced_auth_middleware_1.requirePermission)('admin_users', 'view'), (0, validation_middleware_1.validate)([(0, express_validator_1.param)('id').notEmpty()]), adminController.getAdminUserById);
router.post('/users', (0, enhanced_auth_middleware_1.requirePermission)('admin_users', 'create'), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)('email').isEmail(),
    (0, express_validator_1.body)('name').notEmpty(),
    (0, express_validator_1.body)('password').isLength({ min: 8 }),
    (0, express_validator_1.body)('roleId').notEmpty(),
]), (0, audit_middleware_1.auditLog)('create', 'admin_users'), adminController.createAdminUser);
router.put('/users/:id', (0, enhanced_auth_middleware_1.requirePermission)('admin_users', 'update'), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)('id').notEmpty(),
    (0, express_validator_1.body)('name').optional(),
    (0, express_validator_1.body)('email').optional().isEmail(),
    (0, express_validator_1.body)('roleId').optional(),
    (0, express_validator_1.body)('isActive').optional().isBoolean(),
]), (0, audit_middleware_1.auditLog)('update', 'admin_users'), adminController.updateAdminUser);
router.delete('/users/:id', (0, enhanced_auth_middleware_1.requirePermission)('admin_users', 'delete'), (0, validation_middleware_1.validate)([(0, express_validator_1.param)('id').notEmpty()]), (0, audit_middleware_1.auditLog)('delete', 'admin_users'), adminController.deleteAdminUser);
// Roles management
router.get('/roles', (0, enhanced_auth_middleware_1.requirePermission)('roles', 'view'), adminController.getRoles);
router.get('/roles/:id', (0, enhanced_auth_middleware_1.requirePermission)('roles', 'view'), (0, validation_middleware_1.validate)([(0, express_validator_1.param)('id').notEmpty()]), adminController.getRoleById);
router.post('/roles', (0, enhanced_auth_middleware_1.requirePermission)('roles', 'create'), (0, validation_middleware_1.validate)([(0, express_validator_1.body)('name').notEmpty(), (0, express_validator_1.body)('type').notEmpty(), (0, express_validator_1.body)('permissions').isArray()]), (0, audit_middleware_1.auditLog)('create', 'roles'), adminController.createRole);
router.put('/roles/:id', (0, enhanced_auth_middleware_1.requirePermission)('roles', 'update'), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)('id').notEmpty(),
    (0, express_validator_1.body)('name').optional(),
    (0, express_validator_1.body)('permissions').optional().isArray(),
    (0, express_validator_1.body)('isActive').optional().isBoolean(),
]), (0, audit_middleware_1.auditLog)('update', 'roles'), adminController.updateRole);
router.delete('/roles/:id', (0, enhanced_auth_middleware_1.requirePermission)('roles', 'delete'), (0, validation_middleware_1.validate)([(0, express_validator_1.param)('id').notEmpty()]), (0, audit_middleware_1.auditLog)('delete', 'roles'), adminController.deleteRole);
// Permissions management
router.get('/permissions', (0, enhanced_auth_middleware_1.requirePermission)('permissions', 'view'), adminController.getPermissions);
// Audit logs
router.get('/audit-logs', (0, enhanced_auth_middleware_1.requirePermission)('audit_logs', 'view'), adminController.getAuditLogs);
router.get('/audit-logs/:id', (0, enhanced_auth_middleware_1.requirePermission)('audit_logs', 'view'), (0, validation_middleware_1.validate)([(0, express_validator_1.param)('id').notEmpty()]), adminController.getAuditLogById);
// System settings
router.get('/settings', (0, enhanced_auth_middleware_1.requirePermission)('settings', 'view'), adminController.getSystemSettings);
router.put('/settings/:key', (0, enhanced_auth_middleware_1.requirePermission)('settings', 'update'), (0, validation_middleware_1.validate)([(0, express_validator_1.param)('key').notEmpty(), (0, express_validator_1.body)('value').notEmpty()]), (0, audit_middleware_1.auditLog)('update', 'settings'), adminController.updateSystemSetting);
exports.default = router;
//# sourceMappingURL=admin.routes.js.map