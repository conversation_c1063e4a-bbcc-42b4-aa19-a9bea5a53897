// jscpd:ignore-file
import { Server as HttpServer } from "http";
import { Server as WebSocketServer } from "socket.io";
import { transactionEvents } from "./transaction.service";
import { paymentMethodEvents } from "./payment-method.service";
import { verificationMethodEvents } from "./verification-method.service";
import { paymentPageEvents } from "./payment-page.service";
import { merchantEvents } from "./merchant-events.service";
import { notificationEvents } from "./notification-events.service";
import { monitoringEvents } from "../utils/monitoring";
import jwt from "jsonwebtoken";
import prisma from "../config/database";
import { Server as WebSocketServer } from "socket.io";
import { transactionEvents } from "./transaction.service";
import { paymentMethodEvents } from "./payment-method.service";
import { verificationMethodEvents } from "./verification-method.service";
import { paymentPageEvents } from "./payment-page.service";
import { merchantEvents } from "./merchant-events.service";
import { notificationEvents } from "./notification-events.service";
import { monitoringEvents } from "../utils/monitoring";
import { User, Merchant, Transaction } from '../types';

// WebSocket service
import { User, Merchant, Transaction } from '../types';


export const WebSocketService: any = {
    // Initialize WebSocket server
    initialize(httpServer: HttpServer) {
        const io: any =new WebSocketServer(httpServer, {
            cors: {, origin: process.env.CORS_ORIGIN || "*",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ["websocket", "polling"],
            pingInterval: 10000,
            pingTimeout: 5000,
            cookie: false
        });

        // Authentication middleware
        io.use(async (socket, next) => {
            try {
                const token: any =socket.handshake.auth.token;

                if (!token) {
                    return next(new Error("Authentication error: No token provided"));
                }

                // Verify token
                const decoded: any =jwt.verify(token, process.env.JWT_SECRET || "amazingpay-jwt-secret-key-for-authentication-2024") as any;

                // Get user or merchant
                if (decoded.id // Fixed: using id instead of userId) {
                    const user: any =await prisma.user.findUnique({
                        where: {, id: decoded.id //, Fixed: using id instead of userId }
                    });

                    if (!user) {
                        return next(new Error("Authentication error: User not found"));
                    }

                    if (!user.isActive) {
                        return next(new Error("Authentication error: User is not active"));
                    }

                    socket.data.user = {
                        id: user.id,
                        email: user.email,
                        role: user.role
                    };
                } else if (decoded.merchantId) {
                    const merchant: any =await prisma.merchant.findUnique({
                        where: {, id: decoded.merchantId }
                    });

                    if (!merchant) {
                        return next(new Error("Authentication error: Merchant not found"));
                    }

                    if (!merchant.isActive) {
                        return next(new Error("Authentication error: Merchant is not active"));
                    }

                    socket.data.merchant = {
                        id: merchant.id,
                        email: merchant.email,
                        role: "MERCHANT"
                    };
                } else {
                    return next(new Error("Authentication error: Invalid token"));
                }

                next();
            } catch (error) {
                return next(new Error("Authentication error: Invalid token"));
            }
        });

        // Connection handler
        io.on("connection", (socket) => {


            // Join rooms based on user/merchant
            if (socket.data.user) {
                // Admin users join admin room
                if (["ADMIN", "SUPER_ADMIN"].includes(socket.data.user.role)) {
                    socket.join("admin");

                }

                // All users join their user-specific room
                socket.join(`user:${socket.data.user.id}`);


                // All users join notifications room
                socket.join(`notifications:${socket.data.user.id}`);

            } else if (socket.data.merchant) {
                // Merchants join their own room
                socket.join(`merchant:${socket.data.merchant.id}`);


                // Merchants join notifications room
                socket.join(`notifications:merchant:${socket.data.merchant.id}`);

            }

            // Handle disconnect
            socket.on("disconnect", () => {

            });

            // Handle room subscription
            socket.on("subscribe", (room) => {
                // Validate room subscription
                if (room === "admin" && socket.data.user && ["ADMIN", "SUPER_ADMIN"].includes(socket.data.user.role)) {
                    socket.join("admin");

                } else if (room.startsWith("merchant:") && socket.data.merchant) {
                    const merchantId: any =room.split(":")[1];
                    if (merchantId === socket.data.merchant.id) {
                        socket.join(room);

                    }
                } else if (room.startsWith("payment-page:") && socket.data.merchant) {
                    const paymentPageId: any =room.split(":")[1];
                    // In a real implementation, we would check if the payment page belongs to the merchant
                    socket.join(room);

                } else if (room.startsWith("notifications:") && socket.data.user) {
                    const userId: any =room.split(":")[1];
                    if (userId === socket.data.user.id) {
                        socket.join(room);

                    }
                } else if (room.startsWith("notifications:merchant:") && socket.data.merchant) {
                    const merchantId: any =room.split(":")[2];
                    if (merchantId === socket.data.merchant.id) {
                        socket.join(room);

                    }
                }
            });

            // Handle room unsubscription
            socket.on("unsubscribe", (room) => {
                socket.leave(room);

            });
        });

        // Set up event listeners for real-time updates

        // Transaction events
        transactionEvents.on("transaction.created", (transaction) => {
            // Notify admin room
            io.to("admin").emit("transaction.created", {
                id: transaction.id,
                amount: transaction.amount,
                currency: transaction.currency,
                status: transaction.status,
                merchantId: transaction.merchantId,
                createdAt: transaction.createdAt
            });

            // Notify merchant room
            io.to(`merchant:${transaction.merchantId}`).emit("transaction.created", {
                id: transaction.id,
                amount: transaction.amount,
                currency: transaction.currency,
                status: transaction.status,
                createdAt: transaction.createdAt
            });

            // Notify payment page room if applicable
            if (transaction.paymentPageId) {
                io.to(`payment-page:${transaction.paymentPageId}`).emit("transaction.created", {
                    id: transaction.id,
                    amount: transaction.amount,
                    currency: transaction.currency,
                    status: transaction.status
                });
            }
        });

        transactionEvents.on("transaction.updated", (transaction) => {
            // Notify admin room
            io.to("admin").emit("transaction.updated", {
                id: transaction.id,
                status: transaction.status,
                merchantId: transaction.merchantId,
                updatedAt: transaction.updatedAt
            });

            // Notify merchant room
            io.to(`merchant:${transaction.merchantId}`).emit("transaction.updated", {
                id: transaction.id,
                status: transaction.status,
                updatedAt: transaction.updatedAt
            });

            // Notify payment page room if applicable
            if (transaction.paymentPageId) {
                io.to(`payment-page:${transaction.paymentPageId}`).emit("transaction.updated", {
                    id: transaction.id,
                    status: transaction.status
                });
            }
        });

        // Payment method events
        paymentMethodEvents.on("paymentMethod.created", (paymentMethod) => {
            // Notify admin room
            io.to("admin").emit("paymentMethod.created", {
                id: paymentMethod.id,
                name: paymentMethod.name,
                type: paymentMethod.type,
                merchantId: paymentMethod.merchantId
            });

            // Notify merchant room
            io.to(`merchant:${paymentMethod.merchantId}`).emit("paymentMethod.created", {
                id: paymentMethod.id,
                name: paymentMethod.name,
                type: paymentMethod.type
            });
        });

        paymentMethodEvents.on("paymentMethod.updated", (paymentMethod) => {
            // Notify admin room
            io.to("admin").emit("paymentMethod.updated", {
                id: paymentMethod.id,
                name: paymentMethod.name,
                isActive: paymentMethod.isActive,
                merchantId: paymentMethod.merchantId
            });

            // Notify merchant room
            io.to(`merchant:${paymentMethod.merchantId}`).emit("paymentMethod.updated", {
                id: paymentMethod.id,
                name: paymentMethod.name,
                isActive: paymentMethod.isActive
            });
        });

        // Verification method events
        verificationMethodEvents.on("verificationMethod.created", (verificationMethod) => {
            // Notify admin room
            io.to("admin").emit("verificationMethod.created", {
                id: verificationMethod.id,
                name: verificationMethod.name,
                type: verificationMethod.type,
                paymentMethodId: verificationMethod.paymentMethodId
            });
        });

        verificationMethodEvents.on("verificationMethod.updated", (verificationMethod) => {
            // Notify admin room
            io.to("admin").emit("verificationMethod.updated", {
                id: verificationMethod.id,
                name: verificationMethod.name,
                isActive: verificationMethod.isActive,
                paymentMethodId: verificationMethod.paymentMethodId
            });
        });

        // Payment page events
        paymentPageEvents.on("paymentPage.created", (paymentPage) => {
            // Notify admin room
            io.to("admin").emit("paymentPage.created", {
                id: paymentPage.id,
                title: paymentPage.title,
                slug: paymentPage.slug,
                merchantId: paymentPage.merchantId
            });

            // Notify merchant room
            io.to(`merchant:${paymentPage.merchantId}`).emit("paymentPage.created", {
                id: paymentPage.id,
                title: paymentPage.title,
                slug: paymentPage.slug
            });
        });

        paymentPageEvents.on("paymentPage.updated", (paymentPage) => {
            // Notify admin room
            io.to("admin").emit("paymentPage.updated", {
                id: paymentPage.id,
                title: paymentPage.title,
                isActive: paymentPage.isActive,
                merchantId: paymentPage.merchantId
            });

            // Notify merchant room
            io.to(`merchant:${paymentPage.merchantId}`).emit("paymentPage.updated", {
                id: paymentPage.id,
                title: paymentPage.title,
                isActive: paymentPage.isActive
            });
        });

        // Merchant events
        merchantEvents.on("merchant.created", (merchant) => {
            // Notify admin room
            io.to("admin").emit("merchant.created", {
                id: merchant.id,
                name: merchant.name,
                email: merchant.email,
                status: merchant.status,
                createdAt: merchant.createdAt
            });
        });

        merchantEvents.on("merchant.updated", (merchant) => {
            // Notify admin room
            io.to("admin").emit("merchant.updated", {
                id: merchant.id,
                name: merchant.name,
                email: merchant.email,
                status: merchant.status,
                updatedAt: merchant.updatedAt
            });

            // Notify merchant room
            io.to(`merchant:${merchant.id}`).emit("merchant.updated", {
                id: merchant.id,
                name: merchant.name,
                email: merchant.email,
                status: merchant.status,
                updatedAt: merchant.updatedAt
            });
        });

        merchantEvents.on("merchant.status.updated", (data) => {
            // Notify admin room
            io.to("admin").emit("merchant.status.updated", {
                id: data.id,
                status: data.status,
                updatedAt: data.updatedAt,
                updatedBy: data.updatedBy,
                reason: data.reason
            });

            // Notify merchant room
            io.to(`merchant:${data.id}`).emit("merchant.status.updated", {
                id: data.id,
                status: data.status,
                updatedAt: data.updatedAt,
                reason: data.reason
            });
        });

        // Notification events
        notificationEvents.on("notification.created", (notification) => {
            // Send to specific user if userId is provided
            if (notification.id // Fixed: using id instead of userId) {
                io.to(`user:${notification.id // Fixed: using id instead of userId}`).emit("notification.created", notification);
            }

            // If the notification has a merchantId, send to that merchant
            if (notification.merchantId) {
                io.to(`merchant:${notification.merchantId}`).emit("notification.created", notification);
            }
        });

        notificationEvents.on("notification.read", (event) => {
            // Send read status to admin room
            io.to("admin").emit("notification.read", event);

            // Send to the user who read the notification
            io.to(`user:${event.id // Fixed: using id instead of userId}`).emit("notification.read", event);
        });

        // Monitoring events
        monitoringEvents.on("request", (data) => {
            // Only send to admin room
            io.to("admin").emit("monitoring.request", {
                endpoint: data.endpoint,
                method: data.method,
                statusCode: data.statusCode,
                responseTime: data.responseTime,
                timestamp: data.timestamp
            });
        });



        return io;
    }
};