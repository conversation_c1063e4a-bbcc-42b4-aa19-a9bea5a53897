/**
 * <PERSON>ie Utility
 *
 * This utility provides functions for managing environment-specific cookies
 * to ensure complete isolation between production and demo environments.
 */
import { Request, Response } from 'express';
/**
 * Cookie options interface
 */
export interface CookieOptions {
    /** Cookie max age in milliseconds */
    maxAge?: number;
    /** <PERSON>ie expiration date */
    expires?: Date;
    /** Cookie path */
    path?: string;
    /** Cookie domain */
    domain?: string;
    /** Cookie secure flag */
    secure?: boolean;
    /** Cookie HTTP only flag */
    httpOnly?: boolean;
    /** Cookie same site policy */
    sameSite?: 'strict' | 'lax' | 'none';
}
/**
 * Get environment-specific cookie name
 * @param baseName Base cookie name
 * @returns Environment-specific cookie name
 */
export declare const getCookieName: (baseName: string) => string;
/**
 * Get environment-specific cookie domain
 * @returns Environment-specific cookie domain
 */
export declare const getCookieDomain: () => string;
/**
 * Get environment-specific cookie options
 * @param options Additional cookie options
 * @returns Environment-specific cookie options
 */
export declare const getCookieOptions: (options?: CookieOptions) => CookieOptions;
/**
 * Set environment-specific cookie
 * @param res Express response
 * @param baseName Base cookie name
 * @param value Cookie value
 * @param options Additional cookie options
 */
export declare const setCookie: (res: Response, baseName: string, value: string, options?: CookieOptions) => void;
/**
 * Get environment-specific cookie
 * @param req Express request
 * @param baseName Base cookie name
 * @returns Cookie value or undefined
 */
export declare const getCookie: (req: Request, baseName: string) => string | undefined;
/**
 * Clear environment-specific cookie
 * @param res Express response
 * @param baseName Base cookie name
 * @param options Additional cookie options
 */
export declare const clearCookie: (res: Response, baseName: string, options?: CookieOptions) => void;
declare const _default: {
    getCookieName: (baseName: string) => string;
    getCookieDomain: () => string;
    getCookieOptions: (options?: CookieOptions) => CookieOptions;
    setCookie: (res: Response, baseName: string, value: string, options?: CookieOptions) => void;
    getCookie: (req: Request, baseName: string) => string | undefined;
    clearCookie: (res: Response, baseName: string, options?: CookieOptions) => void;
};
export default _default;
//# sourceMappingURL=cookie.d.ts.map