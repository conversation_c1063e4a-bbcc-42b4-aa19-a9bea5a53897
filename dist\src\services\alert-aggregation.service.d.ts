import { BaseService } from '../base/BaseService';
/**
 * Alert aggregation service
 */
export declare class AlertAggregationService extends BaseService {
    private alertService;
    private checkInterval;
    private intervalId;
    private isRunning;
    private lastCheckTime;
    private aggregationRules;
    private correlationRules;
    /**
     * Create a new alert aggregation service
     * @param checkInterval Check interval in milliseconds (default: 5 minutes)
     */
    constructor(checkInterval?: number);
    /**
     * Start aggregation service
     */
    start(): Promise<void>;
    /**
     * Stop aggregation service
     */
    stop(): void;
    /**
     * Load aggregation and correlation rules
     */
    private loadRules;
    /**
     * Set default rules
     */
    private setDefaultRules;
    /**
     * Check alerts for aggregation and correlation
     */
    private checkAlerts;
    /**
     * Process aggregation rules
     * @param now Current time
     */
    private processAggregationRules;
    /**
     * Process correlation rules
     * @param now Current time
     */
    private processCorrelationRules;
    /**
     * Get alerts for aggregation
     * @param rule Aggregation rule
     * @param startTime Start time
     * @param endTime End time
     * @returns Alerts
     */
    private getAlertsForAggregation;
    /**
     * Get alerts for correlation
     * @param type Alert type
     * @param startTime Start time
     * @param endTime End time
     * @returns Alerts
     */
    private getAlertsForCorrelation;
    /**
     * Get secondary alerts for correlation
     * @param types Alert types
     * @param startTime Start time
     * @param endTime End time
     * @returns Alerts
     */
    private getSecondaryAlertsForCorrelation;
    /**
     * Group alerts by specified fields
     * @param alerts Alerts
     * @param groupBy Fields to group by
     * @returns Grouped alerts
     */
    private groupAlerts;
    /**
     * Group alerts by merchant
     * @param alerts Alerts
     * @returns Grouped alerts
     */
    private groupAlertsByMerchant;
    /**
     * Create aggregated alert
     * @param rule Aggregation rule
     * @param groupKey Group key
     * @param alerts Alerts in group
     */
    private createAggregatedAlert;
    /**
     * Create correlated alert
     * @param rule Correlation rule
     * @param merchantId Merchant ID
     * @param primaryAlerts Primary alerts
     * @param secondaryAlerts Secondary alerts
     */
    private createCorrelatedAlert;
}
declare const _default: AlertAggregationService;
export default _default;
//# sourceMappingURL=alert-aggregation.service.d.ts.map