"use strict";
// jscpd:ignore-file
/**
 * Common Fee Calculation Strategies
 *
 * Implements common fee calculation strategies.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantSpecificFeeStrategy = exports.FixedFeeStrategy = exports.TieredFeeStrategy = exports.PercentageFeeStrategy = void 0;
const logger_1 = require("../../../../lib/logger");
/**
 * Percentage fee strategy
 *
 * Calculates fee as a percentage of the transaction amount.
 */
class PercentageFeeStrategy {
    /**
     * Constructor
     *
     * @param prisma Prisma client
     * @param defaultPercentage Default fee percentage
     */
    constructor(prisma, defaultPercentage) {
        this.defaultPercentage = 2.5; // 2.5%
        this.methodPercentages = {};
        this.prisma = prisma;
        if (defaultPercentage !== undefined) {
            this.defaultPercentage = defaultPercentage;
        }
        this.loadMethodPercentages();
    }
    /**
     * Get the strategy name
     */
    getName() {
        return 'percentage_fee';
    }
    /**
     * Calculate fee
     */
    calculate(context) {
        const { amount, paymentMethodType } = context;
        // Get percentage for this payment method
        const percentage = this.methodPercentages[paymentMethodType] || this.defaultPercentage;
        // Calculate fee
        const fee = (amount * percentage) / 100;
        return {
            fee,
            description: `${percentage}% of ${amount} ${context.currency}`,
        };
    }
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context) {
        // This strategy applies to all payment methods
        return true;
    }
    /**
     * Load method percentages from database or cache
     */
    async loadMethodPercentages() {
        try {
            // In a real implementation, this would load from database
            // For now, we'll use some hardcoded percentages
            this.methodPercentages = {
                credit_card: 2.9,
                paypal: 3.5,
                crypto_transfer: 1.5,
                binance_pay: 1.0,
                binance_c2c: 1.2,
                binance_trc20: 1.0,
                alipay: 2.0,
                wechat_pay: 2.0,
            };
        }
        catch (error) {
            logger_1.logger.error('Error loading method percentages:', error);
        }
    }
}
exports.PercentageFeeStrategy = PercentageFeeStrategy;
/**
 * Tiered fee strategy
 *
 * Calculates fee based on transaction amount tiers.
 */
class TieredFeeStrategy {
    /**
     * Constructor
     *
     * @param tiers Fee tiers
     */
    constructor(tiers) {
        this.tiers = [];
        if (tiers) {
            this.tiers = tiers;
        }
        else {
            // Default tiers
            this.tiers = [
                { min: 0, max: 100, percentage: 3.0 },
                { min: 100, max: 1000, percentage: 2.5 },
                { min: 1000, max: 10000, percentage: 2.0 },
                { min: 10000, max: Infinity, percentage: 1.5 },
            ];
        }
    }
    /**
     * Get the strategy name
     */
    getName() {
        return 'tiered_fee';
    }
    /**
     * Calculate fee
     */
    calculate(context) {
        const { amount } = context;
        // Find applicable tier
        const tier = this.tiers.find((t) => amount >= t.min && amount < t.max);
        if (!tier) {
            return {
                fee: 0,
                description: 'No applicable tier found',
            };
        }
        // Calculate fee
        const fee = (amount * tier.percentage) / 100;
        return {
            fee,
            description: `${tier.percentage}% (tier ${tier.min}-${tier.max})`,
        };
    }
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context) {
        // This strategy applies to high-value transactions
        return context.amount >= 100;
    }
}
exports.TieredFeeStrategy = TieredFeeStrategy;
/**
 * Fixed fee strategy
 *
 * Adds a fixed fee to the transaction.
 */
class FixedFeeStrategy {
    /**
     * Constructor
     *
     * @param fixedFees Fixed fees by currency
     * @param defaultFee Default fixed fee
     */
    constructor(fixedFees, defaultFee) {
        this.fixedFees = {};
        this.defaultFee = 0.5;
        if (fixedFees) {
            this.fixedFees = fixedFees;
        }
        else {
            // Default fixed fees
            this.fixedFees = {
                USD: 0.3,
                EUR: 0.25,
                GBP: 0.2,
                JPY: 30,
                CNY: 2,
            };
        }
        if (defaultFee !== undefined) {
            this.defaultFee = defaultFee;
        }
    }
    /**
     * Get the strategy name
     */
    getName() {
        return 'fixed_fee';
    }
    /**
     * Calculate fee
     */
    calculate(context) {
        const { currency } = context;
        // Get fixed fee for this currency
        const fixedFee = this.fixedFees[currency] || this.defaultFee;
        return {
            fee: fixedFee,
            description: `Fixed fee of ${fixedFee} ${currency}`,
        };
    }
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context) {
        // This strategy applies to all payment methods except crypto
        return !context.paymentMethodType.includes('crypto');
    }
}
exports.FixedFeeStrategy = FixedFeeStrategy;
/**
 * Merchant-specific fee strategy
 *
 * Applies custom fees for specific merchants.
 */
class MerchantSpecificFeeStrategy {
    /**
     * Constructor
     *
     * @param prisma Prisma client
     */
    constructor(prisma) {
        this.merchantFees = {};
        this.prisma = prisma;
        this.loadMerchantFees();
    }
    /**
     * Get the strategy name
     */
    getName() {
        return 'merchant_specific_fee';
    }
    /**
     * Calculate fee
     */
    calculate(context) {
        const { merchantId, amount } = context;
        // Get percentage for this merchant
        const percentage = this.merchantFees[merchantId];
        if (percentage === undefined) {
            return {
                fee: 0,
                description: 'No merchant-specific fee',
            };
        }
        // Calculate fee
        const fee = (amount * percentage) / 100;
        return {
            fee,
            description: `Merchant-specific, fee: ${percentage}%`,
        };
    }
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context) {
        // This strategy applies only to merchants with specific fees
        return this.merchantFees[context.merchantId] !== undefined;
    }
    /**
     * Load merchant fees from database or cache
     */
    async loadMerchantFees() {
        try {
            // In a real implementation, this would load from database
            // For now, we'll use some hardcoded merchant fees
            this.merchantFees = {
                'merchant-1': 1.5,
                'merchant-2': 2.0,
                'merchant-3': 1.8,
            };
        }
        catch (error) {
            logger_1.logger.error('Error loading merchant fees:', error);
        }
    }
}
exports.MerchantSpecificFeeStrategy = MerchantSpecificFeeStrategy;
//# sourceMappingURL=CommonFeeStrategies.js.map