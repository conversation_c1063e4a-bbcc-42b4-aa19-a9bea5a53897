"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BinanceApiService = void 0;
// jscpd:ignore-file
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const environment_1 = require("../config/environment");
const service_config_1 = require("../utils/service-config");
/**
 * Service for interacting with the Binance API
 */
class BinanceApiService {
    /**
   * Create a new BinanceApiService instance
   * @param apiConfig Binance API configuration
   */
    constructor(apiConfig) {
        // Get environment-specific configuration
        const env = (0, environment_1.getEnvironment)();
        // Use provided config, environment-specific config, or default
        this.apiKey = apiConfig?.apiKey || (0, service_config_1.getApiKey)("binance", config_1.config.binance?.apiKey || "");
        this.apiSecret = apiConfig?.secretKey || (0, service_config_1.getApiSecret)("binance", config_1.config.binance?.secretKey || "");
        this.baseUrl = apiConfig?.baseUrl || (0, service_config_1.getApiUrl)("binance", config_1.config.binance?.apiUrl || "https://api.binance.com");
        // Log service configuration (masked)
        (0, service_config_1.logServiceConfig)("binance");
        logger_1.logger.debug(`Initialized Binance API service for ${env} environment with URL: ${this.baseUrl}`);
    }
    /**
   * Create a BinanceApiService instance from merchant configuration
   * @param apiKey Binance API key
   * @param secretKey Binance API secret
   * @returns BinanceApiService instance
   */
    static createFromMerchantConfig(apiKey, secretKey) {
        return new BinanceApiService({
            apiKey,
            secretKey
        });
    }
    /**
   * Generate signature for Binance API requests
   * @param queryString Query string to sign
   * @returns Signature
   */
    async makeRequest(endpoint, method = "GET", params = {}) {
        try {
            // Add timestamp to params
            const timestamp = Date.now();
            params.timestamp = timestamp;
            // Convert params to query string
            const queryString = Object.entries(params)
                .map(([key, value]) => `${key}=${value}`)
                .join("&");
            // Generate signature
            const signature = this.generateSignature(queryString);
            // Add signature to query string
            const queryStringWithSignature = `${queryString}&signature=${signature}`;
            // Make request
            const url = `${this.baseUrl}${endpoint}?${queryStringWithSignature}`;
            logger_1.logger.debug(`Making ${method} request to ${url}`);
            const response = await (0, axios_1.default)({
                method,
                url,
                headers: this.createHeaders()
            });
            return response.data;
        }
        catch (error) {
            logger_1.logger.error(`Binance API error: ${error}`);
            throw error;
        }
    }
    /**
   * Test API connection
   * @returns Connection status
   */
    async testConnection() {
        try {
            await this.makeRequest("/api/v3/ping");
            return {
                success: true,
                data: { success: true },
                message: "Connection successful"
            };
        }
        catch (error) {
            logger_1.logger.error("Error testing Binance API connection", { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Connection failed"
            };
        }
    }
    /**
   * Get account information
   * @returns Account information
   */
    async getAccountInfo() {
        try {
            const data = await this.makeRequest("/api/v3/account");
            return {
                success: true,
                data
            };
        }
        catch (error) {
            logger_1.logger.error("Error getting account information from Binance API", { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Failed to get account information"
            };
        }
    }
    /**
   * Get deposit history
   * @param coin Coin symbol (e.g., USDT)
   * @param network Network (e.g., TRC20)
   * @param startTime Start time in milliseconds
   * @param endTime End time in milliseconds
   * @param status Deposit status (0: pending, 1: success, etc.)
   * @returns Deposit history
   */
    async getDepositHistory(coin, network, startTime, endTime, status) {
        try {
            const params = {};
            if (coin)
                params.coin = coin;
            if (network)
                params.network = network;
            if (startTime)
                params.startTime = startTime;
            if (endTime)
                params.endTime = endTime;
            if (status !== undefined)
                params.status = status;
            const data = await this.makeRequest("/sapi/v1/capital/deposit/hisrec", "GET", params);
            return {
                success: true,
                data
            };
        }
        catch (error) {
            logger_1.logger.error("Error getting deposit history from Binance API", { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Failed to get deposit history"
            };
        }
    }
    /**
   * Verify a TRC20 transaction
   * @param walletAddress Wallet address
   * @param amount Expected amount
   * @param txHash Transaction hash (optional)
   * @param coin Coin symbol (e.g., USDT)
   * @returns Verification result
   */
    async verifyTRC20Transaction(walletAddress, amount, txHash, coin = "USDT") {
        try {
            logger_1.logger.info(`Verifying TRC20 transaction for wallet: ${walletAddress}, amount: ${amount}, txHash: ${txHash || "not provided"}`);
            // Get deposit history for the last 24 hours
            const endTime = Date.now();
            const startTime = endTime - 24 * 60 * 60 * 1000; // 24 hours ago
            const depositsResponse = await this.getDepositHistory(coin, "TRC20", startTime, endTime, 1); // Status 1 = success
            if (!depositsResponse.success || !depositsResponse.data) {
                return {
                    success: false,
                    error: depositsResponse.error || "Failed to get deposit history",
                    message: "Failed to verify transaction: could not retrieve deposit history"
                };
            }
            const deposits = depositsResponse.data;
            logger_1.logger.debug(`Found ${deposits.length} deposits for ${coin} on TRC20 network`);
            // If txHash is provided, find the specific transaction
            if (txHash) {
                const matchingDeposit = deposits.find((deposit) => deposit.txId === txHash &&
                    deposit.address === walletAddress &&
                    Math.abs(deposit.amount - amount) < 0.01 // Allow small difference due to fees
                );
                if (matchingDeposit) {
                    logger_1.logger.info(`Found matching deposit for txHash: ${txHash}`);
                    return {
                        success: true,
                        data: { verified: true,
                            transaction: matchingDeposit
                        },
                        message: "Transaction verified successfully"
                    };
                }
                else {
                    logger_1.logger.warn(`No matching deposit found for txHash: ${txHash}`);
                    return {
                        success: false,
                        message: "Transaction not found or details do not match"
                    };
                }
            }
            else {
                // If no txHash is provided, find any transaction with matching amount and address
                const matchingDeposits = deposits.filter((deposit) => deposit.address === walletAddress &&
                    Math.abs(deposit.amount - amount) < 0.01);
                if (matchingDeposits.length > 0) {
                    // Sort by timestamp (newest first) and take the first one
                    matchingDeposits.sort((a, b) => b.insertTime - a.insertTime);
                    const latestDeposit = matchingDeposits[0];
                    logger_1.logger.info(`Found matching deposit without txHash: ${latestDeposit.txId}`);
                    return {
                        success: true,
                        data: { verified: true,
                            transaction: latestDeposit
                        },
                        message: "Transaction verified successfully"
                    };
                }
                else {
                    logger_1.logger.warn(`No matching deposit found for address: ${walletAddress} and amount: ${amount}`);
                    return {
                        success: false,
                        message: "No matching transaction found"
                    };
                }
            }
        }
        catch (error) {
            logger_1.logger.error("Error verifying TRC20 transaction", { error, walletAddress, amount, txHash });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                message: "Failed to verify transaction due to an error"
            };
        }
    }
}
exports.BinanceApiService = BinanceApiService;
//# sourceMappingURL=binance-api.service.js.map