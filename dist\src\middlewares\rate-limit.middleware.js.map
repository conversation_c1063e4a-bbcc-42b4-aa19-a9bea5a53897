{"version": 3, "file": "rate-limit.middleware.js", "sourceRoot": "", "sources": ["../../../src/middlewares/rate-limit.middleware.ts"], "names": [], "mappings": ";;;;;;AAAA,oBAAoB;AACpB,4EAA2C;AAC3C,0CAAuC;AAevC;;;GAGG;AACU,QAAA,UAAU,GAAQ,IAAA,4BAAS,EAAC;IACvC,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE,wBAAwB;IAC9F,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,EAAE,EAAE,CAAC,EAAE,6CAA6C;IACrG,eAAe,EAAE,IAAI,EAAE,sDAAsD;IAC7E,aAAa,EAAE,KAAK,EAAE,sCAAsC;IAC5D,OAAO,EAAE;QACP,MAAM,EAAE,OAAO;QACf,IAAI,EAAE,qBAAqB;QAC3B,OAAO,EAAE,4CAA4C;KACtD;IACD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QACnC,eAAM,CAAC,IAAI,CAAC,+BAA+B,GAAG,CAAC,EAAE,EAAE,EAAE;YACnD,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;SACrC,CAAC,CAAC;QAEH,qBAAqB;QACrB,GAAG,CAAC,SAAS,CACX,aAAa,EACb,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAC7E,CAAC;QAEF,gBAAgB;QAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IACD,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE;QACZ,gDAAgD;QAChD,OAAO,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,CAAC;IAC9D,CAAC;IACD,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE;QACpB,mCAAmC;QACnC,IAAI,GAAG,GAAQ,GAAG,CAAC,EAAE,CAAC;QAEtB,+CAA+C;QAC/C,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;YACjB,oCAAoC;YACpC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,oCAAoC;QACzD,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;CACF,CAAC,CAAC;AAEH;;;GAGG;AACU,QAAA,WAAW,GAAQ,IAAA,4BAAS,EAAC;IACxC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;IACnC,GAAG,EAAE,CAAC,EAAE,6CAA6C;IACrD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE;QACP,MAAM,EAAE,OAAO;QACf,IAAI,EAAE,0BAA0B;QAChC,OAAO,EAAE,kDAAkD;KAC5D;IACD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QACnC,eAAM,CAAC,IAAI,CAAC,8CAA8C,GAAG,CAAC,EAAE,EAAE,EAAE;YAClE,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,8CAA8C;SACvE,CAAC,CAAC;QAEH,qBAAqB;QACrB,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEjD,gBAAgB;QAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;CACF,CAAC,CAAC;AAEH;;;GAGG;AACU,QAAA,oBAAoB,GAAQ,IAAA,4BAAS,EAAC;IACjD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;IACnC,GAAG,EAAE,CAAC,EAAE,sDAAsD;IAC9D,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE;QACP,MAAM,EAAE,OAAO;QACf,IAAI,EAAE,oCAAoC;QAC1C,OAAO,EAAE,2DAA2D;KACrE;IACD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QACnC,eAAM,CAAC,IAAI,CAAC,8CAA8C,GAAG,CAAC,EAAE,EAAE,EAAE;YAClE,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,8CAA8C;SACvE,CAAC,CAAC;QAEH,qBAAqB;QACrB,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEjD,gBAAgB;QAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;CACF,CAAC,CAAC;AAEH;;;GAGG;AACU,QAAA,yBAAyB,GAAQ,IAAA,4BAAS,EAAC;IACtD,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;IAC1C,GAAG,EAAE,CAAC,EAAE,kDAAkD;IAC1D,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE;QACP,MAAM,EAAE,OAAO;QACf,IAAI,EAAE,yCAAyC;QAC/C,OAAO,EAAE,wDAAwD;KAClE;IACD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QACnC,eAAM,CAAC,IAAI,CAAC,mDAAmD,GAAG,CAAC,EAAE,EAAE,EAAE;YACvE,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,oCAAoC;SAC3D,CAAC,CAAC;QAEH,qBAAqB;QACrB,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEtD,gBAAgB;QAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;CACF,CAAC,CAAC"}