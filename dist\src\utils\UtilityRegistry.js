"use strict";
// jscpd:ignore-file
/**
 * Utility Registry
 *
 * This file serves as a central registry for all utility functions in the application.
 * It helps eliminate duplication by providing a single source of truth for utility functions.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UtilityRegistry = void 0;
exports.getUtilityRegistry = getUtilityRegistry;
// Import all utility functions
const errorHandling = __importStar(require("./errorHandling"));
const repositoryUtils = __importStar(require("./repositoryUtils"));
const controllerUtils = __importStar(require("./controllerUtils"));
const common = __importStar(require("./common"));
/**
 * Utility registry
 */
exports.UtilityRegistry = {
    // Error handling utilities
    handleError: errorHandling.handleError,
    validateField: errorHandling.validateField,
    createSuccessResponse: errorHandling.createSuccessResponse,
    createPaginatedResponse: errorHandling.createPaginatedResponse,
    // Repository utilities
    executeRepositoryMethod: repositoryUtils.executeRepositoryMethod,
    executeTransaction: repositoryUtils.executeTransaction,
    createWhereClause: repositoryUtils.createWhereClause,
    createOrderByClause: repositoryUtils.createOrderByClause,
    // Controller utilities
    sendSuccess: controllerUtils.sendSuccess,
    sendPaginatedResponse: controllerUtils.sendPaginatedResponse,
    checkAuthorization: controllerUtils.checkAuthorization,
    checkAdminRole: controllerUtils.checkAdminRole,
    parseDateRange: controllerUtils.parseDateRange,
    determineTargetMerchantId: controllerUtils.determineTargetMerchantId,
    // Common utilities
    isDefined: common.isDefined,
    isUndefined: common.isUndefined,
    isEmpty: common.isEmpty,
    isNotEmpty: common.isNotEmpty
};
/**
 * Get utility registry
 * @returns Utility registry
 */
function getUtilityRegistry() {
    return exports.UtilityRegistry;
}
//# sourceMappingURL=UtilityRegistry.js.map