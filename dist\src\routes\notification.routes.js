"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const notification_controller_ts_1 = require("../controllers/refactored/notification.controller.ts");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// User notification preferences
router.get("/preferences", auth_1.authenticate, notification_controller_ts_1.NotificationController.getUserPreferences);
router.put("/preferences", auth_1.authenticate, notification_controller_ts_1.NotificationController.updateUserPreferences);
// Merchant notification preferences
router.get("/merchant-preferences", auth_1.authenticate, notification_controller_ts_1.NotificationController.getMerchantPreferences);
router.put("/merchant-preferences", auth_1.authenticate, notification_controller_ts_1.NotificationController.updateMerchantPreferences);
// Test notification
router.post("/test", auth_1.authenticate, notification_controller_ts_1.NotificationController.sendTestNotification);
// Notification templates
router.get("/templates", auth_1.authenticate, notification_controller_ts_1.NotificationController.getTemplates);
router.post("/templates", auth_1.authenticate, notification_controller_ts_1.NotificationController.createTemplate);
router.put("/templates/:id", auth_1.authenticate, notification_controller_ts_1.NotificationController.updateTemplate);
router.delete("/templates/:id", auth_1.authenticate, notification_controller_ts_1.NotificationController.deleteTemplate);
exports.default = router;
//# sourceMappingURL=notification.routes.js.map