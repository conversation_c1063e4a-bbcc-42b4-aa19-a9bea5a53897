"use strict";
// jscpd:ignore-file
/**
 * Feature Flags Middleware
 *
 * This middleware provides functions for checking feature flags in requests
 * to ensure complete isolation between production and demo environments.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkRealPaymentsEnabled = exports.addFeatureFlagsToResponse = exports.checkFeatureEnabled = void 0;
const logger_1 = require("../lib/logger");
const feature_flags_1 = require("../utils/feature-flags");
const error_middleware_1 = require("./error.middleware");
/**
 * Check if a feature is enabled
 * @param featureName Feature name
 * @returns Middleware function
 */
const checkFeatureEnabled = (featureName) => {
    return (req, res, next) => {
        try {
            // Check if feature is enabled
            if (!(0, feature_flags_1.isFeatureEnabled)(featureName)) {
                logger_1.logger.warn(`Feature not enabled: ${featureName}`, {
                    feature: featureName,
                    environment: req.environment,
                    ip: req.ip,
                    path: req.path,
                    method: req.method,
                    requestId: req.requestId
                });
                return next(new error_middleware_1.AppError(`Feature not enabled: ${featureName}`, 403, true));
            }
            next();
        }
        catch (error) {
            logger_1.logger.error(`Error checking feature flag: ${featureName}`, error);
            next(error);
        }
    };
};
exports.checkFeatureEnabled = checkFeatureEnabled;
/**
 * Add feature flags to response
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const addFeatureFlagsToResponse = (req, res, next) => {
    try {
        // Add feature flags to response headers
        res.setHeader("X-Feature-Binance-Payments", (0, feature_flags_1.isFeatureEnabled)("ENABLE_BINANCE_PAYMENTS") ? "true" : "false");
        res.setHeader("X-Feature-Crypto-Payments", (0, feature_flags_1.isFeatureEnabled)("ENABLE_CRYPTO_PAYMENTS") ? "true" : "false");
        res.setHeader("X-Feature-Fiat-Payments", (0, feature_flags_1.isFeatureEnabled)("ENABLE_FIAT_PAYMENTS") ? "true" : "false");
        res.setHeader("X-Feature-2FA", (0, feature_flags_1.isFeatureEnabled)("ENABLE_2FA") ? "true" : "false");
        res.setHeader("X-Feature-Demo-Banner", (0, feature_flags_1.isFeatureEnabled)("ENABLE_DEMO_BANNER") ? "true" : "false");
        next();
    }
    catch (error) {
        logger_1.logger.error("Error adding feature flags to response", error);
        next(error);
    }
};
exports.addFeatureFlagsToResponse = addFeatureFlagsToResponse;
/**
 * Check if real payments are enabled
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const checkRealPaymentsEnabled = (req, res, next) => {
    try {
        // Check if real payments are disabled
        if ((0, feature_flags_1.isFeatureEnabled)("DISABLE_REAL_PAYMENTS")) {
            logger_1.logger.warn("Real payments are disabled in this environment", {
                environment: req.environment,
                ip: req.ip,
                path: req.path,
                method: req.method,
                requestId: req.requestId
            });
            return next(new error_middleware_1.AppError("Real payments are disabled in this environment", 403, true));
        }
        next();
    }
    catch (error) {
        logger_1.logger.error("Error checking real payments feature flag", error);
        next(error);
    }
};
exports.checkRealPaymentsEnabled = checkRealPaymentsEnabled;
exports.default = {
    checkFeatureEnabled: exports.checkFeatureEnabled,
    addFeatureFlagsToResponse: exports.addFeatureFlagsToResponse,
    checkRealPaymentsEnabled: exports.checkRealPaymentsEnabled
};
//# sourceMappingURL=feature-flags.middleware.js.map