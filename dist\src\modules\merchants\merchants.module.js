"use strict";
// jscpd:ignore-file
/**
 * Merchants Module
 *
 * This module handles merchant management.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const ModuleFactory_1 = require("../../factories/ModuleFactory");
const logger_1 = require("../../utils/logger");
/**
 * Merchants Module
 */
class MerchantsModule extends ModuleFactory_1.BaseModule {
    /**
     * Constructor
     */
    constructor() {
        super('MerchantsModule');
    }
    /**
     * Initialize the module
     */
    initialize() {
        logger_1.logger.info('Initializing MerchantsModule');
        // Get controllers
        const merchantController = this.controllerFactory.getController('merchant');
        // Set up routes
        this.router.get('/', merchantController.getAll);
        this.router.get('/:id', merchantController.getById);
        this.router.post('/', merchantController.create);
        this.router.put('/:id', merchantController.update);
        this.router.delete('/:id', merchantController.delete);
        this.router.get('/:id/payment-methods', merchantController.getPaymentMethods);
        this.router.get('/:id/transactions', merchantController.getTransactions);
        this.router.get('/:id/statistics', merchantController.getStatistics);
        logger_1.logger.info('MerchantsModule initialized');
    }
}
// Export the module
exports.default = new MerchantsModule();
//# sourceMappingURL=merchants.module.js.map