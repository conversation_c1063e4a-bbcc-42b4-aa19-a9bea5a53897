import { Request, Response } from 'express';
import { ReportService } from '../services/reporting';

export class AdvancedReportController {
  private reportService: ReportService;

  constructor() {
    this.reportService = new ReportService();
  }

  /**
   * Generate a report
   */
  public generateReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { type, format, ...parameters } = req.body;

      // Add user info to parameters
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const reportParams = {
        ...parameters,
        userId,
        userRole,
      };

      const result = await this.reportService.generateReport(type, reportParams, format);

      res.json({
        success: true,
        data: result,
      });
    } catch (error: any) {
      console.error('Error generating report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error generating report',
      });
    }
  };

  /**
   * Get report templates
   */
  public getReportTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const includeSystem = req.query.includeSystem !== 'false';

      const templates = await this.reportService.getReportTemplates(userId, includeSystem);

      res.json({
        success: true,
        data: templates,
      });
    } catch (error: any) {
      console.error('Error getting report templates:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting report templates',
      });
    }
  };

  /**
   * Get a report template by ID
   */
  public getReportTemplateById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const template = await this.reportService.getReportTemplateById(id);

      if (!template) {
        res.status(404).json({ success: false, message: 'Report template not found' });
        return;
      }

      res.json({
        success: true,
        data: template,
      });
    } catch (error: any) {
      console.error('Error getting report template:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting report template',
      });
    }
  };

  /**
   * Create a report template
   */
  public createReportTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const data = {
        ...req.body,
        createdById: userId,
      };

      const template = await this.reportService.createReportTemplate(data);

      res.status(201).json({
        success: true,
        data: template,
      });
    } catch (error: any) {
      console.error('Error creating report template:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error creating report template',
      });
    }
  };

  /**
   * Update a report template
   */
  public updateReportTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const template = await this.reportService.updateReportTemplate(id, req.body);

      res.json({
        success: true,
        data: template,
      });
    } catch (error: any) {
      console.error('Error updating report template:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error updating report template',
      });
    }
  };

  /**
   * Delete a report template
   */
  public deleteReportTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      await this.reportService.deleteReportTemplate(id);

      res.json({
        success: true,
        message: 'Report template deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting report template:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error deleting report template',
      });
    }
  };

  /**
   * Get scheduled reports
   */
  public getScheduledReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const reports = await this.reportService.getScheduledReports(userId);

      res.json({
        success: true,
        data: reports,
      });
    } catch (error: any) {
      console.error('Error getting scheduled reports:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting scheduled reports',
      });
    }
  };

  /**
   * Get a scheduled report by ID
   */
  public getScheduledReportById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const report = await this.reportService.getScheduledReportById(id);

      if (!report) {
        res.status(404).json({ success: false, message: 'Scheduled report not found' });
        return;
      }

      res.json({
        success: true,
        data: report,
      });
    } catch (error: any) {
      console.error('Error getting scheduled report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting scheduled report',
      });
    }
  };

  /**
   * Create a scheduled report
   */
  public createScheduledReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const data = {
        ...req.body,
        createdById: userId,
      };

      const report = await this.reportService.createScheduledReport(data);

      res.status(201).json({
        success: true,
        data: report,
      });
    } catch (error: any) {
      console.error('Error creating scheduled report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error creating scheduled report',
      });
    }
  };

  /**
   * Update a scheduled report
   */
  public updateScheduledReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const report = await this.reportService.updateScheduledReport(id, req.body);

      res.json({
        success: true,
        data: report,
      });
    } catch (error: any) {
      console.error('Error updating scheduled report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error updating scheduled report',
      });
    }
  };

  /**
   * Delete a scheduled report
   */
  public deleteScheduledReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      await this.reportService.deleteScheduledReport(id);

      res.json({
        success: true,
        message: 'Scheduled report deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting scheduled report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error deleting scheduled report',
      });
    }
  };

  /**
   * Run a scheduled report now
   */
  public runScheduledReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const result = await this.reportService.runScheduledReport(id);

      res.json({
        success: true,
        data: result,
      });
    } catch (error: any) {
      console.error('Error running scheduled report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error running scheduled report',
      });
    }
  };

  /**
   * Get saved reports
   */
  public getSavedReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const reports = await this.reportService.getSavedReports(userId);

      res.json({
        success: true,
        data: reports,
      });
    } catch (error: any) {
      console.error('Error getting saved reports:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting saved reports',
      });
    }
  };

  /**
   * Get a saved report by ID
   */
  public getSavedReportById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const report = await this.reportService.getSavedReportById(id);

      if (!report) {
        res.status(404).json({ success: false, message: 'Saved report not found' });
        return;
      }

      res.json({
        success: true,
        data: report,
      });
    } catch (error: any) {
      console.error('Error getting saved report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error getting saved report',
      });
    }
  };

  /**
   * Delete a saved report
   */
  public deleteSavedReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      await this.reportService.deleteSavedReport(id);

      res.json({
        success: true,
        message: 'Saved report deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting saved report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error deleting saved report',
      });
    }
  };

  /**
   * Download a saved report
   */
  public downloadSavedReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      // Get the saved report
      const savedReport = await this.reportService.getSavedReportById(id);

      if (!savedReport) {
        res.status(404).json({ success: false, message: 'Report not found' });
        return;
      }

      // Check if user has permission to download this report
      if (savedReport.createdById !== userId && req.user?.role !== 'ADMIN') {
        res.status(403).json({ success: false, message: 'Access denied' });
        return;
      }

      // Check if file exists
      const fs = require('fs');
      if (!fs.existsSync(savedReport.filePath)) {
        res.status(404).json({ success: false, message: 'Report file not found' });
        return;
      }

      // Set content type based on format
      const contentTypes: { [key: string]: string } = {
        CSV: 'text/csv',
        PDF: 'application/pdf',
        EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        JSON: 'application/json',
      };

      const contentType = contentTypes[savedReport.format] || 'application/octet-stream';
      const fileName = `${savedReport.name}_${savedReport.id}.${savedReport.format.toLowerCase()}`;

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

      // Stream file to response
      const fileStream = fs.createReadStream(savedReport.filePath);
      fileStream.pipe(res);
    } catch (error: any) {
      console.error('Error downloading saved report:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Error downloading saved report',
      });
    }
  };
}
