"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const analytics_controller_1 = __importDefault(require("../controllers/analytics.controller"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
/**
 * @route   GET /api/analytics/payments
 * @desc    Get payment analytics
 * @access  Admin
 */
router.get("/payments", auth_1.authenticateJWT, auth_1.isAdmin, analytics_controller_1.default.getPaymentAnalytics);
/**
 * @route   GET /api/analytics/merchants/:merchantId
 * @desc    Get merchant analytics
 * @access  Merchant or Admin
 */
router.get("/merchants/:merchantId", auth_1.authenticateJWT, auth_1.isMerchantOrAdmin, analytics_controller_1.default.getMerchantAnalytics);
/**
 * @route   GET /api/analytics/payment-methods/:paymentMethodType
 * @desc    Get payment method analytics
 * @access  Admin
 */
router.get("/payment-methods/:paymentMethodType", auth_1.authenticateJWT, auth_1.isAdmin, analytics_controller_1.default.getPaymentMethodAnalytics);
/**
 * @route   GET /api/analytics/dashboard
 * @desc    Get dashboard analytics
 * @access  Merchant or Admin
 */
router.get("/dashboard", auth_1.authenticateJWT, auth_1.isMerchantOrAdmin, analytics_controller_1.default.getDashboardAnalytics);
exports.default = router;
//# sourceMappingURL=analytics.routes.js.map