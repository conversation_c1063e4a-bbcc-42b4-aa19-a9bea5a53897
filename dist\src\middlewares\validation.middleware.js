"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateEnumParams = exports.validateNumericParams = exports.validateDateRange = exports.validateDateParams = exports.validate = void 0;
const express_validator_1 = require("express-validator");
const appError_1 = require("../utils/appError");
/**
 * Validate request using express-validator
 * @param validations Validation chains
 */
const validate = (validations) => {
    return async (req, res, next) => {
        await Promise.all(validations.map((validation) => validation.run(req)));
        const errors = (0, express_validator_1.validationResult)(req);
        if (errors.isEmpty()) {
            return next();
        }
        res.status(400).json({
            status: 'error',
            errors: errors.array(),
        });
    };
};
exports.validate = validate;
/**
 * Middleware to validate date parameters
 * @param params Parameters to validate
 */
const validateDateParams = (params) => {
    return (req, res, next) => {
        for (const param of params) {
            const value = req.query[param];
            if (!value) {
                return next(new appError_1.AppError(`${param} is required`, 400));
            }
            const date = new Date(value);
            if (isNaN(date.getTime())) {
                return next(new appError_1.AppError(`Invalid ${param} format`, 400));
            }
        }
        next();
    };
};
exports.validateDateParams = validateDateParams;
/**
 * Middleware to validate date range parameters
 * @param startParam Start date parameter name
 * @param endParam End date parameter name
 */
const validateDateRange = (startParam = 'startDate', endParam = 'endDate') => {
    return (req, res, next) => {
        const startDateStr = req.query[startParam];
        const endDateStr = req.query[endParam];
        if (!startDateStr || !endDateStr) {
            return next(new appError_1.AppError(`${startParam} and ${endParam} are required`, 400));
        }
        const startDate = new Date(startDateStr);
        const endDate = new Date(endDateStr);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return next(new appError_1.AppError({
                message: 'Invalid date format',
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT,
            }));
        }
        if (startDate > endDate) {
            return next(new appError_1.AppError(`${startParam} must be before ${endParam}`, 400));
        }
        next();
    };
};
exports.validateDateRange = validateDateRange;
/**
 * Middleware to validate numeric parameters
 * @param params Parameters to validate with options
 */
const validateNumericParams = (params) => {
    return (req, res, next) => {
        for (const param of params) {
            const source = param.source || 'query';
            const value = source === 'query'
                ? req.query[param.name]
                : source === 'body'
                    ? req.body[param.name]
                    : req.params[param.name];
            if (param.required && (value === undefined || value === null || value === '')) {
                return next(new appError_1.AppError(`${param.name} is required`, 400));
            }
            if (value !== undefined && value !== null && value !== '') {
                const numValue = Number(value);
                if (isNaN(numValue)) {
                    return next(new appError_1.AppError(`${param.name} must be a number`, 400));
                }
                if (param.min !== undefined && numValue < param.min) {
                    return next(new appError_1.AppError(`${param.name} must be at least ${param.min}`, 400));
                }
                if (param.max !== undefined && numValue > param.max) {
                    return next(new appError_1.AppError(`${param.name} must be at most ${param.max}`, 400));
                }
            }
        }
        next();
    };
};
exports.validateNumericParams = validateNumericParams;
/**
 * Middleware to validate enum parameters
 * @param params Parameters to validate with options
 */
const validateEnumParams = (params) => {
    return (req, res, next) => {
        for (const param of params) {
            const source = param.source || 'query';
            const value = source === 'query'
                ? req.query[param.name]
                : source === 'body'
                    ? req.body[param.name]
                    : req.params[param.name];
            if (param.required && (value === undefined || value === null || value === '')) {
                return next(new appError_1.AppError(`${param.name} is required`, 400));
            }
            if (value !== undefined && value !== null && value !== '' && !param.values.includes(value)) {
                return next(new appError_1.AppError(`Invalid ${param.name}. Must be one of: ${param.values.join(', ')}`, 400));
            }
        }
        next();
    };
};
exports.validateEnumParams = validateEnumParams;
//# sourceMappingURL=validation.middleware.js.map