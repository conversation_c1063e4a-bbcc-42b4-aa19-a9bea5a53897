"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const location_controller_1 = require("../controllers/location.controller");
const router = express_1.default.Router();
// Public routes
router.get("/countries", location_controller_1.getCountries);
router.get("/governorates/:countryCode", location_controller_1.getGovernoratesByCountry);
exports.default = router;
//# sourceMappingURL=location.routes.js.map