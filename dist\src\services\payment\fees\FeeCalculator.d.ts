/**
 * Fee Calculator
 *
 * Implements a flexible fee calculation system.
 */
import { PaymentMethodType } from '../../../types/payment-method.types';
/**
 * Fee calculation context
 */
export interface FeeCalculationContext {
    merchantId: string;
    amount: number;
    currency: string;
    paymentMethodType: PaymentMethodType;
    metadata?: Record<string, any>;
}
/**
 * Fee calculation result
 */
export interface FeeCalculationResult {
    fee: number;
    feePercentage: number;
    breakdown: Record<string, number>;
    currency: string;
    description: string;
}
/**
 * Fee calculation strategy interface
 */
export interface IFeeCalculationStrategy {
    /**
     * Get the strategy name
     */
    getName(): string;
    /**
     * Calculate fee
     */
    calculate(context: FeeCalculationContext): {
        fee: number;
        description: string;
    };
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context: FeeCalculationContext): boolean;
}
/**
 * Fee calculator
 */
export declare class FeeCalculator {
    private strategies;
    /**
     * Add a fee calculation strategy
     *
     * @param strategy Fee calculation strategy
     * @returns This calculator for chaining
     */
    addStrategy(strategy: IFeeCalculationStrategy): FeeCalculator;
    /**
     * Add multiple fee calculation strategies
     *
     * @param strategies Array of fee calculation strategies
     * @returns This calculator for chaining
     */
    addStrategies(strategies: IFeeCalculationStrategy[]): FeeCalculator;
    /**
     * Calculate fee
     *
     * @param context Fee calculation context
     * @returns Fee calculation result
     */
    calculateFee(context: FeeCalculationContext): FeeCalculationResult;
}
//# sourceMappingURL=FeeCalculator.d.ts.map