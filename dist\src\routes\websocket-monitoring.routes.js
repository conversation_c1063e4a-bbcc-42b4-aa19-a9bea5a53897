"use strict";
// jscpd:ignore-file
/**
 * WebSocket Monitoring Routes
 *
 * These routes provide endpoints for monitoring WebSocket connections
 * and retrieving statistics and health information.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const websocket_monitor_1 = __importDefault(require("../utils/websocket-monitor"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const logger_1 = require("../lib/logger");
const router = (0, express_1.Router)();
/**
 * Get WebSocket statistics
 * @route GET /api/monitoring/websocket/stats
 * @access Admin
 */
router.get("/stats", auth_middleware_1.isAdmin, (req, res) => {
    try {
        const stats = websocket_monitor_1.default.getStats();
        res.status(200).json({
            status: "success",
            data: {
                stats
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error retrieving WebSocket stats:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to retrieve WebSocket statistics",
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Get active WebSocket connections
 * @route GET /api/monitoring/websocket/connections
 * @access Admin
 */
router.get("/connections", auth_middleware_1.isAdmin, (req, res) => {
    try {
        const stats = websocket_monitor_1.default.getStats();
        res.status(200).json({
            status: "success",
            data: { activeConnections: stats.activeConnections,
                totalConnections: stats.totalConnections,
                lastActivity: stats.lastActivity
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error retrieving WebSocket connections:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to retrieve WebSocket connections",
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
/**
 * Get WebSocket connection history
 * @route GET /api/monitoring/websocket/history
 * @access Admin
 */
router.get("/history", auth_middleware_1.isAdmin, (req, res) => {
    try {
        const stats = websocket_monitor_1.default.getStats();
        res.status(200).json({
            status: "success",
            data: { connectionHistory: stats.connectionHistory,
                disconnectionHistory: stats.disconnectionHistory,
                errorHistory: stats.errorHistory,
                messageHistory: stats.messageHistory
            }
        });
    }
    catch (error) {
        logger_1.logger.error("Error retrieving WebSocket history:", error);
        res.status(500).json({
            status: "error",
            message: "Failed to retrieve WebSocket history",
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
exports.default = router;
//# sourceMappingURL=websocket-monitoring.routes.js.map