"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainAllEntries = toContainAllEntries;
const utils_1 = require("../utils");
function toContainAllEntries(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    const pass = typeof actual === 'object' &&
        actual !== null &&
        !Array.isArray(actual) &&
        expected.length == Object.keys(actual).length &&
        expected.every(entry => 
        // @ts-expect-error containsEntry takes an any type
        (0, utils_1.containsEntry)((a, b) => this.equals(a, b, this.customTesters), actual, entry));
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainAllEntries') +
                '\n\n' +
                'Expected object to not only contain all of the given entries:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainAllEntries') +
                '\n\n' +
                'Expected object to only contain all of the given entries:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
