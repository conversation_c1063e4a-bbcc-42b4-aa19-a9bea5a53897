import { BaseService } from "./base.service";
import { Transaction, Merchant } from "@prisma/client";
/**
 * Risk level enum
 */
export declare enum RiskLevel {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
/**
 * Risk factor enum
 */
export declare enum RiskFactor {
    AMOUNT = "AMOUNT",
    LOCATION = "LOCATION",
    FREQUENCY = "FREQUENCY",
    TIME = "TIME",
    IP = "IP",
    DEVICE = "DEVICE",
    PAYMENT_METHOD = "PAYMENT_METHOD",
    BEHAVIOR = "BEHAVIOR",
    HISTORY = "HISTORY"
}
/**
 * Risk score interface
 */
export interface RiskScore {
    /**
     * Overall risk score (0-100)
     */
    score: number;
    /**
     * Risk level
     */
    level: RiskLevel;
    /**
     * Risk factors with their individual scores
     */
    factors: {
        factor: RiskFactor;
        score: number;
        reason: string;
    }[];
    /**
     * Timestamp of the risk assessment
     */
    timestamp: Date;
}
/**
 * Transaction risk assessment interface
 */
export interface TransactionRiskAssessment {
    /**
     * Transaction ID
     */
    transactionId: string;
    /**
     * Risk score
     */
    riskScore: RiskScore;
    /**
     * Whether the transaction is flagged as potentially fraudulent
     */
    isFlagged: boolean;
    /**
     * Whether the transaction is blocked
     */
    isBlocked: boolean;
    /**
     * Reason for flagging or blocking
     */
    reason?: string;
    /**
     * Recommended action
     */
    recommendedAction?: string;
}
/**
 * Fraud detection configuration
 */
export interface FraudDetectionConfig {
    /**
     * Threshold for flagging transactions (0-100)
     */
    flagThreshold: number;
    /**
     * Threshold for blocking transactions (0-100)
     */
    blockThreshold: number;
    /**
     * Whether to automatically block high-risk transactions
     */
    autoBlock: boolean;
    /**
     * Factor weights (0-1)
     */
    factorWeights: {
        [key in RiskFactor]?: number;
    };
    /**
     * High-risk countries
     */
    highRiskCountries: string[];
    /**
     * High-risk IP ranges
     */
    highRiskIpRanges: string[];
    /**
     * Maximum transaction amount before flagging
     */
    maxTransactionAmount: number;
    /**
     * Maximum transactions per hour before flagging
     */
    maxTransactionsPerHour: number;
    /**
     * Maximum transactions per day before flagging
     */
    maxTransactionsPerDay: number;
}
/**
 * Fraud detection service
 */
export declare class FraudDetectionService extends BaseService {
    constructor();
    /**
   * Default configuration
   */
    private defaultConfig;
    /**
   * Assess transaction risk
   * @param transaction Transaction to assess
   * @param ipAddress IP address of the user
   * @param userAgent User agent of the user
   * @param deviceId Device ID of the user
   * @param merchant Merchant associated with the transaction
   * @returns Transaction risk assessment
   */
    assessTransactionRisk(transaction: Transaction, ipAddress: string, userAgent: string, deviceId: string, merchant: Merchant): Promise<TransactionRiskAssessment>;
    /**
   * Calculate risk factors for a transaction
   * @param transaction Transaction to assess
   * @param ipAddress IP address of the user
   * @param userAgent User agent of the user
   * @param deviceId Device ID of the user
   * @param merchant Merchant associated with the transaction
   * @param config Fraud detection configuration
   * @returns Risk factors with their individual scores
   */
    private calculateRiskFactors;
    /**
   * Calculate overall risk score
   * @param factors Risk factors with their individual scores
   * @param config Fraud detection configuration
   * @returns Overall risk score (0-100)
   */
    private calculateOverallRiskScore;
    /**
   * Determine risk level based on score
   * @param score Risk score
   * @returns Risk level
   */
    private determineRiskLevel;
    /**
   * Generate reason for risk assessment
   * @param factors Risk factors
   * @param level Risk level
   * @returns Reason for risk assessment
   */
    private generateReason;
    /**
   * Generate recommended action based on risk level
   * @param level Risk level
   * @param isBlocked Whether the transaction is blocked
   * @returns Recommended action
   */
    private generateRecommendedAction;
    /**
   * Save risk assessment to database
   * @param transactionId Transaction ID
   * @param riskScore Risk score
   * @param isFlagged Whether the transaction is flagged
   * @param isBlocked Whether the transaction is blocked
   */
    private saveRiskAssessment;
    /**
   * Get merchant-specific fraud detection configuration
   * @param merchantId Merchant ID
   * @returns Fraud detection configuration
   */
    private getMerchantFraudConfig;
    /**
   * Calculate amount risk score
   * @param amount Transaction amount
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
    private calculateAmountRiskScore;
    /**
   * Calculate location risk score
   * @param ipAddress IP address
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
    private calculateLocationRiskScore;
    /**
   * Calculate frequency risk score
   * @param transaction Transaction
   * @param merchantId Merchant ID
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
    private calculateFrequencyRiskScore;
    /**
   * Calculate time risk score
   * @param timestamp Transaction timestamp
   * @returns Risk score (0-100)
   */
    private calculateTimeRiskScore;
    /**
   * Calculate IP risk score
   * @param ipAddress IP address
   * @param config Fraud detection configuration
   * @returns Risk score (0-100)
   */
    private calculateIpRiskScore;
    /**
   * Check if IP is in range
   * @param ip IP address
   * @param range IP range in CIDR notation
   * @returns Whether IP is in range
   */
    private isIpInRange;
    /**
   * Calculate device risk score
   * @param deviceId Device ID
   * @param userAgent User agent
   * @param merchantId Merchant ID
   * @returns Risk score (0-100)
   */
    private calculateDeviceRiskScore;
    /**
   * Calculate payment method risk score
   * @param paymentMethodId Payment method ID
   * @returns Risk score (0-100)
   */
    private calculatePaymentMethodRiskScore;
    /**
   * Calculate behavior risk score
   * @param transaction Transaction
   * @param ipAddress IP address
   * @param deviceId Device ID
   * @param merchantId Merchant ID
   * @returns Risk score (0-100)
   */
    private calculateBehaviorRiskScore;
    /**
   * Calculate history risk score
   * @param customerEmail Customer email
   * @param merchantId Merchant ID
   * @returns Risk score (0-100)
   */
    private calculateHistoryRiskScore;
}
declare const _default: FraudDetectionService;
export default _default;
//# sourceMappingURL=fraud-detection.service.d.ts.map