/**
 * Operational Mode Middleware
 *
 * Middleware for checking operational mode.
 */
/**
 * Check if system is enabled
 */
export declare const checkSystemEnabled: any;
/**
 * Check if system is in production mode
 */
export declare const requireProductionMode: any;
/**
 * Add operational mode to response headers
 */
export declare const addOperationalModeHeaders: any;
//# sourceMappingURL=operational-mode.middleware.d.ts.map