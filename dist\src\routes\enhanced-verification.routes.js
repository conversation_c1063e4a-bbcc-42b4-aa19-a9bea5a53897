"use strict";
// jscpd:ignore-file
/**
 * Enhanced Verification Routes
 *
 * Routes for enhanced verification operations.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const enhanced_auth_middleware_1 = require("../middlewares/enhanced-auth.middleware");
const enhanced_verification_controller_1 = __importDefault(require("../controllers/enhanced-verification.controller"));
const router = (0, express_1.Router)();
// Public routes (no authentication required)
router.post("/verify", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("transactionId").notEmpty(),
    (0, express_validator_1.body)("merchantId").notEmpty(),
    (0, express_validator_1.body)("paymentMethodId").notEmpty(),
    (0, express_validator_1.body)("paymentMethodType").notEmpty(),
    (0, express_validator_1.body)("amount").isNumeric(),
    (0, express_validator_1.body)("currency").notEmpty(),
    (0, express_validator_1.body)("verificationData").notEmpty(),
    (0, express_validator_1.body)("verificationData.verificationMethod").notEmpty()
]), enhanced_verification_controller_1.default.verifyPayment);
// Routes requiring authentication
router.use(enhanced_auth_middleware_1.enhancedAuthenticate);
// Get verification methods for a payment method
router.get("/methods/payment-method/:paymentMethodType", (0, enhanced_auth_middleware_1.requirePermission)("verification_methods", "view"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("paymentMethodType").notEmpty()
]), enhanced_verification_controller_1.default.getVerificationMethodsForPaymentMethod);
// Get all verification methods
router.get("/methods", (0, enhanced_auth_middleware_1.requirePermission)("verification_methods", "view"), enhanced_verification_controller_1.default.getAllVerificationMethods);
// Get verification method by type
router.get("/methods/:type", (0, enhanced_auth_middleware_1.requirePermission)("verification_methods", "view"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("type").notEmpty()
]), enhanced_verification_controller_1.default.getVerificationMethodByType);
exports.default = router;
//# sourceMappingURL=enhanced-verification.routes.js.map