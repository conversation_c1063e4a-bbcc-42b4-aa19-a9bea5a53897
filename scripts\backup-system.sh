#!/bin/bash

# AmazingPay Flow - Comprehensive Backup Script
# This script creates backups of database, application, and logs

set -e

# Configuration
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to backup database
backup_database() {
    echo "📦 Backing up database..."

    DB_BACKUP_DIR="$BACKUP_DIR/database"
    mkdir -p $DB_BACKUP_DIR

    # Load environment variables
    source .env.production

    # Create database backup using Node.js (since pg_dump might not be available)
    cat > temp_db_backup.js << 'EOF'
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');

const prisma = new PrismaClient();

async function backupDatabase() {
    try {
        console.log('📦 Creating database backup...');

        // Get all data
        const users = await prisma.user.findMany();
        const merchants = await prisma.merchant.findMany();
        const transactions = await prisma.transaction.findMany();
        const systemSettings = await prisma.systemSetting.findMany();
        const roles = await prisma.role.findMany();

        const backup = {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            data: {
                users: users.length,
                merchants: merchants.length,
                transactions: transactions.length,
                systemSettings: systemSettings.length,
                roles: roles.length
            },
            structure: {
                users,
                systemSettings,
                roles
            }
        };

        const backupFile = `backups/database/backup_${Date.now()}.json`;
        fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));

        console.log('✅ Database backup created:', backupFile);
        console.log('📊 Records backed up:', backup.data);
    } catch (error) {
        console.error('❌ Database backup failed:', error.message);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

backupDatabase();
EOF

    # Run the backup script
    node temp_db_backup.js
    rm temp_db_backup.js

    print_success "Database backup completed"
}

# Function to backup application files
backup_application() {
    echo "📦 Backing up application files..."

    APP_BACKUP_DIR="$BACKUP_DIR/application"
    mkdir -p $APP_BACKUP_DIR

    # Create application backup
    tar -czf "$APP_BACKUP_DIR/app_$DATE.tar.gz" \
        --exclude=node_modules \
        --exclude=dist \
        --exclude=logs \
        --exclude=backups \
        .

    print_success "Application backup created: app_$DATE.tar.gz"
}

# Function to backup logs
backup_logs() {
    echo "📦 Backing up logs..."

    LOG_BACKUP_DIR="$BACKUP_DIR/logs"
    mkdir -p $LOG_BACKUP_DIR

    if [ -d "logs" ]; then
        tar -czf "$LOG_BACKUP_DIR/logs_$DATE.tar.gz" logs/
        print_success "Logs backup created: logs_$DATE.tar.gz"
    else
        print_warning "No logs directory found"
    fi
}

# Function to cleanup old backups
cleanup_old_backups() {
    echo "🧹 Cleaning up old backups..."

    # Remove backups older than retention period
    find $BACKUP_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete
    find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

    print_success "Old backups cleaned up (older than $RETENTION_DAYS days)"
}

# Function to verify backups
verify_backups() {
    echo "🔍 Verifying backups..."

    # Check if backup files exist and are not empty
    LATEST_DB_BACKUP=$(ls -t $BACKUP_DIR/database/*.gz 2>/dev/null | head -1)
    LATEST_APP_BACKUP=$(ls -t $BACKUP_DIR/application/*.tar.gz 2>/dev/null | head -1)

    if [ -f "$LATEST_DB_BACKUP" ] && [ -s "$LATEST_DB_BACKUP" ]; then
        print_success "Database backup verified"
    else
        print_error "Database backup verification failed"
    fi

    if [ -f "$LATEST_APP_BACKUP" ] && [ -s "$LATEST_APP_BACKUP" ]; then
        print_success "Application backup verified"
    else
        print_error "Application backup verification failed"
    fi
}

# Main backup process
main() {
    echo "🔄 Starting AmazingPay Flow backup process..."
    echo "============================================="

    backup_database
    backup_application
    backup_logs
    cleanup_old_backups
    verify_backups

    echo ""
    print_success "🎉 Backup process completed successfully!"
    echo "📊 Backup summary:"
    echo "   Database: $(ls -lh $BACKUP_DIR/database/*$DATE* 2>/dev/null | awk '{print $5}' || echo 'N/A')"
    echo "   Application: $(ls -lh $BACKUP_DIR/application/*$DATE* 2>/dev/null | awk '{print $5}' || echo 'N/A')"
    echo "   Logs: $(ls -lh $BACKUP_DIR/logs/*$DATE* 2>/dev/null | awk '{print $5}' || echo 'N/A')"
}

# Run backup
main "$@"
