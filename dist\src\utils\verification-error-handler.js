"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationError = exports.VerificationErrorType = void 0;
exports.createVerificationError = createVerificationError;
exports.formatVerificationErrorResponse = formatVerificationErrorResponse;
exports.handleVerificationError = handleVerificationError;
const logger_1 = require("./logger");
/**
 * Verification error types
 */
var VerificationErrorType;
(function (VerificationErrorType) {
    VerificationErrorType["INVALID_INPUT"] = "INVALID_INPUT";
    VerificationErrorType["PROVIDER_ERROR"] = "PROVIDER_ERROR";
    VerificationErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    VerificationErrorType["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    VerificationErrorType["RATE_LIMIT_ERROR"] = "RATE_LIMIT_ERROR";
    VerificationErrorType["AUTHENTICATION_ERROR"] = "AUTHENTICATION_ERROR";
    VerificationErrorType["AUTHORIZATION_ERROR"] = "AUTHORIZATION_ERROR";
    VerificationErrorType["NOT_FOUND_ERROR"] = "NOT_FOUND_ERROR";
    VerificationErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    VerificationErrorType["INTERNAL_ERROR"] = "INTERNAL_ERROR";
    VerificationErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(VerificationErrorType || (exports.VerificationErrorType = VerificationErrorType = {}));
/**
 * Verification error class
 */
class VerificationError extends Error {
    constructor(code, message, originalError, context) {
        super(message);
        this.name = 'VerificationError';
        this.code = code;
        this.originalError = originalError;
        this.context = context;
        // Ensure the prototype chain is properly set up
        Object.setPrototypeOf(this, VerificationError.prototype);
    }
}
exports.VerificationError = VerificationError;
/**
 * Create a verification error
 * @param code Error code
 * @param message Error message
 * @param originalError Original error (if any)
 * @param context Additional context
 * @returns Verification error
 */
function createVerificationError(code, message, originalError, context) {
    // Log the error
    logger_1.logger.error(`Verification error: ${code} - ${message}`, {
        code,
        message,
        originalError,
        context,
    });
    return new VerificationError(code, message, originalError, context);
}
/**
 * Format a verification error response
 * @param error Verification error
 * @returns Formatted error response
 */
function formatVerificationErrorResponse(error) {
    const status = 400;
    const responseData = {
        success: false,
        error: {
            code: error.code,
            message: error.message,
            details: error.originalError
                ? {
                    originalError: error.originalError,
                    context: error.context,
                }
                : undefined,
        },
        data: null,
    };
    return responseData;
}
/**
 * Handle a verification error
 * @param error Error to handle
 * @param defaultMessage Default error message
 * @param context Additional context
 * @returns Verification error
 */
function handleVerificationError(error, defaultMessage = 'Verification failed', context) {
    // If it's already a VerificationError, just return it
    if (error instanceof VerificationError) {
        return error;
    }
    // Determine the error type based on the error
    let errorType = VerificationErrorType.UNKNOWN_ERROR;
    let errorMessage = defaultMessage;
    if (error) {
        // Extract message if available
        if (error.message) {
            errorMessage = error.message;
        }
        // Determine error type based on error properties
        if (error.code === 'ECONNREFUSED' ||
            error.code === 'ECONNRESET' ||
            error.code === 'ETIMEDOUT') {
            errorType = VerificationErrorType.NETWORK_ERROR;
        }
        else if (error.status === 401 || error.statusCode === 401) {
            errorType = VerificationErrorType.AUTHENTICATION_ERROR;
        }
        else if (error.status === 403 || error.statusCode === 403) {
            errorType = VerificationErrorType.AUTHORIZATION_ERROR;
        }
        else if (error.status === 404 || error.statusCode === 404) {
            errorType = VerificationErrorType.NOT_FOUND_ERROR;
        }
        else if (error.status === 422 ||
            error.statusCode === 422 ||
            error.name === 'ValidationError') {
            errorType = VerificationErrorType.VALIDATION_ERROR;
        }
        else if (error.status === 429 ||
            error.statusCode === 429 ||
            error.message?.includes('rate limit')) {
            errorType = VerificationErrorType.RATE_LIMIT_ERROR;
        }
        else if (error.timeout || error.message?.includes('timeout')) {
            errorType = VerificationErrorType.TIMEOUT_ERROR;
        }
        else if (error.status >= 500 || (error.statusCode && error.statusCode >= 500)) {
            errorType = VerificationErrorType.PROVIDER_ERROR;
        }
    }
    return createVerificationError(errorType, errorMessage, error, context);
}
