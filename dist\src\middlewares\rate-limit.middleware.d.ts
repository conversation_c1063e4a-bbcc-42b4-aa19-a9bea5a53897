declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * General API rate limiter
 * Limits each IP to 100 requests per 15 minutes
 */
export declare const apiLimiter: any;
/**
 * Stricter rate limiter for authentication routes
 * Limits each IP to 5 login attempts per hour
 */
export declare const authLimiter: any;
/**
 * Rate limiter for password reset routes
 * Limits each IP to 3 password reset attempts per hour
 */
export declare const passwordResetLimiter: any;
/**
 * Rate limiter for sensitive operations
 * Limits each IP to 5 sensitive operations per day
 */
export declare const sensitiveOperationLimiter: any;
//# sourceMappingURL=rate-limit.middleware.d.ts.map