/**
 * Error Handler Utility
 *
 * This utility provides centralized error handling for the application.
 * It includes functions for handling errors in controllers, services, and middleware.
 */
/**
 * Error response structure
 */
export interface ErrorResponse {
    status: string;
    statusCode: number;
    message: string;
    error?: string;
    stack?: string;
    timestamp: string;
    path: string;
    requestId?: string;
    code?: string;
    details?: any;
}
/**
 * Handle controller errors
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export declare const handleControllerError: any;
export declare const handleServiceError: any;
/**
 * Global error handler middleware
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export declare const globalErrorHandler: any;
export declare const handle404Error: any;
declare const _default: {
    handleControllerError: any;
    handleServiceError: any;
    globalErrorHandler: any;
    handle404Error: any;
};
export default _default;
//# sourceMappingURL=error-handler.d.ts.map