"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const RouteTestRunner_1 = require("./runners/RouteTestRunner");
const logger_1 = require("../lib/logger");
/**
 * Run route tests
 */
async function runRouteTests() {
    logger_1.logger.info("Starting route tests...");
    try {
        // Create test runner
        const testRunner = new RouteTestRunner_1.RouteTestRunner();
        // Run all tests
        await testRunner.runAllTests();
        logger_1.logger.info("Route tests completed successfully");
        process.exit(0);
    }
    catch (error) {
        logger_1.logger.error("Route tests failed:", error);
        process.exit(1);
    }
}
// Run tests
runRouteTests();
//# sourceMappingURL=route-tests.js.map