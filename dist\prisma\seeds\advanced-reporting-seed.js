"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedAdvancedReporting = seedAdvancedReporting;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedAdvancedReporting() {
    console.log('🌱 Seeding advanced reporting data...');
    try {
        // Create sample report templates
        const transactionTemplate = await prisma.reportTemplate.upsert({
            where: { name: 'Standard Transaction Report' },
            update: {},
            create: {
                name: 'Standard Transaction Report',
                description: 'Comprehensive transaction report with all key metrics',
                type: 'TRANSACTION',
                config: {
                    columns: [
                        'reference',
                        'amount',
                        'currency',
                        'status',
                        'paymentMethod',
                        'merchantName',
                        'createdAt'
                    ],
                    groupBy: ['status', 'paymentMethod'],
                    sortBy: 'createdAt',
                    sortDirection: 'desc',
                    filters: {
                        status: ['COMPLETED', 'PENDING', 'FAILED'],
                        dateRange: 'last_30_days'
                    }
                },
                createdById: 'system', // Will be updated with actual user ID
            },
        });
        const customerTemplate = await prisma.reportTemplate.upsert({
            where: { name: 'Customer Analytics Report' },
            update: {},
            create: {
                name: 'Customer Analytics Report',
                description: 'Customer demographics and activity analysis',
                type: 'CUSTOMER',
                config: {
                    columns: [
                        'email',
                        'firstName',
                        'lastName',
                        'status',
                        'merchantName',
                        'createdAt'
                    ],
                    groupBy: ['status'],
                    sortBy: 'createdAt',
                    sortDirection: 'desc',
                    filters: {
                        status: ['ACTIVE', 'INACTIVE'],
                        dateRange: 'last_90_days'
                    }
                },
                createdById: 'system',
            },
        });
        const paymentMethodTemplate = await prisma.reportTemplate.upsert({
            where: { name: 'Payment Method Usage Report' },
            update: {},
            create: {
                name: 'Payment Method Usage Report',
                description: 'Analysis of payment method preferences and usage patterns',
                type: 'PAYMENT_METHOD',
                config: {
                    columns: [
                        'type',
                        'last4',
                        'isDefault',
                        'customerEmail',
                        'merchantName',
                        'createdAt'
                    ],
                    groupBy: ['type', 'isDefault'],
                    sortBy: 'createdAt',
                    sortDirection: 'desc',
                    filters: {
                        type: ['CARD', 'BANK_ACCOUNT', 'DIGITAL_WALLET'],
                        dateRange: 'last_60_days'
                    }
                },
                createdById: 'system',
            },
        });
        const subscriptionTemplate = await prisma.reportTemplate.upsert({
            where: { name: 'Subscription Revenue Report' },
            update: {},
            create: {
                name: 'Subscription Revenue Report',
                description: 'Subscription billing and revenue analysis',
                type: 'SUBSCRIPTION',
                config: {
                    columns: [
                        'status',
                        'planName',
                        'amount',
                        'currency',
                        'interval',
                        'customerEmail',
                        'nextBillingDate',
                        'createdAt'
                    ],
                    groupBy: ['status', 'planName'],
                    sortBy: 'amount',
                    sortDirection: 'desc',
                    filters: {
                        status: ['ACTIVE', 'CANCELLED', 'PAUSED'],
                        dateRange: 'last_30_days'
                    }
                },
                createdById: 'system',
            },
        });
        // Create sample dashboards
        const executiveDashboard = await prisma.dashboard.upsert({
            where: { name: 'Executive Dashboard' },
            update: {},
            create: {
                name: 'Executive Dashboard',
                description: 'High-level overview of key business metrics',
                layout: {
                    columns: 3,
                    rows: 2,
                    widgets: [
                        { id: 'revenue-chart', x: 0, y: 0, width: 2, height: 1 },
                        { id: 'transaction-count', x: 2, y: 0, width: 1, height: 1 },
                        { id: 'customer-growth', x: 0, y: 1, width: 1, height: 1 },
                        { id: 'payment-methods', x: 1, y: 1, width: 2, height: 1 }
                    ]
                },
                isPublic: false,
                createdById: 'system',
            },
        });
        const operationalDashboard = await prisma.dashboard.upsert({
            where: { name: 'Operational Dashboard' },
            update: {},
            create: {
                name: 'Operational Dashboard',
                description: 'Operational metrics and system health monitoring',
                layout: {
                    columns: 4,
                    rows: 3,
                    widgets: [
                        { id: 'system-health', x: 0, y: 0, width: 1, height: 1 },
                        { id: 'active-reports', x: 1, y: 0, width: 1, height: 1 },
                        { id: 'error-rate', x: 2, y: 0, width: 1, height: 1 },
                        { id: 'response-time', x: 3, y: 0, width: 1, height: 1 },
                        { id: 'transaction-volume', x: 0, y: 1, width: 2, height: 1 },
                        { id: 'failed-transactions', x: 2, y: 1, width: 2, height: 1 },
                        { id: 'recent-reports', x: 0, y: 2, width: 4, height: 1 }
                    ]
                },
                isPublic: false,
                createdById: 'system',
            },
        });
        // Create sample dashboard widgets
        const widgets = [
            {
                dashboardId: executiveDashboard.id,
                title: 'Revenue Chart',
                type: 'CHART',
                config: {
                    chartType: 'line',
                    dataSource: 'transactions',
                    metric: 'amount',
                    groupBy: 'date',
                    timeRange: '30d'
                },
                width: 2,
                height: 1,
                x: 0,
                y: 0,
            },
            {
                dashboardId: executiveDashboard.id,
                title: 'Transaction Count',
                type: 'METRIC',
                config: {
                    dataSource: 'transactions',
                    metric: 'count',
                    timeRange: '24h',
                    comparison: 'previous_period'
                },
                width: 1,
                height: 1,
                x: 2,
                y: 0,
            },
            {
                dashboardId: executiveDashboard.id,
                title: 'Customer Growth',
                type: 'CHART',
                config: {
                    chartType: 'bar',
                    dataSource: 'customers',
                    metric: 'count',
                    groupBy: 'month',
                    timeRange: '12m'
                },
                width: 1,
                height: 1,
                x: 0,
                y: 1,
            },
            {
                dashboardId: executiveDashboard.id,
                title: 'Payment Methods',
                type: 'CHART',
                config: {
                    chartType: 'pie',
                    dataSource: 'payment_methods',
                    metric: 'count',
                    groupBy: 'type',
                    timeRange: '30d'
                },
                width: 2,
                height: 1,
                x: 1,
                y: 1,
            },
            {
                dashboardId: operationalDashboard.id,
                title: 'System Health',
                type: 'STATUS',
                config: {
                    dataSource: 'system_health',
                    metric: 'status',
                    thresholds: {
                        healthy: 'green',
                        degraded: 'yellow',
                        unhealthy: 'red'
                    }
                },
                width: 1,
                height: 1,
                x: 0,
                y: 0,
            },
            {
                dashboardId: operationalDashboard.id,
                title: 'Active Reports',
                type: 'METRIC',
                config: {
                    dataSource: 'reports',
                    metric: 'active_count',
                    timeRange: 'current'
                },
                width: 1,
                height: 1,
                x: 1,
                y: 0,
            },
        ];
        for (const widget of widgets) {
            await prisma.dashboardWidget.upsert({
                where: {
                    dashboardId_title: {
                        dashboardId: widget.dashboardId,
                        title: widget.title,
                    },
                },
                update: {},
                create: widget,
            });
        }
        console.log('✅ Advanced reporting seed data created successfully');
        console.log(`📊 Created ${4} report templates`);
        console.log(`📈 Created ${2} dashboards`);
        console.log(`🔧 Created ${widgets.length} dashboard widgets`);
    }
    catch (error) {
        console.error('❌ Error seeding advanced reporting data:', error);
        throw error;
    }
}
// Run seed if called directly
if (require.main === module) {
    seedAdvancedReporting()
        .catch((e) => {
        console.error(e);
        process.exit(1);
    })
        .finally(async () => {
        await prisma.$disconnect();
    });
}
