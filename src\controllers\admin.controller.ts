// jscpd:ignore-file
/**
 * Admin Controller
 * 
 * Handles admin operations with RBAC.
 */

import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import { logger } from "../lib/logger";
import { AppError } from "../middlewares/error.middleware";
import { AuditService } from "../services/audit.service";
import { User } from '../types';
import { PrismaClient } from "@prisma/client";
import { logger } from "../lib/logger";
import { AppError } from "../middlewares/error.middleware";
import { AuditService } from "../services/audit.service";
import { User } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


const prisma: any = new PrismaClient();
const auditService: any = new AuditService(prisma);

/**
 * Get dashboard data
 */
const getDashboardData: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
    // Get counts
        const [
            merchantCount,
            transactionCount,
            activePaymentMethodsCount,
            recentTransactions,
            recentMerchants
        ] = await Promise.all([
            prisma.merchant.count({ where: { isActive: true } }),
            prisma.transaction.count(),
            prisma.paymentMethod.count({ where: { isActive: true } }),
            prisma.transaction.findMany({
                take: 5,
                orderBy: { createdAt: "desc" },
                include: { merchant: true }
            }),
            prisma.merchant.findMany({
                take: 5,
                orderBy: { createdAt: "desc" }
            })
        ]);

        // Calculate revenue
        const totalRevenue: any = await prisma.transaction.aggregate({
            _sum: { amount: true },
            where: { status: "SUCCESS" }
        });

        res.json({
            merchantCount,
            transactionCount,
            activePaymentMethodsCount,
            totalRevenue: totalRevenue._sum.amount || 0,
            recentTransactions,
            recentMerchants
        });
    } catch (error) {
        logger.error("Error getting dashboard data:", error);
        next(new AppError({
            message: "Failed to get dashboard data",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get admin users
 */
const getAdminUsers: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const adminUsers: any = await prisma.admin.findMany({
            include: { user: {
                    include: { roles: {
                            include: { permissions: true
                            }
                        }
                    }
                }
            }
        });

        res.json(adminUsers);
    } catch (error) {
        logger.error("Error getting admin users:", error);
        next(new AppError({
            message: "Failed to get admin users",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get admin user by ID
 */
const getAdminUserById: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { id } = req.params;

        const adminUser: any = await prisma.admin.findUnique({
            where: { id },
            include: { user: {
                    include: { roles: {
                            include: { permissions: true
                            }
                        }
                    }
                }
            }
        });

        if (!adminUser) {
            return next(new AppError({
            message: "Admin user not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        }));
        }

        res.json(adminUser);
    } catch (error) {
        logger.error("Error getting admin user by ID:", error);
        next(new AppError({
            message: "Failed to get admin user",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Create admin user
 */
const createAdminUser: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { email, name, password, roleId } = req.body;

        // Check if user already exists
        const existingUser: any = await prisma.user.findUnique({
            where: { email }
        });

        if (existingUser) {
            return next(new AppError({
            message: "User with this email already exists",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        }));
        }

        // Hash password
        const hashedPassword: any = await bcrypt.hash(password, 10);

        // Create user and admin in a transaction
        const result: any = await prisma.$transaction(async (tx) => {
            // Create user
            const user = await tx.user.create({
                data: {
                    email,
                    hashedPassword,
                    roles: { connect: { id: roleId }
                    }
                }
            });

            // Create admin
            const admin: any = await tx.admin.create({
                data: { userId: user.id,
                    name,
                    email,
                    hashedPassword,
                    role: "admin" // Default role
                }
            });

            return { user, admin };
        });

        // Audit the action
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            action: "create",
            resource: "admin_users",
            resourceId: result.admin.id,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            newValues: { email, name, roleId }
        });

        res.status(201).json(result.admin);
    } catch (error) {
        logger.error("Error creating admin user:", error);
        next(new AppError({
            message: "Failed to create admin user",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Update admin user
 */
const updateAdminUser: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { id } = req.params;
        const { name, email, roleId, isActive } = req.body;

        // Get current admin user
        const currentAdmin: any = await prisma.admin.findUnique({
            where: { id },
            include: { user: true }
        });

        if (!currentAdmin) {
            return next(new AppError({
            message: "Admin user not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        }));
        }

        // Update admin and user in a transaction
        const result: any = await prisma.$transaction(async (tx) => {
            // Update user if needed
            if (email || roleId !== undefined || isActive !== undefined) {
                await tx.user.update({
                    where: { id: currentAdmin.id //, Fixed: using id instead of userId },
                    data: { email: email || undefined,
                        isActive: isActive !== undefined ? isActive : undefined,
                        roles: roleId ? {
                            set: [],
                            connect: { id: roleId }
                        } : undefined
                    }
                });
            }

            // Update admin
            const admin: any = await tx.admin.update({
                where: { id },
                data: { name: name || undefined,
                    email: email || undefined,
                    status: isActive !== undefined ? (isActive ? "active" : "inactive") : undefined
                }
            });

            return admin;
        });

        // Audit the action
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            action: "update",
            resource: "admin_users",
            resourceId: id,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            oldValues: { name: currentAdmin.name,
                email: currentAdmin.email,
                isActive: currentAdmin.user.isActive
            },
            newValues: { name, email, roleId, isActive }
        });

        res.json(result);
    } catch (error) {
        logger.error("Error updating admin user:", error);
        next(new AppError({
            message: "Failed to update admin user",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Delete admin user
 */
const deleteAdminUser: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { id } = req.params;

        // Get current admin user
        const currentAdmin: any = await prisma.admin.findUnique({
            where: { id }
        });

        if (!currentAdmin) {
            return next(new AppError({
            message: "Admin user not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        }));
        }

        // Delete admin
        await prisma.admin.delete({
            where: { id }
        });

        // Audit the action
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            action: "delete",
            resource: "admin_users",
            resourceId: id,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            oldValues: currentAdmin
        });

        res.status(204).send();
    } catch (error) {
        logger.error("Error deleting admin user:", error);
        next(new AppError({
            message: "Failed to delete admin user",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get roles
 */
const getRoles: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const roles: any = await prisma.role.findMany({
            include: { permissions: true
            }
        });

        res.json(roles);
    } catch (error) {
        logger.error("Error getting roles:", error);
        next(new AppError({
            message: "Failed to get roles",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get role by ID
 */
const getRoleById: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { id } = req.params;

        const role: any = await prisma.role.findUnique({
            where: { id },
            include: { permissions: true
            }
        });

        if (!role) {
            return next(new AppError({
            message: "Role not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        }));
        }

        res.json(role);
    } catch (error) {
        logger.error("Error getting role by ID:", error);
        next(new AppError({
            message: "Failed to get role",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Create role
 */
const createRole: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { name, type, description, permissions } = req.body;

        // Check if role already exists
        const existingRole: any = await prisma.role.findUnique({
            where: { name }
        });

        if (existingRole) {
            return next(new AppError({
            message: "Role with this name already exists",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        }));
        }

        // Create role
        const role: any = await prisma.role.create({
            data: {
                name,
                type,
                description,
                permissions: { connect: permissions.map(((id: string)) => ({ id }))
                },
                createdById: req.user.id // Fixed: using id instead of userId
            },
            include: { permissions: true
            }
        });

        // Audit the action
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            action: "create",
            resource: "roles",
            resourceId: role.id,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            newValues: { name, type, description, permissions }
        });

        res.status(201).json(role);
    } catch (error) {
        logger.error("Error creating role:", error);
        next(new AppError({
            message: "Failed to create role",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Update role
 */
const updateRole: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { id } = req.params;
        const { name, description, permissions, isActive } = req.body;

        // Get current role
        const currentRole: any = await prisma.role.findUnique({
            where: { id },
            include: { permissions: true }
        });

        if (!currentRole) {
            return next(new AppError({
            message: "Role not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        }));
        }

        // Update role
        const role: any = await prisma.role.update({
            where: { id },
            data: { name: name || undefined,
                description: description || undefined,
                isActive: isActive !== undefined ? isActive : undefined,
                permissions: permissions ? {
                    set: [],
                    connect: permissions.map(((id: string)) => ({ id }))
                } : undefined,
                updatedById: req.user.id // Fixed: using id instead of userId
            },
            include: { permissions: true
            }
        });

        // Audit the action
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            action: "update",
            resource: "roles",
            resourceId: id,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            oldValues: { name: currentRole.name,
                description: currentRole.description,
                isActive: currentRole.isActive,
                permissions: currentRole.permissions.map(p => p.id)
            },
            newValues: { name, description, isActive, permissions }
        });

        res.json(role);
    } catch (error) {
        logger.error("Error updating role:", error);
        next(new AppError({
            message: "Failed to update role",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Delete role
 */
const deleteRole: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { id } = req.params;

        // Get current role
        const currentRole: any = await prisma.role.findUnique({
            where: { id }
        });

        if (!currentRole) {
            return next(new AppError({
            message: "Role not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        }));
        }

        // Check if role is a system role
        if (currentRole.isSystem) {
            return next(new AppError({
            message: "Cannot delete system role",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        }));
        }

        // Delete role
        await prisma.role.delete({
            where: { id }
        });

        // Audit the action
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            action: "delete",
            resource: "roles",
            resourceId: id,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            oldValues: currentRole
        });

        res.status(204).send();
    } catch (error) {
        logger.error("Error deleting role:", error);
        next(new AppError({
            message: "Failed to delete role",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get permissions
 */
const getPermissions: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const permissions: any = await prisma.permission.findMany();
        res.json(permissions);
    } catch (error) {
        logger.error("Error getting permissions:", error);
        next(new AppError({
            message: "Failed to get permissions",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get audit logs
 */
const getAuditLogs: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { userId, action, resource, startDate, endDate, limit, offset } = req.query;

        const auditLogs: any = await auditService.getAuditLogs({
            userId: userId as string,
            action: action as string,
            resource: resource as string,
            startDate: startDate ? new Date(startDate as string) : undefined,
            endDate: endDate ? new Date(endDate as string) : undefined,
            limit: limit ? parseInt(limit as string, 10) : undefined,
            offset: offset ? parseInt(offset as string, 10) : undefined
        });

        res.json(auditLogs);
    } catch (error) {
        logger.error("Error getting audit logs:", error);
        next(new AppError({
            message: "Failed to get audit logs",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get audit log by ID
 */
const getAuditLogById: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { id } = req.params;

        const auditLog: any = await auditService.getAuditLogById(id);

        if (!auditLog) {
            return next(new AppError({
            message: "Audit log not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        }));
        }

        res.json(auditLog);
    } catch (error) {
        logger.error("Error getting audit log by ID:", error);
        next(new AppError({
            message: "Failed to get audit log",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Get system settings
 */
const getSystemSettings: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const settings: any = await prisma.systemSetting.findMany();
        res.json(settings);
    } catch (error) {
        logger.error("Error getting system settings:", error);
        next(new AppError({
            message: "Failed to get system settings",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

/**
 * Update system setting
 */
const updateSystemSetting: any = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { key } = req.params;
        const { value } = req.body;

        // Get current setting
        const currentSetting: any = await prisma.systemSetting.findUnique({
            where: { key }
        });

        // Update or create setting
        const setting: any = await prisma.systemSetting.upsert({
            where: { key },
            update: {
                value,
                updatedById: req.user.id //, Fixed: using id instead of userId
            },
            create: {
                key,
                value,
                updatedById: req.user.id //, Fixed: using id instead of userId
            }
        });

        // Audit the action
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            action: currentSetting ? "update" : "create",
            resource: "settings",
            resourceId: key,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            oldValues: currentSetting ? { value: currentSetting.value } : undefined,
            newValues: { value }
        });

        res.json(setting);
    } catch (error) {
        logger.error("Error updating system setting:", error);
        next(new AppError({
            message: "Failed to update system setting",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
    }
};

export default {
    getDashboardData,
    getAdminUsers,
    getAdminUserById,
    createAdminUser,
    updateAdminUser,
    deleteAdminUser,
    getRoles,
    getRoleById,
    createRole,
    updateRole,
    deleteRole,
    getPermissions,
    getAuditLogs,
    getAuditLogById,
    getSystemSettings,
    updateSystemSetting
};
