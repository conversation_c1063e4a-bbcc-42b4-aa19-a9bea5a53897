"use strict";
// jscpd:ignore-file
/**
 * Merchant Access Middleware
 *
 * This middleware ensures that a user can only access resources for their own merchant,
 * while admins can access resources for any merchant.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.merchantAccessMiddleware = void 0;
const logger_1 = require("../utils/logger");
/**
 * Middleware to check if user has access to merchant resources
 * @param req Request
 * @param res Response
 * @param next Next function
 */
const merchantAccessMiddleware = async (req, res, next) => {
    try {
        // Skip check if user is not authenticated
        if (!req.user) {
            return res.status(401).json({
                status: "error",
                message: "Authentication required"
            });
        }
        // Allow admins to access any merchant
        if (req.user.role === "ADMIN") {
            return next();
        }
        // Get merchant ID from request parameters
        const { merchantId } = req.params;
        // Check if user is associated with the merchant
        if (req.user.merchantId !== merchantId) {
            return res.status(403).json({
                status: "error",
                message: "You do not have permission to access this merchant's resources"
            });
        }
        // User has access to the merchant
        next();
    }
    catch (error) {
        logger_1.logger.error("Error in merchant access middleware:", error);
        return res.status(500).json({
            status: "error",
            message: "Internal server error"
        });
    }
};
exports.merchantAccessMiddleware = merchantAccessMiddleware;
//# sourceMappingURL=merchantAccessMiddleware.js.map