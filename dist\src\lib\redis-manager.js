"use strict";
// jscpd:ignore-file
/**
 * Redis Manager
 *
 * Centralized Redis client management for the application.
 * This service provides a singleton Redis client that can be used across the application.
 * It handles connection, authentication, and error handling.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisManager = void 0;
const redis_1 = require("redis");
const logger_1 = require("./logger");
const environment_validator_1 = require("../utils/environment-validator");
// In-memory store for fallback when Redis is not available
class MemoryStore {
    constructor() {
        this.store = new Map();
    }
    async get(key) {
        const item = this.store.get(key);
        if (!item)
            return null;
        // Check if item has expired
        if (item.expiry && item.expiry < Date.now()) {
            this.store.delete(key);
            return null;
        }
        return item.value;
    }
    async set(key, value, expiry) {
        const expiryMs = expiry ? Date.now() + expiry * 1000 : undefined;
        this.store.set(key, { value, expiry: expiryMs });
    }
    async del(key) {
        this.store.delete(key);
    }
    getIsConnected() {
        return true; // Memory store is always connected
    }
    // Clear expired items periodically
    startCleanup() {
    }
}
// Create memory store instance
const memoryStore = new MemoryStore();
memoryStore.startCleanup();
/**
 * Redis Manager Class
 */
class RedisManager {
    constructor() {
        this.client = null;
        this.isRedisAvailable = false;
        this.isConnected = false;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = (0, environment_validator_1.isProduction)() ? 5 : 3;
        this.initialize();
    }
    /**
     * Get Redis Manager instance (Singleton)
     */
    static getInstance() {
        if (!RedisManager.instance) {
            RedisManager.instance = new RedisManager();
        }
        return RedisManager.instance;
    }
    /**
     * Initialize Redis client
     */
    async initialize() {
        try {
            // Check if Redis URL is configured
            const redisUrl = process.env.REDIS_URL || "redis://localhost:6379";
            if (!redisUrl) {
                logger_1.logger.warn("Redis URL not configured, using in-memory store");
                this.isRedisAvailable = false;
                return;
            }
            // Parse Redis URL to extract host, port, and password
            let username = '';
            let password = '';
            let host = 'localhost';
            let port = 6379;
            try {
                const url = new URL(redisUrl);
                host = url.hostname || 'localhost';
                port = parseInt(url.port || '6379', 10);
                if (url.username) {
                    username = url.username;
                }
                if (url.password) {
                    password = url.password;
                }
                logger_1.logger.info(`Redis configuration: host=${host}, port=${port}, auth=${password ? 'yes' : 'no'}`);
            }
            catch (parseError) {
                logger_1.logger.error("Failed to parse Redis URL:", parseError);
            }
            // Create Redis client with proper configuration
            const redisOptions = {
                socket: {
                    host,
                    port,
                    reconnectStrategy: (retries) => {
                        // Maximum retry delay is 5 seconds in production, 3 seconds otherwise
                        const maxDelay = (0, environment_validator_1.isProduction)() ? 5000 : 3000;
                        const delay = Math.min(retries * 500, maxDelay);
                        return delay;
                    },
                    connectTimeout: (0, environment_validator_1.isProduction)() ? 10000 : 5000 // 10 seconds timeout in production, 5 seconds otherwise
                }
            };
            // Add username and password if provided
            if (username || password) {
                redisOptions['username'] = username;
                redisOptions['password'] = password;
            }
            // Create Redis client
            this.client = (0, redis_1.createClient)(redisOptions);
            // Set up event handlers
            this.client.on("connect", () => {
                logger_1.logger.info("Redis client connected");
                this.isConnected = true;
                this.isRedisAvailable = true;
            });
            this.client.on("error", (error) => {
                logger_1.logger.error("Redis client error:", {
                    code: error.code,
                    message: error.message
                });
                this.connectionAttempts++;
                if (this.connectionAttempts >= this.maxConnectionAttempts) {
                    logger_1.logger.warn(`Maximum Redis connection attempts (${this.maxConnectionAttempts}) reached, using in-memory store`);
                    this.isRedisAvailable = false;
                    this.isConnected = false;
                    // Close Redis client to prevent further connection attempts
                    this.client?.quit().catch((err) => {
                        logger_1.logger.error("Error closing Redis client:", err);
                    });
                    this.client = null;
                    // In production, log a warning but continue
                    if ((0, environment_validator_1.isProduction)()) {
                        logger_1.logger.warn("Redis is not available in production mode. Using in-memory store as fallback.");
                    }
                }
            });
            this.client.on("reconnecting", () => {
                logger_1.logger.info("Redis client reconnecting");
                this.isConnected = false;
            });
            this.client.on("end", () => {
                logger_1.logger.info("Redis client disconnected");
                this.isConnected = false;
            });
            // Connect to Redis with proper error handling
            try {
                await this.client.connect();
                // Test connection with a PING command
                const pingResult = await this.client.ping();
                if (pingResult === 'PONG') {
                    logger_1.logger.info("Redis connection test successful");
                    this.isRedisAvailable = true;
                    this.isConnected = true;
                }
                else {
                    throw new Error(`Unexpected response from Redis ping: ${pingResult}`);
                }
            }
            catch (connectError) {
                logger_1.logger.error("Failed to connect to Redis:", connectError);
                logger_1.logger.warn("Using in-memory store as fallback");
                this.isRedisAvailable = false;
                this.isConnected = false;
                this.client = null;
            }
        }
        catch (error) {
            logger_1.logger.error("Failed to initialize Redis client:", error);
            logger_1.logger.warn("Using in-memory store as fallback");
            this.isRedisAvailable = false;
            this.isConnected = false;
            this.client = null;
        }
    }
    /**
     * Get a value from Redis or memory store
     * @param key Key
     * @returns Value or null if not found
     */
    async get(key) {
        try {
            if (this.isRedisAvailable && this.client) {
                return await this.client.get(key);
            }
            else {
                return await memoryStore.get(key);
            }
        }
        catch (error) {
            logger_1.logger.error("Error getting value from Redis:", error);
            return await memoryStore.get(key);
        }
    }
    /**
     * Set a value in Redis or memory store
     * @param key Key
     * @param value Value
     * @param expiry Expiry in seconds
     */
    async set(key, value, expiry) {
        try {
            if (this.isRedisAvailable && this.client) {
                if (expiry) {
                    await this.client.set(key, value, { EX: expiry });
                }
                else {
                    await this.client.set(key, value);
                }
            }
            else {
                await memoryStore.set(key, value, expiry);
            }
        }
        catch (error) {
            logger_1.logger.error("Error setting value in Redis:", error);
            await memoryStore.set(key, value, expiry);
        }
    }
    /**
     * Delete a key from Redis or memory store
     * @param key Key
     */
    async del(key) {
        try {
            if (this.isRedisAvailable && this.client) {
                await this.client.del(key);
            }
            else {
                await memoryStore.del(key);
            }
        }
        catch (error) {
            logger_1.logger.error("Error deleting key from Redis:", error);
            await memoryStore.del(key);
        }
    }
    /**
     * Get connection status
     * @returns True if connected to Redis or using memory store
     */
    getIsConnected() {
        return this.isRedisAvailable ? this.isConnected : memoryStore.getIsConnected();
    }
    /**
     * Get Redis client
     * @returns Redis client or null if not available
     */
    getClient() {
        return this.client;
    }
    /**
     * Check if Redis is available
     * @returns True if Redis is available
     */
    isRedisEnabled() {
        return this.isRedisAvailable;
    }
    /**
     * Close Redis client
     */
    async close() {
        if (this.client) {
            try {
                await this.client.quit();
                this.isConnected = false;
                logger_1.logger.info("Redis client closed");
            }
            catch (error) {
                logger_1.logger.error("Error closing Redis client:", error);
            }
        }
    }
}
// Export Redis manager instance
exports.redisManager = RedisManager.getInstance();
exports.default = exports.redisManager;
//# sourceMappingURL=redis-manager.js.map