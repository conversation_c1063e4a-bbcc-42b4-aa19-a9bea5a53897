"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainAllValues = toContainAllValues;
const utils_1 = require("../utils");
function toContainAllValues(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    let pass = false;
    if (typeof actual === 'object' && actual !== null && !Array.isArray(actual)) {
        const values = Object.keys(actual).map(k => actual[k]);
        pass =
            values.length === expected.length &&
                // @ts-expect-error OK to have implicit any for this.equals
                values.every(objectValue => (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), expected, objectValue));
    }
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainAllValues') +
                '\n\n' +
                'Expected object to not contain all values:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainAllValues') +
                '\n\n' +
                'Expected object to contain all values:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
