"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const client_1 = require("@prisma/client");
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const path_1 = __importDefault(require("path"));
const monitoring_1 = require("./utils/monitoring");
const alert_monitor_service_1 = require("./services/alert-monitor.service");
const alert_aggregation_service_1 = require("./services/alert-aggregation.service");
const verification_alert_job_1 = require("./jobs/verification-alert.job");
const websocket_service_1 = require("./services/websocket.service");
const transaction_monitor_service_1 = require("./services/transaction-monitor.service");
const monitoring_middleware_1 = require("./middlewares/monitoring.middleware");
const apiResponseMiddleware_1 = require("./middlewares/apiResponseMiddleware");
const verification_realtime_service_1 = require("./services/websocket/verification-realtime.service");
// Import routes
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
const merchant_routes_1 = __importDefault(require("./routes/merchant.routes"));
const payment_method_routes_1 = __importDefault(require("./routes/payment-method.routes"));
const verification_method_routes_1 = __importDefault(require("./routes/verification-method.routes"));
const transaction_routes_1 = __importDefault(require("./routes/transaction.routes"));
const payment_page_routes_1 = __importDefault(require("./routes/payment-page.routes"));
const subscription_routes_1 = __importDefault(require("./routes/subscription.routes"));
const monitoring_routes_1 = __importDefault(require("./routes/monitoring.routes"));
const binance_routes_1 = __importDefault(require("./routes/binance.routes"));
const binance_pay_webhook_routes_1 = __importDefault(require("./routes/binance-pay-webhook.routes"));
const location_routes_1 = __importDefault(require("./routes/location.routes"));
const verification_routes_1 = __importDefault(require("./routes/verification.routes"));
const webhook_routes_1 = __importDefault(require("./routes/webhook.routes"));
const alert_routes_1 = __importDefault(require("./routes/alert.routes"));
const alert_aggregation_routes_1 = __importDefault(require("./routes/alert-aggregation.routes"));
const alert_analytics_routes_1 = __importDefault(require("./routes/alert-analytics.routes"));
const email_routes_1 = __importDefault(require("./routes/email.routes"));
const sms_routes_1 = __importDefault(require("./routes/sms.routes"));
const telegram_routes_1 = __importDefault(require("./routes/telegram.routes"));
const notification_routes_1 = __importDefault(require("./routes/notification.routes"));
const push_notification_routes_1 = __importDefault(require("./routes/push-notification.routes"));
const example_routes_1 = __importDefault(require("./routes/example.routes"));
const payment_verification_routes_1 = __importDefault(require("./routes/payment-verification.routes"));
const fraud_detection_routes_1 = __importDefault(require("./routes/fraud-detection.routes"));
const payment_recommendation_routes_1 = __importDefault(require("./routes/payment-recommendation.routes"));
const identity_verification_routes_1 = __importDefault(require("./routes/identity-verification.routes"));
const enhanced_risk_engine_routes_1 = __importDefault(require("./routes/enhanced-risk-engine.routes"));
const merchant_segmentation_routes_1 = __importDefault(require("./routes/merchant-segmentation.routes"));
const merchant_relationship_routes_1 = __importDefault(require("./routes/merchant-relationship.routes"));
const merchant_self_service_routes_1 = __importDefault(require("./routes/merchant-self-service.routes"));
const websocket_monitoring_routes_1 = __importDefault(require("./routes/websocket-monitoring.routes"));
const advanced_report_routes_1 = __importDefault(require("./routes/advanced-report.routes"));
const dashboard_routes_1 = __importDefault(require("./routes/dashboard.routes"));
// All monitoring and service imports are already at the top of the file
// Load environment variables
dotenv_1.default.config();
// Initialize Prisma client
exports.prisma = new client_1.PrismaClient();
// Test database connection
const testDatabaseConnection = async () => {
    try {
        await exports.prisma.$connect();
    }
    catch (error) {
        console.error('Database connection failed:', error);
        console.error('Please check your database configuration in .env file');
        process.exit(1);
    }
};
// Create Express app and HTTP server
const app = (0, express_1.default)();
const httpServer = (0, http_1.createServer)(app);
const PORT = process.env.PORT || 3002; // Use port 3002 as default
// Middleware
app.use((0, cors_1.default)({
    origin: [
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:5175',
        'http://localhost:5176',
        'http://localhost:5177',
        'http://localhost:3000',
        'http://localhost:3002',
        'https://amazingpayme.com',
        '*',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Site-Domain'],
}));
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
}));
app.use((0, morgan_1.default)('dev'));
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
app.use(express_1.default.static(path_1.default.join(__dirname, 'public')));
// Initialize monitoring
const cleanupMonitoring = (0, monitoring_1.initializeMonitoring)();
// Add monitoring middleware
app.use(monitoring_1.monitorRequest);
app.use(monitoring_middleware_1.monitoringMiddleware);
// Add API response middleware
app.use(apiResponseMiddleware_1.apiResponseMiddleware);
// API routes
app.use('/api/auth', auth_routes_1.default);
app.use('/api/users', user_routes_1.default);
app.use('/api/merchants', merchant_routes_1.default);
app.use('/api/payment-methods', payment_method_routes_1.default);
app.use('/api/verification-methods', verification_method_routes_1.default);
app.use('/api/transactions', transaction_routes_1.default);
app.use('/api/payment-pages', payment_page_routes_1.default);
app.use('/api/subscriptions', subscription_routes_1.default);
app.use('/api/monitoring', monitoring_routes_1.default);
app.use('/api/monitoring/websocket', websocket_monitoring_routes_1.default);
app.use('/api/binance', binance_routes_1.default);
app.use('/webhook/binance-pay', binance_pay_webhook_routes_1.default);
app.use('/api/location', location_routes_1.default);
app.use('/api/verification', verification_routes_1.default);
app.use('/api/webhooks', webhook_routes_1.default);
app.use('/api/alerts', alert_routes_1.default);
app.use('/api/alerts', alert_aggregation_routes_1.default);
app.use('/api/alerts', alert_analytics_routes_1.default);
app.use('/api/email', email_routes_1.default);
app.use('/api/sms', sms_routes_1.default);
app.use('/api/telegram', telegram_routes_1.default);
app.use('/api/notifications', notification_routes_1.default);
app.use('/api/push', push_notification_routes_1.default);
app.use('/api/examples', example_routes_1.default);
app.use('/api/payment-verification', payment_verification_routes_1.default);
app.use('/api/fraud-detection', fraud_detection_routes_1.default);
app.use('/api/payment-recommendation', payment_recommendation_routes_1.default);
app.use('/api/identity-verification', identity_verification_routes_1.default);
app.use('/api/risk', enhanced_risk_engine_routes_1.default);
app.use('/api/merchant-segmentation', merchant_segmentation_routes_1.default);
app.use('/api/merchant-relationship', merchant_relationship_routes_1.default);
app.use('/api/merchant-self-service', merchant_self_service_routes_1.default);
app.use('/api/advanced-reports', advanced_report_routes_1.default);
app.use('/api/dashboards', dashboard_routes_1.default);
app.use('/dashboard/reports', dashboard_routes_1.default);
// Health check endpoint
app.get('/health', (req, res) => {
    res.success({ status: 'ok' }, 'Server is healthy and running');
});
// Error handling middleware
app.use(apiResponseMiddleware_1.errorHandlerMiddleware);
// Initialize WebSocket server
const io = websocket_service_1.WebSocketService.initialize(httpServer);
// Initialize verification WebSocket service
const verificationWebSocketService_1 = __importDefault(require("./services/websocket/verificationWebSocketService"));
verificationWebSocketService_1.default.initialize(httpServer);
// Initialize verification real-time service
const verificationRealtimeService = verification_realtime_service_1.VerificationRealtimeService.getInstance();
verificationRealtimeService.initialize(io);
// Initialize WebSocket monitor
const websocket_monitor_1 = __importDefault(require("./utils/websocket-monitor"));
websocket_monitor_1.default.initialize(io);
// Initialize transaction monitor service
const transactionMonitor = transaction_monitor_service_1.TransactionMonitorService.getInstance();
transactionMonitor.initialize().catch((error) => {
    console.error('Failed to initialize transaction monitor service:', error);
});
// Start server
const startServer = async () => {
    // Test database connection first
    await testDatabaseConnection();
    // Log all routes
    app._router.stack.forEach((middleware) => {
        if (middleware.route) {
            // Routes registered directly on the app
            console.log(`Route: ${middleware.route.path}`);
        }
        else if (middleware.name === 'router') {
            // Router middleware
            middleware.handle.stack.forEach((handler) => {
                if (handler.route) {
                    const path = handler.route.path;
                    const methods = Object.keys(handler.route.methods);
                }
            });
        }
    });
    return httpServer.listen(PORT, () => {
        console.log(`🚀 Server running on port ${PORT}`);
        console.log(`🔗 API available at http://localhost:${PORT}/api`);
        console.log(`🩺 Health check at http://localhost:${PORT}/health`);
        // Initialize and start alert monitor
        const alertMonitor = new alert_monitor_service_1.AlertMonitorService();
        alertMonitor.start();
        global.alertMonitor = alertMonitor;
        // Initialize and start alert aggregation service
        const alertAggregation = new alert_aggregation_service_1.AlertAggregationService();
        alertAggregation.start();
        global.alertAggregation = alertAggregation;
        // Initialize and start verification alert job
        const verificationAlertJob = new verification_alert_job_1.VerificationAlertJob();
        verificationAlertJob.start();
        global.verificationAlertJob = verificationAlertJob;
    });
};
// Start the server
startServer().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
});
// Handle graceful shutdown
process.on('SIGTERM', async () => {
    // Close server
    httpServer.close(() => { });
    // Cleanup monitoring
    cleanupMonitoring();
    // Shutdown transaction monitor
    transactionMonitor.shutdown();
    // Shutdown alert monitor
    if (global.alertMonitor) {
        global.alertMonitor.stop();
    }
    // Shutdown alert aggregation service
    if (global.alertAggregation) {
        global.alertAggregation.stop();
    }
    // Shutdown verification alert job
    if (global.verificationAlertJob) {
        global.verificationAlertJob.stop();
    }
    // Disconnect Prisma
    await exports.prisma.$disconnect();
    process.exit(0);
});
exports.default = app;
