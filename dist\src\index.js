"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const client_1 = require("@prisma/client");
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const path_1 = __importDefault(require("path"));
const monitoring_1 = require("./utils/monitoring");
// import { AlertMonitorService } from './services/alert-monitor.service';
// import { AlertAggregationService } from './services/alert-aggregation.service';
// import { VerificationAlertJob } from './jobs/verification-alert.job';
// import { WebSocketService } from './services/websocket.service';
// import { TransactionMonitorService } from './services/transaction-monitor.service';
const monitoring_middleware_1 = require("./middlewares/monitoring.middleware");
const apiResponseMiddleware_1 = require("./middlewares/apiResponseMiddleware");
// Import routes - only the ones we're currently using
const health_routes_1 = __importDefault(require("./routes/health.routes"));
const fee_management_test_routes_1 = __importDefault(require("./routes/fee-management-test.routes"));
// All monitoring and service imports are already at the top of the file
// Load environment variables
dotenv_1.default.config();
// Initialize Prisma client
exports.prisma = new client_1.PrismaClient();
// Test database connection
const testDatabaseConnection = async () => {
    try {
        await exports.prisma.$connect();
    }
    catch (error) {
        console.error('Database connection failed:', error);
        console.error('Please check your database configuration in .env file');
        process.exit(1);
    }
};
// Create Express app and HTTP server
const app = (0, express_1.default)();
const httpServer = (0, http_1.createServer)(app);
const PORT = process.env.PORT || 3002; // Use port 3002 as default
// Middleware
app.use((0, cors_1.default)({
    origin: [
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:5175',
        'http://localhost:5176',
        'http://localhost:5177',
        'http://localhost:3000',
        'http://localhost:3002',
        'https://amazingpayme.com',
        '*',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Site-Domain'],
}));
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
}));
app.use((0, morgan_1.default)('dev'));
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
app.use(express_1.default.static(path_1.default.join(__dirname, 'public')));
// Initialize monitoring
const cleanupMonitoring = (0, monitoring_1.initializeMonitoring)();
// Add monitoring middleware
app.use(monitoring_1.monitorRequest);
app.use(monitoring_middleware_1.monitoringMiddleware);
// Add API response middleware
app.use(apiResponseMiddleware_1.apiResponseMiddleware);
// API routes - temporarily disable most routes to identify the issue
app.use('/api/health', health_routes_1.default);
app.use('/api/fee-management', fee_management_test_routes_1.default);
// Temporarily commented out routes to identify the issue
// app.use('/api/auth', authRoutes);
// app.use('/api/users', userRoutes);
// app.use('/api/merchants', merchantRoutes);
// app.use('/api/payment-methods', paymentMethodRoutes);
// app.use('/api/verification-methods', verificationMethodRoutes);
// app.use('/api/transactions', transactionRoutes);
// app.use('/api/payment-pages', paymentPageRoutes);
// app.use('/api/subscriptions', subscriptionRoutes);
// app.use('/api/monitoring', monitoringRoutes);
// app.use('/api/monitoring/websocket', websocketMonitoringRoutes);
// app.use('/api/binance', binanceRoutes);
// app.use('/webhook/binance-pay', binancePayWebhookRoutes);
// app.use('/api/location', locationRoutes);
// app.use('/api/verification', verificationRoutes);
// app.use('/api/webhooks', webhookRoutes);
// app.use('/api/alerts', alertRoutes);
// app.use('/api/alerts', alertAggregationRoutes);
// app.use('/api/alerts', alertAnalyticsRoutes);
// app.use('/api/email', emailRoutes);
// app.use('/api/sms', smsRoutes);
// app.use('/api/telegram', telegramRoutes);
// app.use('/api/notifications', notificationRoutes);
// app.use('/api/push', pushNotificationRoutes);
// app.use('/api/examples', exampleRoutes);
// app.use('/api/payment-verification', paymentVerificationRoutes);
// app.use('/api/fraud-detection', fraudDetectionRoutes);
// app.use('/api/payment-recommendation', paymentRecommendationRoutes);
// app.use('/api/identity-verification', identityVerificationRoutes);
// app.use('/api/risk', enhancedRiskEngineRoutes);
// app.use('/api/merchant-segmentation', merchantSegmentationRoutes);
// app.use('/api/merchant-relationship', merchantRelationshipRoutes);
// app.use('/api/merchant-self-service', merchantSelfServiceRoutes);
// app.use('/api/advanced-reports', advancedReportRoutes);
// app.use('/api/dashboards', dashboardRoutes);
// Static dashboard page
app.use('/dashboard/reports', express_1.default.static(path_1.default.join(__dirname, 'public/reports')));
// Basic health check endpoint (for load balancers)
app.get('/health', (req, res) => {
    res.success({ status: 'ok' }, 'Server is healthy and running');
});
// Error handling middleware
app.use(apiResponseMiddleware_1.errorHandlerMiddleware);
// Initialize WebSocket server (temporarily disabled)
// const io: any = WebSocketService.initialize(httpServer);
// Initialize verification WebSocket service (temporarily disabled)
// import verificationWebSocketService from './services/websocket/verificationWebSocketService';
// verificationWebSocketService.initialize(httpServer);
// Initialize verification real-time service (temporarily disabled)
// const verificationRealtimeService: any = VerificationRealtimeService.getInstance();
// verificationRealtimeService.initialize(io);
// Initialize WebSocket monitor (temporarily disabled)
// import websocketMonitor from './utils/websocket-monitor';
// websocketMonitor.initialize(io);
// Initialize transaction monitor service (temporarily disabled)
// const transactionMonitor: any = TransactionMonitorService.getInstance();
// transactionMonitor.initialize().catch((error) => {
//   console.error('Failed to initialize transaction monitor service:', error);
// });
// Start server
const startServer = async () => {
    // Test database connection first
    await testDatabaseConnection();
    // Initialize advanced reporting scheduled reports
    try {
        const { AdvancedReportService } = await Promise.resolve().then(() => __importStar(require('./services/advanced-report.service')));
        const reportService = new AdvancedReportService();
        await reportService.initializeScheduledReports();
        console.log('📊 Advanced reporting scheduled reports initialized');
    }
    catch (error) {
        console.error('Failed to initialize advanced reporting:', error);
    }
    // Log all routes
    app._router.stack.forEach((middleware) => {
        if (middleware.route) {
            // Routes registered directly on the app
            console.log(`Route: ${middleware.route.path}`);
        }
        else if (middleware.name === 'router') {
            // Router middleware
            middleware.handle.stack.forEach((handler) => {
                if (handler.route) {
                    const path = handler.route.path;
                    const methods = Object.keys(handler.route.methods);
                }
            });
        }
    });
    return httpServer.listen(PORT, () => {
        console.log(`🚀 Server running on port ${PORT}`);
        console.log(`🔗 API available at http://localhost:${PORT}/api`);
        console.log(`🩺 Health check at http://localhost:${PORT}/health`);
        // Initialize and start alert monitor (temporarily disabled)
        // const alertMonitor: any = new AlertMonitorService();
        // alertMonitor.start();
        // (global as any).alertMonitor = alertMonitor;
        // Initialize and start alert aggregation service (temporarily disabled)
        // const alertAggregation: any = new AlertAggregationService();
        // alertAggregation.start();
        // (global as any).alertAggregation = alertAggregation;
        // Initialize and start verification alert job (temporarily disabled)
        // const verificationAlertJob: any = new VerificationAlertJob();
        // verificationAlertJob.start();
        // (global as any).verificationAlertJob = verificationAlertJob;
    });
};
// Start the server
startServer().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
});
// Handle graceful shutdown
process.on('SIGTERM', async () => {
    // Close server
    httpServer.close(() => { });
    // Cleanup monitoring
    cleanupMonitoring();
    // Shutdown transaction monitor (temporarily disabled)
    // transactionMonitor.shutdown();
    // Shutdown alert monitor (temporarily disabled)
    // if ((global as any).alertMonitor) {
    //   (global as any).alertMonitor.stop();
    // }
    // Shutdown alert aggregation service (temporarily disabled)
    // if ((global as any).alertAggregation) {
    //   (global as any).alertAggregation.stop();
    // }
    // Shutdown verification alert job (temporarily disabled)
    // if ((global as any).verificationAlertJob) {
    //   (global as any).verificationAlertJob.stop();
    // }
    // Disconnect Prisma
    await exports.prisma.$disconnect();
    process.exit(0);
});
exports.default = app;
