"use strict";
// jscpd:ignore-file
/**
 * Health Monitoring System
 *
 * This module provides comprehensive health monitoring for the application:
 * - System health checks
 * - Database connection monitoring
 * - API endpoint monitoring
 * - Resource usage tracking
 * - Alerting for critical issues
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkDatabaseHealth = checkDatabaseHealth;
exports.checkMemoryHealth = checkMemoryHealth;
exports.checkCpuHealth = checkCpuHealth;
exports.checkDiskHealth = checkDiskHealth;
exports.getSystemHealth = getSystemHealth;
exports.scheduleHealthChecks = scheduleHealthChecks;
const os_1 = __importDefault(require("os"));
const logger_1 = require("../lib/logger");
const prisma_client_1 = __importDefault(require("../lib/prisma-client"));
const environment_validator_1 = require("./environment-validator");
// Cache for health check results
let healthCache = null;
let healthCacheTimestamp = 0;
const HEALTH_CACHE_TTL = 30 * 1000; // 30 seconds
/**
 * Check database health
 * @returns Health check result
 */
async function checkDatabaseHealth() {
    try {
        // Try a simple query to check database connection
        await prisma_client_1.default.$queryRaw `SELECT 1`;
        return {
            status: 'healthy',
            component: 'database',
            message: 'Database connection is healthy',
            timestamp: new Date(),
        };
    }
    catch (error) {
        logger_1.logger.error('Database health check failed', error);
        return {
            status: 'unhealthy',
            component: 'database',
            message: 'Database connection failed',
            details: (0, environment_validator_1.isProduction)() ? undefined : { error: error.message },
            timestamp: new Date(),
        };
    }
}
/**
 * Check memory usage
 * @returns Health check result
 */
function checkMemoryHealth() {
    const totalMemory = os_1.default.totalmem();
    const freeMemory = os_1.default.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;
    let status = 'healthy';
    let message = 'Memory usage is normal';
    if (memoryUsagePercent > 90) {
        status = 'unhealthy';
        message = 'Critical memory usage';
    }
    else if (memoryUsagePercent > 80) {
        status = 'degraded';
        message = 'High memory usage';
    }
    return {
        status,
        component: 'memory',
        message,
        details: {
            total: totalMemory,
            free: freeMemory,
            used: usedMemory,
            usedPercent: memoryUsagePercent,
        },
        timestamp: new Date(),
    };
}
/**
 * Check CPU usage
 * @returns Health check result
 */
function checkCpuHealth() {
    const cpus = os_1.default.cpus();
    const cpuUsage = process.cpuUsage();
    const totalCpuTime = cpuUsage.user + cpuUsage.system;
    // Calculate CPU usage percentage (rough estimate)
    const cpuUsagePercent = (totalCpuTime / (os_1.default.cpus().length * 1000000)) * 100;
    let status = 'healthy';
    let message = 'CPU usage is normal';
    if (cpuUsagePercent > 90) {
        status = 'unhealthy';
        message = 'Critical CPU usage';
    }
    else if (cpuUsagePercent > 70) {
        status = 'degraded';
        message = 'High CPU usage';
    }
    return {
        status,
        component: 'cpu',
        message,
        details: {
            usage: cpuUsagePercent,
            cores: cpus.length,
        },
        timestamp: new Date(),
    };
}
/**
 * Check disk space
 * This is a placeholder since Node.js doesn't have built-in disk space checking
 * In a real implementation, you would use a library like 'diskusage'
 * @returns Health check result
 */
function checkDiskHealth() {
    // Placeholder for disk health check
    return {
        status: 'healthy',
        component: 'disk',
        message: 'Disk usage is normal',
        timestamp: new Date(),
    };
}
/**
 * Get overall system health
 * @returns System health information
 */
async function getSystemHealth() {
    // Check if we have a recent health check result in cache
    const now = Date.now();
    if (healthCache && now - healthCacheTimestamp < HEALTH_CACHE_TTL) {
        return healthCache;
    }
    // Run all health checks
    const checks = [
        await checkDatabaseHealth(),
        checkMemoryHealth(),
        checkCpuHealth(),
        checkDiskHealth(),
    ];
    // Determine overall status
    let overallStatus = 'healthy';
    if (checks.some((check) => check.status === 'unhealthy')) {
        overallStatus = 'unhealthy';
    }
    else if (checks.some((check) => check.status === 'degraded')) {
        overallStatus = 'degraded';
    }
    // Get system resource information
    const memoryCheck = checks.find((check) => check.component === 'memory');
    const cpuCheck = checks.find((check) => check.component === 'cpu');
    const systemHealth = {
        status: overallStatus,
        checks,
        timestamp: new Date(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
        resources: {
            cpu: cpuCheck?.details?.usage || 0,
            memory: {
                total: memoryCheck?.details?.total || 0,
                free: memoryCheck?.details?.free || 0,
                used: memoryCheck?.details?.used || 0,
                usedPercent: memoryCheck?.details?.usedPercent || 0,
            },
        },
    };
    // Update cache
    healthCache = systemHealth;
    healthCacheTimestamp = now;
    return systemHealth;
}
/**
 * Schedule regular health checks
 * @param interval Interval in milliseconds (default: 5 minutes)
 * @returns Timer ID
 */
function scheduleHealthChecks(interval = 5 * 60 * 1000) {
    return setInterval(async () => {
        try {
            const health = await getSystemHealth();
            // Log health status
            if (health.status !== 'healthy') {
                logger_1.logger.warn(`System health is ${health.status}`, {
                    checks: health.checks.filter((check) => check.status !== 'healthy'),
                });
            }
            else {
                logger_1.logger.debug('System health check passed');
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to run scheduled health check', error);
        }
    }, interval);
}
exports.default = {
    getSystemHealth,
    checkDatabaseHealth,
    checkMemoryHealth,
    checkCpuHealth,
    checkDiskHealth,
    scheduleHealthChecks,
};
