// jscpd:ignore-file
/**
 * Security Testing Script
 * 
 * This script performs security testing on the API to verify security features.
 * It tests:
 * - CSRF protection
 * - XSS protection
 * - SQL injection protection
 * - Rate limiting
 * - Authentication
 * - Authorization
 * 
 * Note: This is a simple security testing script. For more comprehensive security testing,
 * consider using dedicated tools like OWASP ZAP, Burp Suite, or Snyk.
 */

import axios from "axios";
import { expect } from "chai";

// Configuration
const BASE_URL: string ="http://localhost:3002";

// Security test function
async function runSecurityTests(): any {
    console.log("Starting security tests...");
  
    let passedTests: number =0;
    let totalTests = 0;
  
    // Helper function to run a test
    async function runTest(name: string, testFn: () => Promise<void>) {
        totalTests++;
        try {
            await testFn();
            console.log(`✅ PASSED: ${name}`);
            passedTests++;
        } catch (error) {
            console.error(`❌ FAILED: ${name}`);
            console.error(`   Error: ${(error as Error).message}`);
        }
    }
  
    // Test security headers
    await runTest("Security Headers Test", async () => {
        const response: any = await axios.get(`${BASE_URL}/health`);
    
        // Check for security headers
        expect(response.headers).to.have.property("x-content-type-options", "nosniff");
        expect(response.headers).to.have.property("x-xss-protection");
        expect(response.headers).to.have.property("x-frame-options");
        expect(response.headers).to.have.property("content-security-policy");
        expect(response.headers).to.have.property("referrer-policy");
    });
  
    // Test CSRF protection
    await runTest("CSRF Protection Test", async () => {
        try {
            // Attempt to make a POST request without CSRF token
            await axios.post(`${BASE_URL}/api/auth/login`, {
                email: "<EMAIL>",
                password: "password"
            });
      
            // If we get here, the request succeeded without CSRF token
            throw new Error("POST request succeeded without CSRF token");
        } catch (error) {
            // Expect a 403 Forbidden response
            expect(error.response.status).to.equal(403);
        }
    });
  
    // Test XSS protection
    await runTest("XSS Protection Test", async () => {
        try {
            // Attempt to make a request with XSS payload
            const xssPayload: string ="<script>alert(\"XSS\")</script>";
      
            await axios.get(`${BASE_URL}/api/health?q=${xssPayload}`);
      
            // Check that the response doesn't reflect the XSS payload
            // This is a simplified test - in a real scenario, we would check the response body
        } catch (error) {
            // If the request fails, that's also acceptable
            expect(error.response.status).to.be.oneOf([400, 403, 404]);
        }
    });
  
    // Test SQL injection protection
    await runTest("SQL Injection Protection Test", async () => {
        try {
            // Attempt to make a request with SQL injection payload
            const sqlInjectionPayload: string ="' OR '1'='1";
      
            await axios.get(`${BASE_URL}/api/health?q=${sqlInjectionPayload}`);
      
            // This is a simplified test - in a real scenario, we would check the response
        } catch (error) {
            // If the request fails, that's also acceptable
            expect(error.response.status).to.be.oneOf([400, 403, 404]);
        }
    });
  
    // Test rate limiting
    await runTest("Rate Limiting Test", async () => {
    // Make multiple requests to trigger rate limiting
        const requests: any[] =[];
        for (let i: number =0; i < 100; i++) {
            requests.push(axios.get(`${BASE_URL}/api/health`).catch(error => error.response));
        }
    
        const responses: any = await Promise.all(requests);
    
        // At least some of the responses should be rate limited (429)
        const rateLimited: any = responses.some(res => res && res.status === 429);
        expect(rateLimited).to.equal(true);
    });
  
    // Test authentication
    await runTest("Authentication Test", async () => {
        try {
            // Attempt to access a protected endpoint without authentication
            await axios.get(`${BASE_URL}/api/merchants`);
      
            // If we get here, the request succeeded without authentication
            throw new Error("Protected endpoint accessible without authentication");
        } catch (error) {
            // Expect a 401 Unauthorized response
            expect(error.response.status).to.equal(401);
        }
    });
  
    // Test invalid JWT
    await runTest("Invalid JWT Test", async () => {
        try {
            // Attempt to access a protected endpoint with an invalid JWT
            await axios.get(`${BASE_URL}/api/merchants`, {
                headers: { Authorization: "Bearer invalid.jwt.token"
                }
            });
      
            // If we get here, the request succeeded with an invalid JWT
            throw new Error("Protected endpoint accessible with invalid JWT");
        } catch (error) {
            // Expect a 401 Unauthorized response
            expect(error.response.status).to.equal(401);
        }
    });
  
    // Test expired JWT
    await runTest("Expired JWT Test", async () => {
    // Create an expired JWT (this is a simplified example)
        const expiredJwt: string ="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
    
        try {
            // Attempt to access a protected endpoint with an expired JWT
            await axios.get(`${BASE_URL}/api/merchants`, {
                headers: { Authorization: `Bearer ${expiredJwt}`
                }
            });
      
            // If we get here, the request succeeded with an expired JWT
            throw new Error("Protected endpoint accessible with expired JWT");
        } catch (error) {
            // Expect a 401 Unauthorized response
            expect(error.response.status).to.equal(401);
        }
    });
  
    // Print summary
    console.log("\nSecurity Test Summary:");
    console.log("---------------------");
    console.log(`Passed: ${passedTests}/${totalTests} (${(passedTests / totalTests * 100).toFixed(2)}%)`);
  
    // Check if all tests passed
    const allPassed: any = passedTests === totalTests;
  
    if (allPassed) {
        console.log("\nAll Security Tests PASSED ✅");
    } else {
        console.log("\nSome Security Tests FAILED ❌");
    }
  
    return allPassed;
}

// Run the security tests if this script is executed directly
if (require.main === module) {
    runSecurityTests()
        .then((passed)) => {
            process.exit(passed ? 0 : 1);
        })
        .catch((((error) => {
            console.error("Security tests failed with error:", error);
            process.exit(1);
        });
}

export default runSecurityTests;
