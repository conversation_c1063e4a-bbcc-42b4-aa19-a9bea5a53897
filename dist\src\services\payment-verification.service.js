"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentVerificationService = void 0;
// jscpd:ignore-file
const BaseService_1 = require("../shared/modules/services/BaseService");
const ServiceError_1 = require("../shared/modules/services/ServiceError");
const blockchain_api_service_1 = require("./blockchain/blockchain-api.service");
const binance_api_service_1 = require("./blockchain/binance-api.service");
/**
 * Payment verification service
 */
class PaymentVerificationService extends BaseService_1.BaseService {
    constructor() {
        super();
        this.blockchainApiService = new blockchain_api_service_1.BlockchainApiService();
        this.binanceApiService = new binance_api_service_1.BinanceApiService();
    }
    /**
     * Execute a database operation with error handling
     * @param operation Operation to execute
     * @param errorMessage Error message
     * @param context Error context
     * @returns Operation result
     */
    async executeDbOperation(operation, errorMessage, context) {
        try {
            return await operation();
        }
        catch (error) {
            if (error instanceof ServiceError_1.ServiceError) {
                throw error;
            }
            console.error(`${context} error:`, error);
            throw this.paymentError(errorMessage);
        }
    }
    /**
     * Create a payment error
     * @param message Error message
     * @returns Service error
     */
    paymentError(message) {
        return new ServiceError_1.ServiceError(message, 400, 'PAYMENT_ERROR');
    }
    /**
   * Verify a payment
   * @param method Payment method
   * @param transactionId Transaction ID
   * @param amount Expected amount
   * @param currency Expected currency
   * @param recipientAddress Expected recipient address
   * @param merchantApiKey Merchant API key
   * @param merchantSecretKey Merchant secret key
   * @returns Payment verification result
   */
    async verifyPayment(method, transactionId, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey) {
        return this.executeDbOperation(async () => {
            switch (method) {
                case types_1.PaymentMethod.BINANCE_PAY:
                    return this.verifyBinancePayPayment(transactionId, amount, currency, merchantApiKey, merchantSecretKey);
                case types_1.PaymentMethod.BINANCE_C2C:
                    return this.verifyBinanceC2CPayment(transactionId, amount, currency, merchantApiKey, merchantSecretKey);
                case types_1.PaymentMethod.BINANCE_TRC20:
                    return this.verifyBinanceTRC20Payment(transactionId, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey);
                case types_1.PaymentMethod.CRYPTO_TRANSFER:
                    return this.verifyCryptoTransferPayment(transactionId, amount, currency, recipientAddress);
                default:
                    throw this.paymentError(`Unsupported payment method: ${method}`);
            }
        }, `Failed to verify payment with method ${method} and transaction ID ${transactionId}`, 'Payment');
    }
    /**
     * Verify a Binance Pay payment
     * @param transactionId Transaction ID
     * @param amount Expected amount
     * @param currency Expected currency
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    async verifyBinancePayPayment(transactionId, amount, currency, merchantApiKey, merchantSecretKey) {
        return this.executeDbOperation(async () => {
            if (!merchantApiKey || !merchantSecretKey) {
                throw this.paymentError("Merchant API key and secret key are required for Binance Pay verification");
            }
            // Verify transaction
            const result = await this.binanceApiService.verifyBinancePayTransaction(transactionId, merchantApiKey, merchantSecretKey, amount, currency);
            return {
                verified: result.status === "PAID",
                method: types_1.PaymentMethod.BINANCE_PAY,
                amount: result.amount,
                currency: result.currency,
                transactionId: result.transactionId,
                timestamp: result.timestamp,
                rawData: result.rawData
            };
        }, `Failed to verify Binance Pay payment with transaction ID ${transactionId}`, 'BinancePayPayment');
    }
    /**
     * Verify a Binance C2C payment
     * @param note Transaction note
     * @param amount Expected amount
     * @param currency Expected currency
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    async verifyBinanceC2CPayment(note, amount, currency, merchantApiKey, merchantSecretKey) {
        return this.executeDbOperation(async () => {
            if (!merchantApiKey || !merchantSecretKey) {
                throw this.paymentError("Merchant API key and secret key are required for Binance C2C verification");
            }
            // Verify transaction
            const result = await this.binanceApiService.verifyBinanceC2CTransactionByNote(note, merchantApiKey, merchantSecretKey, amount, currency);
            return {
                verified: result.verified,
                method: types_1.PaymentMethod.BINANCE_C2C,
                amount: result.amount,
                currency: result.currency,
                transactionId: result.transactionId,
                timestamp: result.timestamp,
                sender: result.sender,
                recipient: result.recipient,
                rawData: result.rawData
            };
        }, `Failed to verify Binance C2C payment with note ${note}`, 'BinanceC2CPayment');
    }
    /**
     * Verify a Binance TRC20 payment
     * @param txHash Transaction hash
     * @param amount Expected amount
     * @param currency Expected currency
     * @param recipientAddress Expected recipient address
     * @param merchantApiKey Merchant API key
     * @param merchantSecretKey Merchant secret key
     * @returns Payment verification result
     */
    async verifyBinanceTRC20Payment(txHash, amount, currency, recipientAddress, merchantApiKey, merchantSecretKey) {
        return this.executeDbOperation(async () => {
            if (!merchantApiKey || !merchantSecretKey) {
                throw this.paymentError("Merchant API key and secret key are required for Binance TRC20 verification");
            }
            // Verify transaction
            const result = await this.binanceApiService.verifyBinanceTRC20Transaction(txHash, merchantApiKey, merchantSecretKey, amount, recipientAddress);
            return {
                verified: result.status === "PAID",
                method: types_1.PaymentMethod.BINANCE_TRC20,
                amount: result.amount,
                currency: result.currency,
                transactionId: result.transactionId,
                timestamp: result.timestamp,
                recipient: result.recipient,
                rawData: result.rawData
            };
        }, `Failed to verify Binance TRC20 payment with transaction hash ${txHash}`, 'BinanceTRC20Payment');
    }
    /**
   * Verify a crypto transfer payment
   * @param txHash Transaction hash
   * @param amount Expected amount
   * @param currency Expected currency
   * @param recipientAddress Expected recipient address
   * @returns Payment verification result
   */
    async verifyCryptoTransferPayment(txHash, amount, currency, recipientAddress) {
        return this.executeDbOperation(async () => {
            // Map currency to blockchain network and token
            const { network, token } = this.mapCurrencyToNetworkAndToken(currency);
            // Verify transaction
            const result = await this.blockchainApiService.verifyTransaction(txHash, network, token, amount, recipientAddress);
            return {
                verified: result.status === "confirmed",
                method: types_1.PaymentMethod.CRYPTO_TRANSFER,
                amount: result.amount,
                currency: token,
                transactionId: result.hash,
                timestamp: result.timestamp,
                sender: result.from,
                recipient: result.to,
                rawData: result.rawData
            };
        }, `Failed to verify crypto transfer payment with transaction hash ${txHash}`, 'CryptoTransferPayment');
    }
    /**
   * Map currency to blockchain network and token
   * @param currency Currency
   * @returns Blockchain network and token
   */
    mapCurrencyToNetworkAndToken(currency) {
        // Format: USDT_TRC20, USDC_ERC20, etc.
        const parts = currency.split("_");
        if (parts.length !== 2) {
            throw this.paymentError(`Invalid currency format: ${currency}`);
        }
        const [tokenStr, networkStr] = parts;
        // Map token
        let token;
        switch (tokenStr.toUpperCase()) {
            case "USDT":
                token = blockchain_api_service_1.BlockchainToken.USDT;
                break;
            case "USDC":
                token = blockchain_api_service_1.BlockchainToken.USDC;
                break;
            case "BUSD":
                token = blockchain_api_service_1.BlockchainToken.BUSD;
                break;
            default:
                throw this.paymentError(`Unsupported token: ${tokenStr}`);
        }
        // Map network
        let network;
        switch (networkStr.toUpperCase()) {
            case "TRC20":
                network = blockchain_api_service_1.BlockchainNetwork.TRON;
                break;
            case "ERC20":
                network = blockchain_api_service_1.BlockchainNetwork.ETHEREUM;
                break;
            case "BEP20":
                network = blockchain_api_service_1.BlockchainNetwork.BSC;
                break;
            case "POLYGON":
                network = blockchain_api_service_1.BlockchainNetwork.POLYGON;
                break;
            default:
                throw this.paymentError(`Unsupported network: ${networkStr}`);
        }
        return { network, token };
    }
}
exports.PaymentVerificationService = PaymentVerificationService;
//# sourceMappingURL=payment-verification.service.js.map