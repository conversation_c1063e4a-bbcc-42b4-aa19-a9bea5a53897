"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const binance_controller_1 = require("../controllers/binance.controller");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_middleware_1.authMiddleware);
// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
router.post("/test-connection", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), binance_controller_1.testConnection);
router.post("/account-info", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), binance_controller_1.getAccountInfo);
router.post("/deposit-history", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), binance_controller_1.getDepositHistory);
router.post("/verify-trc20", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), binance_controller_1.verifyTrc20Deposit);
exports.default = router;
//# sourceMappingURL=binance.routes.js.map