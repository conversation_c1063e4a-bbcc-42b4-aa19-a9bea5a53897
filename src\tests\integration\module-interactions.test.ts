/**
 * Integration Tests for Module Interactions
 * 
 * Tests the interaction between different modules to ensure
 * they work together correctly in production scenarios.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { TestUtils } from '../utils';
import { IdentityVerificationService } from '../../services/identity-verification';
import { FraudDetectionService } from '../../services/fraud-detection';
import { ReportingService } from '../../services/reporting';
import { AlertAggregationController } from '../../controllers/alert-aggregation';
import { IdentityVerificationController } from '../../controllers/identity-verification';
import { AdminController } from '../../controllers/admin';
import { FraudDetectionController } from '../../controllers/fraud-detection';

describe('Module Integration Tests', () => {
  let testContext: any;
  let mockPrisma: any;

  beforeAll(async () => {
    // Setup test environment
    TestUtils.setup();
    testContext = TestUtils.createTestContext('integration-tests');
    mockPrisma = testContext.mockPrisma;
  });

  afterAll(async () => {
    // Cleanup test environment
    if (testContext?.cleanup) {
      testContext.cleanup();
    }
  });

  beforeEach(async () => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('Identity Verification + Fraud Detection Integration', () => {
    it('should verify identity and assess fraud risk in sequence', async () => {
      // Arrange
      const identityService = new IdentityVerificationService(mockPrisma);
      const fraudService = new FraudDetectionService(mockPrisma);
      
      const verificationData = {
        address: '******************************************',
        message: 'Verify identity for AmazingPay',
        signature: '0xabcdef...',
        userId: 'user-123',
        merchantId: 'merchant-456'
      };

      const transactionContext = {
        transaction: {
          id: 'tx-123',
          amount: 1000,
          currency: 'USD',
          merchantId: 'merchant-456',
          userId: 'user-123'
        },
        merchant: { id: 'merchant-456', name: 'Test Merchant' },
        ipAddress: '***********',
        userAgent: 'Test Browser',
        deviceId: 'device-123',
        timestamp: new Date()
      };

      // Mock successful identity verification
      const mockVerificationResult = {
        success: true,
        verificationId: 'verification-123',
        method: 'ethereum_signature',
        confidence: 0.95
      };

      // Mock fraud assessment
      const mockFraudResult = {
        transactionId: 'tx-123',
        riskScore: { score: 25, level: 'LOW', factors: [], timestamp: new Date(), confidence: 0.9 },
        isFlagged: false,
        isBlocked: false,
        reason: 'Low risk transaction',
        recommendedAction: 'Process normally',
        processingTime: 150
      };

      // Act
      const verificationResult = await identityService.verifyEthereumSignature(verificationData);
      const fraudResult = await fraudService.assessTransactionRisk(transactionContext);

      // Assert
      expect(verificationResult).toBeDefined();
      expect(fraudResult).toBeDefined();
      expect(verificationResult.success).toBe(true);
      expect(fraudResult.riskScore.level).toBe('LOW');
    });

    it('should handle identity verification failure and block transaction', async () => {
      // Arrange
      const identityService = new IdentityVerificationService(mockPrisma);
      const fraudService = new FraudDetectionService(mockPrisma);
      
      const invalidVerificationData = {
        address: '0xinvalid',
        message: 'Invalid signature',
        signature: '0xinvalid',
        userId: 'user-123',
        merchantId: 'merchant-456'
      };

      // Act & Assert
      await expect(async () => {
        await identityService.verifyEthereumSignature(invalidVerificationData);
      }).rejects.toThrow();
    });
  });

  describe('Fraud Detection + Reporting Integration', () => {
    it('should generate fraud reports based on detection results', async () => {
      // Arrange
      const fraudService = new FraudDetectionService(mockPrisma);
      const reportingService = new ReportingService(mockPrisma);

      const reportRequest = {
        type: 'fraud_summary',
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        },
        filters: {
          merchantId: 'merchant-456',
          riskLevel: 'HIGH'
        },
        format: 'json' as const,
        userId: 'admin-123'
      };

      // Mock fraud statistics
      mockPrisma.riskAssessment.count.mockResolvedValue(100);
      mockPrisma.riskAssessment.aggregate.mockResolvedValue({
        _avg: { score: 45 }
      });

      // Act
      const statistics = await fraudService.getStatistics('merchant-456', reportRequest.dateRange.start, reportRequest.dateRange.end);
      const report = await reportingService.generateReport(reportRequest);

      // Assert
      expect(statistics).toBeDefined();
      expect(report).toBeDefined();
      expect(statistics.totalAssessments).toBe(100);
      expect(report.status).toBe('completed');
    });
  });

  describe('Controller Integration Tests', () => {
    it('should handle end-to-end API flow through multiple controllers', async () => {
      // Arrange
      const identityController = new IdentityVerificationController();
      const fraudController = new FraudDetectionController();
      const adminController = new AdminController();

      const { mockRequest, mockResponse, mockNext } = testContext;

      // Mock request data
      mockRequest.body = {
        address: '******************************************',
        message: 'Verify for transaction',
        signature: '0xabcdef...'
      };
      mockRequest.user = { id: 'user-123', role: 'USER' };
      mockRequest.params = { merchantId: 'merchant-456' };

      // Act
      await identityController.verifyEthereumSignature(mockRequest, mockResponse, mockNext);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle admin operations across multiple modules', async () => {
      // Arrange
      const adminController = new AdminController();
      const { mockRequest, mockResponse, mockNext } = testContext;

      mockRequest.user = { id: 'admin-123', role: 'ADMIN' };
      mockRequest.body = {
        name: 'Test Admin',
        email: '<EMAIL>',
        password: 'securePassword123',
        roleId: 'admin-role-123'
      };

      // Mock admin creation
      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockPrisma.user.create.mockResolvedValue({
        id: 'user-456',
        email: '<EMAIL>',
        role: 'ADMIN'
      });
      mockPrisma.admin.create.mockResolvedValue({
        id: 'admin-456',
        userId: 'user-456',
        department: 'Test Admin',
        permissions: ['admin:access']
      });

      // Act
      await adminController.createAdminUser(mockRequest, mockResponse, mockNext);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling Integration', () => {
    it('should propagate errors correctly across module boundaries', async () => {
      // Arrange
      const identityService = new IdentityVerificationService(mockPrisma);
      
      // Mock database error
      mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database connection failed'));

      const verificationData = {
        address: '******************************************',
        message: 'Test message',
        signature: '0xabcdef...',
        userId: 'user-123',
        merchantId: 'merchant-456'
      };

      // Act & Assert
      await expect(async () => {
        await identityService.verifyEthereumSignature(verificationData);
      }).rejects.toThrow('Database connection failed');
    });

    it('should handle service unavailability gracefully', async () => {
      // Arrange
      const fraudService = new FraudDetectionService(mockPrisma);
      
      // Mock service timeout
      mockPrisma.riskAssessment.create.mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Service timeout')), 100);
        });
      });

      const transactionContext = {
        transaction: { id: 'tx-123', amount: 1000 },
        merchant: { id: 'merchant-456' },
        ipAddress: '***********',
        userAgent: 'Test Browser',
        deviceId: 'device-123',
        timestamp: new Date()
      };

      // Act & Assert
      await expect(async () => {
        await fraudService.assessTransactionRisk(transactionContext);
      }).rejects.toThrow();
    });
  });

  describe('Performance Integration Tests', () => {
    it('should handle concurrent requests across multiple modules', async () => {
      // Arrange
      const identityService = new IdentityVerificationService(mockPrisma);
      const fraudService = new FraudDetectionService(mockPrisma);
      
      const concurrentRequests = 10;
      const requests = [];

      // Act
      for (let i = 0; i < concurrentRequests; i++) {
        const verificationPromise = identityService.verifyEthereumSignature({
          address: `0x123456789012345678901234567890123456789${i}`,
          message: `Test message ${i}`,
          signature: `0xabcdef${i}`,
          userId: `user-${i}`,
          merchantId: `merchant-${i}`
        });
        requests.push(verificationPromise);
      }

      // Assert
      const results = await Promise.allSettled(requests);
      expect(results).toHaveLength(concurrentRequests);
      
      // Check that at least some requests succeeded (depending on mocks)
      const successfulResults = results.filter(result => result.status === 'fulfilled');
      expect(successfulResults.length).toBeGreaterThanOrEqual(0);
    });
  });
});
