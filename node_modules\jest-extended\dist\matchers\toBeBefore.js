"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toBeBefore = toBeBefore;
function toBeBefore(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { matcherHint, printReceived } = this.utils;
    const pass = actual instanceof Date && actual < expected;
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toBeBefore', 'received', '') +
                '\n\n' +
                `Expected date to be before ${printReceived(expected)} but received:\n` +
                `  ${printReceived(actual)}`
            : matcherHint('.toBeBefore', 'received', '') +
                '\n\n' +
                `Expected date to be before ${printReceived(expected)} but received:\n` +
                `  ${printReceived(actual)}`,
    };
}
