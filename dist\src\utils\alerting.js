"use strict";
// jscpd:ignore-file
/**
 * Alerting Utility
 *
 * This utility provides functions for sending alerts when critical issues occur.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendSecurityAlert = exports.sendDatabaseAlert = exports.sendSystemAlert = exports.clearRecentAlerts = exports.getRecentAlerts = exports.sendAlert = void 0;
const logger_1 = require("../lib/logger");
const alert_types_1 = require("../types/alert.types");
// Using imported AlertSeverity and AlertType from types/alert.types.ts
/**
 * Alert notification channels
 * Using AlertChannel from types/alert.types.ts
 */
const alert_types_2 = require("../types/alert.types");
// Store recent alerts for reference
const recentAlerts = [];
const MAX_RECENT_ALERTS = 100;
/**
 * Generate a unique alert ID
 * @returns Unique alert ID
 */
const generateAlertId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
};
/**
 * Send an alert via email
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendEmailAlert = async (alert) => {
    // In a real implementation, you would send an email
    // For now, just log the alert
    logger_1.logger.info(`[EMAIL ALERT] ${alert.severity.toUpperCase()} - ${alert.message}`, {
        alert
    });
    // Simulate sending an email
    await new Promise(resolve => setTimeout(resolve, 100));
};
/**
 * Send an alert via SMS
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendSmsAlert = async (alert) => {
    // In a real implementation, you would send an SMS
    // For now, just log the alert
    logger_1.logger.info(`[SMS ALERT] ${alert.severity.toUpperCase()} - ${alert.message}`, {
        alert
    });
    // Simulate sending an SMS
    await new Promise(resolve => setTimeout(resolve, 100));
};
/**
 * Send an alert via Slack
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendSlackAlert = async (alert) => {
    // In a real implementation, you would send a Slack message
    // For now, just log the alert
    logger_1.logger.info(`[SLACK ALERT] ${alert.severity.toUpperCase()} - ${alert.message}`, {
        alert
    });
    // Simulate sending a Slack message
    await new Promise(resolve => setTimeout(resolve, 100));
};
/**
 * Send an alert via Telegram
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendTelegramAlert = async (alert) => {
    // In a real implementation, you would send a Telegram message
    // For now, just log the alert
    logger_1.logger.info(`[TELEGRAM ALERT] ${alert.severity.toUpperCase()} - ${alert.message}`, {
        alert
    });
    // Simulate sending a Telegram message
    await new Promise(resolve => setTimeout(resolve, 100));
};
/**
 * Send an alert via webhook
 * @param alert Alert data
 * @returns Promise that resolves when the alert is sent
 */
const sendWebhookAlert = async (alert) => {
    // In a real implementation, you would send a webhook request
    // For now, just log the alert
    logger_1.logger.info(`[WEBHOOK ALERT] ${alert.severity.toUpperCase()} - ${alert.message}`, {
        alert
    });
    // Simulate sending a webhook request
    await new Promise(resolve => setTimeout(resolve, 100));
};
/**
 * Send an alert via the specified channels
 * @param alert Alert data
 * @param channels Alert channels
 * @returns Promise that resolves when the alert is sent
 */
const sendAlert = async (alert, channels = [alert_types_2.AlertChannel.EMAIL]) => {
    // Create an alert notification
    const notification = {
        id: generateAlertId(),
        alert,
        channels,
        status: "pending",
        createdAt: new Date()
    };
    try {
        // Send the alert via each channel
        const sendPromises = channels.map((channel));
    }
    finally { }
};
exports.sendAlert = sendAlert;
{
    switch (channel) {
        case alert_types_2.AlertChannel.EMAIL:
            return sendEmailAlert(alert);
        case alert_types_2.AlertChannel.SMS:
            return sendSmsAlert(alert);
        case alert_types_2.AlertChannel.SLACK:
            return sendSlackAlert(alert);
        case alert_types_2.AlertChannel.TELEGRAM:
            return sendTelegramAlert(alert);
        case alert_types_2.AlertChannel.WEBHOOK:
            return sendWebhookAlert(alert);
        default:
            return Promise.resolve();
    }
}
;
// Wait for all alerts to be sent
await Promise.all(sendPromises);
// Update the notification status
notification.status = "sent";
notification.sentAt = new Date();
// Log the alert
logger_1.logger.info(`Alert sent: ${alert.message}`, {
    alertId: notification.id,
    severity: alert.severity,
    type: alert.type,
    channels
});
try { }
catch (error) {
    // Update the notification status
    notification.status = "failed";
    notification.error = error instanceof Error ? error.message : "Unknown error";
    // Log the error
    logger_1.logger.error(`Failed to send alert: ${alert.message}`, {
        alertId: notification.id,
        severity: alert.severity,
        type: alert.type,
        channels,
        error
    });
}
// Add to recent alerts
recentAlerts.unshift(notification);
// Trim to max size
if (recentAlerts.length > MAX_RECENT_ALERTS) {
    recentAlerts.pop();
}
return notification;
;
/**
 * Get recent alerts
 * @returns Recent alerts
 */
const getRecentAlerts = () => {
    return [...recentAlerts];
};
exports.getRecentAlerts = getRecentAlerts;
/**
 * Clear recent alerts
 */
const clearRecentAlerts = () => {
    recentAlerts.length = 0;
};
exports.clearRecentAlerts = clearRecentAlerts;
/**
 * Send a system alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
const sendSystemAlert = async (message, severity = alert_types_1.AlertSeverity.ERROR, details) => {
    return (0, exports.sendAlert)({
        severity,
        type: alert_types_1.AlertType.SYSTEM,
        message,
        details,
        timestamp: new Date()
    }, getChannelsForSeverity(severity));
};
exports.sendSystemAlert = sendSystemAlert;
/**
 * Send a database alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
const sendDatabaseAlert = async (message, severity = alert_types_1.AlertSeverity.ERROR, details) => {
    return (0, exports.sendAlert)({
        severity,
        type: alert_types_1.AlertType.DATABASE,
        message,
        details,
        timestamp: new Date()
    }, getChannelsForSeverity(severity));
};
exports.sendDatabaseAlert = sendDatabaseAlert;
/**
 * Send a security alert
 * @param message Alert message
 * @param severity Alert severity
 * @param details Additional details
 * @returns Promise that resolves when the alert is sent
 */
const sendSecurityAlert = async (message, severity = alert_types_1.AlertSeverity.ERROR, details) => {
    return (0, exports.sendAlert)({
        severity,
        type: alert_types_1.AlertType.SECURITY,
        message,
        details,
        timestamp: new Date()
    }, getChannelsForSeverity(severity));
};
exports.sendSecurityAlert = sendSecurityAlert;
/**
 * Get alert channels for a severity level
 * @param severity Alert severity
 * @returns Alert channels
 */
const getChannelsForSeverity = (severity) => {
    switch (severity) {
        case alert_types_1.AlertSeverity.CRITICAL:
            return [alert_types_2.AlertChannel.EMAIL, alert_types_2.AlertChannel.SMS, alert_types_2.AlertChannel.SLACK, alert_types_2.AlertChannel.TELEGRAM];
        case alert_types_1.AlertSeverity.ERROR:
            return [alert_types_2.AlertChannel.EMAIL, alert_types_2.AlertChannel.SLACK, alert_types_2.AlertChannel.TELEGRAM];
        case alert_types_1.AlertSeverity.WARNING:
            return [alert_types_2.AlertChannel.EMAIL, alert_types_2.AlertChannel.SLACK];
        case alert_types_1.AlertSeverity.INFO:
        default:
            return [alert_types_2.AlertChannel.EMAIL];
    }
};
exports.default = {
    sendAlert: exports.sendAlert,
    getRecentAlerts: exports.getRecentAlerts,
    clearRecentAlerts: exports.clearRecentAlerts,
    sendSystemAlert: exports.sendSystemAlert,
    sendDatabaseAlert: exports.sendDatabaseAlert,
    sendSecurityAlert: exports.sendSecurityAlert,
    AlertSeverity: alert_types_1.AlertSeverity,
    AlertType: alert_types_1.AlertType,
    AlertChannel: alert_types_2.AlertChannel
};
//# sourceMappingURL=alerting.js.map