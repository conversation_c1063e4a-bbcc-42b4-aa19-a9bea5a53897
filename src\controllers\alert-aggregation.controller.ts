// jscpd:ignore-file
import { Request, Response } from "express";
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError, ErrorType, ErrorCode } from '../utils/errors/AppError';
import { AlertType, AlertSeverity } from '../types';
import prisma from '../lib/prisma';
import { BaseController } from "./base.controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError, ErrorType, ErrorCode } from '../utils/errors/AppError';
import { AlertType, AlertSeverity } from '../types';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * AlertAggregationController
 */
export class AlertAggregationController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Get all alert aggregation rules
   */
  getAggregationRules = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }

        // Get aggregation rules
        const rules: any = await prisma.alertAggregationRule.findMany({
            orderBy: { createdAt: "desc" }
        });

        // Return rules
        return res.status(200).json({
            success: true,
            data: rules
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get aggregation rules",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get a specific alert aggregation rule by ID
   */
  getAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }

        // Get rule ID from params
        const ruleId: any = req.params.id;

        if (!ruleId) {
            throw new AppError({
                message: "Rule ID is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            });
        }

        // Get aggregation rule
        const rule: any = await prisma.alertAggregationRule.findUnique({
            where: { id: ruleId }
        });

        // Check if rule exists
        if (!rule) {
            throw new AppError({
                message: "Aggregation rule not found",
                type: ErrorType.NOT_FOUND,
                code: ErrorCode.RESOURCE_NOT_FOUND
            });
        }

        // Return rule
        return res.status(200).json({
            success: true,
            data: rule
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get aggregation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Create a new alert aggregation rule
   */
  createAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }

        // Get rule data from body
        const {
            name,
            description,
            type,
            severity,
            timeWindow,
            threshold,
            groupBy,
            enabled
        } = req.body;

        // Validate required fields
        if (!name || !description || !type || !severity || !timeWindow || !threshold || !groupBy) {
            throw new AppError({
                message: "Missing required fields",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            });
        }

        // Validate type
        if (type !== "ANY" && !Object.values(AlertType).includes(type as AlertType)) {
            throw new AppError({
                message: "Invalid alert type",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate severity
        if (severity !== "ANY" && !Object.values(AlertSeverity).includes(severity as AlertSeverity)) {
            throw new AppError({
                message: "Invalid alert severity",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate timeWindow
        if (timeWindow <= 0) {
            throw new AppError({
                message: "Time window must be greater than 0",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate threshold
        if (threshold <= 0) {
            throw new AppError({
                message: "Threshold must be greater than 0",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate groupBy
        if (!Array.isArray(groupBy) || groupBy.length === 0) {
            throw new AppError({
                message: "Group by must be a non-empty array",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Create aggregation rule
        const rule: any = await prisma.alertAggregationRule.create({
            data: {
                name,
                description,
                type,
                severity,
                timeWindow,
                threshold,
                groupBy,
                enabled: enabled !== undefined ? enabled : true
            }
        });

        // Return created rule
        return res.status(201).json({
            success: true,
            data: rule,
            message: "Aggregation rule created successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to create aggregation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Update an existing alert aggregation rule
   */
  updateAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }

        // Get rule ID from params
        const ruleId: any = req.params.id;

        if (!ruleId) {
            throw new AppError({
                message: "Rule ID is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            });
        }

        // Get rule data from body
        const {
            name,
            description,
            type,
            severity,
            timeWindow,
            threshold,
            groupBy,
            enabled
        } = req.body;

        // Check if rule exists
        const existingRule: any = await prisma.alertAggregationRule.findUnique({
            where: { id: ruleId }
        });

        if (!existingRule) {
            throw new AppError({
                message: "Aggregation rule not found",
                type: ErrorType.NOT_FOUND,
                code: ErrorCode.RESOURCE_NOT_FOUND
            });
        }

        // Validate type if provided
        if (type && type !== "ANY" && !Object.values(AlertType).includes(type as AlertType)) {
            throw new AppError({
                message: "Invalid alert type",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate severity if provided
        if (severity && severity !== "ANY" && !Object.values(AlertSeverity).includes(severity as AlertSeverity)) {
            throw new AppError({
                message: "Invalid alert severity",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate timeWindow if provided
        if (timeWindow && timeWindow <= 0) {
            throw new AppError({
                message: "Time window must be greater than 0",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate threshold if provided
        if (threshold && threshold <= 0) {
            throw new AppError({
                message: "Threshold must be greater than 0",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Validate groupBy if provided
        if (groupBy && (!Array.isArray(groupBy) || groupBy.length === 0)) {
            throw new AppError({
                message: "Group by must be a non-empty array",
                type: ErrorType.VALIDATION,
                code: ErrorCode.INVALID_INPUT
            });
        }

        // Update rule
        const updatedRule: any = await prisma.alertAggregationRule.update({
            where: { id: ruleId },
            data: { name: name || existingRule.name,
                description: description || existingRule.description,
                type: type || existingRule.type,
                severity: severity || existingRule.severity,
                timeWindow: timeWindow || existingRule.timeWindow,
                threshold: threshold || existingRule.threshold,
                groupBy: groupBy || existingRule.groupBy,
                enabled: enabled !== undefined ? enabled : existingRule.enabled
            }
        });

        // Return updated rule
        return res.status(200).json({
            success: true,
            data: updatedRule,
            message: "Aggregation rule updated successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to update aggregation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Delete an alert aggregation rule
   */
  deleteAggregationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
                message: "Unauthorized",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            });
        }

        // Get rule ID from params
        const ruleId: any = req.params.id;

        if (!ruleId) {
            throw new AppError({
                message: "Rule ID is required",
                type: ErrorType.VALIDATION,
                code: ErrorCode.MISSING_REQUIRED_FIELD
            });
        }

        // Check if rule exists
        const existingRule: any = await prisma.alertAggregationRule.findUnique({
            where: { id: ruleId }
        });

        if (!existingRule) {
            throw new AppError({
                message: "Aggregation rule not found",
                type: ErrorType.NOT_FOUND,
                code: ErrorCode.RESOURCE_NOT_FOUND
            });
        }

        // Delete rule
        await prisma.alertAggregationRule.delete({
            where: { id: ruleId }
        });

        // Return success
        return res.status(200).json({
            success: true,
            message: "Aggregation rule deleted successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to delete aggregation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get all alert correlation rules
   */
  getCorrelationRules = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get correlation rules
        const rules: any = await prisma.alertCorrelationRule.findMany({
            orderBy: { createdAt: "desc" }
        });

        // Return rules
        return res.status(200).json({
            success: true,
            data: rules
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get correlation rules",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get a specific alert correlation rule by ID
   */
  getCorrelationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get rule ID from params
        const ruleId: any = req.params.id;

        if (!ruleId) {
            throw new AppError({
            message: "Rule ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Get correlation rule
        const rule: any = await prisma.alertCorrelationRule.findUnique({
            where: { id: ruleId }
        });

        // Check if rule exists
        if (!rule) {
            throw new AppError({
            message: "Correlation rule not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Return rule
        return res.status(200).json({
            success: true,
            data: rule
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get correlation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Create a new alert correlation rule
   */
  createCorrelationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get rule data from body
        const {
            name,
            description,
            primaryType,
            secondaryTypes,
            timeWindow,
            enabled
        } = req.body;

        // Validate required fields
        if (!name || !description || !primaryType || !secondaryTypes || !timeWindow) {
            throw new AppError({
            message: "Missing required fields",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Validate primaryType
        if (primaryType !== "ANY" && !Object.values(AlertType).includes(primaryType as AlertType)) {
            throw new AppError({
            message: "Invalid primary alert type",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Validate secondaryTypes
        if (!Array.isArray(secondaryTypes) || secondaryTypes.length === 0) {
            throw new AppError({
            message: "Secondary types must be a non-empty array",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        for (const type of secondaryTypes) {
            if (type !== "ANY" && !Object.values(AlertType).includes(type as AlertType)) {
                throw new AppError(`Invalid secondary alert type: ${type}`, 400);
            }
        }

        // Validate timeWindow
        if (timeWindow <= 0) {
            throw new AppError({
            message: "Time window must be greater than 0",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Create correlation rule
        const rule: any = await prisma.alertCorrelationRule.create({
            data: {
                name,
                description,
                primaryType,
                secondaryTypes,
                timeWindow,
                enabled: enabled !== undefined ? enabled : true
            }
        });

        // Return created rule
        return res.status(201).json({
            success: true,
            data: rule,
            message: "Correlation rule created successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to create correlation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Update an existing alert correlation rule
   */
  updateCorrelationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get rule ID from params
        const ruleId: any = req.params.id;

        if (!ruleId) {
            throw new AppError({
            message: "Rule ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Get rule data from body
        const {
            name,
            description,
            primaryType,
            secondaryTypes,
            timeWindow,
            enabled
        } = req.body;

        // Check if rule exists
        const existingRule: any = await prisma.alertCorrelationRule.findUnique({
            where: { id: ruleId }
        });

        if (!existingRule) {
            throw new AppError({
            message: "Correlation rule not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Validate primaryType if provided
        if (primaryType && primaryType !== "ANY" && !Object.values(AlertType).includes(primaryType as AlertType)) {
            throw new AppError({
            message: "Invalid primary alert type",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Validate secondaryTypes if provided
        if (secondaryTypes) {
            if (!Array.isArray(secondaryTypes) || secondaryTypes.length === 0) {
                throw new AppError({
            message: "Secondary types must be a non-empty array",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
            }

            for (const type of secondaryTypes) {
                if (type !== "ANY" && !Object.values(AlertType).includes(type as AlertType)) {
                    throw new AppError(`Invalid secondary alert type: ${type}`, 400);
                }
            }
        }

        // Validate timeWindow if provided
        if (timeWindow && timeWindow <= 0) {
            throw new AppError({
            message: "Time window must be greater than 0",
            type: ErrorType.VALIDATION,
            code: ErrorCode.INVALID_INPUT
        });
        }

        // Update rule
        const updatedRule: any = await prisma.alertCorrelationRule.update({
            where: { id: ruleId },
            data: { name: name || existingRule.name,
                description: description || existingRule.description,
                primaryType: primaryType || existingRule.primaryType,
                secondaryTypes: secondaryTypes || existingRule.secondaryTypes,
                timeWindow: timeWindow || existingRule.timeWindow,
                enabled: enabled !== undefined ? enabled : existingRule.enabled
            }
        });

        // Return updated rule
        return res.status(200).json({
            success: true,
            data: updatedRule,
            message: "Correlation rule updated successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to update correlation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Delete an alert correlation rule
   */
  deleteCorrelationRule = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole: any = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get rule ID from params
        const ruleId: any = req.params.id;

        if (!ruleId) {
            throw new AppError({
            message: "Rule ID is required",
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
        }

        // Check if rule exists
        const existingRule: any = await prisma.alertCorrelationRule.findUnique({
            where: { id: ruleId }
        });

        if (!existingRule) {
            throw new AppError({
            message: "Correlation rule not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Delete rule
        await prisma.alertCorrelationRule.delete({
            where: { id: ruleId }
        });

        // Return success
        return res.status(200).json({
            success: true,
            message: "Correlation rule deleted successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to delete correlation rule",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });
}

export default new AlertAggregationController();
