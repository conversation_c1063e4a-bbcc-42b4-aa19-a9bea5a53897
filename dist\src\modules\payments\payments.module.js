"use strict";
// jscpd:ignore-file
/**
 * Payments Module
 *
 * This module handles payment methods and payment processing.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const ModuleFactory_1 = require("../../factories/ModuleFactory");
const logger_1 = require("../../utils/logger");
/**
 * Payments Module
 */
class PaymentsModule extends ModuleFactory_1.BaseModule {
    /**
     * Constructor
     */
    constructor() {
        super('PaymentsModule');
    }
    /**
     * Initialize the module
     */
    initialize() {
        logger_1.logger.info('Initializing PaymentsModule');
        // Get controllers
        const paymentMethodController = this.controllerFactory.getController('paymentMethod');
        const paymentController = this.controllerFactory.getController('payment');
        // Set up payment method routes
        this.router.get('/methods', paymentMethodController.getAll);
        this.router.get('/methods/:id', paymentMethodController.getById);
        this.router.post('/methods', paymentMethodController.create);
        this.router.put('/methods/:id', paymentMethodController.update);
        this.router.delete('/methods/:id', paymentMethodController.delete);
        this.router.put('/methods/:id/default', paymentMethodController.setAsDefault);
        // Set up payment routes
        this.router.post('/process', paymentController.processPayment);
        this.router.post('/verify', paymentController.verifyPayment);
        this.router.post('/refund', paymentController.refundPayment);
        this.router.get('/status/:id', paymentController.getPaymentStatus);
        logger_1.logger.info('PaymentsModule initialized');
    }
}
// Export the module
exports.default = new PaymentsModule();
//# sourceMappingURL=payments.module.js.map