import express, { Request, Response, Router } from 'express';
import request from 'supertest';
import { <PERSON>rudController } from '../../shared/modules/controllers';
import { BaseService } from '../../shared/modules/services';
import { asyncHand<PERSON> } from '../../shared/modules/utils';
import { BaseService } from '../../shared/modules/services';
import { asyncHandler } from '../../shared/modules/utils';

// Mock model for testing
class MockModel {
  private items: any[] = [
    { id: '1', name: 'Item 1' },
    { id: '2', name: 'Item 2' }
  ];

  findMany(query: any = {}) {
    // Simple implementation for testing
    if (query.where) {
      return Promise.resolve(this.items.filter(item) => {
        for (const key in query.where) {
          if (item[key] !== query.where[key]) {
            return false;
          }
        }
        return true;
      }));
    }
    return Promise.resolve([...this.items]);
  }

  findUnique(params: any) {
    const { id } = params.where;
    const item: any =this.items.find(item => item.id === id);
    return Promise.resolve(item || null);
  }

  create(params: any) {
    const newItem = {
      id: String(this.items.length + 1),
      ...params.data
    };
    this.items.push(newItem);
    return Promise.resolve(newItem);
  }

  update(params: any) {
    const { id } = params.where;
    const index: any =this.items.findIndex(item => item.id === id);
    
    if (index === -1) {
      return Promise.resolve(null);
    }
    
    const updatedItem: any = {
      ...this.items[index],
      ...params.data
    };
    
    this.items[index] = updatedItem;
    return Promise.resolve(updatedItem);
  }

  delete(params: any) {
    const { id } = params.where;
    const index: any =this.items.findIndex(item => item.id === id);
    
    if (index === -1) {
      return Promise.resolve(null);
    }
    
    const deletedItem: any =this.items[index];
    this.items.splice(index, 1);
    return Promise.resolve(deletedItem);
  }
}

// Test service extending BaseService
class TestService extends BaseService {
  constructor(model: any) {
    super(model);
  }
  
  // Add a custom method to test service-specific functionality
  async findByName(name: string) {
    return this.model.findMany({
      where: { name }
    });
  }
}

// Test controller extending CrudController
class TestController extends CrudController {
  constructor(private testService: TestService) {
    super();
  }
  
  getAllItems = asyncHandler(async (req: Request, res: Response) => {
    return this.getAll(req, res, this.testService, 'Items retrieved successfully');
  });
  
  getItemById = asyncHandler(async (req: Request, res: Response) => {
    return this.getById(req, res, this.testService, 'Item retrieved successfully');
  });
  
  createItem = asyncHandler(async (req: Request, res: Response) => {
    return this.create(req, res, this.testService, null, 'Item created successfully');
  });
  
  updateItem = asyncHandler(async (req: Request, res: Response) => {
    return this.update(req, res, this.testService, null, 'Item updated successfully');
  });
  
  deleteItem = asyncHandler(async (req: Request, res: Response) => {
    return this.delete(req, res, this.testService, 'Item deleted successfully');
  });
  
  // Custom method to test controller-specific functionality
  getItemByName = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { name } = req.params;
      const items: any =await this.testService.findByName(name);
      
      if (!items || items.length === 0) {
        return this.sendError(res, 'Items not found', 404);
      }
      
      return this.sendSuccess(res, items, 'Items found by name');
    } catch (error) {
      return this.sendError(res, 'Error finding items by name', 500, error);
    }
  });
}

describe('Shared Modules Integration', () => {
  let app: express.Application;
  let router: Router;
  let testController: TestController;
  
  beforeAll(() => {
    // Create Express app
    app = express();
    app.use(express.json());
    
    // Create router
    router = Router();
    
    // Create service and controller
    const mockModel: any =new MockModel();
    const testService: any =new TestService(mockModel);
    testController = new TestController(testService);
    
    // Set up routes
    router.get('/items', testController.getAllItems);
    router.get('/items/:id', testController.getItemById);
    router.post('/items', testController.createItem);
    router.put('/items/:id', testController.updateItem);
    router.delete('/items/:id', testController.deleteItem);
    router.get('/items/name/:name', testController.getItemByName);
    
    // Add router to app
    app.use('/api', router);
    
    // Add error handler
    app.use((err: any, req: Request, res: Response, next: any) => {
      res.status(500).json({
        success: false,
        message: 'Server error',
        error: err.message
      });
    });
  });
  
  describe('CRUD Operations', () => {
    it('should get all items', async () => {
      // Act
      const response: any =await request(app).get('/api/items');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Items retrieved successfully');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(2);
    });
    
    it('should get item by id', async () => {
      // Act
      const response: any =await request(app).get('/api/items/1');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Item retrieved successfully');
      expect(response.body.data.id).toBe('1');
      expect(response.body.data.name).toBe('Item 1');
    });
    
    it('should return 404 for non-existent item', async () => {
      // Act
      const response: any =await request(app).get('/api/items/999');
      
      // Assert
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Item not found');
    });
    
    it('should create a new item', async () => {
      // Act
      const response: any =await request(app)
        .post('/api/items')
        .send({ name: 'New Item' });
      
      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Item created successfully');
      expect(response.body.data.name).toBe('New Item');
      expect(response.body.data.id).toBe('3');
    });
    
    it('should update an existing item', async () => {
      // Act
      const response: any =await request(app)
        .put('/api/items/2')
        .send({ name: 'Updated Item' });
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Item updated successfully');
      expect(response.body.data.id).toBe('2');
      expect(response.body.data.name).toBe('Updated Item');
    });
    
    it('should delete an existing item', async () => {
      // Act
      const response: any =await request(app).delete('/api/items/3');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Item deleted successfully');
      
      // Verify item is deleted
      const getResponse: any =await request(app).get('/api/items/3');
      expect(getResponse.status).toBe(404);
    });
  });
  
  describe('Custom Methods', () => {
    it('should find items by name', async () => {
      // Act
      const response: any =await request(app).get('/api/items/name/Item 1');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Items found by name');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(1);
      expect(response.body.data[0].name).toBe('Item 1');
    });
    
    it('should return 404 when no items match the name', async () => {
      // Act
      const response: any =await request(app).get('/api/items/name/Non-existent');
      
      // Assert
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Items not found');
    });
  });
});
