"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const push_notification_controller_ts_1 = require("../controllers/refactored/push-notification.controller.ts");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// Public routes
router.get("/vapid-public-key", push_notification_controller_ts_1.PushNotificationController.getPublicKey);
// Protected routes
router.post("/subscribe", auth_1.authenticate, push_notification_controller_ts_1.PushNotificationController.subscribe);
router.post("/unsubscribe", auth_1.authenticate, push_notification_controller_ts_1.PushNotificationController.unsubscribe);
router.post("/test", auth_1.authenticate, push_notification_controller_ts_1.PushNotificationController.sendTest);
router.get("/subscriptions", auth_1.authenticate, push_notification_controller_ts_1.PushNotificationController.getUserSubscriptions);
router.get("/merchant-subscriptions", auth_1.authenticate, push_notification_controller_ts_1.PushNotificationController.getMerchantSubscriptions);
router.delete("/subscriptions/:id", auth_1.authenticate, push_notification_controller_ts_1.PushNotificationController.deleteSubscription);
exports.default = router;
//# sourceMappingURL=push-notification.routes.js.map