{"version": 3, "file": "BinanceTRC20VerificationStrategy.js", "sourceRoot": "", "sources": ["../../../../../src/services/verification/strategies/BinanceTRC20VerificationStrategy.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAKH,gDAA6C;AAS7C;;GAEG;AACH,MAAa,gCAAgC;IAA7C;QACY,YAAO,GAAY,IAAI,CAAC;QACxB,kBAAa,GAAwB;YACzC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,yBAAyB;YAChE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE;YACzC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,EAAE;YAC/C,OAAO,EAAE,OAAO;SACnB,CAAC;IAmPN,CAAC;IAjPG;;KAEC;IACM,OAAO;QACV,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;KAEC;IACM,0BAA0B;QAC7B,OAAO,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED;;KAEC;IACM,KAAK,CAAC,MAAM,CAAC,OAA4B;QAC5C,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,oDAAoD,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;YAEzF,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC;YAEzD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,OAAO,EAAE,4BAA4B;oBACrC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC;YACN,CAAC;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,OAAO,EAAE,4BAA4B;oBACrC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC;YACN,CAAC;YAED,4CAA4C;YAC5C,MAAM,kBAAkB,GAAQ,MAAM,IAAI,CAAC,0BAA0B,CACjE,IAAI,EACJ,aAAa,EACb,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,QAAQ,CACnB,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBAC9B,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,OAAO,EAAG,kBAA4B,CAAC,OAAO;oBAC9C,OAAO,EAAE,kBAAkB,CAAC,OAAO;oBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC;YACN,CAAC;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,cAAc,EAAE,kBAAkB,CAAC,OAAO,EAAE,cAAc,IAAI,IAAI;gBAClE,OAAO,EAAE,+BAA+B;gBACxC,OAAO,EAAE,kBAAkB,CAAC,OAAO;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,qCAAsC,KAAe,CAAC,OAAO,EAAE,EAAE;gBAC1E,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,KAAK;aACR,CAAC,CAAC;YAEH,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,OAAO,EAAE,wBAAyB,KAAe,CAAC,OAAO,EAAE;gBAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACN,CAAC;IACL,CAAC;IAED;;KAEC;IACM,iBAAiB;QACpB,OAAO;YACH;gBACI,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,0BAA0B;gBACvC,WAAW,EAAE,mDAAmD;aACnE;YACD;gBACI,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,0BAA0B;gBACvC,WAAW,EAAE,8CAA8C;aAC9D;SACJ,CAAC;IACN,CAAC;IAED;;KAEC;IACM,cAAc;QACjB,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAED;;KAEC;IACM,cAAc;QACjB,OAAO,wDAAwD,CAAC;IACpE,CAAC;IAED;;KAEC;IACM,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;KAEC;IACM,gBAAgB;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;KAEC;IACM,gBAAgB,CAAC,MAA2B;QAC/C,IAAI,CAAC,aAAa,GAAG;YACjB,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,MAAM;SACZ,CAAC;IACN,CAAC;IAED;;KAEC;IACO,KAAK,CAAC,0BAA0B,CACpC,IAAY,EACZ,aAAqB,EACrB,MAAc,EACd,QAAgB;QAEhB,IAAI,CAAC;YACD,4DAA4D;YAC5D,oDAAoD;YAEpD,oBAAoB;YACpB,yCAAyC;YACzC,mEAAmE;YACnE,MAAM;YACN,iBAAiB;YACjB,mDAAmD;YACnD,SAAS;YACT,gBAAgB;YAChB,cAAc;YACd,wBAAwB;YACxB,SAAS;YACT,MAAM;YACN,KAAK;YAEL,iCAAiC;YACjC,MAAM,iBAAiB,GAAQ;gBAC3B,IAAI,EAAE;oBACF;wBACI,EAAE,EAAE,OAAO;wBACX,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE;wBACzB,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,OAAO;wBAChB,MAAM,EAAE,CAAC,EAAE,cAAc;wBACzB,OAAO,EAAE,aAAa;wBACtB,IAAI,EAAE,IAAI;wBACV,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;wBACtB,YAAY,EAAE,CAAC;wBACf,YAAY,EAAE,KAAK;qBACtB;iBACJ;aACJ,CAAC;YAEF,oDAAoD;YACpD,MAAM,WAAW,GAAQ,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAChD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC,OAAO,KAAK,aAAa,CACzD,CAAC;YAEF,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uBAAuB;iBACnC,CAAC;YACN,CAAC;YAED,0CAA0C;YAC1C,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;gBAC5C,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yCAAyC,MAAM,IAAI,QAAQ,SAAS,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,EAAE;oBACrH,OAAO,EAAE,EAAE,cAAc,EAAE,MAAM;wBAC7B,YAAY,EAAE,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC;wBAC5C,QAAQ,EAAE,WAAW,CAAC,IAAI;qBAC7B;iBACJ,CAAC;YACN,CAAC;YAED,wCAAwC;YACxC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,8BAA8B;oBACvC,OAAO,EAAE,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM;qBACpC;iBACJ,CAAC;YACN,CAAC;YAED,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,EAAE,cAAc,EAAE,WAAW,CAAC,EAAE;oBACrC,MAAM,EAAE,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC;oBACtC,QAAQ,EAAE,WAAW,CAAC,IAAI;oBAC1B,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,SAAS,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;iBAC9C;aACJ,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,mCAAoC,KAAe,CAAC,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAEvF,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,sBAAuB,KAAe,CAAC,OAAO,EAAE;aAC5D,CAAC;QACN,CAAC;IACL,CAAC;CACJ;AA1PD,4EA0PC"}