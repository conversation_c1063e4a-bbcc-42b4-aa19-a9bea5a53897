"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertAggregationService = void 0;
// jscpd:ignore-file
const BaseService_1 = require("../base/BaseService");
const logger_1 = require("../utils/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
const alert_types_1 = require("../types/alert.types");
/**
 * Alert aggregation service
 */
class AlertAggregationService extends BaseService_1.BaseService {
    /**
     * Create a new alert aggregation service
     * @param checkInterval Check interval in milliseconds (default: 5 minutes)
     */
    constructor(checkInterval = 5 * 60 * 1000) {
        super(null);
        this.intervalId = null;
        this.isRunning = false;
        this.lastCheckTime = new Date();
        this.aggregationRules = [];
        this.correlationRules = [];
        // Initialize service
    }
    /**
     * Start aggregation service
     */
    async start() {
        if (this.isRunning) {
            logger_1.logger.warn('Alert aggregation service is already running');
            return;
        }
        logger_1.logger.info('Starting alert aggregation service');
        this.isRunning = true;
        // Load rules
        await this.loadRules();
        // Run initial check
        this.checkAlerts().catch((error) => {
            logger_1.logger.error('Error in initial alert aggregation check', { error });
        });
        // Set up interval
        this.intervalId = setInterval(() => {
            this.checkAlerts().catch((error) => {
                logger_1.logger.error('Error in alert aggregation check', { error });
            });
        }, this.checkInterval);
    }
    /**
     * Stop aggregation service
     */
    stop() {
        if (!this.isRunning || !this.intervalId) {
            logger_1.logger.warn('Alert aggregation service is not running');
            return;
        }
        logger_1.logger.info('Stopping alert aggregation service');
        clearInterval(this.intervalId);
        this.isRunning = false;
        this.intervalId = null;
    }
    /**
     * Load aggregation and correlation rules
     */
    async loadRules() {
        try {
            // Load aggregation rules from database
            const aggregationRules = await prisma_1.default.alertAggregationRule.findMany({
                where: { enabled: true },
            });
            this.aggregationRules = aggregationRules.map((rule) => ({
                ...rule,
                groupBy: rule.groupBy,
            }));
            // Load correlation rules from database
            const correlationRules = await prisma_1.default.alertCorrelationRule.findMany({
                where: { enabled: true },
            });
            this.correlationRules = correlationRules.map((rule) => ({
                ...rule,
                secondaryTypes: rule.secondaryTypes,
            }));
            logger_1.logger.info('Alert rules loaded', {
                aggregationRules: this.aggregationRules.length,
                correlationRules: this.correlationRules.length,
            });
        }
        catch (error) {
            logger_1.logger.error('Error loading alert rules', { error });
            // Use default rules if database rules can't be loaded
            this.setDefaultRules();
        }
    }
    /**
     * Set default rules
     */
    setDefaultRules() {
        // Default aggregation rules
        this.aggregationRules = [
            {
                id: 'default-system-health',
                name: 'System Health Aggregation',
                description: 'Aggregates system health alerts',
                type: alert_types_1.AlertType.SYSTEM_HEALTH,
                severity: 'ANY',
                timeWindow: 15, // 15 minutes
                threshold: 3,
                groupBy: ['type', 'severity'],
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                id: 'default-payment-failure',
                name: 'Payment Failure Aggregation',
                description: 'Aggregates payment failure alerts',
                type: alert_types_1.AlertType.PAYMENT_FAILURE,
                severity: 'ANY',
                timeWindow: 15, // 15 minutes
                threshold: 5,
                groupBy: ['type', 'merchantId'],
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                id: 'default-verification-failure',
                name: 'Verification Failure Aggregation',
                description: 'Aggregates verification failure alerts',
                type: alert_types_1.AlertType.VERIFICATION_FAILURE,
                severity: 'ANY',
                timeWindow: 15, // 15 minutes
                threshold: 5,
                groupBy: ['type', 'merchantId'],
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                id: 'default-webhook-failure',
                name: 'Webhook Failure Aggregation',
                description: 'Aggregates webhook failure alerts',
                type: alert_types_1.AlertType.WEBHOOK_FAILURE,
                severity: 'ANY',
                timeWindow: 15, // 15 minutes
                threshold: 5,
                groupBy: ['type', 'merchantId'],
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
        ];
        // Default correlation rules
        this.correlationRules = [
            {
                id: 'default-payment-verification',
                name: 'Payment and Verification Correlation',
                description: 'Correlates payment and verification failures',
                primaryType: alert_types_1.AlertType.PAYMENT_FAILURE,
                secondaryTypes: [alert_types_1.AlertType.VERIFICATION_FAILURE],
                timeWindow: 15, // 15 minutes
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                id: 'default-system-payment',
                name: 'System Health and Payment Correlation',
                description: 'Correlates system health issues with payment failures',
                primaryType: alert_types_1.AlertType.SYSTEM_HEALTH,
                secondaryTypes: [alert_types_1.AlertType.PAYMENT_FAILURE, alert_types_1.AlertType.VERIFICATION_FAILURE],
                timeWindow: 15, // 15 minutes
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
        ];
        logger_1.logger.info('Using default alert rules', {
            aggregationRules: this.aggregationRules.length,
            correlationRules: this.correlationRules.length,
        });
    }
    /**
     * Check alerts for aggregation and correlation
     */
    async checkAlerts() {
        try {
            const now = new Date();
            // Process aggregation rules
            await this.processAggregationRules(now);
            // Process correlation rules
            await this.processCorrelationRules(now);
            // Update last check time
            this.lastCheckTime = now;
        }
        catch (error) {
            logger_1.logger.error('Error checking alerts', { error });
        }
    }
    /**
     * Process aggregation rules
     * @param now Current time
     */
    async processAggregationRules(now) {
        for (const rule of this.aggregationRules) {
            try {
                // Calculate time window
                const timeWindowStart = new Date(now.getTime() - rule.timeWindow * 60 * 1000);
                // Get alerts in time window
                const alerts = await this.getAlertsForAggregation(rule, timeWindowStart, now);
                // Group alerts by specified fields
                const groupedAlerts = this.groupAlerts(alerts, rule.groupBy);
                // Check each group against threshold
                for (const [groupKey, groupAlerts] of Object.entries(groupedAlerts)) {
                    if (groupAlerts.length >= rule.threshold) {
                        // Create aggregated alert
                        await this.createAggregatedAlert(rule, groupKey, groupAlerts);
                    }
                }
            }
            catch (error) {
                logger_1.logger.error('Error processing aggregation rule', { error, rule });
            }
        }
    }
    /**
     * Process correlation rules
     * @param now Current time
     */
    async processCorrelationRules(now) {
        for (const rule of this.correlationRules) {
            try {
                // Calculate time window
                const timeWindowStart = new Date(now.getTime() - rule.timeWindow * 60 * 1000);
                // Get primary alerts in time window
                const primaryAlerts = await this.getAlertsForCorrelation(rule.primaryType, timeWindowStart, now);
                // Skip if no primary alerts
                if (primaryAlerts.length === 0) {
                    continue;
                }
                // Get secondary alerts in time window
                const secondaryAlerts = await this.getSecondaryAlertsForCorrelation(rule.secondaryTypes, timeWindowStart, now);
                // Skip if no secondary alerts
                if (secondaryAlerts.length === 0) {
                    continue;
                }
                // Group primary alerts by merchant
                const groupedPrimaryAlerts = this.groupAlertsByMerchant(primaryAlerts);
                // Group secondary alerts by merchant
                const groupedSecondaryAlerts = this.groupAlertsByMerchant(secondaryAlerts);
                // Find correlations
                for (const [merchantId, merchantPrimaryAlerts] of Object.entries(groupedPrimaryAlerts)) {
                    const merchantSecondaryAlerts = groupedSecondaryAlerts[merchantId] || [];
                    if (merchantSecondaryAlerts.length > 0) {
                        // Create correlated alert
                        await this.createCorrelatedAlert(rule, merchantId, merchantPrimaryAlerts, merchantSecondaryAlerts);
                    }
                }
            }
            catch (error) {
                logger_1.logger.error('Error processing correlation rule', { error, rule });
            }
        }
    }
    /**
     * Get alerts for aggregation
     * @param rule Aggregation rule
     * @param startTime Start time
     * @param endTime End time
     * @returns Alerts
     */
    async getAlertsForAggregation(rule, startTime, endTime) {
        try {
            // Build where clause
            const where = {
                createdAt: {
                    gte: startTime,
                    lte: endTime,
                },
                status: alert_types_1.AlertStatus.ACTIVE,
            };
            // Add type filter if not ANY
            if (rule.type !== 'ANY') {
                where.type = rule.type;
            }
            // Add severity filter if not ANY
            if (rule.severity !== 'ANY') {
                where.severity = rule.severity;
            }
            // Exclude aggregated alerts
            where.source = { not: 'aggregation' };
            // Get alerts
            const alerts = await prisma_1.default.alert.findMany({
                where,
                orderBy: { createdAt: 'desc' },
            });
            return alerts;
        }
        catch (error) {
            logger_1.logger.error('Error getting alerts for aggregation', { error, rule });
            return [];
        }
    }
    /**
     * Get alerts for correlation
     * @param type Alert type
     * @param startTime Start time
     * @param endTime End time
     * @returns Alerts
     */
    async getAlertsForCorrelation(type, startTime, endTime) {
        try {
            // Build where clause
            const where = {
                createdAt: {
                    gte: startTime,
                    lte: endTime,
                },
                status: alert_types_1.AlertStatus.ACTIVE,
            };
            // Add type filter if not ANY
            if (type !== 'ANY') {
                where.type = type;
            }
            // Exclude aggregated and correlated alerts
            where.source = { notIn: ['aggregation', 'correlation'] };
            // Get alerts
            const alerts = await prisma_1.default.alert.findMany({
                where,
                orderBy: { createdAt: 'desc' },
            });
            return alerts;
        }
        catch (error) {
            logger_1.logger.error('Error getting alerts for correlation', { error, type });
            return [];
        }
    }
    /**
     * Get secondary alerts for correlation
     * @param types Alert types
     * @param startTime Start time
     * @param endTime End time
     * @returns Alerts
     */
    async getSecondaryAlertsForCorrelation(types, startTime, endTime) {
        try {
            // Build where clause
            const where = {
                createdAt: {
                    gte: startTime,
                    lte: endTime,
                },
                status: alert_types_1.AlertStatus.ACTIVE,
            };
            // Add type filter if not all ANY
            if (!types.every((type) => type === 'ANY')) {
                const nonAnyTypes = types.filter((type) => type !== 'ANY');
                where.type = { in: nonAnyTypes };
            }
            // Exclude aggregated and correlated alerts
            where.source = { notIn: ['aggregation', 'correlation'] };
            // Get alerts
            const alerts = await prisma_1.default.alert.findMany({
                where,
                orderBy: { createdAt: 'desc' },
            });
            return alerts;
        }
        catch (error) {
            logger_1.logger.error('Error getting secondary alerts for correlation', { error, types });
            return [];
        }
    }
    /**
     * Group alerts by specified fields
     * @param alerts Alerts
     * @param groupBy Fields to group by
     * @returns Grouped alerts
     */
    groupAlerts(alerts, groupBy) {
        const grouped = {};
        for (const alert of alerts) {
            // Create group key
            const groupValues = groupBy.map((field) => {
                const value = alert[field];
                return value !== undefined && value !== null ? value : 'null';
            });
            const groupKey = groupValues.join('|');
            // Add alert to group
            if (!grouped[groupKey]) {
                grouped[groupKey] = [];
            }
            grouped[groupKey].push(alert);
        }
        return grouped;
    }
    /**
     * Group alerts by merchant
     * @param alerts Alerts
     * @returns Grouped alerts
     */
    groupAlertsByMerchant(alerts) {
        const grouped = {};
        for (const alert of alerts) {
            const merchantId = alert.merchantId || 'system';
            if (!grouped[merchantId]) {
                grouped[merchantId] = [];
            }
            grouped[merchantId].push(alert);
        }
        return grouped;
    }
    /**
     * Create aggregated alert
     * @param rule Aggregation rule
     * @param groupKey Group key
     * @param alerts Alerts in group
     */
    async createAggregatedAlert(rule, groupKey, alerts) {
        try {
            // Check if there's already an active aggregated alert for this group
            const existingAlert = await prisma_1.default.alert.findFirst({
                where: {
                    source: 'aggregation',
                    sourceId: `agg-${rule.id}-${groupKey}`,
                    status: alert_types_1.AlertStatus.ACTIVE,
                },
            });
            if (existingAlert) {
                // Update existing alert
                await prisma_1.default.alert.update({
                    where: { id: existingAlert.id },
                    data: {
                        details: {
                            ...existingAlert.details,
                            alertCount: alerts.length,
                            lastUpdated: new Date().toISOString(),
                            alertIds: alerts.map((a) => a.id),
                        },
                        updatedAt: new Date(),
                    },
                });
                logger_1.logger.info('Updated aggregated alert', {
                    alertId: existingAlert.id,
                    rule: rule.name,
                    alertCount: alerts.length,
                });
            }
            else {
                // Determine severity (use highest severity from alerts)
                const severityOrder = {
                    [alert_types_1.AlertSeverity.CRITICAL]: 4,
                    [alert_types_1.AlertSeverity.ERROR]: 3,
                    [alert_types_1.AlertSeverity.WARNING]: 2,
                    [alert_types_1.AlertSeverity.INFO]: 1,
                };
                const highestSeverity = alerts.reduce((highest, alert) => {
                    return severityOrder[alert.severity] > severityOrder[highest] ? alert.severity : highest;
                }, alert_types_1.AlertSeverity.INFO);
                const merchantId = alerts.every((a) => a.merchantId === alerts[0].merchantId)
                    ? alerts[0].merchantId
                    : undefined;
                // Create aggregated alert
                const alertId = await this.alertService.createAlert({
                    type: rule.type === 'ANY' ? alerts[0].type : rule.type,
                    severity: highestSeverity,
                    title: `${rule.name}: ${alerts.length} alerts in ${rule.timeWindow} minutes`,
                    message: `Detected ${alerts.length} similar alerts of type ${alerts[0].type} in the last ${rule.timeWindow} minutes.`,
                    source: 'aggregation',
                    details: {
                        ruleId: rule.id,
                        ruleName: rule.name,
                        groupKey,
                        alertCount: alerts.length,
                        timeWindow: rule.timeWindow,
                        threshold: rule.threshold,
                        alertIds: alerts.map((a) => a.id),
                        alertTypes: [...new Set(alerts.map((a) => a.type))],
                        alertSeverities: [...new Set(alerts.map((a) => a.severity))],
                        createdAt: new Date().toISOString(),
                        lastUpdated: new Date().toISOString(),
                    },
                    merchantId,
                    notificationMethods: [alert_types_1.AlertNotificationMethod.DASHBOARD, alert_types_1.AlertNotificationMethod.EMAIL],
                });
                logger_1.logger.info('Created aggregated alert', {
                    alertId,
                    rule: rule.name,
                    alertCount: alerts.length,
                });
                // Update source ID
                await prisma_1.default.alert.update({
                    where: { id: alertId },
                    data: { sourceId: `agg-${rule.id}-${groupKey}` },
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Error creating aggregated alert', { error, rule, groupKey });
        }
    }
    /**
     * Create correlated alert
     * @param rule Correlation rule
     * @param merchantId Merchant ID
     * @param primaryAlerts Primary alerts
     * @param secondaryAlerts Secondary alerts
     */
    async createCorrelatedAlert(rule, merchantId, primaryAlerts, secondaryAlerts) {
        try {
            // Check if there's already an active correlated alert for this rule and merchant
            const existingAlert = await prisma_1.default.alert.findFirst({
                where: {
                    source: 'correlation',
                    sourceId: `corr-${rule.id}-${merchantId}`,
                    status: alert_types_1.AlertStatus.ACTIVE,
                },
            });
            if (existingAlert) {
                // Update existing alert
                await prisma_1.default.alert.update({
                    where: { id: existingAlert.id },
                    data: {
                        details: {
                            ...existingAlert.details,
                            primaryAlertCount: primaryAlerts.length,
                            secondaryAlertCount: secondaryAlerts.length,
                            lastUpdated: new Date().toISOString(),
                            primaryAlertIds: primaryAlerts.map((a) => a.id),
                            secondaryAlertIds: secondaryAlerts.map((a) => a.id),
                        },
                        updatedAt: new Date(),
                    },
                });
                logger_1.logger.info('Updated correlated alert', {
                    alertId: existingAlert.id,
                    rule: rule.name,
                    primaryAlertCount: primaryAlerts.length,
                    secondaryAlertCount: secondaryAlerts.length,
                });
            }
            else {
                // Determine severity (use highest severity from all alerts)
                const severityOrder = {
                    [alert_types_1.AlertSeverity.CRITICAL]: 4,
                    [alert_types_1.AlertSeverity.ERROR]: 3,
                    [alert_types_1.AlertSeverity.WARNING]: 2,
                    [alert_types_1.AlertSeverity.INFO]: 1,
                };
                const allAlerts = [...primaryAlerts, ...secondaryAlerts];
                const highestSeverity = allAlerts.reduce((highest, alert) => {
                    return severityOrder[alert.severity] > severityOrder[highest] ? alert.severity : highest;
                }, alert_types_1.AlertSeverity.INFO);
                const alertId = await this.alertService.createAlert({
                    type: alert_types_1.AlertType.CUSTOM,
                    severity: highestSeverity,
                    title: `${rule.name}: Correlated alerts detected`,
                    message: `Detected correlation between ${primaryAlerts.length} ${rule.primaryType} alerts and ${secondaryAlerts.length} secondary alerts in the last ${rule.timeWindow} minutes.`,
                    source: 'correlation',
                    details: {
                        ruleId: rule.id,
                        ruleName: rule.name,
                        primaryType: rule.primaryType,
                        secondaryTypes: rule.secondaryTypes,
                        primaryAlertCount: primaryAlerts.length,
                        secondaryAlertCount: secondaryAlerts.length,
                        timeWindow: rule.timeWindow,
                        primaryAlertIds: primaryAlerts.map((a) => a.id),
                        secondaryAlertIds: secondaryAlerts.map((a) => a.id),
                        primaryAlertTypes: [...new Set(primaryAlerts.map((a) => a.type))],
                        secondaryAlertTypes: [...new Set(secondaryAlerts.map((a) => a.type))],
                        createdAt: new Date().toISOString(),
                        lastUpdated: new Date().toISOString(),
                    },
                    merchantId: merchantId === 'system' ? undefined : merchantId,
                    notificationMethods: [alert_types_1.AlertNotificationMethod.DASHBOARD, alert_types_1.AlertNotificationMethod.EMAIL],
                });
                logger_1.logger.info('Created correlated alert', {
                    alertId,
                    rule: rule.name,
                    primaryAlertCount: primaryAlerts.length,
                    secondaryAlertCount: secondaryAlerts.length,
                });
                // Update source ID
                await prisma_1.default.alert.update({
                    where: { id: alertId },
                    data: { sourceId: `corr-${rule.id}-${merchantId}` },
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Error creating correlated alert', {
                error,
                rule,
                merchantId,
            });
        }
    }
}
exports.AlertAggregationService = AlertAggregationService;
exports.default = new AlertAggregationService();
//# sourceMappingURL=alert-aggregation.service.js.map