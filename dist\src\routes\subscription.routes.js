"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const subscription_controller_1 = __importDefault(require("../controllers/subscription.controller"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const router = (0, express_1.Router)();
// Get all subscription plans - public route
router.get("/", subscription_controller_1.default.getAllPlans);
// Get a specific subscription plan - public route
router.get("/:id", (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("id").notEmpty()
]), subscription_controller_1.default.getPlanById);
// Admin routes to manage subscription plans
router.post("/", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("name").notEmpty(),
    (0, express_validator_1.body)("duration").isNumeric(),
    (0, express_validator_1.body)("price").isNumeric()
]), subscription_controller_1.default.createPlan);
router.put("/:id", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("id").notEmpty()
]), subscription_controller_1.default.updatePlan);
router.delete("/:id", auth_middleware_1.authenticate, (0, auth_middleware_1.authorize)(["admin"]), (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("id").notEmpty()
]), subscription_controller_1.default.deletePlan);
// New routes for merchant subscription management
router.post("/merchant/:merchantId/subscribe", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty(),
    (0, express_validator_1.body)("planId").notEmpty(),
    (0, express_validator_1.body)("paymentMethodId").optional()
]), subscription_controller_1.default.subscribeMerchant);
router.post("/merchant/:merchantId/cancel", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty()
]), subscription_controller_1.default.cancelSubscription);
// Get merchant subscription history
router.get("/merchant/:merchantId/history", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty()
]), subscription_controller_1.default.getSubscriptionHistory);
// Download subscription history
router.get("/merchant/:merchantId/history/download", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty()
]), subscription_controller_1.default.downloadSubscriptionHistory);
// Get subscription status
router.get("/merchant/:merchantId/status", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty()
]), subscription_controller_1.default.checkSubscriptionStatus);
// Get subscription analytics
router.get("/merchant/:merchantId/analytics", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("merchantId").notEmpty()
]), subscription_controller_1.default.getSubscriptionAnalytics);
exports.default = router;
//# sourceMappingURL=subscription.routes.js.map