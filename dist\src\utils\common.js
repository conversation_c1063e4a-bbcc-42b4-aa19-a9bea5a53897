"use strict";
// jscpd:ignore-file
/**
 * Common Utility Functions
 *
 * This file contains common utility functions used throughout the application.
 * It helps eliminate duplication by providing a single source of truth for utility functions.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isDefined = isDefined;
exports.isUndefined = isUndefined;
exports.isEmpty = isEmpty;
exports.isNotEmpty = isNotEmpty;
exports.formatCurrency = formatCurrency;
exports.formatDate = formatDate;
exports.generateRandomString = generateRandomString;
exports.truncate = truncate;
/**
 * Check if a value is defined
 * @param value Value to check
 * @returns True if value is defined
 */
function isDefined(value) {
    return value !== undefined && value !== null;
}
/**
 * Check if a value is undefined or null
 * @param value Value to check
 * @returns True if value is undefined or null
 */
function isUndefined(value) {
    return value === undefined || value === null;
}
/**
 * Check if a string is empty
 * @param value String to check
 * @returns True if string is empty
 */
function isEmpty(value) {
    return isUndefined(value) || value.trim() === '';
}
/**
 * Check if a string is not empty
 * @param value String to check
 * @returns True if string is not empty
 */
function isNotEmpty(value) {
    return !isEmpty(value);
}
/**
 * Format currency amount
 * @param amount Amount to format
 * @param currency Currency code
 * @returns Formatted currency amount
 */
function formatCurrency(amount, currency) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}
/**
 * Format date
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date
 */
function formatDate(date, options) {
    return new Intl.DateTimeFormat('en-US', options).format(date);
}
/**
 * Generate a random string
 * @param length Length of the string
 * @returns Random string
 */
function generateRandomString(length) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}
/**
 * Truncate a string
 * @param str String to truncate
 * @param maxLength Maximum length
 * @param suffix Suffix to add if truncated
 * @returns Truncated string
 */
function truncate(str, maxLength, suffix = '...') {
    if (str.length <= maxLength) {
        return str;
    }
    return str.substring(0, maxLength) + suffix;
}
//# sourceMappingURL=common.js.map