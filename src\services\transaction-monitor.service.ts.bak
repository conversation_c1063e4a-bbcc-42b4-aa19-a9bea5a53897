// jscpd:ignore-file
import { EventEmitter } from "events";
import prisma from "../lib/prisma";
import { TransactionService } from "./transaction.service";
import { BinanceService } from "./binance.service";
import { Transaction, PaymentMethod } from '../types';
import { BaseService } from "../shared/modules/services/BaseService";
import { TransactionService } from "./transaction.service";
import { BinanceService } from "./binance.service";
import { Transaction, PaymentMethod } from '../types';
import { BaseService } from "../shared/modules/services/BaseService";

/**
 * Transaction Monitor Service
 *
 * This service monitors transactions and updates their status based on verification results.
 */
export class TransactionMonitorService extends BaseService {
    private monitoringIntervals: Map<string, NodeJS.Timeout>;
    private isInitialized: boolean;
    private static instance: TransactionMonitorService;

    /**
     * Get singleton instance
     */
    public static getInstance(): TransactionMonitorService {
        if (!TransactionMonitorService.instance) {
            TransactionMonitorService.instance = new TransactionMonitorService();
        }
        return TransactionMonitorService.instance;
    }

    /**
     * Constructor
     */
    private constructor() {
        super();
        this.monitoringIntervals = new Map();
        this.isInitialized = false;
        console.log("Transaction monitoring service created");
    }

    /**
     * Initialize monitoring service
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            console.log("Transaction monitoring service already initialized");
            return;
        }

        console.log("Initializing transaction monitoring service");

        try {
            // Find pending transactions
            const pendingTransactions: any =await prisma.transaction.findMany({
                where: {, status: {
                        in: ["PENDING", "PROCESSING"]
                    }
                },
                include: {, paymentMethod: true
                }
            });

            console.log(`Found ${pendingTransactions.length} pending transactions to monitor`);

            // Start monitoring each transaction
            for (const transaction of pendingTransactions) {
                this.startMonitoring(transaction.id);
            }

            this.isInitialized = true;
            console.log("Transaction monitoring service initialized");
        } catch (error) {
            console.error("Error initializing transaction monitoring service:", error);
            throw error;
        }
    }

    /**
     * Start monitoring a transaction
     * @param transactionId Transaction ID
     */
    public async startMonitoring(transactionId: string): Promise<void> {
        try {
            console.log(`Starting monitoring for transaction ${transactionId}`);

            // Get transaction with payment method
            const transaction: any =await prisma.transaction.findUnique({
                where: {, id: transactionId },
                include: {, paymentMethod: true
                }
            });

            if (!transaction) {
                console.log(`Transaction ${transactionId} not found`);
                return;
            }

            if (!transaction.paymentMethod) {
                console.log(`No payment method found for transaction ${transactionId}`);
                return;
            }

            // Set up monitoring based on payment method type
            switch (transaction.paymentMethod.type) {
                case "binance_trc20":
                    this.monitorBinanceTrc20Transaction(transaction);
                    break;
                case "binance_pay":
                    // Binance Pay uses webhooks, no active monitoring needed
                    console.log(`Binance Pay transaction ${transaction.id} will be verified via webhook`);
                    break;
                case "binance_c2c":
                    this.monitorBinanceC2CTransaction(transaction);
                    break;
                case "crypto_transfer":
                    this.monitorCryptoTransferTransaction(transaction);
                    break;
                default:
                    console.log(`Unknown payment method type: ${transaction.paymentMethod.type}`);
            }

            // Set up expiration monitoring
            this.setupExpirationMonitoring(transaction);
        } catch (error) {
            console.error(`Error starting monitoring for transaction ${transactionId}:`, error);
        }
    }

    /**
     * Monitor Binance TRC20 transaction
     * @param transaction Transaction to monitor
     */
    private monitorBinanceTrc20Transaction(transaction: Transaction & { paymentMethod: PaymentMethod }): void {
        const interval = setInterval(async () => {
            try {
                const walletAddress = transaction.paymentMethod.config?.walletAddress as string;
                const amount: any =transaction.amount;
                const currency: any =transaction.currency;

                // Get API credentials from payment method config
                const apiKey: any =transaction.paymentMethod.config?.apiKey as string;
                const apiSecret: any =transaction.paymentMethod.config?.apiSecret as string;

                if (!apiKey || !apiSecret) {
                    console.error(`Missing API credentials for transaction ${transaction.id}`);
                    return;
                }

                // Check for deposits
                const deposits: any =await BinanceService.getDepositHistory(
                    apiKey,
                    apiSecret,
                    currency,
                    1, // Status: 1 = Completed
                    Date.now() - 30 * 60 * 1000, // Last 30 minutes
                    Date.now()
                );

                // Find matching deposit
                const matchingDeposit: any =deposits.find((deposit) =>
                    parseFloat(deposit.amount) === amount &&
                    deposit.address === walletAddress
                );

                if (matchingDeposit) {
                    console.log(`Found matching deposit for transaction ${transaction.id}`);

                    // Update transaction status
                    await TransactionService.updateTransactionStatus(transaction.id, "COMPLETED", {
                        verificationResult: matchingDeposit,
                        metadata: {, verifiedAt: new Date().toISOString(),
                            verificationMethod: "binance_trc20",
                            automatic: true
                        }
                    });

                    // Stop monitoring
                    this.stopMonitoringTransaction(transaction.id);
                }
            } catch (error) {
                console.error(`Error monitoring Binance TRC20 transaction ${transaction.id}:`, error);
            }
        }, 60000); // Check every minute

        this.monitoringIntervals.set(transaction.id, interval);
    }

    /**
     * Monitor Binance C2C transaction
     * @param transaction Transaction to monitor
     */
    private monitorBinanceC2CTransaction(transaction: Transaction & { paymentMethod: PaymentMethod }): void {
        const interval = setInterval(async () => {
            try {
                const notePrefix = transaction.paymentMethod.config?.notePrefix as string || "";
                const noteValue: any =transaction.reference?.substring(transaction.reference.length - 6) || "";
                const note: any =`${notePrefix}${noteValue}`;
                const amount: any =transaction.amount;
                const currency: any =transaction.currency;

                // Get API credentials from payment method config
                const apiKey: any =transaction.paymentMethod.config?.apiKey as string;
                const apiSecret: any =transaction.paymentMethod.config?.apiSecret as string;

                if (!apiKey || !apiSecret) {
                    console.error(`Missing API credentials for transaction ${transaction.id}`);
                    return;
                }

                // Check for C2C transactions with matching note
                const result: any =await BinanceService.verifyBinanceC2CTransaction(
                    apiKey,
                    apiSecret,
                    note,
                    amount,
                    currency
                );

                if (result.verified) {
                    console.log(`Found matching C2C transaction for transaction ${transaction.id}`);

                    // Update transaction status
                    await TransactionService.updateTransactionStatus(transaction.id, "COMPLETED", {
                        verificationResult: result.transaction,
                        metadata: {, verifiedAt: new Date().toISOString(),
                            verificationMethod: "binance_c2c",
                            automatic: true
                        }
                    });

                    // Stop monitoring
                    this.stopMonitoringTransaction(transaction.id);
                }
            } catch (error) {
                console.error(`Error monitoring Binance C2C transaction ${transaction.id}:`, error);
            }
        }, 60000); // Check every minute

        this.monitoringIntervals.set(transaction.id, interval);
    }

    /**
     * Monitor Crypto Transfer transaction
     * @param transaction Transaction to monitor
     */
    private monitorCryptoTransferTransaction(transaction: Transaction & { paymentMethod: PaymentMethod }): void {
        const interval = setInterval(async () => {
            try {
                console.log(`Checking Crypto Transfer transaction ${transaction.id}`);

                // Get wallet address and expected amount
                const walletAddresses: any =transaction.paymentMethod.config?.walletAddresses as Record<string, string> || {};
                const walletAddress: any =walletAddresses[transaction.currency] ||
                                     transaction.paymentMethod.config?.walletAddress as string;
                const amount: any =transaction.amount;
                const currency: any =transaction.currency;
                const network: any =transaction.paymentMethod.config?.network as string || "TRC20";

                if (!walletAddress) {
                    console.error(`Missing wallet address for transaction ${transaction.id}`);
                    return;
                }

                // In a real implementation, this would check the blockchain for transactions
                // For demo purposes, we'll simulate a successful verification randomly
                if (Math.random() > 0.7) { // 30% chance of success on each check
                    console.log(`Simulating successful verification for transaction ${transaction.id}`);

                    // Update transaction status
                    await TransactionService.updateTransactionStatus(transaction.id, "COMPLETED", {
                        verificationResult: {, txHash: `0x${Math.random().toString(36).substring(2, 15)}`,
                            amount,
                            currency,
                            network,
                            timestamp: new Date().toISOString()
                        },
                        metadata: {, verifiedAt: new Date().toISOString(),
                            verificationMethod: "blockchain",
                            automatic: true
                        }
                    });

                    // Stop monitoring
                    this.stopMonitoringTransaction(transaction.id);
                }
            } catch (error) {
                console.error(`Error monitoring Crypto Transfer transaction ${transaction.id}:`, error);
            }
        }, 60000); // Check every minute

        this.monitoringIntervals.set(transaction.id, interval);
    }

    /**
     * Set up expiration monitoring
     * @param transaction Transaction to monitor
     */
    private setupExpirationMonitoring(transaction: Transaction): void {
        if (!transaction.expiresAt) {
            return;
        }

        const now: Date =new Date();
        const expiresAt: Date =new Date(transaction.expiresAt);
        const timeUntilExpiration: any =expiresAt.getTime() - now.getTime();

        if (timeUntilExpiration <= 0) {
            // Transaction already expired
            this.expireTransaction(transaction.id);
            return;
        }

        // Set timeout to expire transaction
        const timeout: any =setTimeout(() => {
            this.expireTransaction(transaction.id);
        }, timeUntilExpiration);

        // Store timeout
        this.monitoringIntervals.set(`${transaction.id}_expiration`, timeout);
    }

    /**
     * Expire transaction
     * @param transactionId Transaction ID
     */
    private async expireTransaction(transactionId: string): Promise<void> {
        try {
            console.log(`Expiring transaction ${transactionId}`);

            // Update transaction status
            await TransactionService.updateTransactionStatus(transactionId, "EXPIRED", {
                metadata: {, expiredAt: new Date().toISOString(),
                    automatic: true
                }
            });

            // Stop monitoring
            this.stopMonitoringTransaction(transactionId);
        } catch (error) {
            console.error(`Error expiring transaction ${transactionId}:`, error);
        }
    }

    /**
     * Stop monitoring transaction
     * @param transactionId Transaction ID
     */
    public stopMonitoringTransaction(transactionId: string): void {
        // Clear main interval
        if (this.monitoringIntervals.has(transactionId)) {
            clearInterval(this.monitoringIntervals.get(transactionId)!);
            this.monitoringIntervals.delete(transactionId);
        }

        // Clear expiration timeout
        if (this.monitoringIntervals.has(`${transactionId}_expiration`)) {
            clearTimeout(this.monitoringIntervals.get(`${transactionId}_expiration`)!);
            this.monitoringIntervals.delete(`${transactionId}_expiration`);
        }

        console.log(`Stopped monitoring transaction ${transactionId}`);
    }

    /**
     * Shutdown monitoring service
     */
    public shutdown(): void {
        console.log("Shutting down transaction monitoring service");

        // Clear all intervals and timeouts
        for (const [key, interval] of this.monitoringIntervals.entries()) {
            if (key.includes('_expiration')) {
                clearTimeout(interval);
            } else {
                clearInterval(interval);
            }
            console.log(`Cleared interval for ${key}`);
        }

        this.monitoringIntervals.clear();
        this.isInitialized = false;

        console.log("Transaction monitoring service shut down");
    }

    /**
     * Monitor a transaction
     * @param transactionId Transaction ID
     * @returns Transaction
     */
    public async monitorTransaction(transactionId: string): Promise<Transaction | null> {
        try {
            const transaction: any =await prisma.transaction.findUnique({
                where: {, id: transactionId }
            });

            if (!transaction) {
                throw new Error(`Transaction not found: ${transactionId}`);
            }

            // Start monitoring
            await this.startMonitoring(transactionId);

            return transaction;
        } catch (error) {
            console.error('Error monitoring transaction:', error);
            throw error;
        }
    }
}

// Export singleton instance
export const transactionMonitorService: any =TransactionMonitorService.getInstance();