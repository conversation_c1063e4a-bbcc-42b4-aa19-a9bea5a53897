/**
 * System Initializer
 *
 * Service for initializing the system with predefined components.
 */
import { PrismaClient } from "@prisma/client";
/**
 * System initializer service
 */
export declare class SystemInitializer {
    private prisma;
    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient);
    /**
   * Initialize the system
   */
    initialize(): Promise<void>;
    /**
   * Initialize operational mode service
   */
    private initializeOperationalMode;
    /**
   * Register core services in DI container
   */
    private registerServices;
    /**
   * Register modules in module registry
   */
    private registerModules;
    /**
   * Initialize RBAC system
   */
    private initializeRBAC;
    /**
   * Initialize verification system
   */
    private initializeVerification;
    /**
   * Initialize payment system
   */
    private initializePayment;
    /**
   * Set up event listeners
   */
    private setupEventListeners;
}
//# sourceMappingURL=SystemInitializer.d.ts.map