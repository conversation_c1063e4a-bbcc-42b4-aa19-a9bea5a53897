"use strict";
// jscpd:ignore-file
/**
 * Verification Optimization Routes
 *
 * This file defines routes for the verification optimization API.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const verification_optimization_controller_1 = require("../controllers/optimization/verification-optimization.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
const verificationOptimizationController = new verification_optimization_controller_1.VerificationOptimizationController();
// All routes require authentication and admin authorization
router.use(auth_middleware_1.authenticate);
router.use((0, auth_middleware_1.authorize)(["admin"]));
/**
 * @route GET /api/optimization/verification/performance
 * @desc Analyze verification performance
 * @access Private (Admin)
 */
router.get("/performance", verificationOptimizationController.analyzePerformance.bind(verificationOptimizationController));
/**
 * @route GET /api/optimization/verification/recommendations
 * @desc Generate optimization recommendations
 * @access Private (Admin)
 */
router.get("/recommendations", verificationOptimizationController.generateRecommendations.bind(verificationOptimizationController));
/**
 * @route POST /api/optimization/verification/apply
 * @desc Apply optimization recommendations
 * @access Private (Admin)
 */
router.post("/apply", verificationOptimizationController.applyRecommendations.bind(verificationOptimizationController));
exports.default = router;
//# sourceMappingURL=verification-optimization.routes.js.map