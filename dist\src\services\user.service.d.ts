import { User, <PERSON>rism<PERSON> } from "@prisma/client";
import { GenericService } from "../../core/GenericService";
/**
 * User service
 * This service handles business logic for users
 */
export declare class UserService extends GenericService<User, Prisma.UserCreateInput, Prisma.UserUpdateInput> {
    private userRepository;
    /**
     * Create a new user service
     */
    constructor();
    /**
     * Get users with pagination
     * @param options Query options
     * @returns Paginated users
     */
    getUsers(options: {
        limit?: number;
        offset?: number;
        search?: string;
    }): Promise<{
        data: User[];
        total: number;
    }>;
    /**
     * Get a user by ID
     * @param id User ID
     * @returns User or null
     */
    getUserById(id: string): Promise<User | null>;
    /**
     * Get a user by email
     * @param email User email
     * @returns User or null
     */
    getUserByEmail(email: string): Promise<User | null>;
    /**
     * Create a new user
     * @param data User data
     * @returns Created user
     */
    createUser(data: {
        email: string;
        hashedPassword: string;
        name: string;
        role?: string;
        merchantId?: string;
    }): Promise<User>;
    /**
     * Update a user
     * @param id User ID
     * @param data User data
     * @returns Updated user
     */
    updateUser(id: string, data: Prisma.UserUpdateInput): Promise<User>;
    /**
     * Delete a user
     * @param id User ID
     * @returns Deleted user
     */
    deleteUser(id: string): Promise<User>;
    /**
     * Validate user credentials
     * @param email User email
     * @param password User password
     * @returns User if credentials are valid, null otherwise
     */
    validateCredentials(email: string, password: string): Promise<User | null>;
    /**
     * Change user password
     * @param id User ID
     * @param currentPassword Current password
     * @param newPassword New password
     * @returns Updated user
     */
    changePassword(id: string, currentPassword: string, newPassword: string): Promise<User>;
}
//# sourceMappingURL=user.service.d.ts.map