"use strict";
// jscpd:ignore-file
/**
 * Two-Factor Authentication Service
 *
 * Provides functionality for managing two-factor authentication
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const otplib_1 = require("otplib");
const qrcode_1 = __importDefault(require("qrcode"));
const crypto_1 = __importDefault(require("crypto"));
const logger_1 = require("../utils/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
const error_middleware_1 = require("../middlewares/error.middleware");
// Number of backup codes to generate
const BACKUP_CODES_COUNT = 10;
// Length of each backup code segment
const BACKUP_CODE_SEGMENT_LENGTH = 4;
// Number of segments in a backup code
const BACKUP_CODE_SEGMENTS = 2;
// Separator for backup code segments
const BACKUP_CODE_SEPARATOR = '-';
/**
 * Two-Factor Authentication Service
 */
class TwoFactorAuthService {
    constructor(prismaClient = prisma_1.default) {
        this.prisma = prismaClient;
    }
    /**
     * Get 2FA status for a user
     * @param userId User ID
     * @returns 2FA status
     */
    async getTwoFactorStatus(userId) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { twoFactorEnabled: true },
            });
            if (!user) {
                throw (0, error_middleware_1.createNotFoundError)('User not found');
            }
            return { enabled: user.twoFactorEnabled };
        }
        catch (error) {
            logger_1.logger.error('Error getting 2FA status:', error);
            throw error;
        }
    }
    /**
     * Setup 2FA for a user
     * @param userId User ID
     * @returns Setup data including secret, QR code URL, and backup codes
     */
    async setupTwoFactor(userId) {
        try {
            // Get user
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, email: true, twoFactorEnabled: true },
            });
            if (!user) {
                throw (0, error_middleware_1.createNotFoundError)('User not found');
            }
            // Check if 2FA is already enabled
            if (user.twoFactorEnabled) {
                throw (0, error_middleware_1.createBadRequestError)('Two-factor authentication is already enabled');
            }
            // Generate secret
            const secret = otplib_1.authenticator.generateSecret();
            // Generate QR code
            const serviceName = 'AmazingPay';
            const otpauth = otplib_1.authenticator.keyuri(user.email, serviceName, secret);
            const qrCodeUrl = await qrcode_1.default.toDataURL(otpauth);
            // Generate backup codes
            const backupCodes = this.generateBackupCodes();
            // Hash backup codes
            const hashedBackupCodes = backupCodes.map((code) => this.hashBackupCode(code));
            // Store secret and hashed backup codes in database
            await this.prisma.user.update({
                where: { id: userId },
                data: {
                    twoFactorSecret: secret,
                    // Store hashed backup codes in metadata or a separate table in a real implementation
                },
            });
            return {
                secret,
                qrCodeUrl,
                backupCodes,
            };
        }
        catch (error) {
            logger_1.logger.error('Error setting up 2FA:', error);
            throw error;
        }
    }
    /**
     * Verify and enable 2FA for a user
     * @param userId User ID
     * @param token Token from authenticator app
     * @param secret Secret key
     * @returns Success status
     */
    async verifyAndEnableTwoFactor(userId, token, secret) {
        try {
            // Get user
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },
            });
            if (!user) {
                throw (0, error_middleware_1.createNotFoundError)('User not found');
            }
            if (user.twoFactorEnabled) {
                throw (0, error_middleware_1.createBadRequestError)('Two-factor authentication is already enabled');
            }
            // Verify that the provided secret matches the stored secret
            if (user.twoFactorSecret !== secret) {
                throw (0, error_middleware_1.createBadRequestError)('Invalid secret');
            }
            // Verify token
            const isValid = otplib_1.authenticator.verify({ token, secret });
            if (!isValid) {
                throw (0, error_middleware_1.createBadRequestError)('Invalid verification code');
            }
            // Enable 2FA
            await this.prisma.user.update({
                where: { id: userId },
                data: { twoFactorEnabled: true },
            });
            return { success: true };
        }
        catch (error) {
            logger_1.logger.error('Error verifying and enabling 2FA:', error);
            throw error;
        }
    }
    /**
     * Verify a 2FA token
     * @param userId User ID
     * @param token Token from authenticator app
     * @returns Success status
     */
    async verifyTwoFactorToken(userId, token) {
        try {
            // Get user
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },
            });
            if (!user) {
                throw (0, error_middleware_1.createNotFoundError)('User not found');
            }
            if (!user.twoFactorEnabled || !user.twoFactorSecret) {
                throw (0, error_middleware_1.createBadRequestError)('Two-factor authentication is not enabled');
            }
            // Verify token
            const isValid = otplib_1.authenticator.verify({ token, secret: user.twoFactorSecret });
            if (!isValid) {
                throw (0, error_middleware_1.createBadRequestError)('Invalid verification code');
            }
            return { success: true };
        }
        catch (error) {
            logger_1.logger.error('Error verifying 2FA token:', error);
            throw error;
        }
    }
    /**
     * Disable 2FA for a user
     * @param userId User ID
     * @returns Success status
     */
    async disableTwoFactor(userId) {
        try {
            // Get user
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, twoFactorEnabled: true },
            });
            if (!user) {
                throw (0, error_middleware_1.createNotFoundError)('User not found');
            }
            if (!user.twoFactorEnabled) {
                throw (0, error_middleware_1.createBadRequestError)('Two-factor authentication is not enabled');
            }
            // Disable 2FA
            await this.prisma.user.update({
                where: { id: userId },
                data: {
                    twoFactorEnabled: false,
                    twoFactorSecret: null,
                },
            });
            return { success: true };
        }
        catch (error) {
            logger_1.logger.error('Error disabling 2FA:', error);
            throw error;
        }
    }
    /**
     * Generate backup codes
     * @returns Array of backup codes
     */
    generateBackupCodes() {
        const backupCodes = [];
        for (let i = 0; i < BACKUP_CODES_COUNT; i++) {
            const segments = [];
            for (let j = 0; j < BACKUP_CODE_SEGMENTS; j++) {
                const segment = crypto_1.default.randomBytes(BACKUP_CODE_SEGMENT_LENGTH / 2).toString('hex');
                segments.push(segment);
            }
            backupCodes.push(segments.join(BACKUP_CODE_SEPARATOR));
        }
        return backupCodes;
    }
    /**
     * Hash a backup code
     * @param code Backup code
     * @returns Hashed backup code
     */
    hashBackupCode(code) {
        return crypto_1.default.createHash('sha256').update(code).digest('hex');
    }
}
exports.default = new TwoFactorAuthService();
