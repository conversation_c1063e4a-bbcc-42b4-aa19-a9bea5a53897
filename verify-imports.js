/**
 * Import Verification Script
 * 
 * Verifies that all imports have been updated to use the new modular structure.
 */

const fs = require('fs');
const path = require('path');

// Old import patterns to check for
const oldImportPatterns = [
  /from\s+['"]\.\.?\/.*\/identity-verification\.service['"]/g,
  /from\s+['"]\.\.?\/.*\/advanced-report\.service['"]/g,
  /from\s+['"]\.\.?\/.*\/fraud-detection\.service['"]/g,
  /from\s+['"]\.\.?\/.*\/admin\.controller['"]/g,
  /from\s+['"]\.\.?\/.*\/alert-aggregation\.controller['"]/g,
  /from\s+['"]\.\.?\/.*\/identity-verification\.controller['"]/g,
  /from\s+['"]\.\.?\/.*\/fraud-detection\.controller['"]/g,
  /from\s+['"]\.\.?\/.*\/TestUtility['"]/g,
];

// Expected new import patterns
const newImportPatterns = [
  /from\s+['"]\.\.?\/.*\/identity-verification['"]/g,
  /from\s+['"]\.\.?\/.*\/reporting['"]/g,
  /from\s+['"]\.\.?\/.*\/fraud-detection['"]/g,
  /from\s+['"]\.\.?\/.*\/admin['"]/g,
  /from\s+['"]\.\.?\/.*\/alert-aggregation['"]/g,
  /from\s+['"]\.\.?\/.*\/test-utilities['"]/g,
];

/**
 * Check for old imports in a file
 */
function checkFileForOldImports(filePath) {
  if (!fs.existsSync(filePath)) {
    return { hasOldImports: false, oldImports: [] };
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const oldImports = [];

  oldImportPatterns.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach(match => {
        oldImports.push({
          pattern: match,
          line: content.substring(0, content.indexOf(match)).split('\n').length
        });
      });
    }
  });

  return {
    hasOldImports: oldImports.length > 0,
    oldImports
  };
}

/**
 * Check for new imports in a file
 */
function checkFileForNewImports(filePath) {
  if (!fs.existsSync(filePath)) {
    return { hasNewImports: false, newImports: [] };
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const newImports = [];

  newImportPatterns.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach(match => {
        newImports.push({
          pattern: match,
          line: content.substring(0, content.indexOf(match)).split('\n').length
        });
      });
    }
  });

  return {
    hasNewImports: newImports.length > 0,
    newImports
  };
}

/**
 * Recursively find all TypeScript files
 */
function findTypeScriptFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules, dist, and .git directories
      if (!['node_modules', 'dist', '.git', '.next'].includes(item)) {
        findTypeScriptFiles(fullPath, files);
      }
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Main verification function
 */
function main() {
  console.log('🔍 Starting import verification...\n');
  
  const projectRoot = process.cwd();
  const tsFiles = findTypeScriptFiles(projectRoot);
  
  console.log(`📁 Found ${tsFiles.length} TypeScript files\n`);
  
  let filesWithOldImports = 0;
  let filesWithNewImports = 0;
  let totalOldImports = 0;
  let totalNewImports = 0;
  
  const problemFiles = [];
  const successFiles = [];
  
  tsFiles.forEach(filePath => {
    const relativePath = path.relative(projectRoot, filePath);
    
    // Skip our new modular files
    if (relativePath.includes('/identity-verification/') ||
        relativePath.includes('/reporting/') ||
        relativePath.includes('/fraud-detection/') ||
        relativePath.includes('/admin/') ||
        relativePath.includes('/alert-aggregation/') ||
        relativePath.includes('/test-utilities/')) {
      return;
    }
    
    const oldImportCheck = checkFileForOldImports(filePath);
    const newImportCheck = checkFileForNewImports(filePath);
    
    if (oldImportCheck.hasOldImports) {
      filesWithOldImports++;
      totalOldImports += oldImportCheck.oldImports.length;
      problemFiles.push({
        file: relativePath,
        oldImports: oldImportCheck.oldImports
      });
    }
    
    if (newImportCheck.hasNewImports) {
      filesWithNewImports++;
      totalNewImports += newImportCheck.newImports.length;
      successFiles.push({
        file: relativePath,
        newImports: newImportCheck.newImports
      });
    }
  });
  
  // Report results
  console.log('📊 IMPORT VERIFICATION RESULTS\n');
  
  console.log(`✅ Files with new modular imports: ${filesWithNewImports}`);
  console.log(`❌ Files with old imports: ${filesWithOldImports}`);
  console.log(`📈 Total new imports found: ${totalNewImports}`);
  console.log(`📉 Total old imports found: ${totalOldImports}\n`);
  
  if (problemFiles.length > 0) {
    console.log('🚨 FILES WITH OLD IMPORTS THAT NEED UPDATING:\n');
    problemFiles.forEach(({ file, oldImports }) => {
      console.log(`❌ ${file}`);
      oldImports.forEach(({ pattern, line }) => {
        console.log(`   Line ${line}: ${pattern}`);
      });
      console.log('');
    });
  }
  
  if (successFiles.length > 0) {
    console.log('✅ FILES WITH NEW MODULAR IMPORTS:\n');
    successFiles.slice(0, 10).forEach(({ file, newImports }) => {
      console.log(`✅ ${file}`);
      newImports.slice(0, 3).forEach(({ pattern, line }) => {
        console.log(`   Line ${line}: ${pattern}`);
      });
      console.log('');
    });
    
    if (successFiles.length > 10) {
      console.log(`   ... and ${successFiles.length - 10} more files\n`);
    }
  }
  
  // Summary
  if (filesWithOldImports === 0) {
    console.log('🎉 SUCCESS! All imports have been updated to use the new modular structure!');
    console.log('✅ No old import patterns found');
    console.log(`✅ Found ${totalNewImports} new modular imports across ${filesWithNewImports} files`);
  } else {
    console.log('⚠️  IMPORT UPDATE INCOMPLETE');
    console.log(`❌ ${filesWithOldImports} files still have old import patterns`);
    console.log(`❌ ${totalOldImports} old imports need to be updated`);
    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Update the files listed above');
    console.log('2. Run this verification script again');
    console.log('3. Test the application to ensure functionality is preserved');
  }
  
  console.log('\n📋 IMPORT MIGRATION STATUS:');
  const migrationProgress = filesWithNewImports / (filesWithNewImports + filesWithOldImports) * 100;
  console.log(`Progress: ${migrationProgress.toFixed(1)}% complete`);
  
  return {
    success: filesWithOldImports === 0,
    filesWithOldImports,
    filesWithNewImports,
    totalOldImports,
    totalNewImports,
    problemFiles,
    successFiles
  };
}

// Run the script
if (require.main === module) {
  const result = main();
  process.exit(result.success ? 0 : 1);
}

module.exports = { checkFileForOldImports, checkFileForNewImports, findTypeScriptFiles };
