/**
 * Crypto Utilities
 * This class provides utility methods for cryptographic operations
 */
export declare class CryptoUtils {
    /**
     * Generate a random string
     * @param length Length of the string
     * @param encoding Encoding of the string
     * @returns Random string
     */
    static generateRandomString(length?: number, encoding?: BufferEncoding): Promise<string>;
    /**
     * Generate a random string synchronously
     * @param length Length of the string
     * @param encoding Encoding of the string
     * @returns Random string
     */
    static generateRandomStringSync(length?: number, encoding?: BufferEncoding): string;
    /**
     * Generate a UUID v4
     * @returns UUID v4
     */
    static generateUuid(): string;
    /**
     * Hash a password
     * @param password Password to hash
     * @param salt Salt to use (optional, will be generated if not provided)
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns Object containing the hash and salt
     */
    static hashPassword(password: string, salt?: string, iterations?: number, keylen?: number, digest?: string): Promise<{
        hash: string;
        salt: string;
    }>;
    /**
     * Hash a password synchronously
     * @param password Password to hash
     * @param salt Salt to use (optional, will be generated if not provided)
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns Object containing the hash and salt
     */
    static hashPasswordSync(password: string, salt?: string, iterations?: number, keylen?: number, digest?: string): {
        hash: string;
        salt: string;
    };
    /**
     * Verify a password
     * @param password Password to verify
     * @param hash Hash to compare against
     * @param salt Salt used to hash the password
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns True if the password is valid
     */
    static verifyPassword(password: string, hash: string, salt: string, iterations?: number, keylen?: number, digest?: string): Promise<boolean>;
    /**
     * Verify a password synchronously
     * @param password Password to verify
     * @param hash Hash to compare against
     * @param salt Salt used to hash the password
     * @param iterations Number of iterations (default: 10000)
     * @param keylen Key length (default: 64)
     * @param digest Digest algorithm (default: sha512)
     * @returns True if the password is valid
     */
    static verifyPasswordSync(password: string, hash: string, salt: string, iterations?: number, keylen?: number, digest?: string): boolean;
    /**
     * Encrypt data
     * @param data Data to encrypt
     * @param key Encryption key
     * @param algorithm Encryption algorithm (default: aes-256-cbc)
     * @returns Encrypted data
     */
    static encrypt(data: string, key: string, algorithm?: string): {
        encrypted: string;
        iv: string;
    };
    /**
     * Decrypt data
     * @param encrypted Encrypted data
     * @param key Encryption key
     * @param iv Initialization vector
     * @param algorithm Encryption algorithm (default: aes-256-cbc)
     * @returns Decrypted data
     */
    static decrypt(encrypted: string, key: string, iv: string, algorithm?: string): string;
    /**
     * Generate a hash
     * @param data Data to hash
     * @param algorithm Hash algorithm (default: sha256)
     * @returns Hash
     */
    static hash(data: string, algorithm?: string): string;
    /**
     * Generate an HMAC
     * @param data Data to sign
     * @param key Key to use
     * @param algorithm Hash algorithm (default: sha256)
     * @returns HMAC
     */
    static hmac(data: string, key: string, algorithm?: string): string;
    /**
     * Generate a secure token
     * @param length Token length (default: 32)
     * @returns Secure token
     */
    static generateToken(length?: number): Promise<string>;
    /**
     * Generate a secure token synchronously
     * @param length Token length (default: 32)
     * @returns Secure token
     */
    static generateTokenSync(length?: number): string;
    /**
     * Generate a secure API key
     * @param prefix API key prefix
     * @param length API key length (default: 32)
     * @returns Secure API key
     */
    static generateApiKey(prefix: string, length?: number): Promise<string>;
    /**
     * Generate a secure API key synchronously
     * @param prefix API key prefix
     * @param length API key length (default: 32)
     * @returns Secure API key
     */
    static generateApiKeySync(prefix: string, length?: number): string;
    /**
     * Compare two strings in constant time
     * @param a First string
     * @param b Second string
     * @returns True if the strings are equal
     */
    static constantTimeCompare(a: string, b: string): boolean;
}
//# sourceMappingURL=CryptoUtils.d.ts.map