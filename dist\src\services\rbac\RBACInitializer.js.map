{"version": 3, "file": "RBACInitializer.js", "sourceRoot": "", "sources": ["../../../../src/services/rbac/RBACInitializer.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAGH,mEAA+E;AAC/E,yEAAqE;AACrE,6CAA0C;AAQ1C;;GAEG;AACH,MAAa,eAAe;IAGxB;;;;KAIC;IACD,YAAY,MAAoB;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;KAEC;IACM,KAAK,CAAC,UAAU;QACnB,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,IAAI,CAAC;YACD,qDAAqD;YACrD,MAAM,aAAa,GAAQ,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE7D,IAAI,aAAa,EAAE,CAAC;gBAChB,yBAAyB;gBACzB,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAEnC,mBAAmB;gBACnB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAE7B,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,eAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;gBAChF,eAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;YACzF,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IAED;;KAEC;IACO,KAAK,CAAC,oBAAoB;QAC9B,IAAI,CAAC;YACD,0DAA0D;YAC1D,MAAM,kBAAkB,GAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACzD,MAAM,YAAY,GAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAClD,MAAM,sBAAsB,GAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjE,MAAM,gBAAgB,GAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YAErD,MAAM,cAAc,GAAQ,kBAAkB,IAAI,YAAY,IAAI,sBAAsB,IAAI,gBAAgB,CAAC;YAE7G,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBACpE,IAAI,CAAC,kBAAkB;oBAAE,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBACtE,IAAI,CAAC,YAAY;oBAAE,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC/D,IAAI,CAAC,sBAAsB;oBAAE,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAC9E,IAAI,CAAC,gBAAgB;oBAAE,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACtE,CAAC;YAED,OAAO,cAAc,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;KAEC;IACO,KAAK,CAAC,qBAAqB;QAC/B,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,IAAI,CAAC;YACD,6CAA6C;YAC7C,MAAM,cAAc,GAAQ,kCAAe,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAA;YAAI,CAAC;gBAC9D,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAEjD,OAAO;oBACH,QAAQ;oBACR,MAAM;oBACN,WAAW,EAAE,GAAG,MAAM,IAAI,QAAQ,EAAE;iBACvC,CAAC;YACN,CAAC;YAAC,CAAC;YAEH,uCAAuC;YACvC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACzB,iCAAiC;gBACjC,KAAK,MAAM,UAAU,IAAI,cAAc,EAAE,CAAC;oBACtC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;wBAChC,KAAK,EAAE,EAAE,eAAe,EAAE;gCAClB,QAAQ,EAAE,UAAU,CAAC,QAAQ;gCAC7B,MAAM,EAAE,UAAU,CAAC,MAAM;6BAC5B;yBACJ;wBACD,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,CAAC,WAAW;yBAC5C;wBACD,MAAM,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ;4BACnC,MAAM,EAAE,UAAU,CAAC,MAAM;4BACzB,WAAW,EAAE,UAAU,CAAC,WAAW;yBACtC;qBACJ,CAAC,CAAC;gBACP,CAAC;gBAED,eAAM,CAAC,IAAI,CAAC,eAAe,cAAc,CAAC,MAAM,cAAc,CAAC,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACJ,eAAM,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;YACpG,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;IAED;;KAEC;IACO,KAAK,CAAC,eAAe;QACzB,eAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAElC,IAAI,CAAC;YACD,iCAAiC;YACjC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACxB,2BAA2B;gBAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,8BAAc,CAAC,EAAE,CAAC;oBAC3D,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBAC5C,CAAC;gBAED,eAAM,CAAC,IAAI,CAAC,eAAe,MAAM,CAAC,IAAI,CAAC,8BAAc,CAAC,CAAC,MAAM,QAAQ,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACJ,eAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;YACvF,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;;;KAIC;IACO,KAAK,CAAC,kBAAkB,CAAC,QAAsB;QACnD,IAAI,CAAC;YACD,iCAAiC;YACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACzB,eAAM,CAAC,IAAI,CAAC,oEAAoE,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;gBAClG,OAAO;YACX,CAAC;YAED,sBAAsB;YACtB,MAAM,IAAI,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBACjD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI;iBAC3B;gBACD,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACzB,WAAW,EAAE,QAAQ,CAAC,WAAW;oBACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK;iBACvC;gBACD,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;oBACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK;iBACvC;aACJ,CAAC,CAAC;YAEH,uCAAuC;YACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,wFAAwF,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;gBACtH,OAAO;YACX,CAAC;YAED,kBAAkB;YAClB,MAAM,WAAW,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,EAAA;aAAA,CAAA,CAAA;YAAI,CAAC;gBAChD,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACjD,OAAO;oBACH,QAAQ;oBACR,MAAM;iBACT,CAAC;YACN,CAAC;QACL,CAAC;gBAGL,2CAA2C;SAF3C,CAAC;QAAD,CAAC,AADI;IACL,CAAC;IAAC,CAAC;IAEH,2CAA2C;IAC3C,EAAE,CAAE,EAAM,MAAM,EAAC,cAAc;QAC3B,eAAM,CAAC,IAAI,CAAC,4FAA4F,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;QAC1H,OAAO;IACX,CAAC;CAAA;AA7Lb,0CA6La;AAED,kCAAkC;AAClC,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;IACxC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;KACvB;CACJ,CAAC,CAAC;AAEH,0BAA0B;AAC1B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;IACnC,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;QACpC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;YACnB,YAAY,EAAE,UAAU,CAAC,EAAE;SAC9B;KACJ,CAAC,CAAC;AACP,CAAC;AAED,eAAM,CAAC,IAAI,CAAC,yBAAyB,QAAQ,CAAC,IAAI,SAAS,WAAW,CAAC,MAAM,cAAc,CAAC,CAAC;AAC/F,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACb,eAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;IACtE,kDAAkD;AACtD,CAAC;AAUE,KAAK,CAAA;AAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;AAAE,OAAO,GAAC,KAAI,GAAE;IACxF,GAAG,EAAC;QACA,MAAM,EAAN,eAAM,EAAA,EAAA,CAAC,IAAI,CAAC,8BAA8B,KAAK,EAAE,CAAC;QAGzC,EAAA,CAAC,MAAM,CAAC,SAAS;KAAC;CAAA,CAAA;AAAC,CAAC;IACzB,eAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;IACpF,OAAO;AACX,CAAC;AAED,wBAAwB;AACxB,MAAM,cAAc,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;IAC/D,KAAK,EAAE,EAAE,IAAI,EAAE,aAAa;KAC3B;CACJ,CAAC,CAAC;AAEH,IAAI,CAAC,cAAc,EAAE,CAAC;IAClB,eAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;IAC1E,OAAO;AACX,CAAC;AAED,iCAAiC;AACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACpB,eAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;IACrF,OAAO;AACX,CAAC;AAED,cAAc;AACd,MAAM,IAAI,GAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;IAC5C,KAAK,EAAE;QACH,KAAK;KACR;IACD,MAAM,EAAE;QACJ,IAAI;QACJ,QAAQ,EAAE,IAAI;KACjB;IACD,MAAM,EAAE;QACJ,KAAK;QACL,cAAc,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QACjD,IAAI;QACJ,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;KACjB;CACJ,CAAC,CAAC;AAEH,qCAAqC;AACrC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;IACxB,eAAM,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;IAChG,OAAO;AACX,CAAC;AAED,0BAA0B;AAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC9B,KAAK,EAAE,EAAE,aAAa,EAAE;YAChB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,MAAM,EAAE,cAAc,CAAC,EAAE;SAC5B;KACJ;IACD,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;QACrB,MAAM,EAAE,cAAc,CAAC,EAAE;KAC5B;CACJ,CAAC,CAAC;AAEH,eAAM,CAAC,IAAI,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;AACpD,IAAA,CAAC,CAAD,CAAC,AAAF;AAAC,OAAO,KAAK,EAAE,CAAC;IACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;IACxD,kDAAkD;AACtD,CAAC;AASG,KAAK,CAAA;AAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;AAAE,OAAO,GAAC,MAAM,GAAE;IAC9D,6DAA6D;IAC7D,0CAA0C;IACtC,MAAM,EAAC,QAAQ;CAClB,CAAA"}