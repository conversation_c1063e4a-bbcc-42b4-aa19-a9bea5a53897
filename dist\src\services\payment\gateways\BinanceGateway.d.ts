/**
 * Binance Gateway
 *
 * Implements the payment gateway for Binance.
 */
import { IPaymentGateway, GatewayPaymentRequest, GatewayPaymentResponse, GatewayRefundRequest, GatewayRefundResponse } from '../../../interfaces/payment/IPaymentGateway';
import { PaymentMethodType } from '../../../types/payment-method.types';
/**
 * Binance gateway
 */
export declare class BinanceGateway implements IPaymentGateway {
    private enabled;
    private configuration;
    /**
     * Get the gateway name
     */
    getName(): string;
    /**
     * Get the supported payment method types
     */
    getSupportedPaymentMethods(): PaymentMethodType[];
    /**
     * Process a payment through the gateway
     */
    processPayment(request: GatewayPaymentRequest): Promise<GatewayPaymentResponse>;
    /**
     * Process a refund through the gateway
     */
    processRefund(request: GatewayRefundRequest): Promise<GatewayRefundResponse>;
    /**
     * Check the status of a transaction
     */
    checkTransactionStatus(transactionId: string): Promise<{
        status: 'pending' | 'completed' | 'failed' | 'refunded' | 'partially_refunded';
        details?: Record<string, any>;
    }>;
    /**
     * Get the gateway configuration
     */
    getConfiguration(): Record<string, any>;
    /**
     * Set the gateway configuration
     */
    setConfiguration(config: Record<string, any>): void;
    /**
     * Check if the gateway is enabled
     */
    isEnabled(): boolean;
    /**
     * Get the supported currencies
     */
    getSupportedCurrencies(): string[];
    /**
     * Validate gateway-specific payment data
     */
    validatePaymentData(paymentMethodType: PaymentMethodType, data: Record<string, any>): {
        valid: boolean;
        errors?: string[];
    };
    /**
     * Check if a TRC20 address is valid
     * @param address The address to validate
     * @returns True if the address is valid
     */
    private isValidTRC20Address;
}
//# sourceMappingURL=BinanceGateway.d.ts.map