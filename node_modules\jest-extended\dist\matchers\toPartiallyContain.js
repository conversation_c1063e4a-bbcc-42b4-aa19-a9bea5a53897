"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toPartiallyContain = toPartiallyContain;
const utils_1 = require("../utils");
function toPartiallyContain(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    const pass = Array.isArray(actual) &&
        Array.isArray([expected]) &&
        [expected].every(partial => actual.some(value => typeof partial === 'object' &&
            partial != null &&
            Object.entries(partial).every(entry => 
            // @ts-expect-error OK to have implicit any for this.equals
            (0, utils_1.containsEntry)((a, b) => this.equals(a, b, this.customTesters), value, entry))));
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toPartiallyContain') +
                '\n\n' +
                'Expected array not to partially contain:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toPartiallyContain') +
                '\n\n' +
                'Expected array to partially contain:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
