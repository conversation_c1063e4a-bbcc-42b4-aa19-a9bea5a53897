/**
 * Fraud Detection Controller
 *
 * Modular controller for fraud detection operations.
 */

import { Response } from 'express';
import { BaseController } from '../base.controller';
import { asyncHandler } from '../../utils/asyncHandler';
import * as prisma from '../../lib/prisma';

import { FraudDetectionAuthService } from './services/FraudDetectionAuthService';
import { FraudDetectionValidationService } from './services/FraudDetectionValidationService';
import { FraudDetectionBusinessService } from './services/FraudDetectionBusinessService';
import { FraudDetectionResponseMapper } from './mappers/FraudDetectionResponseMapper';

import { AuthenticatedRequest, FraudDetectionFilters } from './types/FraudDetectionControllerTypes';

/**
 * Modular Fraud Detection Controller
 */
export class FraudDetectionController extends BaseController {
  private authService: FraudDetectionAuthService;
  private validationService: FraudDetectionValidationService;
  private businessService: FraudDetectionBusinessService;

  constructor() {
    super();
    this.authService = new FraudDetectionAuthService();
    this.validationService = new FraudDetectionValidationService();
    this.businessService = new FraudDetectionBusinessService(prisma as any);
  }

  /**
   * Assess transaction risk
   */
  assessTransactionRisk = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'risk_assessment',
        'assess_risk'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateAssessTransactionRisk(req.body);

      // Business logic
      const assessment = await this.businessService.assessTransactionRisk(validatedData);

      // Response
      FraudDetectionResponseMapper.sendRiskAssessment(res, assessment);
    } catch (error) {
      FraudDetectionResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get transaction risk assessment
   */
  getTransactionRiskAssessment = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'risk_assessment',
        'view_assessment',
        req.params.transactionId
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const transactionId = this.validationService.validateTransactionId(req.params.transactionId);

      // Business logic
      const assessment = await this.businessService.getTransactionRiskAssessment(transactionId);

      // Response
      FraudDetectionResponseMapper.sendTransactionRiskAssessment(res, assessment);
    } catch (error) {
      FraudDetectionResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get merchant fraud configuration
   */
  getMerchantFraudConfig = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'fraud_config',
        'view_config',
        req.params.merchantId
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const merchantId = this.validationService.validateMerchantId(req.params.merchantId);

      // Business logic
      const config = await this.businessService.getMerchantFraudConfig(merchantId);

      // Response
      FraudDetectionResponseMapper.sendFraudConfig(res, config);
    } catch (error) {
      FraudDetectionResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Update merchant fraud configuration
   */
  updateMerchantFraudConfig = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'fraud_config',
        'update_config',
        req.params.merchantId
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const merchantId = this.validationService.validateMerchantId(req.params.merchantId);
      const validatedData = this.validationService.validateUpdateFraudConfig(req.body);

      // Business logic
      const config = await this.businessService.updateMerchantFraudConfig(
        merchantId,
        validatedData
      );

      // Response
      FraudDetectionResponseMapper.sendFraudConfigUpdated(res, config);
    } catch (error) {
      FraudDetectionResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get flagged transactions
   */
  getFlaggedTransactions = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'flagged_transactions',
        'view_flagged'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const pagination = this.validationService.validatePaginationParams(req.query);

      // Extract merchant context
      const { merchantId } = this.authService.extractMerchantContext(req);

      // Build filters
      const filters: FraudDetectionFilters = { merchantId };
      if (req.query.riskLevel) filters.riskLevel = req.query.riskLevel as any;
      if (req.query.isBlocked !== undefined) filters.isBlocked = req.query.isBlocked === 'true';
      if (req.query.startDate) filters.startDate = new Date(req.query.startDate as string);
      if (req.query.endDate) filters.endDate = new Date(req.query.endDate as string);
      if (req.query.minScore) filters.minScore = parseFloat(req.query.minScore as string);
      if (req.query.maxScore) filters.maxScore = parseFloat(req.query.maxScore as string);

      // Business logic
      const result = await this.businessService.getFlaggedTransactions(filters, pagination);

      // Response
      FraudDetectionResponseMapper.sendFlaggedTransactionsList(
        res,
        result.transactions,
        result.total,
        pagination.page,
        pagination.limit
      );
    } catch (error) {
      FraudDetectionResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get fraud statistics
   */
  getFraudStatistics = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'fraud_statistics',
        'view_statistics'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const { start, end } = this.validationService.validateDateRange(
        req.query.startDate,
        req.query.endDate
      );

      // Extract merchant context
      const { merchantId } = this.authService.extractMerchantContext(req);

      // Business logic
      const statistics = await this.businessService.getFraudStatistics(merchantId, start, end);

      // Response
      FraudDetectionResponseMapper.sendFraudStatistics(res, statistics);
    } catch (error) {
      FraudDetectionResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Health check endpoint
   */
  healthCheck = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      FraudDetectionResponseMapper.sendSuccess(
        res,
        {
          status: 'healthy',
          timestamp: new Date(),
          version: '1.0.0',
          services: {
            authorization: 'active',
            validation: 'active',
            business: 'active',
            fraudDetection: 'active',
            database: 'connected',
          },
        },
        'Fraud Detection Controller is healthy'
      );
    } catch (error) {
      FraudDetectionResponseMapper.sendError(res, error as Error);
    }
  });
}
