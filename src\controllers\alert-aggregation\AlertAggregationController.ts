/**
 * Alert Aggregation Controller
 * 
 * Modular controller for alert aggregation operations.
 */

import { Response } from 'express';
import { BaseController } from '../base.controller';
import { asyncHandler } from '../../utils/asyncHandler';
import prisma from '../../lib/prisma';

import { AuthorizationService } from './services/AuthorizationService';
import { ValidationService } from './services/ValidationService';
import { AlertAggregationBusinessService } from './services/AlertAggregationBusinessService';
import { ResponseMapper } from './mappers/ResponseMapper';

import {
  AuthenticatedRequest,
  CreateAggregationRuleRequest,
  UpdateAggregationRuleRequest,
  AggregationRuleFilters,
  CorrelationRuleFilters
} from './types/AlertAggregationTypes';

/**
 * Modular Alert Aggregation Controller
 */
export class AlertAggregationController extends BaseController {
  private authService: AuthorizationService;
  private validationService: ValidationService;
  private businessService: AlertAggregationBusinessService;

  constructor() {
    super();
    this.authService = new AuthorizationService();
    this.validationService = new ValidationService();
    this.businessService = new AlertAggregationBusinessService(prisma);
  }

  /**
   * Get all aggregation rules
   */
  getAggregationRules = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'aggregation-rules',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const pagination = this.validationService.validatePaginationParams(req.query);
      
      // Build filters
      const filters: AggregationRuleFilters = {};
      if (req.query.type) filters.type = req.query.type as any;
      if (req.query.severity) filters.severity = req.query.severity as any;
      if (req.query.enabled !== undefined) filters.enabled = req.query.enabled === 'true';
      if (req.query.search) filters.search = req.query.search as string;

      // Business logic
      const result = await this.businessService.getAggregationRules(filters, pagination);

      // Response
      ResponseMapper.sendAggregationRulesList(
        res,
        result.rules,
        result.total,
        pagination.page,
        pagination.limit
      );
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get a specific aggregation rule
   */
  getAggregationRule = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'aggregation-rules',
        'read',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');

      // Business logic
      const rule = await this.businessService.getAggregationRule(ruleId);

      // Response
      ResponseMapper.sendAggregationRule(res, rule);
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Create a new aggregation rule
   */
  createAggregationRule = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'aggregation-rules',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateCreateAggregationRule(req.body);

      // Business logic
      const rule = await this.businessService.createAggregationRule(validatedData);

      // Response
      ResponseMapper.sendAggregationRuleCreated(res, rule);
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Update an existing aggregation rule
   */
  updateAggregationRule = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'aggregation-rules',
        'update',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');
      const validatedData = this.validationService.validateUpdateAggregationRule(req.body);

      // Business logic
      const rule = await this.businessService.updateAggregationRule(ruleId, validatedData);

      // Response
      ResponseMapper.sendAggregationRuleUpdated(res, rule);
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Delete an aggregation rule
   */
  deleteAggregationRule = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'aggregation-rules',
        'delete',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');

      // Business logic
      await this.businessService.deleteAggregationRule(ruleId);

      // Response
      ResponseMapper.sendAggregationRuleDeleted(res);
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get all correlation rules
   */
  getCorrelationRules = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'correlation-rules',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const pagination = this.validationService.validatePaginationParams(req.query);
      
      // Build filters
      const filters: CorrelationRuleFilters = {};
      if (req.query.enabled !== undefined) filters.enabled = req.query.enabled === 'true';
      if (req.query.search) filters.search = req.query.search as string;

      // Business logic
      const result = await this.businessService.getCorrelationRules(filters, pagination);

      // Response
      ResponseMapper.sendCorrelationRulesList(
        res,
        result.rules,
        result.total,
        pagination.page,
        pagination.limit
      );
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get a specific correlation rule
   */
  getCorrelationRule = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'correlation-rules',
        'read',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');

      // Business logic
      const rule = await this.businessService.getCorrelationRule(ruleId);

      // Response
      ResponseMapper.sendCorrelationRule(res, rule);
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Health check endpoint
   */
  healthCheck = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      ResponseMapper.sendSuccess(res, {
        status: 'healthy',
        timestamp: new Date(),
        version: '1.0.0',
        services: {
          authorization: 'active',
          validation: 'active',
          business: 'active',
          database: 'connected'
        }
      }, 'Alert Aggregation Controller is healthy');
    } catch (error) {
      ResponseMapper.sendError(res, error as Error);
    }
  });
}
