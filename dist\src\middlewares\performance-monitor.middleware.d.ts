/**
 * Performance Monitoring Middleware
 *
 * This middleware tracks API performance metrics:
 * - Response time
 * - Request count
 * - Error rate
 * - Endpoint usage
 */
/**
 * Performance monitoring middleware
 * Tracks request/response metrics
 */
export declare const performanceMonitor: any;
/**
 * Get current performance metrics
 * @returns Performance metrics
 */
export declare function getPerformanceMetrics(): any;
/**
 * Reset performance metrics
 */
export declare function resetPerformanceMetrics(): void;
/**
 * Schedule periodic logging of performance metrics
 * @param interval Interval in milliseconds (default: 1 hour)
 * @returns Timer ID
 */
export declare function schedulePerformanceLogging(interval?: number): NodeJS.Timeout;
declare const _default: {
    performanceMonitor: any;
    getPerformanceMetrics: typeof getPerformanceMetrics;
    resetPerformanceMetrics: typeof resetPerformanceMetrics;
    schedulePerformanceLogging: typeof schedulePerformanceLogging;
};
export default _default;
//# sourceMappingURL=performance-monitor.middleware.d.ts.map