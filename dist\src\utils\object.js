"use strict";
// jscpd:ignore-file
/**
 * Object utility functions
 * Re-exports from shared ObjectUtils to eliminate duplication
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.pick = pick;
exports.omit = omit;
exports.deepClone = deepClone;
exports.isEmpty = isEmpty;
exports.deepMerge = deepMerge;
exports.flatten = flatten;
exports.toQueryString = toQueryString;
exports.parseQueryString = parseQueryString;
const utils_1 = require("../utils");
/**
 * Pick specific properties from an object
 * @param obj Source object
 * @param keys Keys to pick
 * @returns New object with picked properties
 */
function pick(obj, keys) {
    return utils_1.ObjectUtils.pick(obj, keys);
}
/**
 * Omit specific properties from an object
 * @param obj Source object
 * @param keys Keys to omit
 * @returns New object without omitted properties
 */
function omit(obj, keys) {
    return utils_1.ObjectUtils.omit(obj, keys);
}
/**
 * Deep clone an object
 * @param obj Object to clone
 * @returns Cloned object
 */
function deepClone(obj) {
    return utils_1.ObjectUtils.deepClone(obj);
}
/**
 * Check if an object is empty
 * @param obj Object to check
 * @returns Whether the object is empty
 */
function isEmpty(obj) {
    return utils_1.ObjectUtils.isEmpty(obj);
}
/**
 * Merge two objects deeply
 * @param target Target object
 * @param source Source object
 * @returns Merged object
 */
function deepMerge(target, source) {
    return utils_1.ObjectUtils.deepMerge(target, source);
}
/**
 * Flatten an object (convert nested objects to dot notation)
 * @param obj Object to flatten
 * @param prefix Prefix for keys
 * @returns Flattened object
 */
function flatten(obj, prefix = '') {
    const result = {};
    const flattenRecursive = (obj, currentPrefix) => {
        Object.keys(obj).forEach((key) => {
            const prefixedKey = currentPrefix ? `${currentPrefix}.${key}` : key;
            if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
                flattenRecursive(obj[key], prefixedKey);
            }
            else {
                result[prefixedKey] = obj[key];
            }
        });
    };
    flattenRecursive(obj, prefix);
    return result;
}
/**
 * Convert an object to query string
 * @param obj Object to convert
 * @returns Query string
 */
function toQueryString(obj) {
    const params = new URLSearchParams();
    Object.entries(obj).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
            if (Array.isArray(value)) {
                value.forEach((v) => params.append(key, String(v)));
            }
            else {
                params.append(key, String(value));
            }
        }
    });
    return params.toString();
}
/**
 * Parse query string to object
 * @param queryString Query string to parse
 * @returns Parsed object
 */
function parseQueryString(queryString) {
    const params = new URLSearchParams(queryString);
    const result = {};
    for (const [key, value] of params.entries()) {
        if (result[key]) {
            if (Array.isArray(result[key])) {
                result[key].push(value);
            }
            else {
                result[key] = [result[key], value];
            }
        }
        else {
            result[key] = value;
        }
    }
    return result;
}
