// jscpd:ignore-file
/**
 * Validation utility functions
 * Re-exports from shared ValidationUtils to eliminate duplication
 */

// ValidationUtils implementation
export const ValidationUtils: any = {
  validateEmail: (email) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  },
  
  validatePassword: (password) => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex: any =/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
    return passwordRegex.test(password);
  },
  
  validateUrl: (url) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  }
};

/**
 * Check if a value is defined (not undefined or null)
 * @param value Value to check
 * @returns Whether the value is defined
 */
export function isDefined<T>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null;
}

/**
 * Check if a value is a string
 * @param value Value to check
 * @returns Whether the value is a string
 */
export function isString(value: unknown): value is string {
  return ValidationUtils.isString(value);
}

/**
 * Check if a value is a number
 * @param value Value to check
 * @returns Whether the value is a number
 */
export function isNumber(value: unknown): value is number {
  return ValidationUtils.isNumber(value);
}

/**
 * Check if a value is a boolean
 * @param value Value to check
 * @returns Whether the value is a boolean
 */
export function isBoolean(value: unknown): value is boolean {
  return ValidationUtils.isBoolean(value);
}

/**
 * Check if a value is an object
 * @param value Value to check
 * @returns Whether the value is an object
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return ValidationUtils.isObject(value);
}

/**
 * Check if a value is an array
 * @param value Value to check
 * @returns Whether the value is an array
 */
export function isArray<T = unknown>(value: unknown): value is T[] {
  return ValidationUtils.isArray(value);
}

/**
 * Check if a value is a function
 * @param value Value to check
 * @returns Whether the value is a function
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

/**
 * Check if a value is a date
 * @param value Value to check
 * @returns Whether the value is a date
 */
export function isDate(value: unknown): value is Date {
  return ValidationUtils.isDate(value);
}

/**
 * Check if a string is a valid email
 * @param value String to check
 * @returns Whether the string is a valid email
 */
export function isEmail(value: string): boolean {
  return ValidationUtils.isEmail(value);
}

/**
 * Check if a string is a valid URL
 * @param value String to check
 * @returns Whether the string is a valid URL
 */
export function isUrl(value: string): boolean {
  return ValidationUtils.isUrl(value);
}

/**
 * Check if a string is a valid UUID
 * @param value String to check
 * @returns Whether the string is a valid UUID
 */
export function isUuid(value: string): boolean {
  return ValidationUtils.isUuid(value);
}

/**
 * Check if a value is empty (undefined, null, empty string, empty array, or empty object)
 * @param value Value to check
 * @returns Whether the value is empty
 */
export function isEmpty(value: unknown): boolean {
  return ValidationUtils.isEmpty(value);
}

/**
 * Check if a number is within a range
 * @param value Number to check
 * @param min Minimum value (inclusive)
 * @param max Maximum value (inclusive)
 * @returns Whether the number is within the range
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return ValidationUtils.isInRange(value, min, max);
}

/**
 * Check if a string matches a regular expression
 * @param value String to check
 * @param regex Regular expression to match
 * @returns Whether the string matches the regular expression
 */
export function matchesRegex(value: string, regex: RegExp): boolean {
  return ValidationUtils.matches(value, regex);
}

/**
 * Validate an object against a schema
 * @param obj Object to validate
 * @param schema Validation schema
 * @returns Validation result
 */
export function validateObject<T extends Record<string, unknown>>(
  obj: T,
  schema: Record<keyof T, (value: unknown) => boolean>
): { isValid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};

  for (const key in schema) {
    if (!(key in obj)) {
      errors[key as string] = `Missing required field: ${key as string}`;
      continue;
    }

    const validator: any =schema[key];
    const value: any =obj[key];

    if (!validator(value)) {
      errors[key as string] = `Invalid value for field: ${key as string}`;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}
