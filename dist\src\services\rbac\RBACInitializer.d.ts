/**
 * RBAC Initializer
 *
 * Service for initializing the RBAC system with predefined roles and permissions.
 */
import { PrismaClient } from "@prisma/client";
/**
 * RBAC initializer service
 */
export declare class RBACInitializer {
    private prisma;
    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient);
    /**
   * Initialize the RBAC system
   */
    initialize(): Promise<void>;
    /**
   * Check if RBAC models exist in the database
   */
    private checkRbacModelsExist;
    /**
   * Initialize permissions
   */
    private initializePermissions;
    /**
   * Initialize roles
   */
    private initializeRoles;
    /**
   * Create or update a role
   *
   * @param template Role template
   */
    private createOrUpdateRole;
    if(: any, this: any, prisma: any, rolePermission: any): void;
}
//# sourceMappingURL=RBACInitializer.d.ts.map