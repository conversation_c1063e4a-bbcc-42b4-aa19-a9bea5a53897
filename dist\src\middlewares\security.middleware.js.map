{"version": 3, "file": "security.middleware.js", "sourceRoot": "", "sources": ["../../../src/middlewares/security.middleware.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAGH,0CAAuC;AACvC,yDAAiF;AACjF,wCAAoE;AACpE,0EAAsE;AAsBtE;;;GAGG;AACI,MAAM,qBAAqB,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACzF,qCAAqC;IACrC,GAAG,CAAC,SAAS,CACT,yBAAyB,EACzB,sBAAsB;QAC1B,qCAAqC;QACrC,oCAAoC;QACpC,8BAA8B;QAC9B,yBAAyB;QACzB,mDAAmD;QACnD,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;QACnB,qBAAqB,CACpB,CAAC;IAEF,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAjBW,QAAA,qBAAqB,yBAiBhC;AAEF;;;GAGG;AAEI,MAAM,cAAc,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAClF,kDAAkD;IAClD,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAClD,OAAO,IAAI,EAAE,CAAC;IAClB,CAAC;IAED,gEAAgE;IAChE,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC5D,OAAO,IAAI,EAAE,CAAC;IAClB,CAAC;IAED,qBAAqB;IACrB,MAAM,SAAS,GAAO,IAAA,uBAAgB,EAAC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9B,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,GAAG,CAAC,SAAS;SAC3B,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,IAAA,uCAAoB,EAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAClF,CAAC;IAED,sCAAsC;IACtC,MAAM,MAAM,GAAO,GAAG,CAAC,IAAI,EAAE,EAAE,CAAA,CAAC,oDAAoD;IAEpF,sBAAsB;IACtB,IAAI,CAAC,IAAA,wBAAiB,EAAC,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;QACxC,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9B,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,MAAM;SACT,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,IAAA,uCAAoB,EAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAClF,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AA1CW,QAAA,cAAc,kBA0CzB;AAEF;;;GAGG;AACI,MAAM,IAAI,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACxE,uCAAuC;IACvC,GAAG,CAAC,SAAS,CACT,2BAA2B,EAC3B,gDAAgD,CACnD,CAAC;IAEF,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AARW,QAAA,IAAI,QAQf;AAEF;;;GAGG;AACI,MAAM,OAAO,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC3E,oCAAoC;IACpC,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;IAEnD,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AALW,QAAA,OAAO,WAKlB;AAEF;;;GAGG;AACI,MAAM,UAAU,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC9E,6BAA6B;IAC7B,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;IAE/C,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AALW,QAAA,UAAU,cAKrB;AAEF;;;GAGG;AACI,MAAM,SAAS,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC7E,8BAA8B;IAC9B,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;IAErD,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AALW,QAAA,SAAS,aAKpB;AAEF;;;GAGG;AACI,MAAM,cAAc,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAClF,6BAA6B;IAC7B,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;IAEpE,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AALW,QAAA,cAAc,kBAKzB;AAEF;;;GAGG;AACI,MAAM,iBAAiB,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACrF,gCAAgC;IAChC,GAAG,CAAC,SAAS,CACT,oBAAoB,EACpB,8DAA8D,CACjE,CAAC;IAEF,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AARW,QAAA,iBAAiB,qBAQ5B;AAEF;;;GAGG;AACI,MAAM,YAAY,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAChF,0CAA0C;IAC1C,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QACnC,uCAAuC;QACnC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,uDAAuD,CAAC,CAAC;QACxF,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAVW,QAAA,YAAY,gBAUvB;AAEF;;;GAGG;AACI,MAAM,sBAAsB,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC1F,8DAA8D;IAC9D,MAAM,mBAAmB,GAAO,+MAA+M,CAAC;IAEhP,yBAAyB;IACzB,MAAM,WAAW,GAAO,GAAG,CAAC,KAAK,CAAC;IAClC,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;QAC5B,IAAI,OAAO,WAAW,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAW,CAAC,EAAE,CAAC;YAC/F,eAAM,CAAC,IAAI,CAAC,6DAA6D,EAAE;gBACvE,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,SAAS,EAAE,GAAG;gBACd,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC;aAC1B,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,IAAA,wCAAqB,EAAC,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAtBW,QAAA,sBAAsB,0BAsBjC;AAEF;;;GAGG;AACI,MAAM,iBAAiB,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACrF,kDAAkD;IAClD,IAAI,IAAA,8BAAM,GAAE,EAAE,CAAC;QACX,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACvC,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IACzC,CAAC;SAAM,IAAI,CAAC,IAAA,oCAAY,GAAE,EAAE,CAAC;QACzB,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAVW,QAAA,iBAAiB,qBAU5B;AAEF;;;GAGG;AACI,MAAM,iBAAiB,GAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACrF,kCAAkC;IAClC,MAAM,aAAa,GAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAW,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;IACvF,IAAI,aAAa,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM;QACrC,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACrC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,aAAa;SAChB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,IAAA,wCAAqB,EAAC,2BAA2B,EAAE,mBAAmB,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAhBW,QAAA,iBAAiB,qBAgB5B;AAEF;;GAEG;AACU,QAAA,kBAAkB,GAAO;IAClC,6BAAqB;IACrB,sBAAc;IACd,YAAI;IACJ,eAAO;IACP,kBAAU;IACV,iBAAS;IACT,sBAAc;IACd,yBAAiB;IACjB,oBAAY;IACZ,8BAAsB;IACtB,yBAAiB;IACjB,yBAAiB;CACpB,CAAC;AAEF,kBAAe,0BAAkB,CAAC"}