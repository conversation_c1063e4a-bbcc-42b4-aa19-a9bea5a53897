"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainAnyValues = toContainAnyValues;
const utils_1 = require("../utils");
function toContainAnyValues(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    let pass = false;
    if (typeof actual === 'object' && actual !== null && !Array.isArray(actual)) {
        const objectValues = Object.keys(actual).map(k => actual[k]);
        // @ts-expect-error OK to have implicit any for this.equals
        pass = expected.some(value => (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), objectValues, value));
    }
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainAnyValues') +
                '\n\n' +
                'Expected object to not contain any of the following values:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainAnyValues') +
                '\n\n' +
                'Expected object to contain any of the following values:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
