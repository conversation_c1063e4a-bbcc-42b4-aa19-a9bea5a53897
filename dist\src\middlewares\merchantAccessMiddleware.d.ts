/**
 * Merchant Access Middleware
 *
 * This middleware ensures that a user can only access resources for their own merchant,
 * while admins can access resources for any merchant.
 */
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Middleware to check if user has access to merchant resources
 * @param req Request
 * @param res Response
 * @param next Next function
 */
export declare const merchantAccessMiddleware: any;
//# sourceMappingURL=merchantAccessMiddleware.d.ts.map