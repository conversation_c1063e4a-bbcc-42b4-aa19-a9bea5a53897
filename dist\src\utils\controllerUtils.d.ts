/**
 * Controller Utilities
 *
 * This file contains utility functions for controllers.
 */
import { Request } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Check authorization
 * @param req Request object
 * @returns User role, ID, and merchant ID
 * @throws AppError if user is not authorized
 */
export declare function checkAuthorization(req: Request): {
    userRole: string;
    userId: string;
    merchantId?: string;
};
/**
 * Check admin role
 * @param userRole User role
 * @throws AppError if user is not an admin
 */
export declare function checkAdminRole(userRole: string): void;
/**
 * Parse date range
 * @param startDateStr Start date string
 * @param endDateStr End date string
 * @returns Start and end dates
 * @throws AppError if dates are invalid
 */
export declare function parseDateRange(startDateStr?: string, endDateStr?: string): {
    startDate: Date;
    endDate: Date;
};
/**
 * Determine target merchant ID
 * @param userRole User role
 * @param merchantId User's merchant ID
 * @param requestedMerchantId Requested merchant ID
 * @returns Target merchant ID
 * @throws AppError if user is not authorized
 */
export declare function determineTargetMerchantId(userRole: string, merchantId?: string, requestedMerchantId?: string): string;
//# sourceMappingURL=controllerUtils.d.ts.map