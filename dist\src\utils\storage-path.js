"use strict";
// jscpd:ignore-file
/**
 * Storage Path Utility
 *
 * This utility provides functions for managing environment-specific storage paths
 * to ensure complete isolation between production and demo environments.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeStorage = exports.ensureDirectoryExists = exports.getCachePath = exports.getTempPath = exports.getUploadsPath = exports.getLogsPath = exports.getStoragePath = void 0;
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const logger_1 = require("../lib/logger");
const environment_1 = require("../config/environment");
/**
 * Base storage directory
 */
const BASE_STORAGE_DIR = path_1.default.join(process.cwd(), "storage");
/**
 * Get environment-specific storage path
 * @param subPath Optional sub-path within the environment directory
 * @returns Full path to the environment-specific storage location
 */
const getStoragePath = (subPath = "") => {
    const env = (0, environment_1.getEnvironment)();
    const storagePath = path_1.default.join(BASE_STORAGE_DIR, env, subPath);
    // Ensure the directory exists
    (0, exports.ensureDirectoryExists)(storagePath);
    return storagePath;
};
exports.getStoragePath = getStoragePath;
/**
 * Get environment-specific logs path
 * @returns Full path to the environment-specific logs directory
 */
const getLogsPath = () => {
    const env = (0, environment_1.getEnvironment)();
    const logsPath = path_1.default.join(process.cwd(), "logs", env);
    // Ensure the directory exists
    (0, exports.ensureDirectoryExists)(logsPath);
    return logsPath;
};
exports.getLogsPath = getLogsPath;
/**
 * Get environment-specific uploads path
 * @param subPath Optional sub-path within the uploads directory
 * @returns Full path to the environment-specific uploads location
 */
const getUploadsPath = (subPath = "") => {
    return (0, exports.getStoragePath)(path_1.default.join("uploads", subPath));
};
exports.getUploadsPath = getUploadsPath;
/**
 * Get environment-specific temp path
 * @param subPath Optional sub-path within the temp directory
 * @returns Full path to the environment-specific temp location
 */
const getTempPath = (subPath = "") => {
    return (0, exports.getStoragePath)(path_1.default.join("temp", subPath));
};
exports.getTempPath = getTempPath;
/**
 * Get environment-specific cache path
 * @param subPath Optional sub-path within the cache directory
 * @returns Full path to the environment-specific cache location
 */
const getCachePath = (subPath = "") => {
    return (0, exports.getStoragePath)(path_1.default.join("cache", subPath));
};
exports.getCachePath = getCachePath;
/**
 * Ensure a directory exists, creating it if necessary
 * @param dirPath Directory path to ensure
 */
const ensureDirectoryExists = (dirPath) => {
    if (!fs_1.default.existsSync(dirPath)) {
        try {
            fs_1.default.mkdirSync(dirPath, { recursive: true });
            logger_1.logger.debug(`Created directory: ${dirPath}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create directory: ${dirPath}`, error);
            throw error;
        }
    }
};
exports.ensureDirectoryExists = ensureDirectoryExists;
/**
 * Initialize storage directories for the current environment
 */
const initializeStorage = () => {
    const env = (0, environment_1.getEnvironment)();
    // Create base directories
    const directories = [
        (0, exports.getStoragePath)(),
        (0, exports.getLogsPath)(),
        (0, exports.getUploadsPath)(),
        (0, exports.getTempPath)(),
        (0, exports.getCachePath)()
    ];
    // Ensure all directories exist
    directories.forEach(exports.ensureDirectoryExists);
    logger_1.logger.info(`Storage initialized for environment: ${env}`);
};
exports.initializeStorage = initializeStorage;
exports.default = {
    getStoragePath: exports.getStoragePath,
    getLogsPath: exports.getLogsPath,
    getUploadsPath: exports.getUploadsPath,
    getTempPath: exports.getTempPath,
    getCachePath: exports.getCachePath,
    ensureDirectoryExists: exports.ensureDirectoryExists,
    initializeStorage: exports.initializeStorage
};
//# sourceMappingURL=storage-path.js.map