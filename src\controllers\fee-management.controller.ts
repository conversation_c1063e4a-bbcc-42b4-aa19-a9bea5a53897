// jscpd:ignore-file
/**
 * Fee Management Controller
 *
 * This controller handles fee management-related API endpoints.
 */

import { Request, Response } from 'express';
import { BaseController } from './base/BaseController';
import {
  FeeManagementService,
  FeeTier,
  FeeTierType,
  FeeCalculationMethod,
} from '../services/fee-management.service';
import { logger } from '../lib/logger';
import { ApiResponse } from '../middlewares/apiResponseMiddleware';
import { Merchant } from '../types';
import prisma from '../lib/prisma';

/**
 * Fee management controller
 */
export class FeeManagementController extends BaseController {
  private feeManagementService: FeeManagementService;

  constructor() {
    super();
    this.feeManagementService = new FeeManagementService();
  }

  /**
   * Calculate fee for a transaction
   * @param req Request
   * @param res Response
   */
  calculateFee = async (req: Request, res: Response): Promise<void> => {
    try {
      const { merchantId } = req.params;
      const { amount, currency, paymentMethodId } = req.body;

      // Validate required parameters
      if (!merchantId || !amount || !currency) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      // Calculate fee
      const result: any = await this.feeManagementService.calculateFee(
        merchantId,
        parseFloat(amount),
        currency,
        paymentMethodId
      );

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error calculating fee:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to calculate fee',
        error.statusCode || 500
      );
    }
  };

  /**
   * Create fee tier
   * @param req Request
   * @param res Response
   */
  createFeeTier = async (req: Request, res: Response): Promise<void> => {
    try {
      const feeTier: FeeTier = req.body;

      // Validate required parameters
      if (!feeTier || !feeTier.name || !feeTier.type || !feeTier.calculationMethod) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      // Create fee tier
      const result: any = await this.feeManagementService.createFeeTier(feeTier);

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error creating fee tier:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to create fee tier',
        error.statusCode || 500
      );
    }
  };

  /**
   * Get fee tiers
   * @param req Request
   * @param res Response
   */
  getFeeTiers = async (req: Request, res: Response): Promise<void> => {
    try {
      const { type, merchantId, paymentMethodType } = req.query;

      // Get fee tiers
      const result: any = await this.feeManagementService.getFeeTiers(
        type as FeeTierType,
        merchantId as string,
        paymentMethodType as string
      );

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error getting fee tiers:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to get fee tiers',
        error.statusCode || 500
      );
    }
  };

  /**
   * Get fee tier by ID
   * @param req Request
   * @param res Response
   */
  getFeeTierById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tierId } = req.params;

      // Validate required parameters
      if (!tierId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      const result: any = await prisma.feeTier.findUnique({
        where: { id: tierId },
      });

      if (!result) {
        ApiResponse.error(res, 'Fee tier not found', 404);
        return;
      }

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error getting fee tier:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to get fee tier',
        error.statusCode || 500
      );
    }
  };

  /**
   * Update fee tier
   * @param req Request
   * @param res Response
   */

  updateFeeTier = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tierId } = req.params;
      const updates: any = req.body;

      // Validate required parameters
      if (!tierId || !updates) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      // Update fee tier
      const result: any = await this.feeManagementService.updateFeeTier(tierId, updates);

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error updating fee tier:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to update fee tier',
        error.statusCode || 500
      );
    }
  };

  /**
   * Delete fee tier
   * @param req Request
   * @param res Response
   */
  deleteFeeTier = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tierId } = req.params;

      // Validate required parameters
      if (!tierId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      await this.feeManagementService.deleteFeeTier(tierId);

      ApiResponse.success(res, { message: 'Fee tier deleted successfully' });
    } catch (error) {
      logger.error('Error deleting fee tier:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to delete fee tier',
        error.statusCode || 500
      );
    }
  };

  /**
   * Get fee calculations for merchant
   * @param req Request
   * @param res Response
   */
  getMerchantFeeCalculations = async (req: Request, res: Response): Promise<void> => {
    try {
      const { merchantId } = req.params;
      const { startDate, endDate } = req.query;

      // Validate required parameters
      if (!merchantId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      // Build query
      const whereClause: any = { merchantId };

      if (startDate) {
        whereClause.createdAt = {
          ...(whereClause.createdAt || {}),
          gte: new Date(startDate as string),
        };
      }

      if (endDate) {
        whereClause.createdAt = {
          ...(whereClause.createdAt || {}),
          lte: new Date(endDate as string),
        };
      }

      // Get fee calculations
      const result: any = await prisma.feeCalculation.findMany({
        where: whereClause,
        include: {
          feeTier: true,
          paymentMethod: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error getting merchant fee calculations:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to get merchant fee calculations',
        error.statusCode || 500
      );
    }
  };

  /**
   * Create merchant fee discount
   * @param req Request
   * @param res Response
   */
  createMerchantFeeDiscount = async (req: Request, res: Response): Promise<void> => {
    try {
      const { merchantId } = req.params;
      const { discountType, discountValue, startDate, endDate, reason } = req.body;

      // Validate required parameters
      if (!merchantId || !discountType || discountValue === undefined || !startDate) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      // Create merchant fee discount
      const result: any = await prisma.merchantFeeDiscount.create({
        data: {
          merchantId,
          discountType,
          discountValue: parseFloat(discountValue),
          startDate: new Date(startDate),
          endDate: endDate ? new Date(endDate) : null,
          reason,
        },
      });

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error creating merchant fee discount:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to create merchant fee discount',
        error.statusCode || 500
      );
    }
  };

  /**
   * Get merchant fee discounts
   * @param req Request
   * @param res Response
   */
  getMerchantFeeDiscounts = async (req: Request, res: Response): Promise<void> => {
    try {
      const { merchantId } = req.params;

      // Validate required parameters
      if (!merchantId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      const result: any = await prisma.merchantFeeDiscount.findMany({
        where: { merchantId },
        orderBy: { createdAt: 'desc' },
      });

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error getting merchant fee discounts:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to get merchant fee discounts',
        error.statusCode || 500
      );
    }
  };

  /**
   * Update merchant fee discount
   * @param req Request
   * @param res Response
   */
  updateMerchantFeeDiscount = async (req: Request, res: Response): Promise<void> => {
    try {
      const { discountId } = req.params;
      const { discountValue, endDate, reason, isActive } = req.body;

      // Validate required parameters
      if (!discountId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      // Build update data
      const updateData: any = {};

      if (discountValue !== undefined) {
        updateData.discountValue = parseFloat(discountValue);
      }

      if (endDate !== undefined) {
        updateData.endDate = endDate ? new Date(endDate) : null;
      }

      if (reason !== undefined) {
        updateData.reason = reason;
      }

      if (isActive !== undefined) {
        updateData.isActive = isActive;
      }

      // Update merchant fee discount
      const result: any = await prisma.merchantFeeDiscount.update({
        where: { id: discountId },
        data: updateData,
      });

      ApiResponse.success(res, result);
    } catch (error) {
      logger.error('Error updating merchant fee discount:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to update merchant fee discount',
        error.statusCode || 500
      );
    }
  };

  /**
   * Delete merchant fee discount
   * @param req Request
   * @param res Response
   */
  deleteMerchantFeeDiscount = async (req: Request, res: Response): Promise<void> => {
    try {
      const { discountId } = req.params;

      // Validate required parameters
      if (!discountId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      // Delete merchant fee discount
      await prisma.merchantFeeDiscount.delete({
        where: { id: discountId },
      });

      ApiResponse.success(res, { message: 'Merchant fee discount deleted successfully' });
    } catch (error) {
      logger.error('Error deleting merchant fee discount:', error);
      ApiResponse.error(
        res,
        (error as Error).message || 'Failed to delete merchant fee discount',
        error.statusCode || 500
      );
    }
  };
}

export default new FeeManagementController();
