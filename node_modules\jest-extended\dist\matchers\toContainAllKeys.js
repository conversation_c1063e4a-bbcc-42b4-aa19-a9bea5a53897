"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainAllKeys = toContainAllKeys;
const utils_1 = require("../utils");
function toContainAllKeys(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printExpected, printReceived, matcherHint } = this.utils;
    let pass = false;
    if (typeof actual === 'object' && actual !== null && !Array.isArray(actual)) {
        const objectKeys = Object.keys(actual);
        pass =
            objectKeys.length === expected.length &&
                // @ts-expect-error OK to have implicit any for this.equals
                expected.every(key => (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), objectKeys, key));
    }
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainAllKeys') +
                '\n\n' +
                'Expected object to not contain all keys:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(Object.keys(actual))}`
            : matcherHint('.toContainAllKeys') +
                '\n\n' +
                'Expected object to contain all keys:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(Object.keys(actual))}`,
    };
}
