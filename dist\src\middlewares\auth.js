"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = exports.authorize = exports.isMerchantOrAdmin = exports.isAdmin = exports.authenticateJWT = exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config");
const appError_1 = require("../utils/appError");
const asyncHandler_1 = require("../utils/asyncHandler");
const logger_1 = require("../utils/logger");
const logger = new logger_1.Logger("Auth");
/**
 * Authentication middleware
 */
exports.authenticate = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next(new appError_1.AppError({
            message: "No token provided. Please log in.",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
    }
    // Extract the token
    const token = authHeader.split(' ')[1];
    if (!token) {
        return next(new appError_1.AppError({
            message: "Invalid token format. Please log in again.",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
    }
    try {
        // Verify the token
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
        // Set the user in the request object
        req.user = {
            id: decoded.id,
            role: decoded.role
        };
        logger.debug(`User ${decoded.id} (${decoded.role}) authenticated successfully`);
        next();
    }
    catch (error) {
        logger.error("Authentication error:", error);
        return next(new appError_1.AppError({
            message: "Invalid or expired token. Please log in again.",
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
    }
});
/**
 * JWT Authentication middleware (alias for backward compatibility)
 */
exports.authenticateJWT = exports.authenticate;
/**
 * Admin role middleware
 */
const isAdmin = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ message: "Authentication required. Please log in." });
    }
    if (req.user.role !== "ADMIN") {
        logger.warn(`Unauthorized admin access attempt by user ${req.user.id}`);
        return res.status(403).json({ message: "Admin access required." });
    }
    next();
};
exports.isAdmin = isAdmin;
/**
 * Merchant or Admin role middleware
 */
const isMerchantOrAdmin = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ message: "Authentication required. Please log in." });
    }
    if (req.user.role !== "MERCHANT" && req.user.role !== "ADMIN") {
        logger.warn(`Unauthorized merchant/admin access attempt by user ${req.user.id}`);
        return res.status(403).json({ message: "Merchant or Admin access required." });
    }
    next();
};
exports.isMerchantOrAdmin = isMerchantOrAdmin;
/**
 * Authorization middleware
 * @param roles Allowed roles
 */
const authorize = (roles) => {
    return (req, res, next) => {
        // Check if user exists
        if (!req.user) {
            return next(new appError_1.AppError({
                message: "Authentication required. Please log in.",
                type: ErrorType.AUTHENTICATION,
                code: ErrorCode.INVALID_CREDENTIALS
            }));
        }
        // Check if user has required role
        if (!roles.includes(req.user.role)) {
            return next(new appError_1.AppError({
                message: "You do not have permission to perform this action.",
                type: ErrorType.AUTHORIZATION,
                code: ErrorCode.FORBIDDEN
            }));
        }
        next();
    };
};
exports.authorize = authorize;
// Export all middleware functions as a group
exports.auth = {
    authenticate: exports.authenticate,
    authenticateJWT: exports.authenticateJWT,
    isAdmin: exports.isAdmin,
    isMerchantOrAdmin: exports.isMerchantOrAdmin,
    authorize: exports.authorize
};
exports.default = exports.auth;
//# sourceMappingURL=auth.js.map