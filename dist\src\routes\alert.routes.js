"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const alert_controller_1 = require("../controllers/refactored/alert.controller");
const auth_1 = require("../middlewares/auth");
// Create controller instance
const alertControllerInstance = new alert_controller_1.AlertController();
const router = (0, express_1.Router)();
// Alert routes
router.get("/", auth_1.authenticate, alertControllerInstance.getAlerts);
router.get("/count", auth_1.authenticate, alertControllerInstance.getAlertCount);
router.get("/:id", auth_1.authenticate, alertControllerInstance.getAlert);
router.put("/:id/status", auth_1.authenticate, alertControllerInstance.updateAlertStatus);
router.post("/test", auth_1.authenticate, alertControllerInstance.createTestAlert);
exports.default = router;
//# sourceMappingURL=alert.routes.js.map