"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizationErrorHandler = exports.authenticationErrorHandler = exports.databaseErrorHandler = exports.validationErrorHandler = exports.notFoundHandler = exports.errorHandler = void 0;
const appError_1 = require("../utils/appError");
const logger_1 = require("../lib/logger");
const ErrorFactory_1 = require("../utils/errors/ErrorFactory");
/**
 * Error handler middleware
 * This middleware handles all errors in the application
 */
const errorHandler = (err, req, res, next) => {
    // Convert error to AppError
    const error = err instanceof appError_1.AppError ? err : ErrorFactory_1.ErrorFactory.handle(err);
    // Log error
    if (error.statusCode >= 500) {
        logger_1.logger.error(`[${error.type || 'ERROR'}] ${error.message}`, {
            error,
            request: { method: req.method,
                url: req.originalUrl,
                ip: req.ip,
                userId: req.user?.id
            }
        });
    }
    else {
        logger_1.logger.warn(`[${error.type || 'ERROR'}] ${error.message}`, {
            error,
            request: { method: req.method,
                url: req.originalUrl
            }
        });
    }
    // Prepare response
    const response = {
        success: false,
        message: error.message,
        type: error.type,
        ...(error.details && { details: error.details }),
        ...(process.env.NODE_ENV === 'development' && {
            stack: error.stack,
            originalError: error.originalError
        })
    };
    // Send response
    res.status(error.statusCode).json(response);
};
exports.errorHandler = errorHandler;
/**
 * Not found middleware
 * This middleware handles 404 errors
 */
const notFoundHandler = (req, res, next) => {
    const error = ErrorFactory_1.ErrorFactory.notFound('Route', req.originalUrl);
    next(error);
};
exports.notFoundHandler = notFoundHandler;
/**
 * Validation error handler
 * This function creates a validation error from validation errors
 * @param errors Validation errors
 * @returns AppError
 */
const validationErrorHandler = (errors) => {
    const message = 'Validation failed';
    return ErrorFactory_1.ErrorFactory.validation(message, errors);
};
exports.validationErrorHandler = validationErrorHandler;
/**
 * Database error handler
 * This function handles database errors
 * @param error Database error
 * @returns AppError
 */
const databaseErrorHandler = (error) => {
    // Handle specific database errors
    if (error.name === 'PrismaClientKnownRequestError') {
        // @ts-ignore - PrismaClientKnownRequestError has a code property
        const code = error.code;
        // Handle unique constraint violations
        if (code === 'P2002') {
            // @ts-ignore - PrismaClientKnownRequestError has a meta property
            const fields = error.meta?.target;
            const fieldNames = fields.join(', ');
            return ErrorFactory_1.ErrorFactory.conflict(`Unique constraint violation: ${fieldNames} already exists`);
        }
        // Handle foreign key constraint violations
        if (code === 'P2003') {
            // @ts-ignore - PrismaClientKnownRequestError has a meta property
            const field = error.meta?.field_name;
            return ErrorFactory_1.ErrorFactory.validation(`Foreign key constraint violation: ${field} does not exist`);
        }
        // Handle record not found
        if (code === 'P2001') {
            // @ts-ignore - PrismaClientKnownRequestError has a meta property
            const model = error.meta?.model;
            return ErrorFactory_1.ErrorFactory.notFound(model);
        }
    }
    // Default database error
    return ErrorFactory_1.ErrorFactory.database('Database operation failed', error);
};
exports.databaseErrorHandler = databaseErrorHandler;
/**
 * Authentication error handler
 * This function handles authentication errors
 * @param message Error message
 * @returns AppError
 */
const authenticationErrorHandler = (message = 'Authentication failed') => {
    return ErrorFactory_1.ErrorFactory.authentication(message);
};
exports.authenticationErrorHandler = authenticationErrorHandler;
/**
 * Authorization error handler
 * This function handles authorization errors
 * @param message Error message
 * @returns AppError
 */
const authorizationErrorHandler = (message = 'Authorization failed') => {
    return ErrorFactory_1.ErrorFactory.authorization(message);
};
exports.authorizationErrorHandler = authorizationErrorHandler;
//# sourceMappingURL=errorHandler.js.map