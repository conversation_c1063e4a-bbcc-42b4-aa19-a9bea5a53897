"use strict";
// jscpd:ignore-file
/**
 * System Initializer
 *
 * Service for initializing the system with predefined components.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemInitializer = void 0;
const logger_1 = require("../../lib/logger");
const DIContainer_1 = require("../../lib/DIContainer");
const ModuleRegistry_1 = require("../../lib/ModuleRegistry");
const EventBus_1 = require("../../lib/EventBus");
const RBACInitializer_1 = require("../rbac/RBACInitializer");
const VerificationService_1 = require("../verification/VerificationService");
const PredefinedVerificationPolicies_1 = require("../../config/verification/PredefinedVerificationPolicies");
const EnhancedPaymentService_1 = require("../payment/EnhancedPaymentService");
const enhanced_subscription_service_1 = require("../enhanced-subscription.service");
const FeeCalculator_1 = require("../payment/fees/FeeCalculator");
const CommonFeeStrategies_1 = require("../payment/fees/strategies/CommonFeeStrategies");
const PaymentRouter_1 = require("../payment/routing/PaymentRouter");
const CommonRoutingRules_1 = require("../payment/routing/rules/CommonRoutingRules");
const OperationalModeService_1 = require("./OperationalModeService");
/**
 * System initializer service
 */
class SystemInitializer {
    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
   * Initialize the system
   */
    async initialize() {
        logger_1.logger.info("Initializing system");
        try {
            // Register core services in DI container
            this.registerServices();
            // Register modules in module registry
            this.registerModules();
            // Initialize operational mode service
            await this.initializeOperationalMode();
            // Initialize RBAC system
            await this.initializeRBAC();
            // Initialize verification system
            await this.initializeVerification();
            // Initialize payment system
            await this.initializePayment();
            // Set up event listeners
            this.setupEventListeners();
            logger_1.logger.info("System initialized successfully");
        }
        catch (error) {
            logger_1.logger.error("Error initializing system:", error);
            throw error;
        }
    }
    /**
   * Initialize operational mode service
   */
    async initializeOperationalMode() {
        logger_1.logger.info("Initializing operational mode service");
        try {
            const operationalModeService = DIContainer_1.container.resolve("operationalModeService");
            await operationalModeService.initialize();
            // Set default mode to production
            if (operationalModeService.getCurrentMode() !== OperationalModeService_1.OperationalMode.PRODUCTION) {
                await operationalModeService.setOperationalMode(OperationalModeService_1.OperationalMode.PRODUCTION, "system");
            }
            // Ensure system is enabled
            if (!operationalModeService.isSystemEnabled()) {
                await operationalModeService.setSystemEnabled(true, "system");
            }
            logger_1.logger.info(`Operational mode initialized: ${operationalModeService.getCurrentMode()} (${operationalModeService.isSystemEnabled() ? "enabled" : "disabled"})`);
        }
        catch (error) {
            logger_1.logger.error("Error initializing operational mode service:", error);
            throw error;
        }
    }
    /**
   * Register core services in DI container
   */
    registerServices() {
        logger_1.logger.info("Registering core services");
        // Register PrismaClient
        DIContainer_1.container.registerInstance("prisma", this.prisma);
        // Register operational mode service
        DIContainer_1.container.register("operationalModeService", () => {
            return new OperationalModeService_1.OperationalModeService(this.prisma);
        });
        // Register subscription service
        DIContainer_1.container.register("subscriptionService", () => {
            return new enhanced_subscription_service_1.EnhancedSubscriptionService(this.prisma);
        });
        // Register payment service
        DIContainer_1.container.register("paymentService", () => {
            const subscriptionService = DIContainer_1.container.resolve("subscriptionService");
            return new EnhancedPaymentService_1.EnhancedPaymentService(this.prisma, subscriptionService);
        });
        // Register verification service
        DIContainer_1.container.register("verificationService", () => {
            return new VerificationService_1.VerificationService(this.prisma);
        });
        // Register fee calculator
        DIContainer_1.container.register("feeCalculator", () => {
            const calculator = new FeeCalculator_1.FeeCalculator();
            calculator.addStrategies([
                new CommonFeeStrategies_1.PercentageFeeStrategy(this.prisma),
                new CommonFeeStrategies_1.TieredFeeStrategy(),
                new CommonFeeStrategies_1.FixedFeeStrategy()
            ]);
            return calculator;
        });
        // Register payment router
        DIContainer_1.container.register("paymentRouter", () => {
            const router = new PaymentRouter_1.PaymentRouter();
            router.addRules([
                new CommonRoutingRules_1.CountryBasedRule(this.prisma),
                new CommonRoutingRules_1.AmountBasedRule(),
                new CommonRoutingRules_1.SuccessRateRule(this.prisma)
            ]);
            return router;
        });
        logger_1.logger.info("Core services registered");
    }
    /**
   * Register modules in module registry
   */
    registerModules() {
        logger_1.logger.info("Registering modules");
        // Register core modules
        ModuleRegistry_1.moduleRegistry.registerModule("core", {
            enabled: true,
            config: {},
            version: "1.0.0",
            description: "Core system module"
        });
        // Register RBAC module
        ModuleRegistry_1.moduleRegistry.registerModule("rbac", {
            enabled: true,
            config: {},
            dependencies: ["core"],
            version: "1.0.0",
            description: "Role-based access control module"
        });
        // Register verification module
        ModuleRegistry_1.moduleRegistry.registerModule("verification", {
            enabled: true,
            config: {},
            dependencies: ["core"],
            version: "1.0.0",
            description: "Verification module"
        });
        // Register payment module
        ModuleRegistry_1.moduleRegistry.registerModule("payment", {
            enabled: true,
            config: {},
            dependencies: ["core", "verification"],
            version: "1.0.0",
            description: "Payment module"
        });
        // Register subscription module
        ModuleRegistry_1.moduleRegistry.registerModule("subscription", {
            enabled: true,
            config: {},
            dependencies: ["core", "payment"],
            version: "1.0.0",
            description: "Subscription module"
        });
        logger_1.logger.info("Modules registered");
    }
    /**
   * Initialize RBAC system
   */
    async initializeRBAC() {
        logger_1.logger.info("Initializing RBAC system");
        const rbacInitializer = new RBACInitializer_1.RBACInitializer(this.prisma);
        await rbacInitializer.initialize();
        logger_1.logger.info("RBAC system initialized");
    }
    /**
   * Initialize verification system
   */
    async initializeVerification() {
        logger_1.logger.info("Initializing verification system");
        const verificationService = DIContainer_1.container.resolve("verificationService");
        // Register predefined verification policies
        for (const policy of PredefinedVerificationPolicies_1.PREDEFINED_VERIFICATION_POLICIES) {
            verificationService.registerPolicy(policy);
        }
        logger_1.logger.info("Verification system initialized");
    }
    /**
   * Initialize payment system
   */
    async initializePayment() {
        logger_1.logger.info("Initializing payment system");
        // Nothing to do here yet
        logger_1.logger.info("Payment system initialized");
    }
    /**
   * Set up event listeners
   */
    setupEventListeners() {
        logger_1.logger.info("Setting up event listeners");
        // Listen for verification events
        EventBus_1.eventBus.on("verification.completed", async (data) => {
            logger_1.logger.info(`Verification completed for transaction: ${data.transactionId}`, {
                success: data.success,
                merchantId: data.merchantId
            });
            // Update transaction status in database
            if (data.success) {
                try {
                    await this.prisma.transaction.update({
                        where: { id: data.transactionId },
                        data: { verificationStatus: "verified",
                            updatedAt: new Date()
                        }
                    });
                }
                catch (error) {
                    logger_1.logger.error(`Error updating transaction status: ${error.message}`, {
                        transactionId: data.transactionId,
                        error
                    });
                }
            }
        });
        // Listen for payment events
        EventBus_1.eventBus.on("payment.processed", async (data) => {
            logger_1.logger.info(`Payment processed for transaction: ${data.transactionId}`, {
                success: data.success,
                merchantId: data.merchantId
            });
        });
        // Listen for module events
        EventBus_1.eventBus.on("module.registered", (data) => {
            logger_1.logger.info(`Module registered: ${data.name}`, {
                enabled: data.config.enabled,
                version: data.config.version
            });
        });
        logger_1.logger.info("Event listeners set up");
    }
}
exports.SystemInitializer = SystemInitializer;
//# sourceMappingURL=SystemInitializer.js.map