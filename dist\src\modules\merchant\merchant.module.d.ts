import { Module } from '../../core/module';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
export declare class MerchantModule {
    private moduleFactory;
    private moduleRegistry;
    private container;
    private module;
    /**
     * Create a new merchant module
     */
    constructor();
    /**
     * Get the module
     * @returns Merchant module
     */
    getModule(): Module;
}
//# sourceMappingURL=merchant.module.d.ts.map