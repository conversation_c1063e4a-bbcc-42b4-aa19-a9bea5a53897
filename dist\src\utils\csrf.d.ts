/**
 * CSRF Protection Utility
 *
 * This utility provides functions for generating and validating CSRF tokens.
 */
import { Request } from 'express';
/**
 * Generate a CSRF token
 * @param userId User ID to associate with the token
 * @returns CSRF token
 */
export declare const generateCsrfToken: (userId: string) => string;
/**
 * Validate a CSRF token
 * @param token CSRF token to validate
 * @param userId User ID associated with the token
 * @returns Whether the token is valid
 */
export declare const validateCsrfToken: (token: string, userId: string) => boolean;
/**
 * Extract CSRF token from request
 * @param req Express request
 * @returns CSRF token or null if not found
 */
export declare const extractCsrfToken: (req: Request) => string | null;
declare const _default: {
    generateCsrfToken: (userId: string) => string;
    validateCsrfToken: (token: string, userId: string) => boolean;
    extractCsrfToken: (req: Request) => string | null;
};
export default _default;
//# sourceMappingURL=csrf.d.ts.map