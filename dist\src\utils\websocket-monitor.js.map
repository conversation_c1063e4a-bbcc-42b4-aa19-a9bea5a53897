{"version": 3, "file": "websocket-monitor.js", "sourceRoot": "", "sources": ["../../../src/utils/websocket-monitor.ts"], "names": [], "mappings": ";;;AACA,qCAAkC;AAuClC,MAAa,gBAAgB;IAmB3B;;OAEG;IACH;QApBQ,OAAE,GAA2B,IAAI,CAAC;QAClC,UAAK,GAAmB;YAC9B,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE;YACrD,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;YAC7C,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE;YACtC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,GAAG,EAAE,EAAE;YACtC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;YACrC,WAAW,EAAE;gBACX,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,IAAI,IAAI,EAAE;aAC5B;YACD,cAAc,EAAE,EAAE;SACnB,CAAC;QACM,uBAAkB,GAA0B,IAAI,CAAC;QACjD,iBAAY,GAAW,GAAG,CAAC,CAAC,gDAAgD;IAK7D,CAAC;IAExB;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACrD,CAAC;QACD,OAAO,gBAAgB,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,EAAmB;QACnC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QAED,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;YAClC,0BAA0B;YAC1B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAEhC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;gBAC3E,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;YACzE,CAAC;YAED,iBAAiB;YACjB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,YAAY;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBACzD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YACpC,CAAC;YAED,cAAc;YACd,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACzB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;gBAEtD,iBAAiB;gBACjB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;oBAC7B,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,MAAM;oBACb,IAAI;oBACJ,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBAEH,qBAAqB;gBACrB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACzD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBACpC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;oBACrB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;gBACtD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;gBAEtD,kBAAkB;gBAClB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;oBAC7B,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,OAAO;oBACd,IAAI;oBACJ,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBAEH,qBAAqB;gBACrB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACzD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBACpC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iBAAiB;YACjB,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBAE1B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC7D,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,uBAAuB;YACvB,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC3B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;gBAChC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;gBAEtC,oBAAoB;gBACpB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;oBAC7B,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,YAAY;oBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBAEH,qBAAqB;gBACrB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACzD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBACpC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,eAAe;YACf,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,IAAI,eAAe,CAAC;gBAC7D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBAE7B,YAAY;gBACZ,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;oBAC7B,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBAEH,qBAAqB;gBACrB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACzD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBACpC,CAAC;gBAED,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,4BAA4B;QAC5B,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,WAAW;IACxB,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACrE,OAAO;QACT,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAEpD,mBAAmB;QACnB,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;YACrC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;YACnC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC7B,KAAK,EAAE;gBACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;gBAC7B,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;aACtD;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK;gBAC9B,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;aACrD;YACD,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;SACpC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YAChC,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK;gBAC9B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS;aACvC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO;YACL,GAAG,IAAI,CAAC,KAAK;YACb,WAAW,EAAE;gBACX,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;gBACzB,eAAe,EAAE,IAAI,IAAI,EAAE;aAC5B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,IAAI;QACT,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;CACF;AA5OD,4CA4OC"}