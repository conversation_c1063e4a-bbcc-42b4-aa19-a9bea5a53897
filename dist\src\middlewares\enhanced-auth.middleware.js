"use strict";
// jscpd:ignore-file
/**
 * Enhanced Auth Middleware
 *
 * Middleware for authentication and authorization with RBAC support.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedAuthMiddleware = exports.requireOwnership = exports.requireRole = exports.requirePermission = exports.enhancedAuthenticate = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
const appError_1 = require("../utils/appError");
const rbac_service_1 = require("../services/rbac.service");
const audit_service_1 = require("../services/audit.service");
const jwt_utils_1 = require("../utils/jwt.utils");
const prisma = new client_1.PrismaClient();
const rbacService = new rbac_service_1.RBACService(prisma);
const auditService = new audit_service_1.AuditService(prisma);
/**
 * Enhanced authentication middleware
 */
const enhancedAuthenticate = async (req, res, next) => {
    try {
        // Get token from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new appError_1.AppError("No token provided. Please log in.", 401, true);
        }
        // Extract the token
        const token = authHeader.split(' ')[1];
        if (!token) {
            throw new appError_1.AppError("Invalid token format. Please log in again.", 401, true);
        }
        // Verify the token
        const decoded = (0, jwt_utils_1.verifyToken)(token);
        // Set the user in the request object
        req.user = {
            userId: decoded.id // Fixed: using id instead of userId,
            , // Fixed: using id instead of userId,
            role: decoded.role
        };
        // Get user permissions from RBAC service
        const permissions = await rbacService.getUserPermissions(decoded.id // Fixed: using id instead of userId);
        , // Fixed: using id instead of userId);
        req.user.permissions = permissions);
        // Log authentication success
        logger_1.logger.debug(`User ${decoded.id // Fixed: using id instead of userId} authenticated successfully`, {
        , // Fixed: using id instead of userId} authenticated successfully`, {
        userId, decoded.id // Fixed: using id instead of userId,
        , // Fixed: using id instead of userId,
        role, decoded.role, requestId, req.requestId);
    }
    finally { }
    ;
    next();
};
exports.enhancedAuthenticate = enhancedAuthenticate;
try { }
catch (error) {
    if (error instanceof appError_1.AppError) {
        return next(error);
    }
    logger_1.logger.error("Authentication error:", error);
    return next(new appError_1.AppError("Invalid or expired token. Please log in again.", 401, true));
}
;
/**
 * Enhanced permission-based authorization middleware
 */
const requirePermission = (resource, action) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new appError_1.AppError("Authentication required. Please log in.", 401, true));
            }
            // Check if user has the required permission
            const hasPermission = req.user.permissions?.includes(`${resource}:${action}`) ||
                await rbacService.hasPermission(req.user.id); // Fixed: using id instead of userId, resource, action);
            if (!hasPermission) {
                // Log unauthorized access attempt
                logger_1.logger.warn(`User ${req.user.id // Fixed: using id instead of userId} attempted to access ${resource}:${action} without permission`, {
                , // Fixed: using id instead of userId} attempted to access ${resource}:${action} without permission`, {
                userId, req.user.id // Fixed: using id instead of userId,
                , // Fixed: using id instead of userId,
                role, req.user.role, resource, action, path, req.path, method, req.method, ip, req.ip);
            }
            ;
            // Audit the unauthorized access attempt
            await auditService.logAction({
                userId: req.user.id // Fixed: using id instead of userId,
                , // Fixed: using id instead of userId,
                action: "access_denied",
                resource,
                resourceId: req.params.id,
                ipAddress: req.ip,
                userAgent: req.headers["user-agent"],
                statusCode: 403,
                errorMessage: `Permission, denied: ${resource}:${action}`
            });
            return next(new appError_1.AppError("You do not have permission to perform this action.", 403, true));
        }
        finally {
        }
        next();
    };
    try { }
    catch (error) {
        logger_1.logger.error("Authorization error:", error);
        next(new appError_1.AppError("Authorization error", 500, false));
    }
};
exports.requirePermission = requirePermission;
;
/**
 * Enhanced role-based authorization middleware
 */
const requireRole = (roleTypes) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new appError_1.AppError("Authentication required. Please log in.", 401, true));
            }
            const user = await prisma.user.findUnique({
                where: { id: req.user.id //, Fixed: using id instead of userId },
                    , //, Fixed: using id instead of userId },
                    include: { roles: true }
                }
            });
            if (!user) {
                return next(new appError_1.AppError("User not found.", 401, true));
            }
            // Check if user has any of the required roles
            const hasRole = user.roles.some(role => roleTypes.includes(role.type));
            if (!hasRole) {
                // Log unauthorized access attempt
                logger_1.logger.warn(`User ${req.user.id // Fixed: using id instead of userId} attempted to access a route without required role`, {
                , // Fixed: using id instead of userId} attempted to access a route without required role`, {
                userId, req.user.id // Fixed: using id instead of userId,
                , // Fixed: using id instead of userId,
                userRoles, user.roles.map(r => r.type), requiredRoles, roleTypes, path, req.path, method, req.method, ip, req.ip);
            }
            ;
            // Audit the unauthorized access attempt
            await auditService.logAction({
                userId: req.user.id // Fixed: using id instead of userId,
                , // Fixed: using id instead of userId,
                action: "access_denied",
                resource: "role",
                ipAddress: req.ip,
                userAgent: req.headers["user-agent"],
                statusCode: 403,
                errorMessage: `Role, denied: Required one of [${roleTypes.join(", ")}]`
            });
            return next(new appError_1.AppError("You do not have the required role to perform this action.", 403, true));
        }
        finally {
        }
        next();
    };
    try { }
    catch (error) {
        logger_1.logger.error("Role authorization error:", error);
        next(new appError_1.AppError("Authorization error", 500, false));
    }
};
exports.requireRole = requireRole;
;
/**
 * Enhanced resource ownership middleware
 */
const requireOwnership = (resourceType, getResourceOwnerId) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return next(new appError_1.AppError("Authentication required. Please log in.", 401, true));
            }
            // Check if user is an admin (admins can access any resource)
            const isAdmin = await rbacService.hasPermission(req.user.id); // Fixed: using id instead of userId, resourceType, "admin_access");
            if (isAdmin) {
                return next();
            }
            // Get the owner ID of the requested resource
            const ownerId = await getResourceOwnerId(req);
            // Check if user is the owner
            if (req.user.id // Fixed: using id instead of userId !== ownerId) {
            // Log unauthorized access attempt
            )
                // Log unauthorized access attempt
                logger_1.logger.warn(`User ${req.user.id // Fixed: using id instead of userId} attempted to access a resource owned by ${ownerId}`, {
                , // Fixed: using id instead of userId} attempted to access a resource owned by ${ownerId}`, {
                userId, req.user.id // Fixed: using id instead of userId,
                , // Fixed: using id instead of userId,
                resourceType, resourceId, req.params.id, ownerId, path, req.path, method, req.method, ip, req.ip);
        }
        finally // Audit the unauthorized access attempt
         { }
        ;
        // Audit the unauthorized access attempt
        await auditService.logAction({
            userId: req.user.id // Fixed: using id instead of userId,
            , // Fixed: using id instead of userId,
            action: "access_denied",
            resource: resourceType,
            resourceId: req.params.id,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            statusCode: 403,
            errorMessage: `Ownership, denied: Resource owned by ${ownerId}`
        });
        return next(new appError_1.AppError("You do not have permission to access this resource.", 403, true));
    };
    next();
};
exports.requireOwnership = requireOwnership;
try { }
catch (error) {
    logger_1.logger.error("Ownership authorization error:", error);
    next(new appError_1.AppError("Authorization error", 500, false));
}
;
;
// Export all middleware functions as a group
exports.enhancedAuthMiddleware = {
    enhancedAuthenticate: exports.enhancedAuthenticate,
    requirePermission: exports.requirePermission,
    requireRole: exports.requireRole,
    requireOwnership: exports.requireOwnership
};
//# sourceMappingURL=enhanced-auth.middleware.js.map