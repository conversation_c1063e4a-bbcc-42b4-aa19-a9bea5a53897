"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const payment_page_controller_1 = require("../controllers/payment-page.controller");
const router = express_1.default.Router();
// Public routes (no authentication required)
router.get("/slug/:slug/merchant/:merchantId", payment_page_controller_1.getPaymentPageBySlug);
router.post("/:id/transaction", payment_page_controller_1.createTransactionFromPaymentPage);
// Routes requiring authentication
router.use(auth_middleware_1.authMiddleware);
// Routes accessible by ADMIN and SUPER_ADMIN
router.get("/", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN"]), payment_page_controller_1.getAllPaymentPages);
// Routes accessible by ADMIN, SUPER_ADMIN, and MERCHANT
router.get("/:id", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), payment_page_controller_1.getPaymentPageById);
router.post("/", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), payment_page_controller_1.createPaymentPage);
router.put("/:id", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), payment_page_controller_1.updatePaymentPage);
router.delete("/:id", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), payment_page_controller_1.deletePaymentPage);
// Merchant-specific routes
router.get("/merchant/:merchantId", (0, auth_middleware_1.authorize)(["ADMIN", "SUPER_ADMIN", "MERCHANT"]), payment_page_controller_1.getMerchantPaymentPages);
exports.default = router;
//# sourceMappingURL=payment-page.routes.js.map