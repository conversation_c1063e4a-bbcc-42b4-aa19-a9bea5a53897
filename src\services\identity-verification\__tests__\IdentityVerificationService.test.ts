/**
 * Unit Tests for Identity Verification Service
 *
 * Comprehensive test suite covering all functionality of the IdentityVerificationService
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { IdentityVerificationService } from '../core/IdentityVerificationService';
import { IdentityVerificationError } from '../core/IdentityVerificationError';

// Mock dependencies
jest.mock('@prisma/client');
jest.mock('ethers');

describe('IdentityVerificationService', () => {
  let service: IdentityVerificationService;
  let mockPrisma: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    // Create mock Prisma client
    mockPrisma = {
      identityVerification: {
        create: jest.fn(),
        findUnique: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        count: jest.fn(),
      },
      user: {
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
      },
      merchant: {
        findUnique: jest.fn(),
      },
    } as any;

    // Initialize service with mock
    service = new IdentityVerificationService(mockPrisma);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyEthereumSignature', () => {
    const validVerificationData = {
      address: '******************************************',
      message: 'Verify identity for AmazingPay',
      signature:
        '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
      userId: 'user-123-456-789',
      merchantId: 'merchant-123-456-789',
    };

    it('should successfully verify a valid Ethereum signature', async () => {
      // Arrange
      const mockVerificationResult = {
        id: 'verification-123-456-789',
        userId: validVerificationData.userId,
        merchantId: validVerificationData.merchantId,
        method: 'ETHEREUM_SIGNATURE',
        status: 'VERIFIED',
        address: validVerificationData.address,
        createdAt: new Date(),
      };

      // Mock Prisma calls
      mockPrisma.identityVerification.create.mockResolvedValue(mockVerificationResult);

      // Act
      const result = await service.verifyEthereumSignature(validVerificationData);

      // Assert
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.verificationId).toBe(mockVerificationResult.id);
      expect(result.method).toBe('ETHEREUM_SIGNATURE');
      expect(result.status).toBe('VERIFIED');

      // Verify Prisma calls
      expect(mockPrisma.identityVerification.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: validVerificationData.userId,
          merchantId: validVerificationData.merchantId,
          method: 'ETHEREUM_SIGNATURE',
          status: 'VERIFIED',
          address: expect.any(String),
        }),
      });
    });

    it('should return error for invalid Ethereum address', async () => {
      // Arrange
      const invalidData = {
        ...validVerificationData,
        address: 'invalid-address',
      };

      // Act
      const result = await service.verifyEthereumSignature(invalidData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid Ethereum address format');

      // Verify no database calls were made
      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should throw error for invalid signature', async () => {
      // Arrange
      const mockUser = { id: validVerificationData.userId };
      const mockMerchant = { id: validVerificationData.merchantId };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);
      ethers.utils.verifyMessage.mockReturnValue('0xdifferentaddress');

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(
        IdentityVerificationError
      );

      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should throw error when user not found', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(
        IdentityVerificationError
      );

      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should throw error when merchant not found', async () => {
      // Arrange
      const mockUser = { id: validVerificationData.userId };
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.merchant.findUnique.mockResolvedValue(null);

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(
        IdentityVerificationError
      );

      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const mockUser = { id: validVerificationData.userId };
      const mockMerchant = { id: validVerificationData.merchantId };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
      mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database error'));

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);
      ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData)).rejects.toThrow(
        IdentityVerificationError
      );
    });
  });

  describe('getVerificationById', () => {
    it('should return verification details for valid ID', async () => {
      // Arrange
      const verificationId = 'verification-123-456-789';
      const mockVerification = {
        id: verificationId,
        userId: 'user-123-456-789',
        merchantId: 'merchant-123-456-789',
        method: 'ethereum_signature',
        status: 'verified',
        confidence: 0.95,
        address: '******************************************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);

      // Act
      const result = await service.getVerificationById(verificationId);

      // Assert
      expect(result).toEqual(mockVerification);
      expect(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({
        where: { id: verificationId },
      });
    });

    it('should throw error for non-existent verification', async () => {
      // Arrange
      const verificationId = 'non-existent-verification-id';
      mockPrisma.identityVerification.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getVerificationById(verificationId)).rejects.toThrow(
        IdentityVerificationError
      );
    });
  });

  describe('getVerificationsForUser', () => {
    it('should return user verifications', async () => {
      // Arrange
      const userId = 'user-123-456-789';
      const mockVerifications = [
        {
          id: 'verification-1-123-456',
          userId,
          method: 'ethereum_signature',
          status: 'verified',
          createdAt: new Date(),
        },
        {
          id: 'verification-2-123-456',
          userId,
          method: 'ethereum_signature',
          status: 'verified',
          createdAt: new Date(),
        },
      ];

      mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);

      // Act
      const result = await service.getVerificationsForUser(userId);

      // Assert
      expect(result).toEqual(mockVerifications);

      expect(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({
        where: { userId },
        include: { claims: true },
        orderBy: { createdAt: 'desc' },
        take: 50,
        skip: 0,
      });
    });

    it('should handle empty results', async () => {
      // Arrange
      const userId = 'empty-user-123-456';
      mockPrisma.identityVerification.findMany.mockResolvedValue([]);

      // Act
      const result = await service.getVerificationsForUser(userId);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('getVerificationStats', () => {
    it('should return verification statistics', async () => {
      // Arrange
      const filters = {
        merchantId: 'merchant-stats-123',
        dateFrom: new Date('2024-01-01'),
        dateTo: new Date('2024-01-31'),
      };

      mockPrisma.identityVerification.count
        .mockResolvedValueOnce(100) // total
        .mockResolvedValueOnce(85) // successful
        .mockResolvedValueOnce(5) // failed
        .mockResolvedValueOnce(10); // pending

      mockPrisma.identityVerification.groupBy.mockResolvedValue([
        { method: 'ETHEREUM_SIGNATURE', _count: { method: 50 } },
        { method: 'ERC1484', _count: { method: 30 } },
      ]);

      // Act
      const result = await service.getVerificationStats(filters);

      // Assert
      expect(result).toEqual({
        totalVerifications: 100,
        successfulVerifications: 85,
        failedVerifications: 5,
        pendingVerifications: 10,
        verificationsByMethod: {
          ETHEREUM_SIGNATURE: 50,
          ERC1484: 30,
        },
        averageVerificationTime: 5000,
      });

      // Verify database calls
      expect(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);
      expect(mockPrisma.identityVerification.groupBy).toHaveBeenCalledTimes(1);
    });

    it('should handle zero verifications', async () => {
      // Arrange
      mockPrisma.identityVerification.count.mockResolvedValue(0);
      mockPrisma.identityVerification.groupBy.mockResolvedValue([]);

      // Act
      const result = await service.getVerificationStats();

      // Assert
      expect(result.totalVerifications).toBe(0);
      expect(result.successfulVerifications).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Arrange
      const testData = {
        address: '******************************************',
        message: 'Test message',
        signature: '0xabcdef1234567890',
        userId: 'user-123',
        merchantId: 'merchant-123',
      };

      mockPrisma.user.findUnique.mockRejectedValue(new Error('Network error'));

      // Act & Assert
      const result = await service.verifyEthereumSignature(testData);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
    });

    it('should handle timeout errors', async () => {
      // Arrange
      const testData = {
        address: '******************************************',
        message: 'Test message',
        signature: '0xabcdef1234567890',
        userId: 'user-123',
        merchantId: 'merchant-123',
      };

      mockPrisma.user.findUnique.mockImplementation(
        () => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 100))
      );

      // Act & Assert
      const result = await service.verifyEthereumSignature(testData);
      expect(result.success).toBe(false);
    });
  });

  describe('Input Validation', () => {
    it('should validate required fields', async () => {
      // Test missing address
      const result1 = await service.verifyEthereumSignature({
        address: '',
        message: 'test',
        signature: '0xtest',
        userId: 'user-validation-123',
        merchantId: 'merchant-validation-123',
      });
      expect(result1.success).toBe(false);
      expect(result1.error).toContain('Address is required');

      // Test missing message
      const result2 = await service.verifyEthereumSignature({
        address: '******************************************',
        message: '',
        signature: '0xtest',
        userId: 'user-validation-123',
        merchantId: 'merchant-validation-123',
      });
      expect(result2.success).toBe(false);
      expect(result2.error).toContain('Message is required');

      // Test missing signature
      const result3 = await service.verifyEthereumSignature({
        address: '******************************************',
        message: 'test',
        signature: '',
        userId: 'user-validation-123',
        merchantId: 'merchant-validation-123',
      });
      expect(result3.success).toBe(false);
      expect(result3.error).toContain('Signature is required');
    });

    it('should validate address format', async () => {
      const result = await service.verifyEthereumSignature({
        address: 'invalid-address',
        message: 'test',
        signature: '0xtest',
        userId: 'user-validation-123',
        merchantId: 'merchant-validation-123',
      });
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid Ethereum address format');
    });
  });
});
