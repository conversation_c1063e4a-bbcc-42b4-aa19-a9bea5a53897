/**
 * Unit Tests for Identity Verification Service
 * 
 * Comprehensive test suite covering all functionality of the IdentityVerificationService
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { IdentityVerificationService } from '../core/IdentityVerificationService';
import { IdentityVerificationError } from '../core/IdentityVerificationError';

// Mock dependencies
jest.mock('@prisma/client');
jest.mock('ethers');

describe('IdentityVerificationService', () => {
  let service: IdentityVerificationService;
  let mockPrisma: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    // Create mock Prisma client
    mockPrisma = {
      identityVerification: {
        create: jest.fn(),
        findUnique: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        count: jest.fn(),
      },
      user: {
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
      },
      merchant: {
        findUnique: jest.fn(),
      },
    } as any;

    // Initialize service with mock
    service = new IdentityVerificationService(mockPrisma);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyEthereumSignature', () => {
    const validVerificationData = {
      address: testUtils.mockEthereumAddress(),
      message: 'Verify identity for AmazingPay',
      signature: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab1c',
      userId: testUtils.mockUUID(),
      merchantId: testUtils.mockUUID(),
    };

    it('should successfully verify a valid Ethereum signature', async () => {
      // Arrange
      const mockUser = {
        id: validVerificationData.userId,
        email: '<EMAIL>',
        role: 'USER',
      };

      const mockMerchant = {
        id: validVerificationData.merchantId,
        name: 'Test Merchant',
      };

      const mockVerificationResult = {
        id: testUtils.mockUUID(),
        userId: validVerificationData.userId,
        merchantId: validVerificationData.merchantId,
        method: 'ethereum_signature',
        status: 'verified',
        confidence: 0.95,
        address: validVerificationData.address,
        createdAt: new Date(),
      };

      // Mock Prisma calls
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
      mockPrisma.identityVerification.create.mockResolvedValue(mockVerificationResult);

      // Mock ethers verification
      const { ethers } = require('ethers');
      ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);
      ethers.utils.isAddress.mockReturnValue(true);

      // Act
      const result = await service.verifyEthereumSignature(validVerificationData);

      // Assert
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.verificationId).toBe(mockVerificationResult.id);
      expect(result.method).toBe('ethereum_signature');
      expect(result.confidence).toBe(0.95);

      // Verify Prisma calls
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: validVerificationData.userId },
      });
      expect(mockPrisma.merchant.findUnique).toHaveBeenCalledWith({
        where: { id: validVerificationData.merchantId },
      });
      expect(mockPrisma.identityVerification.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: validVerificationData.userId,
          merchantId: validVerificationData.merchantId,
          method: 'ethereum_signature',
          status: 'verified',
          address: validVerificationData.address,
        }),
      });
    });

    it('should throw error for invalid Ethereum address', async () => {
      // Arrange
      const invalidData = {
        ...validVerificationData,
        address: 'invalid-address',
      };

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(false);

      // Act & Assert
      await expect(service.verifyEthereumSignature(invalidData))
        .rejects
        .toThrow(IdentityVerificationError);

      // Verify no database calls were made
      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should throw error for invalid signature', async () => {
      // Arrange
      const mockUser = { id: validVerificationData.userId };
      const mockMerchant = { id: validVerificationData.merchantId };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);
      ethers.utils.verifyMessage.mockReturnValue('0xdifferentaddress');

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData))
        .rejects
        .toThrow(IdentityVerificationError);

      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should throw error when user not found', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData))
        .rejects
        .toThrow(IdentityVerificationError);

      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should throw error when merchant not found', async () => {
      // Arrange
      const mockUser = { id: validVerificationData.userId };
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.merchant.findUnique.mockResolvedValue(null);

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData))
        .rejects
        .toThrow(IdentityVerificationError);

      expect(mockPrisma.identityVerification.create).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const mockUser = { id: validVerificationData.userId };
      const mockMerchant = { id: validVerificationData.merchantId };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.merchant.findUnique.mockResolvedValue(mockMerchant);
      mockPrisma.identityVerification.create.mockRejectedValue(new Error('Database error'));

      const { ethers } = require('ethers');
      ethers.utils.isAddress.mockReturnValue(true);
      ethers.utils.verifyMessage.mockReturnValue(validVerificationData.address);

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData))
        .rejects
        .toThrow(IdentityVerificationError);
    });
  });

  describe('getVerificationById', () => {
    it('should return verification details for valid ID', async () => {
      // Arrange
      const verificationId = testUtils.mockUUID();
      const mockVerification = {
        id: verificationId,
        userId: testUtils.mockUUID(),
        merchantId: testUtils.mockUUID(),
        method: 'ethereum_signature',
        status: 'verified',
        confidence: 0.95,
        address: testUtils.mockEthereumAddress(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.identityVerification.findUnique.mockResolvedValue(mockVerification);

      // Act
      const result = await service.getVerificationById(verificationId);

      // Assert
      expect(result).toEqual(mockVerification);
      expect(mockPrisma.identityVerification.findUnique).toHaveBeenCalledWith({
        where: { id: verificationId },
      });
    });

    it('should throw error for non-existent verification', async () => {
      // Arrange
      const verificationId = testUtils.mockUUID();
      mockPrisma.identityVerification.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getVerificationById(verificationId))
        .rejects
        .toThrow(IdentityVerificationError);
    });
  });

  describe('getUserVerifications', () => {
    it('should return user verifications with pagination', async () => {
      // Arrange
      const userId = testUtils.mockUUID();
      const mockVerifications = [
        {
          id: testUtils.mockUUID(),
          userId,
          method: 'ethereum_signature',
          status: 'verified',
          createdAt: new Date(),
        },
        {
          id: testUtils.mockUUID(),
          userId,
          method: 'ethereum_signature',
          status: 'verified',
          createdAt: new Date(),
        },
      ];

      mockPrisma.identityVerification.findMany.mockResolvedValue(mockVerifications);
      mockPrisma.identityVerification.count.mockResolvedValue(2);

      // Act
      const result = await service.getUserVerifications(userId, { page: 1, limit: 10 });

      // Assert
      expect(result.verifications).toEqual(mockVerifications);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);

      expect(mockPrisma.identityVerification.findMany).toHaveBeenCalledWith({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 10,
      });
    });

    it('should handle empty results', async () => {
      // Arrange
      const userId = testUtils.mockUUID();
      mockPrisma.identityVerification.findMany.mockResolvedValue([]);
      mockPrisma.identityVerification.count.mockResolvedValue(0);

      // Act
      const result = await service.getUserVerifications(userId);

      // Assert
      expect(result.verifications).toEqual([]);
      expect(result.total).toBe(0);
    });
  });

  describe('updateVerificationStatus', () => {
    it('should successfully update verification status', async () => {
      // Arrange
      const verificationId = testUtils.mockUUID();
      const newStatus = 'rejected';
      const reason = 'Invalid signature detected';

      const mockUpdatedVerification = {
        id: verificationId,
        status: newStatus,
        reason,
        updatedAt: new Date(),
      };

      mockPrisma.identityVerification.update.mockResolvedValue(mockUpdatedVerification);

      // Act
      const result = await service.updateVerificationStatus(verificationId, newStatus, reason);

      // Assert
      expect(result).toEqual(mockUpdatedVerification);
      expect(mockPrisma.identityVerification.update).toHaveBeenCalledWith({
        where: { id: verificationId },
        data: {
          status: newStatus,
          reason,
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should throw error for invalid verification ID', async () => {
      // Arrange
      const verificationId = testUtils.mockUUID();
      mockPrisma.identityVerification.update.mockRejectedValue(new Error('Record not found'));

      // Act & Assert
      await expect(service.updateVerificationStatus(verificationId, 'rejected'))
        .rejects
        .toThrow(IdentityVerificationError);
    });
  });

  describe('getVerificationStatistics', () => {
    it('should return verification statistics', async () => {
      // Arrange
      const merchantId = testUtils.mockUUID();
      const dateFrom = new Date('2024-01-01');
      const dateTo = new Date('2024-01-31');

      mockPrisma.identityVerification.count
        .mockResolvedValueOnce(100) // total
        .mockResolvedValueOnce(85)  // verified
        .mockResolvedValueOnce(10)  // pending
        .mockResolvedValueOnce(5);  // rejected

      // Act
      const result = await service.getVerificationStatistics(merchantId, dateFrom, dateTo);

      // Assert
      expect(result).toEqual({
        total: 100,
        verified: 85,
        pending: 10,
        rejected: 5,
        verificationRate: 85,
      });

      // Verify database calls
      expect(mockPrisma.identityVerification.count).toHaveBeenCalledTimes(4);
    });

    it('should handle zero verifications', async () => {
      // Arrange
      mockPrisma.identityVerification.count.mockResolvedValue(0);

      // Act
      const result = await service.getVerificationStatistics();

      // Assert
      expect(result.total).toBe(0);
      expect(result.verificationRate).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockRejectedValue(new Error('Network error'));

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData))
        .rejects
        .toThrow(IdentityVerificationError);
    });

    it('should handle timeout errors', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      // Act & Assert
      await expect(service.verifyEthereumSignature(validVerificationData))
        .rejects
        .toThrow(IdentityVerificationError);
    });
  });

  describe('Input Validation', () => {
    it('should validate required fields', async () => {
      // Test missing address
      await expect(service.verifyEthereumSignature({
        address: '',
        message: 'test',
        signature: '0xtest',
        userId: testUtils.mockUUID(),
        merchantId: testUtils.mockUUID(),
      })).rejects.toThrow();

      // Test missing message
      await expect(service.verifyEthereumSignature({
        address: testUtils.mockEthereumAddress(),
        message: '',
        signature: '0xtest',
        userId: testUtils.mockUUID(),
        merchantId: testUtils.mockUUID(),
      })).rejects.toThrow();

      // Test missing signature
      await expect(service.verifyEthereumSignature({
        address: testUtils.mockEthereumAddress(),
        message: 'test',
        signature: '',
        userId: testUtils.mockUUID(),
        merchantId: testUtils.mockUUID(),
      })).rejects.toThrow();
    });

    it('should validate UUID format', async () => {
      await expect(service.verifyEthereumSignature({
        address: testUtils.mockEthereumAddress(),
        message: 'test',
        signature: '0xtest',
        userId: 'invalid-uuid',
        merchantId: testUtils.mockUUID(),
      })).rejects.toThrow();
    });
  });
});
