"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const user_controller_1 = __importDefault(require("../controllers/user.controller"));
const router = (0, express_1.Router)();
// Get current user details
router.get("/me", auth_middleware_1.authenticate, user_controller_1.default.getCurrentUser);
// Get user profile
router.get("/:userId/profile", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty()
]), user_controller_1.default.getUserProfile);
// Update user profile
router.put("/:userId/profile", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty()
]), user_controller_1.default.updateUserProfile);
// Get user preferences
router.get("/:userId/preferences", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty()
]), user_controller_1.default.getUserPreferences);
// Update user preferences
router.put("/:userId/preferences", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty()
]), user_controller_1.default.updateUserPreferences);
// Update user settings
router.put("/:userId/settings", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty()
]), user_controller_1.default.updateUserSettings);
// Update notification preferences
router.put("/:userId/notifications/preferences", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty(),
    (0, express_validator_1.body)("email").optional().isBoolean(),
    (0, express_validator_1.body)("sms").optional().isBoolean(),
    (0, express_validator_1.body)("push").optional().isBoolean()
]), user_controller_1.default.updateNotificationPreferences);
// Change password
router.put("/:userId/password", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty(),
    (0, express_validator_1.body)("currentPassword").notEmpty().isString(),
    (0, express_validator_1.body)("newPassword").notEmpty().isString().isLength({ min: 6 }),
    (0, express_validator_1.body)("confirmPassword").notEmpty().isString().isLength({ min: 6 })
]), user_controller_1.default.changePassword);
// Request verification
router.post("/:userId/verification/request", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty(),
    (0, express_validator_1.body)("type").isIn(["email", "phone"])
]), user_controller_1.default.requestVerification);
// Verify account
router.post("/:userId/verification/verify", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("userId").notEmpty(),
    (0, express_validator_1.body)("verificationCode").notEmpty().isString(),
    (0, express_validator_1.body)("type").isIn(["email", "phone"])
]), user_controller_1.default.verifyAccount);
exports.default = router;
//# sourceMappingURL=user.routes.js.map