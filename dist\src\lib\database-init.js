"use strict";
// jscpd:ignore-file
/**
 * Database Initialization Module
 *
 * This module initializes the database connection and ensures that the database is ready
 * for use by the application. It is called during application startup.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = void 0;
const logger_1 = require("./logger");
const database_config_1 = require("../config/database.config");
const database_initializer_1 = require("../utils/database-initializer");
// Initialize database
const initializeDatabase = async () => {
    logger_1.logger.info("Initializing database connection...");
    try {
        // Get database configuration
        const dbConfig = (0, database_config_1.getDatabaseConfig)();
        logger_1.logger.info(`Connecting to database: ${dbConfig.database} at ${dbConfig.host}:${dbConfig.port}`);
        // Initialize database
        const initializer = new database_initializer_1.DatabaseInitializer({
            createDatabase: false, // Don't create database during application startup
            applyMigrations: false, // Don't apply migrations during application startup
            verifySchema: true, // Verify schema during application startup
            seedData: false // Don't seed data during application startup
        });
        // Initialize database
        const result = await initializer.initialize();
        if (!result.success) {
            logger_1.logger.warn(`Database initialization warning: ${result.message}`);
        }
        else {
            logger_1.logger.info("Database initialization successful");
        }
        // Get Prisma client
        const prisma = (0, database_config_1.getPrismaClient)();
        // Test database connection
        await prisma.$queryRaw `SELECT 1`;
        logger_1.logger.info("Database connection established");
        return prisma;
    }
    catch (error) {
        logger_1.logger.error("Failed to initialize database:", error);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
// Export default
exports.default = { initializeDatabase: exports.initializeDatabase };
//# sourceMappingURL=database-init.js.map