"use strict";
// jscpd:ignore-file
/**
 * <PERSON><PERSON>
 *
 * This utility provides functions for managing environment-specific cookies
 * to ensure complete isolation between production and demo environments.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearCookie = exports.getCookie = exports.setCookie = exports.getCookieOptions = exports.getCookieDomain = exports.getCookieName = void 0;
const logger_1 = require("../lib/logger");
const environment_1 = require("../config/environment");
const domain_1 = require("./domain");
/**
 * Get environment-specific cookie name
 * @param baseName Base cookie name
 * @returns Environment-specific cookie name
 */
const getCookieName = (baseName) => {
    const env = (0, environment_1.getEnvironment)();
    // For production, use the base name
    if (env === 'production') {
        return baseName;
    }
    // For other environments, prefix with environment
    return `${env}_${baseName}`;
};
exports.getCookieName = getCookieName;
/**
 * Get environment-specific cookie domain
 * @returns Environment-specific cookie domain
 */
const getCookieDomain = () => {
    const env = (0, environment_1.getEnvironment)();
    const baseDomain = (0, domain_1.getSiteDomain)();
    // For production, use the base domain
    if (env === 'production') {
        return baseDomain;
    }
    // For demo, use demo subdomain
    if (env === 'demo') {
        return `demo.${baseDomain}`;
    }
    // For development, use localhost
    return '';
};
exports.getCookieDomain = getCookieDomain;
/**
 * Get environment-specific cookie options
 * @param options Additional cookie options
 * @returns Environment-specific cookie options
 */
const getCookieOptions = (options = {}) => {
    const env = (0, environment_1.getEnvironment)();
    const domain = (0, exports.getCookieDomain)();
    // Base options
    const baseOptions = {
        path: '/',
        httpOnly: true,
        secure: env !== 'development', // Secure in all environments except development
        sameSite: 'strict',
    };
    // Add domain if not empty
    if (domain) {
        baseOptions.domain = domain;
    }
    // Merge with provided options
    return {
        ...baseOptions,
        ...options,
    };
};
exports.getCookieOptions = getCookieOptions;
/**
 * Set environment-specific cookie
 * @param res Express response
 * @param baseName Base cookie name
 * @param value Cookie value
 * @param options Additional cookie options
 */
const setCookie = (res, baseName, value, options = {}) => {
    const cookieName = (0, exports.getCookieName)(baseName);
    const cookieOptions = (0, exports.getCookieOptions)(options);
    res.cookie(cookieName, value, cookieOptions);
    logger_1.logger.debug(`Set cookie: ${cookieName}`, {
        environment: (0, environment_1.getEnvironment)(),
        domain: cookieOptions.domain,
        secure: cookieOptions.secure,
        sameSite: cookieOptions.sameSite,
    });
};
exports.setCookie = setCookie;
/**
 * Get environment-specific cookie
 * @param req Express request
 * @param baseName Base cookie name
 * @returns Cookie value or undefined
 */
const getCookie = (req, baseName) => {
    const cookieName = (0, exports.getCookieName)(baseName);
    return req.cookies[cookieName];
};
exports.getCookie = getCookie;
/**
 * Clear environment-specific cookie
 * @param res Express response
 * @param baseName Base cookie name
 * @param options Additional cookie options
 */
const clearCookie = (res, baseName, options = {}) => {
    const cookieName = (0, exports.getCookieName)(baseName);
    const cookieOptions = (0, exports.getCookieOptions)(options);
    res.clearCookie(cookieName, cookieOptions);
    logger_1.logger.debug(`Cleared cookie: ${cookieName}`, {
        environment: (0, environment_1.getEnvironment)(),
        domain: cookieOptions.domain,
    });
};
exports.clearCookie = clearCookie;
exports.default = {
    getCookieName: exports.getCookieName,
    getCookieDomain: exports.getCookieDomain,
    getCookieOptions: exports.getCookieOptions,
    setCookie: exports.setCookie,
    getCookie: exports.getCookie,
    clearCookie: exports.clearCookie,
};
