/**
 * Test Utility Library
 *
 * This file provides utility functions for tests to eliminate duplication in test files.
 */
import { PrismaClient } from '@prisma/client';
import { Request, Response } from 'express';
import { BaseController } from '../../core/BaseController';
import { BaseService } from '../../core/BaseService';
import { BaseRepository } from '../../core/BaseRepository';
/**
 * Mock request
 */
export interface MockRequest extends Request {
    params?: any;
    query?: any;
    body?: any;
    headers?: any;
    user?: any;
}
/**
 * Mock response
 */
export interface MockResponse extends Response {
    status?: jest.Mock;
    json?: jest.Mock;
    send?: jest.Mock;
    end?: jest.Mock;
    locals?: any;
}
/**
 * Test options for controllers
 */
export interface ControllerTestOptions {
    req?: MockRequest;
    res?: MockResponse;
    next?: jest.Mock;
    expectedStatus?: number;
    expectedResponse?: any;
    expectedError?: any;
    description?: string;
    setup?: (controller: BaseController) => void;
    cleanup?: (controller: BaseController) => void;
    beforeEach?: () => void;
    afterEach?: () => void;
    timeout?: number;
    skip?: boolean;
    only?: boolean;
}
/**
 * Test options for services
 */
export interface ServiceTestOptions {
    args?: any[];
    expectedResult?: any;
    expectedError?: any;
    description?: string;
    setup?: (service: BaseService) => void;
    cleanup?: (service: BaseService) => void;
    beforeEach?: () => void;
    afterEach?: () => void;
    timeout?: number;
    skip?: boolean;
    only?: boolean;
    mockDependencies?: Record<string, any>;
}
/**
 * Test options for repositories
 */
export interface RepositoryTestOptions extends ServiceTestOptions {
    mockPrisma?: PrismaClient;
    mockTransaction?: boolean;
    mockTransactionResult?: any;
}
/**
 * Test options for middleware
 */
export interface MiddlewareTestOptions {
    req?: MockRequest;
    res?: MockResponse;
    next?: jest.Mock;
    expectedStatus?: number;
    expectedResponse?: any;
    expectedError?: any;
    description?: string;
    setup?: (req: MockRequest, res: MockResponse, next: jest.Mock) => void;
    cleanup?: (req: MockRequest, res: MockResponse, next: jest.Mock) => void;
    beforeEach?: () => void;
    afterEach?: () => void;
    timeout?: number;
    skip?: boolean;
    only?: boolean;
}
/**
 * Test options for validators
 */
export interface ValidatorTestOptions {
    req?: MockRequest;
    res?: MockResponse;
    next?: jest.Mock;
    expectedStatus?: number;
    expectedResponse?: any;
    expectedError?: any;
    description?: string;
    setup?: () => void;
    cleanup?: () => void;
    beforeEach?: () => void;
    afterEach?: () => void;
    timeout?: number;
    skip?: boolean;
    only?: boolean;
    args?: any[];
}
/**
 * Test options for utilities
 */
export interface UtilityTestOptions {
    args?: any[];
    expectedResult?: any;
    expectedError?: any;
    description?: string;
    setup?: () => void;
    cleanup?: () => void;
    beforeEach?: () => void;
    afterEach?: () => void;
    timeout?: number;
    skip?: boolean;
    only?: boolean;
}
/**
 * Create a mock request
 * @param options Request options
 * @returns Mock request
 */
export declare function createMockRequest(options?: {
    params?: any;
    query?: any;
    body?: any;
    headers?: any;
    user?: any;
}): MockRequest;
/**
 * Create a mock response
 * @returns Mock response
 */
export declare function createMockResponse(): MockResponse;
/**
 * Create a mock next function
 * @returns Mock next function
 */
export declare function createMockNext(): jest.Mock;
/**
 * Create a mock Prisma client
 * @returns Mock Prisma client
 */
export declare function createMockPrismaClient(): PrismaClient;
/**
 * Test controller
 * @param controller Controller to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
export declare function testController<T extends BaseController>(controller: T, method: keyof T, options?: ControllerTestOptions): Promise<{
    req: MockRequest;
    res: MockResponse;
    next: jest.Mock;
}>;
/**
 * Test service
 * @param service Service to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
export declare function testService<T extends BaseService>(service: T, method: keyof T, options?: ServiceTestOptions): Promise<any>;
/**
 * Test repository
 * @param repository Repository to test
 * @param method Method to test
 * @param options Test options
 * @returns Test result
 */
export declare function testRepository<T extends BaseRepository>(repository: T, method: keyof T, options?: RepositoryTestOptions): Promise<any>;
/**
 * Create a test suite for a controller
 * @param name Test suite name
 * @param controllerClass Controller class
 * @param tests Test definitions
 */
export declare function testControllerSuite(name: string, controllerClass: new () => BaseController, tests: {
    [method: string]: ControllerTestOptions;
}): void;
/**
 * Create a test suite for a service
 * @param name Test suite name
 * @param serviceClass Service class
 * @param tests Test definitions
 * @param setupFn Setup function
 */
export declare function testServiceSuite(name: string, serviceClass: new () => BaseService, tests: {
    [method: string]: ServiceTestOptions;
}, setupFn?: (service: BaseService) => void): void;
/**
 * Create a test suite for a repository
 * @param name Test suite name
 * @param repositoryClass Repository class
 * @param tests Test definitions
 */
export declare function testRepositorySuite(name: string, repositoryClass: new () => BaseRepository, tests: {
    [method: string]: RepositoryTestOptions;
}): void;
/**
 * Test middleware
 * @param middleware Middleware function
 * @param options Test options
 * @returns Test result
 */
export declare function testMiddleware(middleware: Function, options?: MiddlewareTestOptions): Promise<{
    req: MockRequest;
    res: MockResponse;
    next: jest.Mock;
}>;
/**
 * Test validator
 * @param validator Validator function
 * @param options Test options
 * @returns Test result
 */
export declare function testValidator(validator: Function, options?: ValidatorTestOptions): Promise<any>;
/**
 * Test utility function
 * @param utility Utility function
 * @param options Test options
 * @returns Test result
 */
export declare function testUtility(utility: Function, options?: UtilityTestOptions): Promise<any>;
/**
 * Create a test suite for middleware
 * @param name Test suite name
 * @param middleware Middleware function
 * @param tests Test definitions
 */
export declare function testMiddlewareSuite(name: string, middleware: Function, tests: {
    [testName: string]: MiddlewareTestOptions;
}): void;
/**
 * Create a test suite for validators
 * @param name Test suite name
 * @param validator Validator function
 * @param tests Test definitions
 */
export declare function testValidatorSuite(name: string, validator: Function, tests: {
    [testName: string]: ValidatorTestOptions;
}): void;
/**
 * Create a test suite for utilities
 * @param name Test suite name
 * @param utility Utility function
 * @param tests Test definitions
 */
export declare function testUtilitySuite(name: string, utility: Function, tests: {
    [testName: string]: UtilityTestOptions;
}): void;
/**
 * Create a complete test suite for a module
 * @param name Module name
 * @param options Test options
 */
export declare function testModule(name: string, options: {
    controllerClass?: new () => BaseController;
    serviceClass?: new () => BaseService;
    repositoryClass?: new () => BaseRepository;
    middleware?: Function;
    validators?: {
        [name: string]: Function;
    };
    utilities?: {
        [name: string]: Function;
    };
    controllerTests?: {
        [method: string]: ControllerTestOptions;
    };
    serviceTests?: {
        [method: string]: ServiceTestOptions;
    };
    repositoryTests?: {
        [method: string]: RepositoryTestOptions;
    };
    middlewareTests?: {
        [testName: string]: MiddlewareTestOptions;
    };
    validatorTests?: {
        [validatorName: string]: {
            [testName: string]: ValidatorTestOptions;
        };
    };
    utilityTests?: {
        [utilityName: string]: {
            [testName: string]: UtilityTestOptions;
        };
    };
    setupServiceFn?: (service: BaseService) => void;
}): void;
//# sourceMappingURL=TestUtility.d.ts.map