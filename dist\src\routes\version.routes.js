"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_validator_1 = require("express-validator");
const version_controller_1 = require("../controllers/refactored/version.controller");
const RouteProvider_1 = __importDefault(require("../core/RouteProvider"));
// Create version controller
const versionController = new version_controller_1.VersionController();
// Create a route builder for version routes
const routeBuilder = RouteProvider_1.default.createRouteBuilder("version", "/api/versions", "API version management routes")
    .tags("version", "management");
// Add routes
routeBuilder
    // Get all versions
    .get("/", versionController.getAllVersions)
    // Get current version
    .get("/current", versionController.getCurrentVersion)
    // Get active versions
    .get("/active", versionController.getActiveVersions)
    // Get deprecated versions
    .get("/deprecated", versionController.getDeprecatedVersions)
    // Get version by name
    .get("/:version", versionController.getVersionByName, [], [(0, express_validator_1.param)("version").notEmpty()])
    // Register a new version
    .post("/", versionController.registerVersion, ["ADMIN"], [
    (0, express_validator_1.body)("version").notEmpty(),
    (0, express_validator_1.body)("status").notEmpty(),
    (0, express_validator_1.body)("releaseDate").notEmpty().isISO8601()
])
    // Update version status
    .put("/:version/status", versionController.updateVersionStatus, ["ADMIN"], [
    (0, express_validator_1.param)("version").notEmpty(),
    (0, express_validator_1.body)("status").notEmpty()
])
    // Set current version
    .post("/current", versionController.setCurrentVersion, ["ADMIN"], [
    (0, express_validator_1.body)("version").notEmpty()
]);
// Build the router
const router = routeBuilder.build();
exports.default = router;
//# sourceMappingURL=version.routes.js.map