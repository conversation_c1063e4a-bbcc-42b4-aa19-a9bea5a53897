"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = require("../lib/logger");
const prisma_1 = __importDefault(require("../lib/prisma"));
const utils_1 = require("../utils");
const two_factor_auth_service_1 = __importDefault(require("./two-factor-auth.service"));
class AuthService {
    async login(email, password) {
        try {
            // Find user by email
            const user = await prisma_1.default.user.findUnique({
                where: { email },
            });
            if (!user) {
                throw new Error('Invalid credentials');
            }
            // Check if user is active
            if (!user.isActive) {
                throw new Error('User account is inactive');
            }
            // Verify password
            const isPasswordValid = await utils_1.CryptoUtils.verifyPassword(password, user.hashedPassword, user.salt || '');
            if (!isPasswordValid) {
                throw new Error('Invalid credentials');
            }
            // Update last login timestamp
            await prisma_1.default.user.update({
                where: { id: user.id },
                data: { lastLoginAt: new Date() },
            });
            // Generate JWT token
            const token = jsonwebtoken_1.default.sign({ userId: user.id, role: user.role }, process.env.JWT_SECRET || 'amazingpay_jwt_secret', { expiresIn: process.env.JWT_EXPIRES_IN || '1d' });
            // Get merchant ID if user is a merchant
            let merchantId;
            if (user.role === 'MERCHANT') {
                const merchant = await prisma_1.default.merchant.findFirst({
                    where: { userId: user.id },
                });
                merchantId = merchant?.id;
            }
            return {
                token,
                user: {
                    id: user.id,
                    email: user.email,
                    role: user.role,
                    twoFactorEnabled: user.twoFactorEnabled || false,
                },
                merchantId,
            };
        }
        catch (error) {
            logger_1.logger.error(`Login error: ${error}`);
            throw error;
        }
    }
    // Logout a user
    async logout(userId) {
        try {
            // In a more sophisticated implementation, we would add the token to a blacklist
            // For now, we'll just return success
            logger_1.logger.info(`User ${userId} logged out`);
            return { success: true };
        }
        catch (error) {
            logger_1.logger.error(`Logout error: ${error}`);
            throw error;
        }
    }
    // Get user by ID
    async getUserById(userId) {
        try {
            const user = await prisma_1.default.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new Error('User not found');
            }
            // Return user without sensitive information
            return {
                id: user.id,
                email: user.email,
                role: user.role,
                isActive: user.isActive,
                lastLoginAt: user.lastLoginAt,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            };
        }
        catch (error) {
            logger_1.logger.error(`Get user by ID error: ${error}`);
            throw error;
        }
    }
    // Two-factor authentication methods
    async getTwoFactorStatus(userId) {
        try {
            return await two_factor_auth_service_1.default.getTwoFactorStatus(userId);
        }
        catch (error) {
            logger_1.logger.error(`Get 2FA status error: ${error}`);
            throw error;
        }
    }
    async setupTwoFactor(userId) {
        try {
            return await two_factor_auth_service_1.default.setupTwoFactor(userId);
        }
        catch (error) {
            logger_1.logger.error(`Setup 2FA error: ${error}`);
            throw error;
        }
    }
    async verifyAndEnableTwoFactor(userId, code, secret) {
        try {
            return await two_factor_auth_service_1.default.verifyAndEnableTwoFactor(userId, code, secret);
        }
        catch (error) {
            logger_1.logger.error(`Enable 2FA error: ${error}`);
            throw error;
        }
    }
    async disableTwoFactor(userId, code) {
        try {
            return await two_factor_auth_service_1.default.disableTwoFactor(userId, code);
        }
        catch (error) {
            logger_1.logger.error(`Disable 2FA error: ${error}`);
            throw error;
        }
    }
    /**
     * Verify a two-factor authentication token
     * @param userId User ID
     * @param token Two-factor authentication token
     * @returns Success status
     */
    async verifyTwoFactorToken(userId, token) {
        try {
            return await two_factor_auth_service_1.default.verifyToken(userId, token);
        }
        catch (error) {
            logger_1.logger.error(`Verify 2FA token error: ${error}`);
            throw error;
        }
    }
    async registerMerchant(data) {
        try {
            // Check if merchant already exists
            const existingMerchant = await prisma_1.default.merchant.findFirst({
                where: { email: data.email },
            });
            if (existingMerchant) {
                throw new Error('Merchant with this email already exists');
            }
            // Hash password
            const { hash: hashedPassword, salt } = await utils_1.CryptoUtils.hashPassword(data.password);
            // Generate API key and secret
            const apiKey = utils_1.CryptoUtils.generateUuid();
            const apiSecret = utils_1.CryptoUtils.generateUuid();
            // Create a user first, then associate merchant
            logger_1.logger.info('Creating user and merchant with proper association');
            // Create a user first
            const user = await prisma_1.default.user.create({
                data: {
                    email: data.email,
                    hashedPassword: hashedPassword,
                    salt: salt,
                    role: 'MERCHANT',
                    isActive: true,
                },
            });
            logger_1.logger.info(`Created user with ID: ${user.id}`);
            // Then create merchant with user association
            const merchant = await prisma_1.default.merchant.create({
                data: {
                    name: data.name,
                    email: data.email,
                    businessName: data.businessName || null,
                    contactPhone: data.contactPhone,
                    merchantLocation: data.merchantLocation,
                    country: data.country,
                    governorate: data.governorate,
                    apiKey,
                    apiSecret,
                    isActive: false, // Requires admin approval
                    isVerified: false, // Requires verification
                    user: { connect: { id: user.id } },
                },
            });
            logger_1.logger.info(`Successfully created merchant with ID: ${merchant.id}`);
            // Generate JWT token
            const token = jsonwebtoken_1.default.sign({ merchantId: merchant.id, email: merchant.email }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: process.env.JWT_EXPIRES_IN || '1d' });
            // Return merchant data (excluding sensitive fields)
            return {
                merchant: {
                    id: merchant.id,
                    name: merchant.name,
                    email: merchant.email,
                    businessName: merchant.businessName,
                    contactPhone: merchant.contactPhone,
                    merchantLocation: merchant.merchantLocation,
                    country: merchant.country,
                    governorate: merchant.governorate,
                    isActive: merchant.isActive,
                    isVerified: merchant.isVerified,
                    createdAt: merchant.createdAt,
                },
                token,
            };
        }
        catch (error) {
            logger_1.logger.error(`Merchant registration error: ${error}`);
            throw error;
        }
    }
}
exports.default = new AuthService();
