"use strict";
// jscpd:ignore-file
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const verification_controller_1 = __importDefault(require("../controllers/verification.controller"));
const validation_middleware_1 = require("../middlewares/validation.middleware");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
// Public route to verify a payment
// This is used by payment pages to trigger verification
router.post("/", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("merchantId").notEmpty(),
    (0, express_validator_1.body)("amount").isNumeric(),
    (0, express_validator_1.body)("currency").notEmpty(),
    (0, express_validator_1.body)("paymentMethodId").notEmpty()
]), verification_controller_1.default.verifyPayment);
// Route to verify a payment by ID using the unified verification service
// This is used by the admin dashboard and merchant dashboard
router.get("/payment/:paymentId", auth_middleware_1.authenticate, (0, validation_middleware_1.validate)([
    (0, express_validator_1.param)("paymentId").notEmpty().withMessage("Payment ID is required")
]), verification_controller_1.default.verifyPaymentById);
// Route to process webhooks from payment providers
router.post("/webhook", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("provider").notEmpty().isString(),
    (0, express_validator_1.body)("payload").notEmpty().isObject(),
    (0, express_validator_1.body)("signature").optional().isString()
]), verification_controller_1.default.processWebhook);
exports.default = router;
//# sourceMappingURL=verify.routes.js.map