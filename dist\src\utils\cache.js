"use strict";
// jscpd:ignore-file
/**
 * Cache Utility
 *
 * This utility provides functions for caching API responses and other data.
 * It uses Redis if available, otherwise falls back to an in-memory store.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeCache = exports.invalidateCacheMiddleware = exports.cacheMiddleware = exports.generateCacheKey = exports.getStats = exports.clear = exports.del = exports.setSync = exports.set = exports.getSync = exports.get = void 0;
const node_cache_1 = __importDefault(require("node-cache"));
const logger_1 = require("../lib/logger");
const redis_manager_1 = __importDefault(require("../lib/redis-manager"));
// Get environment-specific cache prefix
const getEnvironmentPrefix = () => {
    const env = process.env.NODE_ENV || 'development';
    return `${env}:`;
};
// Create a cache instance
const cache = new node_cache_1.default({
    stdTTL: parseInt(process.env.CACHE_TTL || '300', 10), // Default TTL: 5 minutes
    checkperiod: 120, // Check for expired keys every 2 minutes
    useClones: false, // Don't clone objects (for performance)
    deleteOnExpire: true, // Delete expired keys
});
// Cache statistics
const cacheStats = {
    hits: 0,
    misses: 0,
    keys: 0,
    ksize: 0,
    vsize: 0,
};
/**
 * Get a value from the cache
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
const get = async (key) => {
    // Add environment prefix to key
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    try {
        // Try to get from Redis first
        if (redis_manager_1.default.isRedisEnabled()) {
            const redisValue = await redis_manager_1.default.get(prefixedKey);
            if (redisValue) {
                cacheStats.hits++;
                return JSON.parse(redisValue);
            }
        }
        // Fall back to local cache
        const value = cache.get(prefixedKey);
        if (value === undefined) {
            cacheStats.misses++;
            return undefined;
        }
        cacheStats.hits++;
        return value;
    }
    catch (error) {
        logger_1.logger.error('Error getting value from cache:', error);
        cacheStats.misses++;
        return undefined;
    }
};
exports.get = get;
/**
 * Get a value from the cache synchronously (local cache only)
 * @param key Cache key
 * @returns Cached value or undefined if not found
 */
const getSync = (key) => {
    // Add environment prefix to key
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    const value = cache.get(prefixedKey);
    if (value === undefined) {
        cacheStats.misses++;
        return undefined;
    }
    cacheStats.hits++;
    return value;
};
exports.getSync = getSync;
/**
 * Set a value in the cache
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
const set = async (key, value, ttl) => {
    // Add environment prefix to key
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    try {
        // Set in local cache
        const success = cache.set(prefixedKey, value, ttl);
        // Also set in Redis if available
        if (redis_manager_1.default.isRedisEnabled()) {
            await redis_manager_1.default.set(prefixedKey, JSON.stringify(value), ttl);
        }
        if (success) {
            // Update cache statistics
            const stats = cache.getStats();
            cacheStats.keys = stats.keys;
            cacheStats.ksize = stats.ksize;
            cacheStats.vsize = stats.vsize;
        }
        return success;
    }
    catch (error) {
        logger_1.logger.error('Error setting value in cache:', error);
        return false;
    }
};
exports.set = set;
/**
 * Set a value in the cache synchronously (local cache only)
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 * @returns Whether the value was set successfully
 */
const setSync = (key, value, ttl) => {
    // Add environment prefix to key
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    const success = cache.set(prefixedKey, value, ttl);
    if (success) {
        // Update cache statistics
        const stats = cache.getStats();
        cacheStats.keys = stats.keys;
        cacheStats.ksize = stats.ksize;
        cacheStats.vsize = stats.vsize;
    }
    return success;
};
exports.setSync = setSync;
/**
 * Delete a value from the cache
 * @param key Cache key
 * @returns Whether the value was deleted successfully
 */
const del = async (key) => {
    // Add environment prefix to key
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    try {
        // Delete from local cache
        const deleted = cache.del(prefixedKey);
        // Also delete from Redis if available
        if (redis_manager_1.default.isRedisEnabled()) {
            await redis_manager_1.default.del(prefixedKey);
        }
        if (deleted > 0) {
            // Update cache statistics
            const stats = cache.getStats();
            cacheStats.keys = stats.keys;
            cacheStats.ksize = stats.ksize;
            cacheStats.vsize = stats.vsize;
        }
        return deleted > 0;
    }
    catch (error) {
        logger_1.logger.error('Error deleting value from cache:', error);
        return false;
    }
};
exports.del = del;
/**
 * Clear the entire cache
 */
const clear = async () => {
    const prefix = getEnvironmentPrefix();
    try {
        // Clear local cache
        const keys = cache.keys();
        const environmentKeys = keys.filter(key => key.startsWith(prefix));
        if (environmentKeys.length > 0) {
            cache.del(environmentKeys);
        }
        // Clear Redis cache if available
        if (redis_manager_1.default.isRedisEnabled()) {
            const redisClient = redis_manager_1.default.getClient();
            if (redisClient) {
                // Get all keys with the environment prefix
                const redisKeys = await redisClient.keys(`${prefix}*`);
                if (redisKeys.length > 0) {
                    await redisClient.del(redisKeys);
                    logger_1.logger.info(`Cleared ${redisKeys.length} keys from Redis cache`);
                }
            }
        }
        // Reset cache statistics
        cacheStats.hits = 0;
        cacheStats.misses = 0;
        cacheStats.keys = 0;
        cacheStats.ksize = 0;
        cacheStats.vsize = 0;
        logger_1.logger.info(`Cache cleared for environment: ${process.env.NODE_ENV || 'development'}`);
    }
    catch (error) {
        logger_1.logger.error('Error clearing cache:', error);
    }
};
exports.clear = clear;
/**
 * Get cache statistics
 * @returns Cache statistics
 */
const getStats = () => {
    return { ...cacheStats };
};
exports.getStats = getStats;
/**
 * Generate a cache key from a request
 * @param req Express request
 * @returns Cache key
 */
const generateCacheKey = (req) => {
    // Create a key based on the request method, path, and query parameters
    const method = req.method;
    const path = req.path;
    const query = req.query ? JSON.stringify(req.query) : '';
    const userId = req.user?.id; // Fixed: using id instead of userId || 'anonymous';
    // No need to add environment prefix here as it will be added by the get/set methods
    return `${method}:${path}:${query}:${userId}`;
};
exports.generateCacheKey = generateCacheKey;
/**
 * Middleware to cache API responses
 * @param ttl Time to live in seconds (optional)
 * @returns Express middleware
 */
const cacheMiddleware = (ttl) => {
    return async (req, res, next) => {
        // Skip caching for non-GET requests
        if (req.method !== 'GET') {
            return next();
        }
        // Generate cache key
        const key = (0, exports.generateCacheKey)(req);
        try {
            // Check if response is in cache
            const cachedResponse = await (0, exports.get)(key);
            if (cachedResponse) {
                // Set headers from cached response
                Object.entries(cachedResponse.headers).forEach((([name, value])));
            }
        }
        finally { }
    };
};
exports.cacheMiddleware = cacheMiddleware;
{
    res.setHeader(name, value);
}
;
// Add cache header
res.setHeader('X-Cache', 'HIT');
// Send cached response
return res.status(cachedResponse.statusCode).json(cachedResponse.body);
// Add cache header
res.setHeader('X-Cache', 'MISS');
// Store original res.json method
const originalJson = res.json;
// Override res.json method to cache the response
res.json = function (body) {
    // Only cache successful responses
    if (res.statusCode >= 200 && res.statusCode < 300) {
        // Get relevant headers to cache
        const headers = {};
        const headersToCache = ['content-type', 'content-language', 'content-encoding', 'cache-control'];
        headersToCache.forEach((header));
        {
            const value = res.getHeader(header);
            if (value) {
                headers[header] = value.toString();
            }
        }
        ;
        // Cache the response (don't await to avoid blocking)
        (0, exports.set)(key, {
            statusCode: res.statusCode,
            body,
            headers
        }, ttl).catch((error) => {
            logger_1.logger.error('Error caching response:', error);
        });
    }
    // Call original res.json method
    return originalJson.call(this, body);
};
next();
try { }
catch (error) {
    logger_1.logger.error('Error in cache middleware:', error);
    next();
}
;
;
/**
 * Middleware to invalidate cache for specific routes
 * @param patterns Array of route patterns to match (e.g., '/api/users')
 * @returns Express middleware
 */
const invalidateCacheMiddleware = (patterns = []) => {
    return (req, res, next) => {
        // Only invalidate cache for non-GET requests
        if (req.method === 'GET') {
            return next();
        }
        // Store original res.end method
        const originalEnd = res.end;
        // Override res.end method to invalidate cache after response is sent
        res.end = function (...args) {
            // Only invalidate cache for successful responses
            if (res.statusCode >= 200 && res.statusCode < 300) {
                // Check if the request path matches any of the patterns
                const shouldInvalidate = patterns.length === 0 || patterns.some(pattern);
            }
        };
    };
};
exports.invalidateCacheMiddleware = invalidateCacheMiddleware;
{
    return req.path.startsWith(pattern) || req.path === pattern;
}
;
if (shouldInvalidate) {
    // Get all cache keys from local cache
    const keys = cache.keys();
    const prefix = getEnvironmentPrefix();
    // Find keys that match the patterns and belong to the current environment
    const keysToInvalidate = keys.filter((key));
    {
        // Only consider keys for the current environment
        if (!key.startsWith(prefix))
            return false;
        // Remove the prefix for pattern matching
        const unprefixedKey = key.substring(prefix.length);
        return patterns.some(pattern => unprefixedKey.includes(pattern));
    }
    ;
    // Invalidate matching keys in local cache
    if (keysToInvalidate.length > 0) {
        keysToInvalidate.forEach((key));
        {
            // Use delSync to avoid blocking
            cache.del(key);
        }
        ;
        logger_1.logger.debug(`Invalidated ${keysToInvalidate.length} local cache entries`, {
            path: req.path,
            method: req.method,
            patterns
        });
    }
    // Invalidate matching keys in Redis if available
    if (redis_manager_1.default.isRedisEnabled()) {
        const redisClient = redis_manager_1.default.getClient();
        if (redisClient) {
            // Use a pattern to match keys in Redis
            const patternPromises = patterns.map((async(pattern)));
            {
                try {
                    const redisPattern = `${prefix}*${pattern}*`;
                    const redisKeys = await redisClient.keys(redisPattern);
                    if (redisKeys.length > 0) {
                        await redisClient.del(redisKeys);
                        logger_1.logger.debug(`Invalidated ${redisKeys.length} Redis cache entries for pattern ${pattern}`, {
                            path: req.path,
                            method: req.method
                        });
                    }
                }
                catch (error) {
                    logger_1.logger.error(`Error invalidating Redis cache for pattern ${pattern}:`, error);
                }
            }
            ;
            // Execute all pattern invalidations (don't await to avoid blocking)
            Promise.all(patternPromises).catch((error) => {
                logger_1.logger.error('Error invalidating Redis cache:', error);
            });
        }
    }
}
// Call original res.end method
return originalEnd.apply(this, args);
;
next();
;
;
/**
 * Initialize cache
 */
const initializeCache = async () => {
    // Set up cache events
    cache.on('expired', (key, value) => {
        logger_1.logger.debug(`Cache key expired: ${key}`);
    });
    cache.on('flush', () => {
        logger_1.logger.info('Cache flushed');
    });
    // Check Redis connection
    const redisEnabled = redis_manager_1.default.isRedisEnabled();
    const redisClient = redis_manager_1.default.getClient();
    // Log initialization
    logger_1.logger.info('Cache initialized', {
        environment: process.env.NODE_ENV || 'development',
        prefix: getEnvironmentPrefix(),
        defaultTTL: cache.options.stdTTL,
        checkPeriod: cache.options.checkperiod,
        redisEnabled,
        storageType: redisEnabled ? 'Redis + Local' : 'Local only'
    });
    // Test Redis connection if available
    if (redisEnabled && redisClient) {
        try {
            await redisClient.ping();
            logger_1.logger.info('Redis cache connection test successful');
        }
        catch (error) {
            logger_1.logger.error('Redis cache connection test failed:', error);
        }
    }
};
exports.initializeCache = initializeCache;
exports.default = {
    get: exports.get,
    getSync: exports.getSync,
    set: exports.set,
    setSync: exports.setSync,
    del: exports.del,
    clear: exports.clear,
    getStats: exports.getStats,
    generateCacheKey: exports.generateCacheKey,
    cacheMiddleware: exports.cacheMiddleware,
    invalidateCacheMiddleware: exports.invalidateCacheMiddleware,
    initializeCache: exports.initializeCache
};
//# sourceMappingURL=cache.js.map