/**
 * Binance TRC20 Payment Method
 *
 * Implements the payment method for Binance TRC20 payments.
 */
import { IPaymentMethod, PaymentRequest, PaymentResult } from '../../../interfaces/payment/IPaymentMethod';
import { PaymentMethodType } from '../../../types/payment-method.types';
import { PaymentField } from '../../../types/payment-field.types';
/**
 * Binance TRC20 payment method
 */
export declare class BinanceTRC20PaymentMethod implements IPaymentMethod {
    private enabled;
    private configuration;
    /**
     * Get the payment method type
     * @returns Payment method type
     */
    getType(): PaymentMethodType;
    /**
     * Process a payment
     * @param request Payment request
     * @returns Payment result
     */
    processPayment(request: PaymentRequest): Promise<PaymentResult>;
    /**
     * Get the required fields for payment
     * @returns Array of payment fields
     */
    getRequiredFields(): PaymentField[];
    /**
     * Get the display name of the payment method
     * @returns Display name
     */
    getDisplayName(): string;
    /**
     * Get the description of the payment method
     * @returns Description
     */
    getDescription(): string;
    /**
     * Check if the payment method is enabled
     * @returns Whether the payment method is enabled
     */
    isEnabled(): boolean;
    /**
     * Get the configuration of the payment method
     * @returns Configuration object
     */
    getConfiguration(): Record<string, any>;
    /**
     * Set the configuration of the payment method
     * @param config Configuration object
     */
    setConfiguration(config: Record<string, any>): void;
    /**
     * Get the supported currencies
     * @returns Array of supported currencies
     */
    getSupportedCurrencies(): string[];
    /**
     * Get the minimum transaction amount
     * @returns Minimum amount
     */
    getMinimumAmount(): number;
    /**
     * Get the maximum transaction amount
     * @returns Maximum amount
     */
    getMaximumAmount(): number;
    /**
     * Get the transaction fee
     * @param amount Transaction amount
     * @param currency Transaction currency
     * @returns Transaction fee
     */
    getTransactionFee(amount: number, currency: string): number;
    /**
     * Get the payment method icon
     * @returns Icon path
     */
    getIcon(): string;
    /**
     * Validate payment data
     * @param data Payment data
     * @returns Validation result
     */
    validatePaymentData(data: Record<string, any>): {
        valid: boolean;
        errors?: string[];
    };
    /**
     * Validate TRC20 address format
     * @param address TRC20 wallet address
     * @returns Whether the address is valid
     */
    private isValidTRC20Address;
}
//# sourceMappingURL=BinanceTRC20PaymentMethod.d.ts.map