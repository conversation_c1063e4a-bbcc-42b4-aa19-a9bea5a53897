import { AdvancedReportService } from '../services/advanced-report.service';
import { PrismaClient } from '@prisma/client';

// Mock Prisma
jest.mock('@prisma/client');
const mockPrisma = {
  transaction: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  customer: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  paymentMethod: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  subscription: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  merchant: {
    findFirst: jest.fn(),
  },
  savedReport: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    delete: jest.fn(),
  },
  reportTemplate: {
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
  scheduledReport: {
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
};

// Mock file system
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true),
  mkdirSync: jest.fn(),
  writeFileSync: jest.fn(),
  unlinkSync: jest.fn(),
  createWriteStream: jest.fn(() => ({
    on: jest.fn(),
  })),
  statSync: jest.fn(() => ({ size: 1024 })),
}));

// Mock external libraries
jest.mock('json2csv', () => ({
  Parser: jest.fn(() => ({
    parse: jest.fn(() => 'csv,data'),
  })),
}));

jest.mock('pdfkit', () => ({
  default: jest.fn(() => ({
    fontSize: jest.fn().mockReturnThis(),
    text: jest.fn().mockReturnThis(),
    moveDown: jest.fn().mockReturnThis(),
    font: jest.fn().mockReturnThis(),
    pipe: jest.fn(),
    end: jest.fn(),
    page: { width: 600, height: 800 },
    y: 100,
    addPage: jest.fn(),
  })),
}));

jest.mock('exceljs', () => ({
  Workbook: jest.fn(() => ({
    addWorksheet: jest.fn(() => ({
      addRow: jest.fn(),
      getRow: jest.fn(() => ({ font: {} })),
      columns: [],
    })),
    xlsx: {
      writeFile: jest.fn(),
    },
  })),
}));

jest.mock('nodemailer', () => ({
  createTransporter: jest.fn(() => ({
    sendMail: jest.fn(),
  })),
}));

describe('AdvancedReportService', () => {
  let reportService: AdvancedReportService;

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the prisma import
    jest.doMock('../index', () => ({
      prisma: mockPrisma,
    }));
    reportService = new AdvancedReportService();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('generateReport', () => {
    it('should generate a transaction report', async () => {
      const mockTransactions = [
        {
          id: '1',
          reference: 'TXN001',
          amount: 100,
          currency: 'USD',
          status: 'COMPLETED',
          paymentMethod: 'CARD',
          merchant: { businessName: 'Test Merchant' },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrisma.transaction.findMany.mockResolvedValue(mockTransactions);
      mockPrisma.savedReport.create.mockResolvedValue({
        id: 'report-1',
        filePath: '/path/to/report.csv',
      });

      const result = await reportService.generateReport(
        'TRANSACTION',
        {
          userId: 'user-1',
          userRole: 'MERCHANT',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        },
        'CSV'
      );

      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('filePath');
      expect(mockPrisma.transaction.findMany).toHaveBeenCalled();
      expect(mockPrisma.savedReport.create).toHaveBeenCalled();
    });

    it('should generate a customer report', async () => {
      const mockCustomers = [
        {
          id: '1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          status: 'ACTIVE',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrisma.customer.findMany.mockResolvedValue(mockCustomers);
      mockPrisma.savedReport.create.mockResolvedValue({
        id: 'report-2',
        filePath: '/path/to/report.csv',
      });

      const result = await reportService.generateReport(
        'CUSTOMER',
        {
          userId: 'user-1',
          userRole: 'ADMIN',
        },
        'CSV'
      );

      expect(result).toHaveProperty('id');
      expect(mockPrisma.customer.findMany).toHaveBeenCalled();
    });

    it('should throw error for unsupported report type', async () => {
      await expect(
        reportService.generateReport(
          'INVALID_TYPE' as any,
          { userId: 'user-1', userRole: 'MERCHANT' },
          'CSV'
        )
      ).rejects.toThrow('Unsupported report type');
    });
  });

  describe('createReportTemplate', () => {
    it('should create a new report template', async () => {
      const templateData = {
        name: 'Test Template',
        description: 'Test Description',
        type: 'TRANSACTION',
        config: { columns: ['id', 'amount'] },
        createdById: 'user-1',
      };

      mockPrisma.reportTemplate.create.mockResolvedValue({
        id: 'template-1',
        ...templateData,
      });

      const result = await reportService.createReportTemplate(templateData);

      expect(result).toHaveProperty('id');
      expect(mockPrisma.reportTemplate.create).toHaveBeenCalledWith({
        data: templateData,
      });
    });
  });

  describe('createScheduledReport', () => {
    it('should create a scheduled report', async () => {
      const scheduledReportData = {
        name: 'Weekly Report',
        templateId: 'template-1',
        schedule: '0 0 * * 1',
        isActive: true,
        createdById: 'user-1',
      };

      mockPrisma.scheduledReport.create.mockResolvedValue({
        id: 'scheduled-1',
        ...scheduledReportData,
      });

      const result = await reportService.createScheduledReport(scheduledReportData);

      expect(result).toHaveProperty('id');
      expect(mockPrisma.scheduledReport.create).toHaveBeenCalledWith({
        data: scheduledReportData,
      });
    });
  });

  describe('getSavedReports', () => {
    it('should get saved reports for a user', async () => {
      const mockReports = [
        {
          id: 'report-1',
          name: 'Test Report',
          type: 'TRANSACTION',
          format: 'CSV',
          createdAt: new Date(),
        },
      ];

      mockPrisma.savedReport.findMany.mockResolvedValue(mockReports);

      const result = await reportService.getSavedReports('user-1');

      expect(result).toEqual(mockReports);
      expect(mockPrisma.savedReport.findMany).toHaveBeenCalledWith({
        where: { createdById: 'user-1' },
        include: { template: true },
        orderBy: { createdAt: 'desc' },
      });
    });
  });

  describe('deleteSavedReport', () => {
    it('should delete a saved report and its file', async () => {
      const mockReport = {
        id: 'report-1',
        filePath: '/path/to/report.csv',
      };

      mockPrisma.savedReport.findUnique.mockResolvedValue(mockReport);
      mockPrisma.savedReport.delete.mockResolvedValue(mockReport);

      const result = await reportService.deleteSavedReport('report-1');

      expect(result).toEqual(mockReport);
      expect(mockPrisma.savedReport.delete).toHaveBeenCalledWith({
        where: { id: 'report-1' },
      });
    });
  });

  describe('getReportTemplates', () => {
    it('should get report templates for a user including system templates', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'User Template',
          createdById: 'user-1',
          isSystem: false,
        },
        {
          id: 'template-2',
          name: 'System Template',
          createdById: 'admin-1',
          isSystem: true,
        },
      ];

      mockPrisma.reportTemplate.findMany.mockResolvedValue(mockTemplates);

      const result = await reportService.getReportTemplates('user-1', true);

      expect(result).toEqual(mockTemplates);
      expect(mockPrisma.reportTemplate.findMany).toHaveBeenCalledWith({
        where: {
          OR: [{ createdById: 'user-1' }, { isSystem: true }],
        },
        orderBy: { name: 'asc' },
      });
    });

    it('should get only user templates when includeSystem is false', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'User Template',
          createdById: 'user-1',
          isSystem: false,
        },
      ];

      mockPrisma.reportTemplate.findMany.mockResolvedValue(mockTemplates);

      const result = await reportService.getReportTemplates('user-1', false);

      expect(result).toEqual(mockTemplates);
      expect(mockPrisma.reportTemplate.findMany).toHaveBeenCalledWith({
        where: {
          OR: [{ createdById: 'user-1' }],
        },
        orderBy: { name: 'asc' },
      });
    });
  });

  describe('updateReportTemplate', () => {
    it('should update a report template', async () => {
      const updateData = {
        name: 'Updated Template',
        description: 'Updated Description',
      };

      const updatedTemplate = {
        id: 'template-1',
        ...updateData,
      };

      mockPrisma.reportTemplate.update.mockResolvedValue(updatedTemplate);

      const result = await reportService.updateReportTemplate('template-1', updateData);

      expect(result).toEqual(updatedTemplate);
      expect(mockPrisma.reportTemplate.update).toHaveBeenCalledWith({
        where: { id: 'template-1' },
        data: updateData,
      });
    });
  });

  describe('deleteReportTemplate', () => {
    it('should delete a report template', async () => {
      const deletedTemplate = {
        id: 'template-1',
        name: 'Deleted Template',
      };

      mockPrisma.reportTemplate.delete.mockResolvedValue(deletedTemplate);

      const result = await reportService.deleteReportTemplate('template-1');

      expect(result).toEqual(deletedTemplate);
      expect(mockPrisma.reportTemplate.delete).toHaveBeenCalledWith({
        where: { id: 'template-1' },
      });
    });
  });
});
