"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toContainAnyEntries = toContainAnyEntries;
const utils_1 = require("../utils");
function toContainAnyEntries(actual, expected) {
    // @ts-expect-error OK to have implicit any for this.utils
    const { printReceived, printExpected, matcherHint } = this.utils;
    let pass = false;
    if (typeof actual === 'object' && actual !== null && !Array.isArray(actual)) {
        const entries = Object.keys(actual).map(k => [
            k,
            actual[k],
        ]);
        // @ts-expect-error OK to have implicit any for this.equals
        pass = expected.some(entry => (0, utils_1.contains)((a, b) => this.equals(a, b, this.customTesters), entries, entry));
    }
    return {
        pass,
        message: () => pass
            ? matcherHint('.not.toContainAnyEntries') +
                '\n\n' +
                'Expected object to not contain any of the provided entries:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`
            : matcherHint('.toContainAnyEntries') +
                '\n\n' +
                'Expected object to contain any of the provided entries:\n' +
                `  ${printExpected(expected)}\n` +
                'Received:\n' +
                `  ${printReceived(actual)}`,
    };
}
