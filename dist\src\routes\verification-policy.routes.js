"use strict";
// jscpd:ignore-file
/**
 * Verification Policy Routes
 *
 * Routes for verification policy operations.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const enhanced_auth_middleware_1 = require("../middlewares/enhanced-auth.middleware");
const audit_middleware_1 = require("../middlewares/audit.middleware");
const verification_policy_controller_1 = __importDefault(require("../controllers/verification-policy.controller"));
const router = (0, express_1.Router)();
// Routes requiring authentication
router.use(enhanced_auth_middleware_1.enhancedAuthenticate);
// Get all verification policies
router.get("/", (0, enhanced_auth_middleware_1.requirePermission)("verification_methods", "view"), verification_policy_controller_1.default.getAllPolicies);
// Create a verification policy
router.post("/", (0, enhanced_auth_middleware_1.requirePermission)("verification_methods", "create"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("name").notEmpty().withMessage("Policy name is required"),
    (0, express_validator_1.body)("requiredMethods").isArray().withMessage("Required methods must be an array")
]), (0, audit_middleware_1.auditLog)("verification_policy", "create"), verification_policy_controller_1.default.createPolicy);
// Get applicable policies for a verification request
router.post("/applicable", (0, enhanced_auth_middleware_1.requirePermission)("verification_methods", "view"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("amount").isNumeric().withMessage("Amount must be a number"),
    (0, express_validator_1.body)("merchantId").notEmpty().withMessage("Merchant ID is required"),
    (0, express_validator_1.body)("paymentMethod").notEmpty().withMessage("Payment method is required")
]), verification_policy_controller_1.default.getApplicablePolicies);
// Verify using policy chain
router.post("/verify", (0, enhanced_auth_middleware_1.requirePermission)("verification_methods", "view"), (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("transactionId").notEmpty().withMessage("Transaction ID is required"),
    (0, express_validator_1.body)("policyId").notEmpty().withMessage("Policy ID is required"),
    (0, express_validator_1.body)("verificationData").isObject().withMessage("Verification data must be an object")
]), (0, audit_middleware_1.auditLog)("verification", "verify"), verification_policy_controller_1.default.verifyWithPolicy);
exports.default = router;
//# sourceMappingURL=verification-policy.routes.js.map