// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id                      String                       @id @default(uuid())
  email                   String                       @unique
  password                String
  firstName               String?
  lastName                String?
  role                    String                       @default("MERCHANT")
  isActive                Boolean                      @default(true)
  twoFactorEnabled        Boolean                      @default(false)
  twoFactorSecret         String?
  lastLogin               DateTime?
  createdAt               DateTime                     @default(now())
  updatedAt               DateTime                     @updatedAt
  merchant                Merchant?
  admin                   Admin?
  transactions            Transaction[]
  alerts                  Alert[]
  systemSettings          SystemSetting[]
  reports                 Report[]
  reportTemplates         ReportTemplate[]
  scheduledReports        ScheduledReport[]
  savedReports            SavedReport[]
  dashboards              Dashboard[]
  notifications           Notification[]
  notificationPreferences UserNotificationPreference[]
  pushSubscriptions       PushSubscription[]
}

// Merchant model
model Merchant {
  id                      String                           @id @default(uuid())
  userId                  String                           @unique
  user                    User                             @relation(fields: [userId], references: [id])
  businessName            String
  businessType            String
  contactEmail            String
  contactPhone            String?
  website                 String?
  country                 String
  address                 String?
  city                    String?
  state                   String?
  postalCode              String?
  verificationStatus      String                           @default("PENDING")
  isVerified              Boolean                          @default(false)
  createdAt               DateTime                         @default(now())
  updatedAt               DateTime                         @updatedAt
  transactions            Transaction[]
  paymentMethods          PaymentMethod[]
  webhooks                Webhook[]
  notifications           Notification[]
  notificationPreferences MerchantNotificationPreference[]
  pushSubscriptions       PushSubscription[]
}

// Admin model
model Admin {
  id          String   @id @default(uuid())
  userId      String   @unique
  user        User     @relation(fields: [userId], references: [id])
  department  String?
  permissions String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Role model
model Role {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  permissions String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Transaction model
model Transaction {
  id            String   @id @default(uuid())
  merchantId    String
  merchant      Merchant @relation(fields: [merchantId], references: [id])
  userId        String
  user          User     @relation(fields: [userId], references: [id])
  amount        Float
  currency      String
  status        String
  type          String
  paymentMethod String
  reference     String   @unique
  description   String?
  metadata      Json?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  alerts        Alert[]
}

// Alert model
model Alert {
  id            String       @id @default(uuid())
  userId        String
  user          User         @relation(fields: [userId], references: [id])
  transactionId String?
  transaction   Transaction? @relation(fields: [transactionId], references: [id])
  type          String
  severity      String
  status        String
  message       String
  metadata      Json?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
}

// Alert Aggregation Rule model
model AlertAggregationRule {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  conditions  Json
  actions     Json
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Alert Correlation Rule model
model AlertCorrelationRule {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  conditions  Json
  actions     Json
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Payment Method model
model PaymentMethod {
  id         String    @id @default(uuid())
  merchantId String?
  merchant   Merchant? @relation(fields: [merchantId], references: [id])
  customerId String?
  customer   Customer? @relation(fields: [customerId], references: [id])
  type       String
  name       String
  isDefault  Boolean   @default(false)
  isActive   Boolean   @default(true)
  details    Json
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
}

// System Setting model
model SystemSetting {
  id          String   @id @default(uuid())
  key         String   @unique
  value       String
  updatedById String
  updatedBy   User     @relation(fields: [updatedById], references: [id])
  updatedAt   DateTime @updatedAt
  createdAt   DateTime @default(now())
}

// Report model
model Report {
  id          String   @id @default(uuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  type        String // TRANSACTION, CUSTOMER, PAYMENT_METHOD, etc.
  format      String // PDF, CSV, EXCEL, etc.
  filters     String // JSON string of filters
  recordCount Int
  filePath    String
  createdAt   DateTime @default(now())
}

// Report Template model
model ReportTemplate {
  id               String            @id @default(uuid())
  name             String
  description      String?
  type             String // TRANSACTION, CUSTOMER, PAYMENT_METHOD, etc.
  config           Json // Configuration for the report (columns, filters, etc.)
  isSystem         Boolean           @default(false) // Whether this is a system template or user-created
  createdById      String
  createdBy        User              @relation(fields: [createdById], references: [id])
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  scheduledReports ScheduledReport[]
  savedReports     SavedReport[]
}

// Scheduled Report model
model ScheduledReport {
  id             String         @id @default(uuid())
  name           String
  description    String?
  templateId     String
  template       ReportTemplate @relation(fields: [templateId], references: [id])
  schedule       String // DAILY, WEEKLY, MONTHLY, etc.
  cronExpression String // Cron expression for the schedule
  parameters     Json // Parameters for the report (date range, filters, etc.)
  recipients     String[] // Email addresses to send the report to
  exportFormat   String // PDF, CSV, EXCEL, etc.
  isActive       Boolean        @default(true)
  lastRunAt      DateTime?
  createdById    String
  createdBy      User           @relation(fields: [createdById], references: [id])
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  reportRuns     ReportRun[]
}

// Saved Report model (generated reports)
model SavedReport {
  id          String         @id @default(uuid())
  name        String
  description String?
  templateId  String
  template    ReportTemplate @relation(fields: [templateId], references: [id])
  parameters  Json // Parameters used to generate the report
  filePath    String // Path to the saved report file
  fileType    String // PDF, CSV, EXCEL, etc.
  createdById String
  createdBy   User           @relation(fields: [createdById], references: [id])
  createdAt   DateTime       @default(now())
  expiresAt   DateTime? // Optional expiration date
}

// Report Run model (execution history)
model ReportRun {
  id                String           @id @default(uuid())
  scheduledReportId String?
  scheduledReport   ScheduledReport? @relation(fields: [scheduledReportId], references: [id])
  status            String // SUCCESS, FAILED, PROCESSING
  startedAt         DateTime
  completedAt       DateTime?
  filePath          String? // Path to the generated report file
  fileType          String? // PDF, CSV, EXCEL, etc.
  error             String? // Error message if the report failed
  metadata          Json? // Additional metadata about the report run
}

// Dashboard model
model Dashboard {
  id          String            @id @default(uuid())
  name        String
  description String?
  layout      Json // Layout configuration for the dashboard
  isPublic    Boolean           @default(false)
  createdById String
  createdBy   User              @relation(fields: [createdById], references: [id])
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  widgets     DashboardWidget[]
}

// Dashboard Widget model
model DashboardWidget {
  id          String    @id @default(uuid())
  dashboardId String
  dashboard   Dashboard @relation(fields: [dashboardId], references: [id])
  type        String // CHART, TABLE, METRIC, etc.
  title       String
  config      Json // Configuration for the widget
  position    Json // Position in the dashboard layout
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Webhook model
model Webhook {
  id            String    @id @default(uuid())
  merchantId    String
  merchant      Merchant  @relation(fields: [merchantId], references: [id])
  event         String
  payload       Json
  status        String
  url           String
  retryCount    Int       @default(0)
  maxRetries    Int       @default(3)
  statusCode    Int?
  response      Json?
  error         String?
  lastAttemptAt DateTime?
  deliveredAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

// Verification History model
model VerificationHistory {
  id        String   @id @default(uuid())
  paymentId String
  status    String
  method    String
  details   Json?
  createdAt DateTime @default(now())
}

// Audit Log model
model AuditLog {
  id        String   @id @default(uuid())
  level     String
  message   String
  source    String
  details   Json?
  createdAt DateTime @default(now())
}

// Customer model
model Customer {
  id             String          @id @default(uuid())
  email          String          @unique
  firstName      String?
  lastName       String?
  phone          String?
  address        String?
  city           String?
  state          String?
  postalCode     String?
  country        String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  paymentMethods PaymentMethod[]
  subscriptions  Subscription[]
}

// Subscription model
model Subscription {
  id              String    @id @default(uuid())
  customerId      String
  customer        Customer  @relation(fields: [customerId], references: [id])
  planId          String
  status          String
  amount          Float
  currency        String
  interval        String // MONTHLY, YEARLY, etc.
  startDate       DateTime
  endDate         DateTime?
  nextBillingDate DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Notification model
model Notification {
  id         String                 @id @default(uuid())
  userId     String?
  user       User?                  @relation(fields: [userId], references: [id])
  merchantId String?
  merchant   Merchant?              @relation(fields: [merchantId], references: [id])
  type       String // transaction, merchant, subscription, system
  channel    String // email, sms, telegram, push
  subject    String
  message    String
  priority   String                 @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  status     String                 @default("PENDING") // pending, sent, failed
  metadata   Json?
  templateId String?
  template   NotificationTemplate?  @relation(fields: [templateId], references: [id])
  sentAt     DateTime?
  createdAt  DateTime               @default(now())
  updatedAt  DateTime               @updatedAt
  deliveries NotificationDelivery[]
}

// Notification Delivery model
model NotificationDelivery {
  id             String       @id @default(uuid())
  notificationId String
  notification   Notification @relation(fields: [notificationId], references: [id])
  channel        String // email, sms, telegram, push
  recipient      String // email address, phone number, chat ID, etc.
  status         String       @default("PENDING") // pending, delivered, failed
  attempts       Int          @default(0)
  lastAttemptAt  DateTime?
  deliveredAt    DateTime?
  error          String?
  metadata       Json?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
}

// Notification Template model
model NotificationTemplate {
  id            String         @id @default(uuid())
  name          String         @unique
  description   String?
  type          String // transaction, merchant, subscription, system
  channel       String // email, sms, telegram, push
  subject       String?
  body          String
  variables     String[] // Available template variables
  isActive      Boolean        @default(true)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  notifications Notification[]
}

// User Notification Preference model
model UserNotificationPreference {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  channel   String // email, sms, telegram, push
  type      String // transaction, merchant, subscription, system
  enabled   Boolean  @default(true)
  settings  Json? // Channel-specific settings
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, channel, type])
}

// Merchant Notification Preference model
model MerchantNotificationPreference {
  id         String   @id @default(uuid())
  merchantId String
  merchant   Merchant @relation(fields: [merchantId], references: [id])
  channel    String // email, sms, telegram, push
  type       String // transaction, merchant, subscription, system
  enabled    Boolean  @default(true)
  settings   Json? // Channel-specific settings
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([merchantId, channel, type])
}

// Push Subscription model
model PushSubscription {
  id           String    @id @default(uuid())
  userId       String?
  user         User?     @relation(fields: [userId], references: [id])
  merchantId   String?
  merchant     Merchant? @relation(fields: [merchantId], references: [id])
  endpoint     String    @unique
  subscription String // JSON string of the push subscription
  deviceInfo   Json?
  active       Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}
