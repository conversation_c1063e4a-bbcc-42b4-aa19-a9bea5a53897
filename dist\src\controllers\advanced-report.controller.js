"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedReportController = void 0;
const advanced_report_service_1 = require("../services/advanced-report.service");
class AdvancedReportController {
    constructor() {
        /**
         * Generate a report
         */
        this.generateReport = async (req, res) => {
            try {
                const { type, format, ...parameters } = req.body;
                // Add user info to parameters
                const userId = req.user?.id;
                const userRole = req.user?.role;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                const reportParams = {
                    ...parameters,
                    userId,
                    userRole,
                };
                const result = await this.reportService.generateReport(type, reportParams, format);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                console.error('Error generating report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error generating report',
                });
            }
        };
        /**
         * Get report templates
         */
        this.getReportTemplates = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                const includeSystem = req.query.includeSystem !== 'false';
                const templates = await this.reportService.getReportTemplates(userId, includeSystem);
                res.json({
                    success: true,
                    data: templates,
                });
            }
            catch (error) {
                console.error('Error getting report templates:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting report templates',
                });
            }
        };
        /**
         * Get a report template by ID
         */
        this.getReportTemplateById = async (req, res) => {
            try {
                const { id } = req.params;
                const template = await this.reportService.getReportTemplateById(id);
                if (!template) {
                    res.status(404).json({ success: false, message: 'Report template not found' });
                    return;
                }
                res.json({
                    success: true,
                    data: template,
                });
            }
            catch (error) {
                console.error('Error getting report template:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting report template',
                });
            }
        };
        /**
         * Create a report template
         */
        this.createReportTemplate = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                const data = {
                    ...req.body,
                    createdById: userId,
                };
                const template = await this.reportService.createReportTemplate(data);
                res.status(201).json({
                    success: true,
                    data: template,
                });
            }
            catch (error) {
                console.error('Error creating report template:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error creating report template',
                });
            }
        };
        /**
         * Update a report template
         */
        this.updateReportTemplate = async (req, res) => {
            try {
                const { id } = req.params;
                const template = await this.reportService.updateReportTemplate(id, req.body);
                res.json({
                    success: true,
                    data: template,
                });
            }
            catch (error) {
                console.error('Error updating report template:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error updating report template',
                });
            }
        };
        /**
         * Delete a report template
         */
        this.deleteReportTemplate = async (req, res) => {
            try {
                const { id } = req.params;
                await this.reportService.deleteReportTemplate(id);
                res.json({
                    success: true,
                    message: 'Report template deleted successfully',
                });
            }
            catch (error) {
                console.error('Error deleting report template:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error deleting report template',
                });
            }
        };
        /**
         * Get scheduled reports
         */
        this.getScheduledReports = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                const reports = await this.reportService.getScheduledReports(userId);
                res.json({
                    success: true,
                    data: reports,
                });
            }
            catch (error) {
                console.error('Error getting scheduled reports:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting scheduled reports',
                });
            }
        };
        /**
         * Get a scheduled report by ID
         */
        this.getScheduledReportById = async (req, res) => {
            try {
                const { id } = req.params;
                const report = await this.reportService.getScheduledReportById(id);
                if (!report) {
                    res.status(404).json({ success: false, message: 'Scheduled report not found' });
                    return;
                }
                res.json({
                    success: true,
                    data: report,
                });
            }
            catch (error) {
                console.error('Error getting scheduled report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting scheduled report',
                });
            }
        };
        /**
         * Create a scheduled report
         */
        this.createScheduledReport = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                const data = {
                    ...req.body,
                    createdById: userId,
                };
                const report = await this.reportService.createScheduledReport(data);
                res.status(201).json({
                    success: true,
                    data: report,
                });
            }
            catch (error) {
                console.error('Error creating scheduled report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error creating scheduled report',
                });
            }
        };
        /**
         * Update a scheduled report
         */
        this.updateScheduledReport = async (req, res) => {
            try {
                const { id } = req.params;
                const report = await this.reportService.updateScheduledReport(id, req.body);
                res.json({
                    success: true,
                    data: report,
                });
            }
            catch (error) {
                console.error('Error updating scheduled report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error updating scheduled report',
                });
            }
        };
        /**
         * Delete a scheduled report
         */
        this.deleteScheduledReport = async (req, res) => {
            try {
                const { id } = req.params;
                await this.reportService.deleteScheduledReport(id);
                res.json({
                    success: true,
                    message: 'Scheduled report deleted successfully',
                });
            }
            catch (error) {
                console.error('Error deleting scheduled report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error deleting scheduled report',
                });
            }
        };
        /**
         * Run a scheduled report now
         */
        this.runScheduledReport = async (req, res) => {
            try {
                const { id } = req.params;
                const result = await this.reportService.runScheduledReport(id);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                console.error('Error running scheduled report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error running scheduled report',
                });
            }
        };
        /**
         * Get saved reports
         */
        this.getSavedReports = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                const reports = await this.reportService.getSavedReports(userId);
                res.json({
                    success: true,
                    data: reports,
                });
            }
            catch (error) {
                console.error('Error getting saved reports:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting saved reports',
                });
            }
        };
        /**
         * Get a saved report by ID
         */
        this.getSavedReportById = async (req, res) => {
            try {
                const { id } = req.params;
                const report = await this.reportService.getSavedReportById(id);
                if (!report) {
                    res.status(404).json({ success: false, message: 'Saved report not found' });
                    return;
                }
                res.json({
                    success: true,
                    data: report,
                });
            }
            catch (error) {
                console.error('Error getting saved report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error getting saved report',
                });
            }
        };
        /**
         * Delete a saved report
         */
        this.deleteSavedReport = async (req, res) => {
            try {
                const { id } = req.params;
                await this.reportService.deleteSavedReport(id);
                res.json({
                    success: true,
                    message: 'Saved report deleted successfully',
                });
            }
            catch (error) {
                console.error('Error deleting saved report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error deleting saved report',
                });
            }
        };
        /**
         * Download a saved report
         */
        this.downloadSavedReport = async (req, res) => {
            try {
                const { id } = req.params;
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ success: false, message: 'Unauthorized' });
                    return;
                }
                // Get the saved report
                const savedReport = await this.reportService.getSavedReportById(id);
                if (!savedReport) {
                    res.status(404).json({ success: false, message: 'Report not found' });
                    return;
                }
                // Check if user has permission to download this report
                if (savedReport.createdById !== userId && req.user?.role !== 'ADMIN') {
                    res.status(403).json({ success: false, message: 'Access denied' });
                    return;
                }
                // Check if file exists
                const fs = require('fs');
                if (!fs.existsSync(savedReport.filePath)) {
                    res.status(404).json({ success: false, message: 'Report file not found' });
                    return;
                }
                // Set content type based on format
                const contentTypes = {
                    CSV: 'text/csv',
                    PDF: 'application/pdf',
                    EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    JSON: 'application/json',
                };
                const contentType = contentTypes[savedReport.format] || 'application/octet-stream';
                const fileName = `${savedReport.name}_${savedReport.id}.${savedReport.format.toLowerCase()}`;
                res.setHeader('Content-Type', contentType);
                res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
                // Stream file to response
                const fileStream = fs.createReadStream(savedReport.filePath);
                fileStream.pipe(res);
            }
            catch (error) {
                console.error('Error downloading saved report:', error);
                res.status(500).json({
                    success: false,
                    message: error.message || 'Error downloading saved report',
                });
            }
        };
        this.reportService = new advanced_report_service_1.AdvancedReportService();
    }
}
exports.AdvancedReportController = AdvancedReportController;
