"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.AppError = exports.ErrorCode = exports.ErrorType = void 0;
/**
 * Application Error Types
 */
var ErrorType;
(function (ErrorType) {
    ErrorType["VALIDATION"] = "VALIDATION";
    ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
    ErrorType["AUTHORIZATION"] = "AUTHORIZATION";
    ErrorType["NOT_FOUND"] = "NOT_FOUND";
    ErrorType["CONFLICT"] = "CONFLICT";
    ErrorType["INTERNAL"] = "INTERNAL";
    ErrorType["EXTERNAL"] = "EXTERNAL";
    ErrorType["BUSINESS"] = "BUSINESS";
    ErrorType["PAYMENT"] = "PAYMENT";
    ErrorType["VERIFICATION"] = "VERIFICATION";
    ErrorType["RATE_LIMIT"] = "RATE_LIMIT";
    ErrorType["TIMEOUT"] = "TIMEOUT";
    ErrorType["NETWORK"] = "NETWORK";
    ErrorType["DATABASE"] = "DATABASE";
    ErrorType["CONFIGURATION"] = "CONFIGURATION";
    ErrorType["INTEGRATION"] = "INTEGRATION";
    ErrorType["SECURITY"] = "SECURITY";
    ErrorType["UNKNOWN"] = "UNKNOWN";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
/**
 * Application Error Codes
 */
var ErrorCode;
(function (ErrorCode) {
    // Validation errors
    ErrorCode["INVALID_INPUT"] = "INVALID_INPUT";
    ErrorCode["MISSING_REQUIRED_FIELD"] = "MISSING_REQUIRED_FIELD";
    ErrorCode["INVALID_FORMAT"] = "INVALID_FORMAT";
    ErrorCode["INVALID_VALUE"] = "INVALID_VALUE";
    // Authentication errors
    ErrorCode["INVALID_CREDENTIALS"] = "INVALID_CREDENTIALS";
    ErrorCode["EXPIRED_TOKEN"] = "EXPIRED_TOKEN";
    ErrorCode["INVALID_TOKEN"] = "INVALID_TOKEN";
    ErrorCode["MISSING_TOKEN"] = "MISSING_TOKEN";
    // Authorization errors
    ErrorCode["INSUFFICIENT_PERMISSIONS"] = "INSUFFICIENT_PERMISSIONS";
    ErrorCode["ACCESS_DENIED"] = "ACCESS_DENIED";
    ErrorCode["FORBIDDEN"] = "FORBIDDEN";
    // Not found errors
    ErrorCode["RESOURCE_NOT_FOUND"] = "RESOURCE_NOT_FOUND";
    ErrorCode["USER_NOT_FOUND"] = "USER_NOT_FOUND";
    ErrorCode["MERCHANT_NOT_FOUND"] = "MERCHANT_NOT_FOUND";
    ErrorCode["PAYMENT_NOT_FOUND"] = "PAYMENT_NOT_FOUND";
    ErrorCode["TRANSACTION_NOT_FOUND"] = "TRANSACTION_NOT_FOUND";
    // Conflict errors
    ErrorCode["RESOURCE_ALREADY_EXISTS"] = "RESOURCE_ALREADY_EXISTS";
    ErrorCode["DUPLICATE_ENTRY"] = "DUPLICATE_ENTRY";
    // Internal errors
    ErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ErrorCode["UNEXPECTED_ERROR"] = "UNEXPECTED_ERROR";
    // External errors
    ErrorCode["EXTERNAL_SERVICE_ERROR"] = "EXTERNAL_SERVICE_ERROR";
    ErrorCode["API_ERROR"] = "API_ERROR";
    // Business errors
    ErrorCode["BUSINESS_RULE_VIOLATION"] = "BUSINESS_RULE_VIOLATION";
    ErrorCode["INVALID_OPERATION"] = "INVALID_OPERATION";
    // Payment errors
    ErrorCode["PAYMENT_FAILED"] = "PAYMENT_FAILED";
    ErrorCode["PAYMENT_EXPIRED"] = "PAYMENT_EXPIRED";
    ErrorCode["PAYMENT_CANCELLED"] = "PAYMENT_CANCELLED";
    ErrorCode["INSUFFICIENT_FUNDS"] = "INSUFFICIENT_FUNDS";
    // Verification errors
    ErrorCode["VERIFICATION_FAILED"] = "VERIFICATION_FAILED";
    ErrorCode["VERIFICATION_EXPIRED"] = "VERIFICATION_EXPIRED";
    ErrorCode["VERIFICATION_CANCELLED"] = "VERIFICATION_CANCELLED";
    // Rate limit errors
    ErrorCode["RATE_LIMIT_EXCEEDED"] = "RATE_LIMIT_EXCEEDED";
    // Timeout errors
    ErrorCode["REQUEST_TIMEOUT"] = "REQUEST_TIMEOUT";
    ErrorCode["OPERATION_TIMEOUT"] = "OPERATION_TIMEOUT";
    // Network errors
    ErrorCode["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorCode["CONNECTION_ERROR"] = "CONNECTION_ERROR";
    // Database errors
    ErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
    ErrorCode["QUERY_ERROR"] = "QUERY_ERROR";
    ErrorCode["TRANSACTION_ERROR"] = "TRANSACTION_ERROR";
    // Configuration errors
    ErrorCode["CONFIGURATION_ERROR"] = "CONFIGURATION_ERROR";
    ErrorCode["MISSING_CONFIGURATION"] = "MISSING_CONFIGURATION";
    // Integration errors
    ErrorCode["INTEGRATION_ERROR"] = "INTEGRATION_ERROR";
    ErrorCode["WEBHOOK_ERROR"] = "WEBHOOK_ERROR";
    // Security errors
    ErrorCode["SECURITY_ERROR"] = "SECURITY_ERROR";
    ErrorCode["ENCRYPTION_ERROR"] = "ENCRYPTION_ERROR";
    // Unknown errors
    ErrorCode["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
/**
 * Application Error
 */
class AppError extends Error {
    constructor(options) {
        super(options.message);
        this.name = 'AppError';
        this.type = options.type;
        this.code = options.code;
        this.statusCode = options.statusCode || this.getDefaultStatusCode();
        this.details = options.details;
        this.timestamp = new Date().toISOString();
        this.path = options.path;
        this.requestId = options.requestId;
        this.originalError = options.originalError;
        // Capture stack trace
        Error.captureStackTrace(this, this.constructor);
    }
    /**
     * Get default status code based on error type
     */
    getDefaultStatusCode() {
        switch (this.type) {
            case ErrorType.VALIDATION:
                return 400;
            case ErrorType.AUTHENTICATION:
                return 401;
            case ErrorType.AUTHORIZATION:
                return 403;
            case ErrorType.NOT_FOUND:
                return 404;
            case ErrorType.CONFLICT:
                return 409;
            case ErrorType.RATE_LIMIT:
                return 429;
            case ErrorType.TIMEOUT:
                return 408;
            case ErrorType.INTERNAL:
            case ErrorType.DATABASE:
            case ErrorType.CONFIGURATION:
                return 500;
            case ErrorType.EXTERNAL:
            case ErrorType.INTEGRATION:
                return 502;
            case ErrorType.NETWORK:
                return 503;
            default:
                return 500;
        }
    }
    /**
     * Convert to JSON
     */
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            type: this.type,
            code: this.code,
            statusCode: this.statusCode,
            details: this.details,
            timestamp: this.timestamp,
            path: this.path,
            requestId: this.requestId,
            stack: process.env.NODE_ENV === 'development' ? this.stack : undefined,
        };
    }
}
exports.AppError = AppError;
/**
 * Validation Error
 */
class ValidationError extends AppError {
    constructor(options) {
        super({
            message: options.message,
            type: ErrorType.VALIDATION,
            code: options.code || ErrorCode.INVALID_INPUT,
            statusCode: 400,
            details: options.details,
            path: options.path,
            requestId: options.requestId,
            originalError: options.originalError,
        });
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
/**
 * Authentication Error
 */
class AuthenticationError extends AppError {
    constructor(options) {
        super({
            message: options.message,
            type: ErrorType.AUTHENTICATION,
            code: options.code || ErrorCode.INVALID_CREDENTIALS,
            statusCode: 401,
            details: options.details,
            path: options.path,
            requestId: options.requestId,
            originalError: options.originalError,
        });
        this.name = 'AuthenticationError';
    }
}
exports.AuthenticationError = AuthenticationError;
/**
 * Authorization Error
 */
class AuthorizationError extends AppError {
    constructor(options) {
        super({
            message: options.message,
            type: ErrorType.AUTHORIZATION,
            code: options.code || ErrorCode.INSUFFICIENT_PERMISSIONS,
            statusCode: 403,
            details: options.details,
            path: options.path,
            requestId: options.requestId,
            originalError: options.originalError,
        });
        this.name = 'AuthorizationError';
    }
}
exports.AuthorizationError = AuthorizationError;
/**
 * Not Found Error
 */
class NotFoundError extends AppError {
    constructor(options) {
        super({
            message: options.message,
            type: ErrorType.NOT_FOUND,
            code: options.code || ErrorCode.RESOURCE_NOT_FOUND,
            statusCode: 404,
            details: options.details,
            path: options.path,
            requestId: options.requestId,
            originalError: options.originalError,
        });
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
