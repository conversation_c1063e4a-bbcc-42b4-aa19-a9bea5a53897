"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const monitoring_controller_1 = __importDefault(require("../controllers/monitoring.controller"));
const auth_1 = require("../middlewares/auth");
const router = express_1.default.Router();
/**
 * @route   GET /api/monitoring/metrics
 * @desc    Get monitoring metrics
 * @access  Admin
 */
router.get("/metrics", auth_1.authenticateJWT, auth_1.isAdmin, monitoring_controller_1.default.getMetrics);
/**
 * @route   GET /api/monitoring/alerts
 * @desc    Get alerts
 * @access  Admin
 */
router.get("/alerts", auth_1.authenticateJWT, auth_1.isAdmin, monitoring_controller_1.default.getAlerts);
/**
 * @route   POST /api/monitoring/alerts/:index/resolve
 * @desc    Resolve alert
 * @access  Admin
 */
router.post("/alerts/:index/resolve", auth_1.authenticateJWT, auth_1.isAdmin, monitoring_controller_1.default.resolveAlert);
/**
 * @route   POST /api/monitoring/alerts
 * @desc    Create alert
 * @access  Admin
 */
router.post("/alerts", auth_1.authenticateJWT, auth_1.isAdmin, monitoring_controller_1.default.createAlert);
/**
 * @route   POST /api/monitoring/metrics/reset
 * @desc    Reset metrics
 * @access  Admin
 */
router.post("/metrics/reset", auth_1.authenticateJWT, auth_1.isAdmin, monitoring_controller_1.default.resetMetrics);
/**
 * @route   POST /api/monitoring/events
 * @desc    Emit monitoring event
 * @access  Admin
 */
router.post("/events", auth_1.authenticateJWT, auth_1.isAdmin, monitoring_controller_1.default.emitEvent);
exports.default = router;
//# sourceMappingURL=monitoring.routes.js.map