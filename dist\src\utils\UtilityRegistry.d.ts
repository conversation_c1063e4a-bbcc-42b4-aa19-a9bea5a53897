/**
 * Utility Registry
 *
 * This file serves as a central registry for all utility functions in the application.
 * It helps eliminate duplication by providing a single source of truth for utility functions.
 */
/**
 * Utility registry
 */
export declare const UtilityRegistry: any;
/**
 * Get utility registry
 * @returns Utility registry
 */
export declare function getUtilityRegistry(): typeof UtilityRegistry;
//# sourceMappingURL=UtilityRegistry.d.ts.map