"use strict";
// jscpd:ignore-file
/**
 * Enhanced Error Handling Middleware
 *
 * This middleware provides improved error handling with:
 * - Structured error responses
 * - Detailed logging
 * - Environment-specific error information
 * - Error tracking
 * - Request context
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedErrorMiddleware = exports.notFoundHandler = exports.enhancedErrorHandler = void 0;
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
const appError_1 = require("../utils/appError");
const environment_validator_1 = require("../utils/environment-validator");
/**
 * Error handler middleware
 * @param err Error object
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const enhancedErrorHandler = (err, req, res, next) => {
    // Generate unique error ID for tracking
    const errorId = (0, uuid_1.v4)();
    // Default error values
    let statusCode = 500;
    let message = "Internal Server Error";
    let errorCode = "INTERNAL_SERVER_ERROR";
    let isOperational = false;
    let details = null;
    // Extract request information for logging
    const requestInfo = {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get("user-agent"),
        requestId: req.requestId || "unknown",
        userId: req.user?.id || "anonymous",
        errorId
    };
    // Handle different error types
    if (err instanceof appError_1.AppError) {
        // Handle our custom AppError
        statusCode = err.statusCode;
        message = err.message;
        errorCode = err.code;
        isOperational = err.isOperational;
        details = err.details || null;
    }
    else if (Array.isArray(err) && err.length > 0 && 'msg' in err[0]) {
        // Handle express-validator validation errors
        statusCode = 422;
        message = "Validation failed";
        errorCode = "VALIDATION_ERROR";
        isOperational = true;
        details = err;
    }
    else if (err.name === "UnauthorizedError") {
        // Handle authentication errors
        statusCode = 401;
        message = "Unauthorized";
        errorCode = "UNAUTHORIZED";
        isOperational = true;
    }
    else if (err.name === "JsonWebTokenError") {
        // Handle JWT errors
        statusCode = 401;
        message = "Invalid token";
        errorCode = "INVALID_TOKEN";
        isOperational = true;
    }
    else if (err.name === "TokenExpiredError") {
        // Handle expired JWT
        statusCode = 401;
        message = "Token expired";
        errorCode = "TOKEN_EXPIRED";
        isOperational = true;
    }
    else if (err.code === "P2002") {
        // Handle Prisma unique constraint errors
        statusCode = 409;
        message = "Resource already exists";
        errorCode = "RESOURCE_CONFLICT";
        isOperational = true;
        details = err.meta?.target || null;
    }
    else if (err.code === "P2025") {
        // Handle Prisma not found errors
        statusCode = 404;
        message = "Resource not found";
        errorCode = "RESOURCE_NOT_FOUND";
        isOperational = true;
    }
    // Prepare error response
    const errorResponse = {
        status: "error",
        code: errorCode,
        message,
        errorId
    };
    // Add details in development or for operational errors
    if (details && ((0, environment_validator_1.isDevelopment)() || isOperational)) {
        errorResponse.details = details;
    }
    // Add stack trace in development
    if ((0, environment_validator_1.isDevelopment)()) {
        errorResponse.stack = err.stack;
    }
    // Log error with appropriate level
    if (isOperational) {
        // Operational errors are expected errors (e.g. validation errors)
        logger_1.logger.warn(`[${errorId}] ${errorCode}: ${message}`, {
            ...requestInfo,
            statusCode,
            details: details || undefined
        });
    }
    else {
        // Programming or unknown errors need more attention
        logger_1.logger.error(`[${errorId}] ${errorCode}: ${message}`, {
            ...requestInfo,
            statusCode,
            error: err.message,
            stack: err.stack
        });
    }
    // Send response
    res.status(statusCode).json(errorResponse);
};
exports.enhancedErrorHandler = enhancedErrorHandler;
/**
 * Not found middleware
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
const notFoundHandler = (req, res, next) => {
    const error = new appError_1.AppError(`Not Found - ${req.originalUrl}`, 404, true, "RESOURCE_NOT_FOUND");
    next(error);
};
exports.notFoundHandler = notFoundHandler;
// Export middleware as a group
exports.enhancedErrorMiddleware = {
    enhancedErrorHandler: exports.enhancedErrorHandler,
    notFoundHandler: exports.notFoundHandler
};
exports.default = exports.enhancedErrorMiddleware;
//# sourceMappingURL=enhanced-error.middleware.js.map