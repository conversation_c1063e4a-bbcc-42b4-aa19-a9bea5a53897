"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const auth_middleware_1 = require("../middlewares/auth.middleware");
const identity_verification_1 = require("../controllers/identity-verification");
// Create controller instance
const identityVerificationController = new identity_verification_1.IdentityVerificationController();
// Extract methods from controller
const { verifyEthereumSignature, verifyERC1484Identity, verifyERC725Identity, verifyENS, verifyPolygonID, verifyWorldcoin, verifyUnstoppableDomains, getVerificationById, getVerificationsForUser, getVerificationsForMerchant, addClaim, revokeClaim, setVerificationExpiration, checkVerificationExpiration, getVerificationStats, createBlockchainVerificationRequest, completeBlockchainVerification, getSupportedNetworks, verifyENSDomain, completeENSVerification, verifyUnstoppableDomain, completeUnstoppableDomainVerification, createPolygonIDVerificationRequest, handlePolygonIDCallback, checkPolygonIDVerificationStatus, } = identityVerificationController;
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_middleware_1.authMiddleware);
// Verification endpoints
router.post('/ethereum-signature', verifyEthereumSignature);
router.post('/erc1484', verifyERC1484Identity);
router.post('/erc725', verifyERC725Identity);
router.post('/ens', verifyENS);
router.post('/polygon-id', verifyPolygonID);
router.post('/worldcoin', verifyWorldcoin);
router.post('/unstoppable-domains', verifyUnstoppableDomains);
// Claim management
router.post('/claims', (0, auth_middleware_1.authorize)(['ADMIN', 'MERCHANT']), addClaim);
router.delete('/claims/:claimId', (0, auth_middleware_1.authorize)(['ADMIN', 'MERCHANT']), revokeClaim);
// Expiration management
router.post('/expiration', (0, auth_middleware_1.authorize)(['ADMIN']), setVerificationExpiration);
router.post('/check-expiration', (0, auth_middleware_1.authorize)(['ADMIN']), checkVerificationExpiration);
// Blockchain-based verification
router.post('/blockchain/request', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), createBlockchainVerificationRequest);
router.post('/blockchain/verify', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), completeBlockchainVerification);
router.get('/blockchain/networks', getSupportedNetworks);
// ENS verification
router.post('/ens/verify', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), verifyENSDomain);
router.post('/ens/complete', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), completeENSVerification);
// Unstoppable Domains verification
router.post('/unstoppable/verify', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), verifyUnstoppableDomain);
router.post('/unstoppable/complete', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), completeUnstoppableDomainVerification);
// Polygon ID verification
router.post('/polygon-id/request', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), createPolygonIDVerificationRequest);
router.post('/polygon-id/callback', handlePolygonIDCallback);
router.get('/polygon-id/status/:verificationId', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), checkPolygonIDVerificationStatus);
// Worldcoin verification
router.post('/worldcoin/verify', (0, auth_middleware_1.authorize)(['USER', 'MERCHANT']), verifyWorldcoinIdentity);
// Statistics
router.get('/stats', (0, auth_middleware_1.authorize)(['ADMIN']), getVerificationStats);
// Get verification by ID
router.get('/:id', getVerificationById);
// Get verifications for user
router.get('/user/me', (0, auth_middleware_1.authorize)(['USER']), getVerificationsForUser);
// Get verifications for merchant
router.get('/merchant/me', (0, auth_middleware_1.authorize)(['MERCHANT']), getVerificationsForMerchant);
exports.default = router;
//# sourceMappingURL=identity-verification.routes.js.map