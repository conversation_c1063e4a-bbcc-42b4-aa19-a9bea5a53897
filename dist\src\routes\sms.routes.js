"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const sms_controller_ts_1 = require("../controllers/refactored/sms.controller.ts");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// SMS routes
router.post("/test", auth_1.authenticate, sms_controller_ts_1.SmsController.testSmsService);
router.post("/send", auth_1.authenticate, sms_controller_ts_1.SmsController.sendCustomSms);
router.get("/admin-phone-numbers", auth_1.authenticate, sms_controller_ts_1.SmsController.getAdminPhoneNumbers);
exports.default = router;
//# sourceMappingURL=sms.routes.js.map