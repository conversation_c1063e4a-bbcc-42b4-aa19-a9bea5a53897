"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// jscpd:ignore-file
const express_1 = require("express");
const alert_aggregation_1 = require("../controllers/alert-aggregation");
const auth_1 = require("../middlewares/auth");
const router = (0, express_1.Router)();
// Aggregation rule routes
router.get('/aggregation/rules', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.getAggregationRules);
router.get('/aggregation/rules/:id', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.getAggregationRule);
router.post('/aggregation/rules', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.createAggregationRule);
router.put('/aggregation/rules/:id', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.updateAggregationRule);
router.delete('/aggregation/rules/:id', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.deleteAggregationRule);
// Correlation rule routes
router.get('/correlation/rules', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.getCorrelationRules);
router.get('/correlation/rules/:id', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.getCorrelationRule);
router.post('/correlation/rules', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.createCorrelationRule);
router.put('/correlation/rules/:id', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.updateCorrelationRule);
router.delete('/correlation/rules/:id', auth_1.authenticate, alert_aggregation_1.AlertAggregationController.deleteCorrelationRule);
exports.default = router;
//# sourceMappingURL=alert-aggregation.routes.js.map