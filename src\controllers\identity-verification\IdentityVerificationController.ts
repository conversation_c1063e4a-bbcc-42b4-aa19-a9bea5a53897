/**
 * Identity Verification Controller
 * 
 * Modular controller for identity verification operations.
 */

import { Response } from 'express';
import { BaseController } from '../base.controller';
import { asyncHandler } from '../../utils/asyncHandler';
import { IdentityVerificationService } from '../../services/identity-verification/IdentityVerificationService';

import { IdentityVerificationAuthService } from './services/IdentityVerificationAuthService';
import { IdentityVerificationValidationService } from './services/IdentityVerificationValidationService';
import { IdentityVerificationResponseMapper } from './mappers/IdentityVerificationResponseMapper';

import {
  AuthenticatedRequest,
  VerificationFilters
} from './types/IdentityVerificationControllerTypes';

/**
 * Modular Identity Verification Controller
 */
export class IdentityVerificationController extends BaseController {
  private authService: IdentityVerificationAuthService;
  private validationService: IdentityVerificationValidationService;
  private identityService: IdentityVerificationService;

  constructor() {
    super();
    this.authService = new IdentityVerificationAuthService();
    this.validationService = new IdentityVerificationValidationService();
    this.identityService = new IdentityVerificationService();
  }

  /**
   * Verify identity using Ethereum signature
   */
  verifyEthereumSignature = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateEthereumSignature(req.body);
      const { userId, merchantId } = this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyEthereumSignature(
        validatedData.address,
        validatedData.message,
        validatedData.signature,
        userId,
        merchantId
      );

      // Response
      IdentityVerificationResponseMapper.sendVerificationResult(res, result);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using ERC-1484
   */
  verifyERC1484Identity = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateERC1484Identity(req.body);
      const { userId, merchantId } = this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyERC1484Identity(
        validatedData.address,
        validatedData.ein,
        validatedData.registryAddress,
        userId,
        merchantId
      );

      // Response
      IdentityVerificationResponseMapper.sendVerificationResult(res, result);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using ERC-725
   */
  verifyERC725Identity = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateERC725Identity(req.body);
      const { userId, merchantId } = this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyERC725Identity(
        validatedData.address,
        validatedData.key,
        validatedData.value,
        userId,
        merchantId
      );

      // Response
      IdentityVerificationResponseMapper.sendVerificationResult(res, result);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using ENS
   */
  verifyENS = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateENSVerification(req.body);
      const { userId, merchantId } = this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyENS(
        validatedData.ensName,
        validatedData.address,
        userId,
        merchantId
      );

      // Response
      IdentityVerificationResponseMapper.sendVerificationResult(res, result);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using Polygon ID
   */
  verifyPolygonID = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validatePolygonID(req.body);
      const { userId, merchantId } = this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyPolygonID(
        validatedData.address,
        validatedData.proof,
        userId,
        merchantId
      );

      // Response
      IdentityVerificationResponseMapper.sendVerificationResult(res, result);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using Worldcoin
   */
  verifyWorldcoin = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateWorldcoin(req.body);
      const { userId, merchantId } = this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyWorldcoin(
        validatedData.address,
        validatedData.nullifier,
        validatedData.proof,
        userId,
        merchantId
      );

      // Response
      IdentityVerificationResponseMapper.sendVerificationResult(res, result);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using Unstoppable Domains
   */
  verifyUnstoppableDomains = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const validatedData = this.validationService.validateUnstoppableDomains(req.body);
      const { userId, merchantId } = this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyUnstoppableDomains(
        validatedData.domain,
        validatedData.address,
        userId,
        merchantId
      );

      // Response
      IdentityVerificationResponseMapper.sendVerificationResult(res, result);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get verification by ID
   */
  getVerificationById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'read',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const verificationId = this.validationService.validateId(req.params.id, 'Verification ID');

      // Business logic
      const verification = await this.identityService.getVerificationById(verificationId);

      // Response
      const transformedVerification = IdentityVerificationResponseMapper.transformVerification(verification);
      IdentityVerificationResponseMapper.sendVerification(res, transformedVerification);
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Get verifications for user
   */
  getVerificationsForUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Get user ID
      const { userId } = this.authService.extractUserContext(req);
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Business logic
      const verifications = await this.identityService.getVerificationsForUser(userId);

      // Response
      const transformedVerifications = verifications.map(
        IdentityVerificationResponseMapper.transformVerification
      );
      IdentityVerificationResponseMapper.sendVerificationsList(
        res,
        transformedVerifications,
        transformedVerifications.length
      );
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });

  /**
   * Health check endpoint
   */
  healthCheck = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      IdentityVerificationResponseMapper.sendSuccess(res, {
        status: 'healthy',
        timestamp: new Date(),
        version: '1.0.0',
        services: {
          authorization: 'active',
          validation: 'active',
          identityService: 'active'
        }
      }, 'Identity Verification Controller is healthy');
    } catch (error) {
      IdentityVerificationResponseMapper.sendError(res, error as Error);
    }
  });
}
