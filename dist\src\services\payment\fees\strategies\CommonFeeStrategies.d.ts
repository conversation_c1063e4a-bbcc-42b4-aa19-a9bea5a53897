/**
 * Common Fee Calculation Strategies
 *
 * Implements common fee calculation strategies.
 */
import { IFeeCalculationStrategy, FeeCalculationContext } from '../FeeCalculator';
import { PrismaClient } from '@prisma/client';
/**
 * Percentage fee strategy
 *
 * Calculates fee as a percentage of the transaction amount.
 */
export declare class PercentageFeeStrategy implements IFeeCalculationStrategy {
    private prisma;
    private defaultPercentage;
    private methodPercentages;
    /**
     * Constructor
     *
     * @param prisma Prisma client
     * @param defaultPercentage Default fee percentage
     */
    constructor(prisma: PrismaClient, defaultPercentage?: number);
    /**
     * Get the strategy name
     */
    getName(): string;
    /**
     * Calculate fee
     */
    calculate(context: FeeCalculationContext): {
        fee: number;
        description: string;
    };
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context: FeeCalculationContext): boolean;
    /**
     * Load method percentages from database or cache
     */
    private loadMethodPercentages;
}
/**
 * Tiered fee strategy
 *
 * Calculates fee based on transaction amount tiers.
 */
export declare class TieredFeeStrategy implements IFeeCalculationStrategy {
    private tiers;
    /**
     * Constructor
     *
     * @param tiers Fee tiers
     */
    constructor(tiers?: {
        min: number;
        max: number;
        percentage: number;
    }[]);
    /**
     * Get the strategy name
     */
    getName(): string;
    /**
     * Calculate fee
     */
    calculate(context: FeeCalculationContext): {
        fee: number;
        description: string;
    };
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context: FeeCalculationContext): boolean;
}
/**
 * Fixed fee strategy
 *
 * Adds a fixed fee to the transaction.
 */
export declare class FixedFeeStrategy implements IFeeCalculationStrategy {
    private fixedFees;
    private defaultFee;
    /**
     * Constructor
     *
     * @param fixedFees Fixed fees by currency
     * @param defaultFee Default fixed fee
     */
    constructor(fixedFees?: Record<string, number>, defaultFee?: number);
    /**
     * Get the strategy name
     */
    getName(): string;
    /**
     * Calculate fee
     */
    calculate(context: FeeCalculationContext): {
        fee: number;
        description: string;
    };
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context: FeeCalculationContext): boolean;
}
/**
 * Merchant-specific fee strategy
 *
 * Applies custom fees for specific merchants.
 */
export declare class MerchantSpecificFeeStrategy implements IFeeCalculationStrategy {
    private prisma;
    private merchantFees;
    /**
     * Constructor
     *
     * @param prisma Prisma client
     */
    constructor(prisma: PrismaClient);
    /**
     * Get the strategy name
     */
    getName(): string;
    /**
     * Calculate fee
     */
    calculate(context: FeeCalculationContext): {
        fee: number;
        description: string;
    };
    /**
     * Check if strategy applies to the context
     */
    appliesTo(context: FeeCalculationContext): boolean;
    /**
     * Load merchant fees from database or cache
     */
    private loadMerchantFees;
}
//# sourceMappingURL=CommonFeeStrategies.d.ts.map