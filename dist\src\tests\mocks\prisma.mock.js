"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockPrismaClient = exports.PaymentStatusEnum = exports.PaymentMethodTypeEnum = exports.IdentityVerificationStatusEnum = exports.IdentityVerificationMethodEnum = void 0;
// Define common enums used in tests
var IdentityVerificationMethodEnum;
(function (IdentityVerificationMethodEnum) {
    IdentityVerificationMethodEnum["EMAIL"] = "EMAIL";
    IdentityVerificationMethodEnum["PHONE"] = "PHONE";
    IdentityVerificationMethodEnum["DOCUMENT"] = "DOCUMENT";
    IdentityVerificationMethodEnum["BLOCKCHAIN"] = "BLOCKCHAIN";
    IdentityVerificationMethodEnum["SOCIAL"] = "SOCIAL";
})(IdentityVerificationMethodEnum || (exports.IdentityVerificationMethodEnum = IdentityVerificationMethodEnum = {}));
var IdentityVerificationStatusEnum;
(function (IdentityVerificationStatusEnum) {
    IdentityVerificationStatusEnum["PENDING"] = "PENDING";
    IdentityVerificationStatusEnum["VERIFIED"] = "VERIFIED";
    IdentityVerificationStatusEnum["REJECTED"] = "REJECTED";
    IdentityVerificationStatusEnum["EXPIRED"] = "EXPIRED";
})(IdentityVerificationStatusEnum || (exports.IdentityVerificationStatusEnum = IdentityVerificationStatusEnum = {}));
var PaymentMethodTypeEnum;
(function (PaymentMethodTypeEnum) {
    PaymentMethodTypeEnum["CRYPTO"] = "CRYPTO";
    PaymentMethodTypeEnum["BINANCE_PAY"] = "BINANCE_PAY";
    PaymentMethodTypeEnum["BINANCE_C2C"] = "BINANCE_C2C";
    PaymentMethodTypeEnum["BINANCE_TRC20"] = "BINANCE_TRC20";
    PaymentMethodTypeEnum["BANK_TRANSFER"] = "BANK_TRANSFER";
})(PaymentMethodTypeEnum || (exports.PaymentMethodTypeEnum = PaymentMethodTypeEnum = {}));
var PaymentStatusEnum;
(function (PaymentStatusEnum) {
    PaymentStatusEnum["PENDING"] = "PENDING";
    PaymentStatusEnum["COMPLETED"] = "COMPLETED";
    PaymentStatusEnum["FAILED"] = "FAILED";
    PaymentStatusEnum["EXPIRED"] = "EXPIRED";
    PaymentStatusEnum["REFUNDED"] = "REFUNDED";
})(PaymentStatusEnum || (exports.PaymentStatusEnum = PaymentStatusEnum = {}));
// Create mock PrismaClient
exports.mockPrismaClient = {};
//# sourceMappingURL=prisma.mock.js.map