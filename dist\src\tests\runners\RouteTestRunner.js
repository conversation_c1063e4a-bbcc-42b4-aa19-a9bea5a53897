"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteTestRunner = void 0;
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
const RouteTestSuite_1 = require("../suites/RouteTestSuite");
const Container_1 = require("../../core/Container");
const ContainerBootstrap_1 = require("../../core/ContainerBootstrap");
const logger_1 = require("../../lib/logger");
/**
 * Route test runner
 * This class runs route tests
 */
class RouteTestRunner {
    /**
     * Create a new route test runner
     */
    constructor() {
        // Create Express application
        this.app = (0, express_1.default)();
        // Create container
        this.container = Container_1.Container.getInstance();
        // Bootstrap container
        ContainerBootstrap_1.ContainerBootstrap.bootstrap(this.container);
        // Create route test suite
        this.routeTestSuite = new RouteTestSuite_1.RouteTestSuite(this.app);
    }
    /**
     * Run all tests
     */
    async runAllTests() {
        logger_1.logger.info("Running all route tests...");
        try {
            await this.routeTestSuite.runAllTests();
            logger_1.logger.info("All route tests passed");
        }
        catch (error) {
            logger_1.logger.error("Route tests failed:", error);
            throw error;
        }
    }
    /**
     * Get the Express application
     * @returns Express application
     */
    getApp() {
        return this.app;
    }
    /**
     * Get the container
     * @returns Container
     */
    getContainer() {
        return this.container;
    }
    /**
     * Get the route test suite
     * @returns Route test suite
     */
    getRouteTestSuite() {
        return this.routeTestSuite;
    }
}
exports.RouteTestRunner = RouteTestRunner;
exports.default = RouteTestRunner;
//# sourceMappingURL=RouteTestRunner.js.map