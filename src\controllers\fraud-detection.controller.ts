// jscpd:ignore-file
import { Request, Response } from 'express';
import { BaseController } from './base/BaseController';
import { FraudDetectionService, RiskLevel, RiskFactor } from '../services/fraud-detection.service';
import { ServiceError } from '../shared/modules/services/ServiceError';
import prisma from '../lib/prisma';
import { Transaction, Merchant } from '../types';
import { BaseController } from './base/BaseController';
import { FraudDetectionService, RiskLevel, RiskFactor } from '../services/fraud-detection.service';
import { ServiceError } from '../shared/modules/services/ServiceError';
import { Transaction, Merchant } from '../types';

export class FraudDetectionController extends BaseController {
  private fraudDetectionService: FraudDetectionService;

  /**
   * Async handler for controller methods
   * @param fn Function to handle
   * @returns Express handler
   */
  private asyncHandler(fn: (req: Request, res: Response) => Promise<any>) {
    return async (req: Request, res: Response) => {
      try {
        await fn(req, res);
      } catch (error) {
        this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
          (error as Error).message || 'An unexpected error occurred',
          500
        ));
      }
    };
  }

  constructor() {
    super();
    this.fraudDetectionService = new FraudDetectionService();
  }

  /**
   * Assess transaction risk
   */
  assessTransactionRisk = this.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { transactionId } = req.params;
      const { ipAddress, userAgent, deviceId } = req.body;

      // Validate required fields
      if (!transactionId || !ipAddress) {
        return this.sendValidationError(res, {
          transactionId: !transactionId ? ['Transaction ID is required'] : [],
          ipAddress: !ipAddress ? ['IP address is required'] : [],
        });
      }

      // Get transaction
      const transaction: any = await prisma.transaction.findUnique({
        where: { id: transactionId },
      });

      if (!transaction) {
        return this.sendNotFound(res, 'Transaction', transactionId);
      }

      // Get merchant
      const merchant: any = await prisma.merchant.findUnique({
        where: { id: transaction.merchantId },
      });

      if (!merchant) {
        return this.sendNotFound(res, 'Merchant', transaction.merchantId);
      }

      // Assess transaction risk
      const riskAssessment: any = await this.fraudDetectionService.assessTransactionRisk(
        transaction,
        ipAddress,
        userAgent || 'Unknown',
        deviceId || 'Unknown',
        merchant
      );

      // Send success response
      this.sendSuccess(res, riskAssessment, 'Transaction risk assessment completed');
    } catch (error) {
      this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
        (error as Error).message || 'Failed to assess transaction risk',
        500
      ));
    }
  });

  /**
   * Get risk assessment for a transaction
   */
  getTransactionRiskAssessment = this.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { transactionId } = req.params;

      // Get risk assessment
      const riskAssessment: any = await prisma.riskAssessment.findFirst({
        where: { transactionId },
        orderBy: { createdAt: 'desc' },
      });

      if (!riskAssessment) {
        return this.sendNotFound(res, 'Risk assessment', transactionId);
      }

      // Format response
      const response: any = {
        transactionId: riskAssessment.transactionId,
        riskScore: { score: riskAssessment.score,
          level: riskAssessment.level as RiskLevel,
          factors: JSON.parse(riskAssessment.factors),
          timestamp: riskAssessment.createdAt,
        },
        isFlagged: riskAssessment.isFlagged,
        isBlocked: riskAssessment.isBlocked,
        createdAt: riskAssessment.createdAt,
      };

      // Send success response
      this.sendSuccess(res, response, 'Transaction risk assessment retrieved');
    } catch (error) {
      this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
        (error as Error).message || 'Failed to get transaction risk assessment',
        500
      ));
    }
  });

  /**
   * Get fraud detection configuration for a merchant
   */
  getMerchantFraudConfig = this.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { merchantId } = req.params;

      // Get fraud detection configuration
      const config: any = await prisma.fraudDetectionConfig.findUnique({
        where: { merchantId: parseInt(merchantId) },
      });

      if (!config) {
        return this.sendNotFound(res, 'Fraud detection configuration', merchantId);
      }

      // Format response
      const response: any = {
        merchantId: config.merchantId,
        flagThreshold: config.flagThreshold,
        blockThreshold: config.blockThreshold,
        autoBlock: config.autoBlock,
        factorWeights: JSON.parse(config.factorWeights),
        highRiskCountries: JSON.parse(config.highRiskCountries),
        highRiskIpRanges: JSON.parse(config.highRiskIpRanges),
        maxTransactionAmount: config.maxTransactionAmount,
        maxTransactionsPerHour: config.maxTransactionsPerHour,
        maxTransactionsPerDay: config.maxTransactionsPerDay,
        createdAt: config.createdAt,
        updatedAt: config.updatedAt,
      };

      // Send success response
      this.sendSuccess(res, response, 'Fraud detection configuration retrieved');
    } catch (error) {
      this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
        (error as Error).message || 'Failed to get fraud detection configuration',
        500
      ));
    }
  });

  /**
   * Update fraud detection configuration for a merchant
   */
  updateMerchantFraudConfig = this.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { merchantId } = req.params;
      const {
        flagThreshold,
        blockThreshold,
        autoBlock,
        factorWeights,
        highRiskCountries,
        highRiskIpRanges,
        maxTransactionAmount,
        maxTransactionsPerHour,
        maxTransactionsPerDay,
      } = req.body;

      // Validate input
      this.validateFraudConfig(req.body);

      // Check if merchant exists
      const merchant: any = await prisma.merchant.findUnique({
        where: { id: parseInt(merchantId) },
      });

      if (!merchant) {
        return this.sendNotFound(res, 'Merchant', merchantId);
      }

      // Update or create fraud detection configuration
      const config: any = await prisma.fraudDetectionConfig.upsert({
        where: { merchantId: parseInt(merchantId) },
        update: {
          flagThreshold,
          blockThreshold,
          autoBlock,
          factorWeights: JSON.stringify(factorWeights),
          highRiskCountries: JSON.stringify(highRiskCountries),
          highRiskIpRanges: JSON.stringify(highRiskIpRanges),
          maxTransactionAmount,
          maxTransactionsPerHour,
          maxTransactionsPerDay,
          updatedAt: new Date(),
        },
        create: { merchantId: parseInt(merchantId),
          flagThreshold,
          blockThreshold,
          autoBlock,
          factorWeights: JSON.stringify(factorWeights),
          highRiskCountries: JSON.stringify(highRiskCountries),
          highRiskIpRanges: JSON.stringify(highRiskIpRanges),
          maxTransactionAmount,
          maxTransactionsPerHour,
          maxTransactionsPerDay,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Format response
      const response: any = {
        merchantId: config.merchantId,
        flagThreshold: config.flagThreshold,
        blockThreshold: config.blockThreshold,
        autoBlock: config.autoBlock,
        factorWeights: JSON.parse(config.factorWeights),
        highRiskCountries: JSON.parse(config.highRiskCountries),
        highRiskIpRanges: JSON.parse(config.highRiskIpRanges),
        maxTransactionAmount: config.maxTransactionAmount,
        maxTransactionsPerHour: config.maxTransactionsPerHour,
        maxTransactionsPerDay: config.maxTransactionsPerDay,
        createdAt: config.createdAt,
        updatedAt: config.updatedAt,
      };

      // Send success response
      this.sendSuccess(res, response, 'Fraud detection configuration updated');
    } catch (error) {
      this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
        (error as Error).message || 'Failed to update fraud detection configuration',
        500
      ));
    }
  });

  /**
   * Get flagged transactions
   */
  getFlaggedTransactions = this.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { merchantId } = req.query;
      const { skip, take } = this.getPaginationParams(req);

      // Build where clause
      const where: any = {
        isFlagged: true,
      };

      // Add merchant filter if provided
      if (merchantId) {
        where.transaction = {
          merchantId: parseInt(merchantId as string),
        };
      }

      // Get flagged transactions
      const riskAssessments: any = await prisma.riskAssessment.findMany({
        where,
        include: { transaction: {
            include: { merchant: {
                select: { id: true,
                  name: true,
                },
              },
              paymentMethod: { select: {
                  id: true,
                  name: true,
                  code: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take,
      });

      // Get total count
      const total: any = await prisma.riskAssessment.count({
        where,
      });

      // Format response
      const formattedAssessments: any = riskAssessments.map(assessment => ({
        id: assessment.id,
        transactionId: assessment.transactionId,
        score: assessment.score,
        level: assessment.level,
        isFlagged: assessment.isFlagged,
        isBlocked: assessment.isBlocked,
        createdAt: assessment.createdAt,
        transaction: { id: assessment.transaction.id,
          amount: assessment.transaction.amount,
          currency: assessment.transaction.currency,
          status: assessment.transaction.status,
          customerEmail: assessment.transaction.customerEmail,
          customerName: assessment.transaction.customerName,
          createdAt: assessment.transaction.createdAt,
          merchant: assessment.transaction.merchant,
          paymentMethod: assessment.transaction.paymentMethod,
        },
      }));

      // Create pagination info
      const pagination: any = this.createPaginationInfo(req, total);

      // Send paginated response
      this.sendPaginatedSuccess(res, formattedAssessments, pagination, 'Flagged transactions retrieved');
    } catch (error) {
      this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
        (error as Error).message || 'Failed to get flagged transactions',
        500
      ));
    }
  });

  /**
   * Get fraud statistics
   */
  getFraudStatistics = this.asyncHandler(async (req: Request, res: Response) => {
    try {
      const { merchantId, startDate, endDate } = req.query;

      // Parse dates
      const start: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const end: any = endDate ? new Date(endDate as string) : new Date();

      // Build where clause
      const where: any = {
        createdAt: { gte: start,
          lte: end,
        },
      };

      // Add merchant filter if provided
      if (merchantId) {
        where.transaction = {
          merchantId: parseInt(merchantId as string),
        };
      }

      // Get risk assessments
      const riskAssessments: any = await prisma.riskAssessment.findMany({
        where,
        select: { id: true,
          score: true,
          level: true,
          isFlagged: true,
          isBlocked: true,
          createdAt: true,
        },
      });

      // Calculate statistics
      const totalAssessments: any = riskAssessments.length;
      const flaggedCount: any = riskAssessments.filter(a => a.isFlagged).length;
      const blockedCount: any = riskAssessments.filter(a => a.isBlocked).length;

      const levelCounts: any = {
        [RiskLevel.LOW]: riskAssessments.filter(a => a.level === RiskLevel.LOW).length,
        [RiskLevel.MEDIUM]: riskAssessments.filter(a => a.level === RiskLevel.MEDIUM).length,
        [RiskLevel.HIGH]: riskAssessments.filter(a => a.level === RiskLevel.HIGH).length,
        [RiskLevel.CRITICAL]: riskAssessments.filter(a => a.level === RiskLevel.CRITICAL).length,
      };

      // Group by day
      const dailyStats: any = this.groupByDay(riskAssessments, start, end);

      // Format response
      const response: any = {
        totalAssessments,
        flaggedCount,
        blockedCount,
        flaggedRate: totalAssessments > 0 ? (flaggedCount / totalAssessments) * 100 : 0,
        blockedRate: totalAssessments > 0 ? (blockedCount / totalAssessments) * 100 : 0,
        levelCounts,
        dailyStats,
        period: {
          start,
          end,
        },
      };

      // Send success response
      this.sendSuccess(res, response, 'Fraud statistics retrieved');
    } catch (error) {
      this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
        (error as Error).message || 'Failed to get fraud statistics',
        500
      ));
    }
  });

  /**
   * Get pagination parameters from request
   * @param req Request
   * @returns Pagination parameters
   */
  private getPaginationParams(req: Request): { skip: number; take: number } {
    const page = parseInt(req.query.page as string) || 1;
    const limit: any = parseInt(req.query.limit as string) || 10;
    const skip: any = (page - 1) * limit;
    const take: any = limit;

    return { skip, take };
  }

  /**
   * Create pagination info
   * @param req Request
   * @param total Total count
   * @returns Pagination info
   */
  private createPaginationInfo(req: Request, total: number): any {
    const page = parseInt(req.query.page as string) || 1;
    const limit: any = parseInt(req.query.limit as string) || 10;
    const totalPages: any = Math.ceil(total / limit);

    return {
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  /**
   * Group risk assessments by day
   * @param assessments Risk assessments
   * @param startDate Start date
   * @param endDate End date
   * @returns Daily statistics
   */
  private groupByDay(assessments: any[], startDate: Date, endDate: Date): any[] {
    const dailyStats: any[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const day: any = currentDate.toISOString().split('T')[0];
      const dayAssessments: any = assessments.filter((a)) => {
        const assessmentDay = a.createdAt.toISOString().split('T')[0];
        return assessmentDay === day;
      });

      dailyStats.push({
        date: day,
        totalAssessments: dayAssessments.length,
        flaggedCount: dayAssessments.filter(a => a.isFlagged).length,
        blockedCount: dayAssessments.filter(a => a.isBlocked).length,
        levelCounts: {
          [RiskLevel.LOW]: dayAssessments.filter(a => a.level === RiskLevel.LOW).length,
          [RiskLevel.MEDIUM]: dayAssessments.filter(a => a.level === RiskLevel.MEDIUM).length,
          [RiskLevel.HIGH]: dayAssessments.filter(a => a.level === RiskLevel.HIGH).length,
          [RiskLevel.CRITICAL]: dayAssessments.filter(a => a.level === RiskLevel.CRITICAL).length,
        },
      });

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dailyStats;
  }

  /**
   * Send validation error response
   * @param res Response
   * @param errors Validation errors
   * @returns Response
   */
  private sendValidationError(res: Response, errors: Record<string, string[]>): Response {
    return res.status(400).json({
      status: 'error',
      message: 'Validation failed',
      errors
    });
  }

  /**
   * Send not found error response
   * @param res Response
   * @param entity Entity name
   * @param id Entity ID
   * @returns Response
   */
  private sendNotFound(res: Response, entity: string, id: string): Response {
    return res.status(404).json({
      status: 'error',
      message: `${entity} with ID ${id} not found`
    });
  }

  /**
   * Send paginated success response
   * @param res Response
   * @param data Response data
   * @param pagination Pagination info
   * @param message Success message
   * @returns Response
   */
  private sendPaginatedSuccess(res: Response, data: any, pagination: any, message: string): Response {
    return res.status(200).json({
      status: 'success',
      message,
      data,
      pagination
    });
  }

  /**
   * Send success response
   * @param res Response
   * @param data Response data
   * @param message Success message
   * @returns Response
   */
  private sendSuccess(res: Response, data: any, message: string): Response {
    return res.status(200).json({
      status: 'success',
      message,
      data
    });
  }

  /**
   * Send error response
   * @param res Response
   * @param error Service error
   * @returns Response
   */
  private sendError(res: Response, error: ServiceError): Response {
    return res.status(error.statusCode || 500).json({
      status: 'error',
      message: (error as Error).message,
      code: error.errorCode,
      errors: error.validationErrors
    });
  }

  /**
   * Validate fraud detection configuration
   * @param config Fraud detection configuration
   * @throws ServiceError if validation fails
   */
  private validateFraudConfig(config): void {
    const validationErrors: Record<string, string[]> = {};

    // Validate thresholds
    if (config.flagThreshold !== undefined) {
      if (typeof config.flagThreshold !== 'number' || config.flagThreshold < 0 || config.flagThreshold > 100) {
        validationErrors.flagThreshold = ['Flag threshold must be a number between 0 and 100'];
      }
    }

    if (config.blockThreshold !== undefined) {
      if (typeof config.blockThreshold !== 'number' || config.blockThreshold < 0 || config.blockThreshold > 100) {
        validationErrors.blockThreshold = ['Block threshold must be a number between 0 and 100'];
      }
    }

    // Validate autoBlock
    if (config.autoBlock !== undefined && typeof config.autoBlock !== 'boolean') {
      validationErrors.autoBlock = ['Auto block must be a boolean'];
    }

    // Validate factorWeights
    if (config.factorWeights !== undefined) {
      if (typeof config.factorWeights !== 'object') {
        validationErrors.factorWeights = ['Factor weights must be an object'];
      } else {
        const factorWeightErrors: string[] = [];

        // Check if all factors are valid
        Object.keys(config.factorWeights).forEach((factor)) => {
          if (!Object.values(RiskFactor).includes(factor as RiskFactor)) {
            factorWeightErrors.push(`Invalid risk factor: ${factor}`);
          }
        });

        // Check if all weights are valid
        Object.values(config.factorWeights).forEach(((weight)) => {
          if (typeof weight !== 'number' || weight < 0 || weight > 1) {
            factorWeightErrors.push('Factor weights must be numbers between 0 and 1');
          }
        });

        if (factorWeightErrors.length > 0) {
          validationErrors.factorWeights = factorWeightErrors;
        }
      }
    }

    // Validate arrays
    if (config.highRiskCountries !== undefined && !Array.isArray(config.highRiskCountries)) {
      validationErrors.highRiskCountries = ['High risk countries must be an array'];
    }

    if (config.highRiskIpRanges !== undefined && !Array.isArray(config.highRiskIpRanges)) {
      validationErrors.highRiskIpRanges = ['High risk IP ranges must be an array'];
    }

    // Validate numeric values
    if (config.maxTransactionAmount !== undefined && (typeof config.maxTransactionAmount !== 'number' || config.maxTransactionAmount <= 0)) {
      validationErrors.maxTransactionAmount = ['Max transaction amount must be a positive number'];
    }

    if (config.maxTransactionsPerHour !== undefined && (typeof config.maxTransactionsPerHour !== 'number' || config.maxTransactionsPerHour <= 0)) {
      validationErrors.maxTransactionsPerHour = ['Max transactions per hour must be a positive number'];
    }

    if (config.maxTransactionsPerDay !== undefined && (typeof config.maxTransactionsPerDay !== 'number' || config.maxTransactionsPerDay <= 0)) {
      validationErrors.maxTransactionsPerDay = ['Max transactions per day must be a positive number'];
    }

    // Throw validation error if there are any errors
    if (Object.keys(validationErrors).length > 0) {
      throw new ServiceError(
        'Validation failed',
        400,
        'VALIDATION_ERROR',
        undefined,
        undefined,
        validationErrors
      );
    }
  }
}
