/**
 * Security Middleware
 *
 * This middleware provides additional security features for the API.
 */
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                role: string;
                merchantId?: string;
            };
        }
    }
}
/**
 * Content Security Policy middleware
 * Sets CSP headers to prevent XSS attacks
 */
export declare const contentSecurityPolicy: any;
/**
 * CSRF Protection middleware
 * Validates CSRF tokens for mutating requests
 */
export declare const csrfProtection: any;
/**
 * HTTP Strict Transport Security middleware
 * Forces browsers to use HTTPS
 */
export declare const hsts: any;
/**
 * X-Content-Type-Options middleware
 * Prevents MIME type sniffing
 */
export declare const noSniff: any;
/**
 * X-Frame-Options middleware
 * Prevents clickjacking
 */
export declare const frameGuard: any;
/**
 * X-XSS-Protection middleware
 * Enables browser XSS filtering
 */
export declare const xssFilter: any;
/**
 * Referrer-Policy middleware
 * Controls the Referer header
 */
export declare const referrerPolicy: any;
/**
 * Permissions-Policy middleware
 * Restricts browser features
 */
export declare const permissionsPolicy: any;
/**
 * Cache Control middleware
 * Sets cache control headers to prevent sensitive information caching
 */
export declare const cacheControl: any;
/**
 * SQL Injection Protection middleware
 * Basic protection against SQL injection attacks
 */
export declare const sqlInjectionProtection: any;
/**
 * Environment Banner middleware
 * Adds environment information to response headers
 */
export declare const environmentBanner: any;
/**
 * Request Validation middleware
 * Validates request parameters for common security issues
 */
export declare const requestValidation: any;
/**
 * Apply all security middleware
 */
export declare const securityMiddleware: any;
export default securityMiddleware;
//# sourceMappingURL=security.middleware.d.ts.map