"use strict";
// jscpd:ignore-file
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const router = (0, express_1.Router)();
// Send receipt via email
router.post("/email", (0, validation_middleware_1.validate)([
    (0, express_validator_1.body)("transactionId").notEmpty().isString(),
    (0, express_validator_1.body)("email").notEmpty().isEmail()
]), 
// This would connect to an email service in a real implementation
(req, res) => {
    const { transactionId, email } = req.body;
    // In a real implementation, this would generate and send the email
    // Simulate successful email sending
    return res.json({
        success: true,
        message: "Receipt email sent successfully"
    });
});
exports.default = router;
//# sourceMappingURL=receipt.routes.js.map