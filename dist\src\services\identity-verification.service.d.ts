import { IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from "@prisma/client";
import { AppError } from "../utils/app-error";
/**
 * Error codes for identity verification
 */
export declare enum IdentityVerificationErrorCode {
    INVALID_SIGNATURE = "INVALID_SIGNATURE",
    INVALID_ADDRESS = "INVALID_ADDRESS",
    INVALID_PROOF = "INVALID_PROOF",
    VERIFICATION_FAILED = "VERIFICATION_FAILED",
    VERIFICATION_NOT_FOUND = "VERIFICATION_NOT_FOUND",
    CLAIM_NOT_FOUND = "CLAIM_NOT_FOUND",
    INTERNAL_ERROR = "INTERNAL_ERROR",
    PROVIDER_ERROR = "PROVIDER_ERROR",
    INVALID_PARAMETERS = "INVALID_PARAMETERS",
    UNAUTHORIZED = "UNAUTHORIZED"
}
/**
 * Custom error class for identity verification errors
 */
export declare class IdentityVerificationError extends AppError {
    code: IdentityVerificationErrorCode;
    constructor(message: string, code: IdentityVerificationErrorCode, statusCode?: number);
}
export interface IdentityVerificationResult {
    success: boolean;
    method: IdentityVerificationMethodEnum;
    status: IdentityVerificationStatusEnum;
    message: string;
    data?: any;
    error?: string;
    verificationId?: string;
}
export declare class IdentityVerificationService {
    /**
   * Verify identity using Ethereum signature
   */
    verifyEthereumSignature(address: string, message: string, signature: string, userId?: string, merchantId?: string): Promise<IdentityVerificationResult>;
    /**
   * Verify identity using ERC-1484 (Ethereum Identity Registry)
   */
    verifyERC1484Identity(address: string, ein: string, registryAddress: string, userId?: string, merchantId?: string): Promise<IdentityVerificationResult>;
    /**
   * Get identity verification by ID
   */
    getVerificationById(id: string): Promise<any>;
    /**
   * Get identity verifications for user
   */
    getVerificationsForUser(userId: string): Promise<any>;
    /**
   * Get identity verifications for merchant
   */
    getVerificationsForMerchant(merchantId: string): Promise<any>;
    /**
   * Verify identity using ERC-725 (Identity)
   */
    verifyERC725Identity(address: string, key: string, value: string, userId?: string, merchantId?: string): Promise<IdentityVerificationResult>;
    /**
   * Verify identity using ENS
   */
    verifyENS(ensName: string, address: string, userId?: string, merchantId?: string): Promise<IdentityVerificationResult>;
    /**
   * Verify identity using Polygon ID
   */
    verifyPolygonID(address: string, proof: string, userId?: string, merchantId?: string): Promise<IdentityVerificationResult>;
    /**
   * Verify identity using Worldcoin
   */
    verifyWorldcoin(address: string, nullifier: string, proof: string, userId?: string, merchantId?: string): Promise<IdentityVerificationResult>;
    /**
   * Verify identity using Unstoppable Domains
   */
    verifyUnstoppableDomains(domain: string, address: string, userId?: string, merchantId?: string): Promise<IdentityVerificationResult>;
    /**
   * Simulate Unstoppable Domains resolution
   * In a real implementation, this would call the Unstoppable Domains API
   */
    private simulateUnstoppableDomainsResolve;
    /**
   * Add a claim to an identity verification
   */
    addClaim(verificationId: string, type: string, value: string, issuer: string): Promise<any>;
    /**
   * Revoke a claim
   */
    revokeClaim(claimId: string): Promise<any>;
    /**
   * Set expiration for a verification
   */
    setVerificationExpiration(verificationId: string, expiresAt: Date): Promise<any>;
    /**
   * Check if a verification is expired
   */
    checkVerificationExpiration(): Promise<any>;
    /**
   * Get verification statistics
   */
    getVerificationStats(): Promise<{
        totalCount: any;
        methodCounts: any;
        statusCounts: any;
        recentVerifications: any;
    }>;
    /**
   * Create a blockchain-based verification request
   * @param userId User ID
   * @param walletAddress Wallet address
   * @param network Blockchain network
   */
    createBlockchainVerificationRequest(userId: string, walletAddress: string, network: string): Promise<any>;
    /**
   * Complete a blockchain-based verification request
   * @param requestId Verification request ID
   * @param signature Signature provided by the user
   */
    completeBlockchainVerification(requestId: string, signature: string): Promise<boolean>;
    /**
   * Verify Tron signature
   * @param address Tron wallet address
   * @param signature Signature provided by the user
   * @param message Message that was signed
   */
    private verifyTronSignature;
    /**
   * Get supported blockchain networks
   */
    getSupportedNetworks(): Promise<any[]>;
    /**
   * Verify ENS domain ownership
   * @param userId User ID
   * @param ensName ENS domain name
   */
    verifyENSDomain(userId: string, ensName: string): Promise<any>;
    /**
   * Complete ENS verification with signature
   * @param verificationId Verification ID
   * @param signature Signature
   */
    completeENSVerification(verificationId: string, signature: string): Promise<boolean>;
    catch(error: any): void;
}
//# sourceMappingURL=identity-verification.service.d.ts.map