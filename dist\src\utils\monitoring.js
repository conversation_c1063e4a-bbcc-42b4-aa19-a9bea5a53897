"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkSystemHealth = exports.scheduleMonitoringCleanup = exports.initializeMonitoring = exports.getMonitoringData = exports.logMonitoringData = exports.monitorRequest = exports.monitoringEvents = exports.MonitoringUtils = void 0;
const prisma_1 = __importDefault(require("../lib/prisma"));
const logger_1 = require("../lib/logger");
const environment_1 = require("../config/environment");
// MonitoringUtils implementation
exports.MonitoringUtils = {
    recordMetric: (metricName, value, tags = {}) => {
        // Implementation of metric recording
        console.log(`Recording metric: ${metricName} = ${value}`, tags);
    },
    startTimer: (metricName, tags = {}) => {
        const startTime = Date.now();
        return {
            stop: () => {
                const duration = Date.now() - startTime;
                console.log(`Timer ${metricName} stopped: ${duration}ms`, tags);
                return duration;
            }
        };
    }
};
// Re-export the monitoring events from shared MonitoringUtils
exports.monitoringEvents = exports.MonitoringUtils.monitoringEvents;
// Middleware to monitor API requests
const monitorRequest = (req, res, next) => {
    const startTime = Date.now();
    // Store original end method
    const originalEnd = res.end;
    // Override end method to capture response data
    res.end = function (chunk, encoding, callback) {
        const responseTime = Date.now() - startTime;
        const endpoint = req.originalUrl;
        const method = req.method;
        const statusCode = res.statusCode;
        const ipAddress = req.ip || req.socket.remoteAddress || "";
        const userAgent = req.headers["user-agent"] || "";
        const userId = req.user?.id; // Fixed: using id instead of userId
        const userRole = req.user?.role;
        const environment = (0, environment_1.getEnvironment)();
        // Create monitoring data
        const monitoringData = {
            timestamp: new Date(),
            endpoint,
            method,
            statusCode,
            responseTime,
            userId,
            userRole,
            ipAddress,
            userAgent,
            environment
        };
        // Add error message if status code is 4xx or 5xx
        if (statusCode >= 400) {
            monitoringData.errorMessage = res.statusMessage || "Unknown error";
        }
        // Log monitoring data
        (0, exports.logMonitoringData)(monitoringData);
        // Call original end method
        return originalEnd.call(this, chunk, encoding, callback);
    };
    next();
};
exports.monitorRequest = monitorRequest;
// Log monitoring data to database
const logMonitoringData = async (data) => {
    try {
        // Use shared MonitoringUtils to log monitoring data
        await exports.MonitoringUtils.logMonitoringData(data, logger_1.logger, prisma_1.default);
    }
    catch (error) {
        logger_1.logger.error("Error logging monitoring data:", error);
    }
};
exports.logMonitoringData = logMonitoringData;
// Get monitoring data
const getMonitoringData = () => {
    return exports.MonitoringUtils.getMonitoringData();
};
exports.getMonitoringData = getMonitoringData;
// Initialize monitoring
const initializeMonitoring = () => {
    // Listen for monitoring events
    exports.monitoringEvents.on("request", exports.logMonitoringData);
    // Set up periodic health checks
    const healthCheckInterval = parseInt(process.env.HEALTH_CHECK_INTERVAL || "300000", 10); // 5 minutes by default
    const healthCheckTimer = setInterval(async () => {
        try {
            const healthReport = await (0, exports.checkSystemHealth)();
            // Alert on critical issues
            if (healthReport.status === "unhealthy") {
                logger_1.logger.error("System health check failed", healthReport);
                // In a real implementation, you would send alerts via email, SMS, etc.
                // For now, just log the error
            }
            else if (healthReport.status === "warning") {
                logger_1.logger.warn("System health check warning", healthReport);
                // In a real implementation, you would send warnings via email, Slack, etc.
                // For now, just log the warning
            }
        }
        catch (error) {
            logger_1.logger.error("Error during health check:", error);
        }
    }, healthCheckInterval);
    // Log startup
    // Get current environment
    const env = (0, environment_1.getEnvironment)();
    logger_1.logger.info("Monitoring system initialized", {
        environment: env,
        healthCheckInterval
    });
    // Return cleanup function
    return () => {
        exports.monitoringEvents.removeAllListeners();
        clearInterval(healthCheckTimer);
        logger_1.logger.info("Monitoring system stopped");
    };
};
exports.initializeMonitoring = initializeMonitoring;
// Schedule periodic database cleanup
const scheduleMonitoringCleanup = () => {
    // Clean up old monitoring data periodically
    const cleanupInterval = parseInt(process.env.MONITORING_CLEANUP_INTERVAL || "86400000", 10); // 24 hours by default
    const cleanupTimer = setInterval(async () => {
        try {
            // In a real implementation, you would clean up old monitoring data from the database
            // For example:
            // const thirtyDaysAgo = new Date();
            // thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            // await prisma.apiLog.deleteMany({
            //   where: {
            //     timestamp: {
            //       lt: thirtyDaysAgo
            //     }
            //   }
            // });
            logger_1.logger.info("Monitoring data cleanup scheduled");
        }
        catch (error) {
            logger_1.logger.error("Error during monitoring data cleanup:", error);
        }
    }, cleanupInterval);
    // Return the timer
    return cleanupTimer;
};
exports.scheduleMonitoringCleanup = scheduleMonitoringCleanup;
// Check system health
const checkSystemHealth = async () => {
    return exports.MonitoringUtils.checkSystemHealth(prisma_1.default, logger_1.logger);
};
exports.checkSystemHealth = checkSystemHealth;
