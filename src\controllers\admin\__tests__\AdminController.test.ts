/**
 * Unit Tests for Admin Controller
 * 
 * Comprehensive test suite covering all functionality of the AdminController
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Request, Response, NextFunction } from 'express';
import { AdminController } from '../AdminController';
import { AdminBusinessService } from '../services/AdminBusinessService';

// Mock dependencies
jest.mock('../services/AdminBusinessService');

describe('AdminController', () => {
  let controller: AdminController;
  let mockBusinessService: jest.Mocked<AdminBusinessService>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.MockedFunction<NextFunction>;

  beforeEach(() => {
    // Create mock business service
    mockBusinessService = {
      createAdminUser: jest.fn(),
      getAllAdminUsers: jest.fn(),
      getAdminUserById: jest.fn(),
      updateAdminUser: jest.fn(),
      deleteAdminUser: jest.fn(),
      updateAdminPermissions: jest.fn(),
      getAdminStatistics: jest.fn(),
    } as any;

    // Mock AdminBusinessService constructor
    (AdminBusinessService as jest.MockedClass<typeof AdminBusinessService>).mockImplementation(() => mockBusinessService);

    // Initialize controller
    controller = new AdminController();

    // Create mock Express objects
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    mockNext = jest.fn();

    // Reset request for each test
    mockRequest = {
      body: {},
      params: {},
      query: {},
      user: {
        id: testUtils.mockUUID(),
        role: 'ADMIN',
      },
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createAdminUser', () => {
    it('should successfully create admin user', async () => {
      // Arrange
      const adminData = {
        name: 'John Admin',
        email: '<EMAIL>',
        password: 'securePassword123',
        roleId: testUtils.mockUUID(),
      };

      const mockCreatedAdmin = {
        id: testUtils.mockUUID(),
        name: adminData.name,
        email: adminData.email,
        role: 'ADMIN',
        createdAt: new Date(),
      };

      mockRequest.body = adminData;
      mockBusinessService.createAdminUser.mockResolvedValue(mockCreatedAdmin);

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.createAdminUser).toHaveBeenCalledWith(adminData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockCreatedAdmin,
        message: 'Admin user created successfully',
        timestamp: expect.any(String),
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle validation errors', async () => {
      // Arrange
      const invalidData = {
        name: '',
        email: 'invalid-email',
        password: '123', // Too short
      };

      mockRequest.body = invalidData;
      mockBusinessService.createAdminUser.mockRejectedValue(new Error('Validation failed'));

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
    });

    it('should handle duplicate email error', async () => {
      // Arrange
      const adminData = {
        name: 'John Admin',
        email: '<EMAIL>',
        password: 'securePassword123',
        roleId: testUtils.mockUUID(),
      };

      mockRequest.body = adminData;
      mockBusinessService.createAdminUser.mockRejectedValue(new Error('Email already exists'));

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should require admin role', async () => {
      // Arrange
      mockRequest.user = { id: testUtils.mockUUID(), role: 'USER' };
      mockRequest.body = {
        name: 'John Admin',
        email: '<EMAIL>',
        password: 'securePassword123',
        roleId: testUtils.mockUUID(),
      };

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockBusinessService.createAdminUser).not.toHaveBeenCalled();
    });
  });

  describe('getAllAdminUsers', () => {
    it('should return paginated admin users', async () => {
      // Arrange
      const mockAdminUsers = {
        users: [
          {
            id: testUtils.mockUUID(),
            name: 'Admin 1',
            email: '<EMAIL>',
            role: 'ADMIN',
            lastLogin: new Date(),
          },
          {
            id: testUtils.mockUUID(),
            name: 'Admin 2',
            email: '<EMAIL>',
            role: 'SUPER_ADMIN',
            lastLogin: new Date(),
          },
        ],
        total: 2,
        page: 1,
        limit: 20,
      };

      mockRequest.query = { page: '1', limit: '20' };
      mockBusinessService.getAllAdminUsers.mockResolvedValue(mockAdminUsers);

      // Act
      await controller.getAllAdminUsers(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.getAllAdminUsers).toHaveBeenCalledWith({ page: 1, limit: 20 });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockAdminUsers,
        timestamp: expect.any(String),
      });
    });

    it('should use default pagination values', async () => {
      // Arrange
      mockRequest.query = {};
      mockBusinessService.getAllAdminUsers.mockResolvedValue({
        users: [],
        total: 0,
        page: 1,
        limit: 20,
      });

      // Act
      await controller.getAllAdminUsers(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.getAllAdminUsers).toHaveBeenCalledWith({ page: 1, limit: 20 });
    });

    it('should handle service errors', async () => {
      // Arrange
      mockBusinessService.getAllAdminUsers.mockRejectedValue(new Error('Database error'));

      // Act
      await controller.getAllAdminUsers(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('getAdminUserById', () => {
    it('should return admin user by ID', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      const mockAdmin = {
        id: adminId,
        name: 'John Admin',
        email: '<EMAIL>',
        role: 'ADMIN',
        permissions: ['admin:read', 'admin:write'],
        createdAt: new Date(),
      };

      mockRequest.params = { id: adminId };
      mockBusinessService.getAdminUserById.mockResolvedValue(mockAdmin);

      // Act
      await controller.getAdminUserById(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.getAdminUserById).toHaveBeenCalledWith(adminId);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockAdmin,
        timestamp: expect.any(String),
      });
    });

    it('should handle admin not found', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      mockRequest.params = { id: adminId };
      mockBusinessService.getAdminUserById.mockRejectedValue(new Error('Admin not found'));

      // Act
      await controller.getAdminUserById(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should validate admin ID format', async () => {
      // Arrange
      mockRequest.params = { id: 'invalid-id' };

      // Act
      await controller.getAdminUserById(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockBusinessService.getAdminUserById).not.toHaveBeenCalled();
    });
  });

  describe('updateAdminUser', () => {
    it('should successfully update admin user', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      const updateData = {
        name: 'Updated Admin Name',
        email: '<EMAIL>',
      };

      const mockUpdatedAdmin = {
        id: adminId,
        ...updateData,
        role: 'ADMIN',
        updatedAt: new Date(),
      };

      mockRequest.params = { id: adminId };
      mockRequest.body = updateData;
      mockBusinessService.updateAdminUser.mockResolvedValue(mockUpdatedAdmin);

      // Act
      await controller.updateAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.updateAdminUser).toHaveBeenCalledWith(adminId, updateData);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedAdmin,
        message: 'Admin user updated successfully',
        timestamp: expect.any(String),
      });
    });

    it('should prevent updating own admin record', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      mockRequest.user = { id: adminId, role: 'ADMIN' };
      mockRequest.params = { id: adminId };
      mockRequest.body = { name: 'New Name' };

      // Act
      await controller.updateAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockBusinessService.updateAdminUser).not.toHaveBeenCalled();
    });
  });

  describe('deleteAdminUser', () => {
    it('should successfully delete admin user', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      mockRequest.params = { id: adminId };
      mockRequest.user = { id: 'different-admin-id', role: 'SUPER_ADMIN' };
      mockBusinessService.deleteAdminUser.mockResolvedValue(undefined);

      // Act
      await controller.deleteAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.deleteAdminUser).toHaveBeenCalledWith(adminId);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Admin user deleted successfully',
        timestamp: expect.any(String),
      });
    });

    it('should prevent deleting own admin record', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      mockRequest.user = { id: adminId, role: 'ADMIN' };
      mockRequest.params = { id: adminId };

      // Act
      await controller.deleteAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockBusinessService.deleteAdminUser).not.toHaveBeenCalled();
    });

    it('should require SUPER_ADMIN role for deletion', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      mockRequest.user = { id: 'different-id', role: 'ADMIN' };
      mockRequest.params = { id: adminId };

      // Act
      await controller.deleteAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockBusinessService.deleteAdminUser).not.toHaveBeenCalled();
    });
  });

  describe('updateAdminPermissions', () => {
    it('should successfully update admin permissions', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      const permissions = ['admin:read', 'admin:write', 'users:manage'];

      const mockUpdatedAdmin = {
        id: adminId,
        permissions,
        updatedAt: new Date(),
      };

      mockRequest.params = { id: adminId };
      mockRequest.body = { permissions };
      mockBusinessService.updateAdminPermissions.mockResolvedValue(mockUpdatedAdmin);

      // Act
      await controller.updateAdminPermissions(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.updateAdminPermissions).toHaveBeenCalledWith(adminId, permissions);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedAdmin,
        message: 'Admin permissions updated successfully',
        timestamp: expect.any(String),
      });
    });

    it('should validate permissions array', async () => {
      // Arrange
      const adminId = testUtils.mockUUID();
      mockRequest.params = { id: adminId };
      mockRequest.body = { permissions: 'invalid-permissions' };

      // Act
      await controller.updateAdminPermissions(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockBusinessService.updateAdminPermissions).not.toHaveBeenCalled();
    });
  });

  describe('getAdminStatistics', () => {
    it('should return admin statistics', async () => {
      // Arrange
      const mockStatistics = {
        totalAdmins: 10,
        activeAdmins: 8,
        superAdmins: 2,
        recentLogins: 5,
        averageSessionDuration: 3600,
      };

      mockBusinessService.getAdminStatistics.mockResolvedValue(mockStatistics);

      // Act
      await controller.getAdminStatistics(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.getAdminStatistics).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockStatistics,
        timestamp: expect.any(String),
      });
    });

    it('should handle service errors', async () => {
      // Arrange
      mockBusinessService.getAdminStatistics.mockRejectedValue(new Error('Statistics error'));

      // Act
      await controller.getAdminStatistics(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('Authorization Middleware', () => {
    it('should allow SUPER_ADMIN access to all endpoints', async () => {
      // Arrange
      mockRequest.user = { id: testUtils.mockUUID(), role: 'SUPER_ADMIN' };
      mockRequest.body = {
        name: 'Test Admin',
        email: '<EMAIL>',
        password: 'password123',
        roleId: testUtils.mockUUID(),
      };

      mockBusinessService.createAdminUser.mockResolvedValue({
        id: testUtils.mockUUID(),
        name: 'Test Admin',
        email: '<EMAIL>',
        role: 'ADMIN',
        createdAt: new Date(),
      });

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockBusinessService.createAdminUser).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(201);
    });

    it('should deny access to non-admin users', async () => {
      // Arrange
      mockRequest.user = { id: testUtils.mockUUID(), role: 'USER' };

      // Act
      await controller.getAllAdminUsers(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockBusinessService.getAllAdminUsers).not.toHaveBeenCalled();
    });
  });

  describe('Input Validation', () => {
    it('should validate email format', async () => {
      // Arrange
      mockRequest.body = {
        name: 'Test Admin',
        email: 'invalid-email-format',
        password: 'password123',
        roleId: testUtils.mockUUID(),
      };

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should validate password strength', async () => {
      // Arrange
      mockRequest.body = {
        name: 'Test Admin',
        email: '<EMAIL>',
        password: '123', // Too weak
        roleId: testUtils.mockUUID(),
      };

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should validate required fields', async () => {
      // Arrange
      mockRequest.body = {
        name: '', // Missing name
        email: '<EMAIL>',
        password: 'password123',
      };

      // Act
      await controller.createAdminUser(mockRequest as Request, mockResponse as Response, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});
