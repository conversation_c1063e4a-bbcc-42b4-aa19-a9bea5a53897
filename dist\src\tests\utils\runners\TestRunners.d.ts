/**
 * Test Runners
 *
 * Functions to execute tests for different types of components.
 */
import { ControllerTestOptions, ServiceTestOptions, RepositoryTestOptions, MiddlewareTestOptions, MockRequest, MockResponse, MockNext, TestResult } from '../core/TestTypes';
/**
 * Test a controller method
 */
export declare function testController(controller: any, method: string, options?: ControllerTestOptions): Promise<{
    req: MockRequest;
    res: MockResponse;
    next: MockNext;
    result?: any;
}>;
/**
 * Test a service method
 */
export declare function testService(service: any, method: string, options?: ServiceTestOptions): Promise<TestResult>;
/**
 * Test a repository method
 */
export declare function testRepository(repository: any, method: string, options?: RepositoryTestOptions): Promise<TestResult>;
/**
 * Test middleware
 */
export declare function testMiddleware(middleware: Function, options?: MiddlewareTestOptions): Promise<{
    req: MockRequest;
    res: MockResponse;
    next: <PERSON>ck<PERSON><PERSON><PERSON>;
}>;
/**
 * Test utility function
 */
export declare function testUtility(utilityFunction: Function, options?: {
    args?: any[];
    expectedResult?: any;
    expectedError?: any;
    setup?: () => void | Promise<void>;
    cleanup?: () => void | Promise<void>;
}): Promise<TestResult>;
//# sourceMappingURL=TestRunners.d.ts.map