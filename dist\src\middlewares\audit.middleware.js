"use strict";
// jscpd:ignore-file
/**
 * Audit Middleware
 *
 * Middleware for audit logging of admin actions.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditError = exports.auditLog = void 0;
const audit_service_1 = require("../services/audit.service");
const client_1 = require("@prisma/client");
const logger_1 = require("../lib/logger");
const prisma = new client_1.PrismaClient();
const auditService = new audit_service_1.AuditService(prisma);
/**
 * Middleware to log admin actions
 */
const auditLog = (action, resource) => {
    return async (req, res, next) => {
        // Store the original end method
        const originalEnd = res.end;
        const originalJson = res.json;
        // Get resource ID from request parameters
        const resourceId = req.params.id;
        // Get old values for update/delete operations
        let oldValues;
        if (["update", "delete"].includes(action) && resourceId) {
            try {
                // Attempt to get the current state of the resource
                const resourceData = await prisma[resource].findUnique({
                    where: { id: resourceId }
                });
                if (resourceData) {
                    oldValues = resourceData;
                }
            }
            catch (error) {
                logger_1.logger.error(`Error getting old values for ${resource}:${resourceId}`, error);
            }
        }
        // Override the end method to capture the response
        res.end = function (chunk, encoding, callback) {
            // Restore the original end method
            res.end = originalEnd;
            // Log the action
            if (req.user) {
                auditService.logAction({
                    userId: req.user.id,
                    action,
                    resource,
                    resourceId,
                    ipAddress: req.ip,
                    userAgent: req.headers["user-agent"],
                    oldValues,
                    statusCode: res.statusCode
                }).catch((error));
            }
        };
    };
};
exports.auditLog = auditLog;
{
    logger_1.logger.error("Error logging audit action:", error);
}
;
// Call the original end method
return originalEnd.call(this, chunk, encoding, callback);
;
// Override the json method to capture the response data
res.json = function (body) {
    // Restore the original json method
    res.json = originalJson;
    // Log the action with the response data
    if (req.user) {
        auditService.logAction({
            userId: req.user.id,
            action,
            resource,
            resourceId,
            ipAddress: req.ip,
            userAgent: req.headers["user-agent"],
            oldValues,
            newValues: body,
            statusCode: res.statusCode
        }).catch((error));
        {
            logger_1.logger.error("Error logging audit action:", error);
        }
        ;
    }
    // Call the original json method
    return originalJson.call(this, body);
};
next();
;
;
/**
 * Middleware to log errors
 */
const auditError = (action, resource) => {
    return (err, req, res, next) => {
        if (req.user) {
            auditService.logAction({
                userId: req.user.id,
                action,
                resource,
                resourceId: req.params.id,
                ipAddress: req.ip,
                userAgent: req.headers["user-agent"],
                statusCode: err.statusCode || 500,
                errorMessage: err.message
            }).catch((error));
        }
    };
};
exports.auditError = auditError;
{
    logger_1.logger.error("Error logging audit error:", error);
}
;
next(err);
;
;
//# sourceMappingURL=audit.middleware.js.map